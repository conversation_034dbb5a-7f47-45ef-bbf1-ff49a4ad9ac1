package com.hvisions.hiperbase.controller;

import com.hvisions.hiperbase.equipment.equipmentclass.EquipmentClassDTO;
import com.hvisions.hiperbase.equipment.equipmentclass.EquipmentClassProperty;
import com.hvisions.hiperbase.equipment.equipmentclass.EquipmentClassQuery;
import com.hvisions.hiperbase.service.equipment.EquipmentClassService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title: EquipmentClassController</p>
 * <p>Description: 设备属性类型</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/3/25</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@Api(description = "设备属性类型控制器")
@RequestMapping("/equipmentClass")
public class EquipmentClassController {
    private EquipmentClassService equipmentClassService;

    @Autowired
    public EquipmentClassController(EquipmentClassService equipmentClassService) {
        this.equipmentClassService = equipmentClassService;
    }

    /**
     * 创建设备属性类型
     *
     * @param classDTO 属性类型
     * @return 主键
     */
    @PostMapping("/create")
    @ApiOperation("创建设备属性类型")
    public Integer create(@Valid @RequestBody EquipmentClassDTO classDTO) {
        return equipmentClassService.create(classDTO);
    }

    /**
     * 修改设备属性类型
     *
     * @param classDTO 属性类型
     */
    @PutMapping("/update")
    @ApiOperation("修改设备属性类型")
    public void update(@Valid @RequestBody EquipmentClassDTO classDTO) {
        equipmentClassService.update(classDTO);
    }


    /**
     * 删除设备属性类型
     *
     * @param id 属性类型
     */
    @DeleteMapping("/deleteById/{id}")
    @ApiOperation("删除设备属性类型")
    public void deleteById(@PathVariable Integer id) {
        equipmentClassService.deleteById(id);
    }

    /**
     * 获取设备属性类型
     *
     * @param id 属性类型主键
     * @return 属性类型
     */
    @GetMapping("/findById/{id}")
    @ApiOperation("获取设备属性类型")
    public EquipmentClassDTO findById(@PathVariable Integer id) {
        return equipmentClassService.findById(id);
    }

    /**
     * 获取设备属性类型
     *
     * @param code 属性类型编码
     * @return 属性类型
     */
    @GetMapping("/findByCode/{code}")
    @ApiOperation("获取设备属性类型")
    public EquipmentClassDTO findByCode(@PathVariable String code) {
        return equipmentClassService.findByCode(code);
    }

    /**
     * 获取设备属性类型分页数据
     *
     * @param query 属性类型编码
     * @return 属性类型分页数据
     */
    @PostMapping("/findPage")
    @ApiOperation("获取设备属性类型分页数据")
    public Page<EquipmentClassDTO> findPage(@RequestBody EquipmentClassQuery query) {
        return equipmentClassService.findPage(query);
    }

    /**
     * 根据设备id查询设备属性类型列表
     *
     * @param id 设备di
     * @return 设备属性类型列表
     */
    @GetMapping("/findByEquipmentId/{id}")
    @ApiOperation("根据设备id查询设备属性类型列表")
    public List<EquipmentClassDTO> findByEquipmentId(@PathVariable Integer id) {
        return equipmentClassService.findByEquipmentId(id);
    }

    /**
     * 向设备添加设备属性类型
     *
     * @param equipmentId 设备di
     * @param classId     属性类型id
     */
    @PostMapping("/addClassToEquipment")
    @ApiOperation("向设备添加设备属性类型")
    public void addClassToEquipment(@RequestParam Integer equipmentId, @RequestParam Integer classId) {
        equipmentClassService.addClassToEquipment(equipmentId, classId);
    }

    @PostMapping("/addEquipmentClass")
    @ApiOperation("向设备添加设备属性类型")
    public void addEquipmentClass(@RequestBody @Valid EquipmentClassProperty property) {
        equipmentClassService.addEquipmentClass(property);
    }

    /**
     * 删除设备的设备属性类型
     *
     * @param equipmentId 设备di
     * @param classId     属性类型id
     */
    @DeleteMapping("/removeClassToEquipment")
    @ApiOperation("删除设备的设备属性类型")
    public void removeClassToEquipment(@RequestParam Integer equipmentId, @RequestParam Integer classId) {
        equipmentClassService.removeClassToEquipment(equipmentId, classId);
    }

    /**
     * 查询设备是否具有设备属性类型
     *
     * @param equipmentId 设备id
     * @param classCode   类型编码
     * @return 是否存在属性类型
     */
    @GetMapping("/existsEquipmentClass")
    @ApiOperation("查询设备是否具有设备属性类型")
    public Boolean existsEquipmentClass(@RequestParam Integer equipmentId, @RequestParam String classCode) {
        List<EquipmentClassDTO> classes = equipmentClassService.findByEquipmentId(equipmentId);
        return classes.stream().anyMatch(t -> t.getCode().equals(classCode));
    }
}









