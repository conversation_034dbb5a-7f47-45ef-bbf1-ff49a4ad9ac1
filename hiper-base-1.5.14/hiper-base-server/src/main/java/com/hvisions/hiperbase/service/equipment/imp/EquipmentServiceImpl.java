package com.hvisions.hiperbase.service.equipment.imp;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.hvisions.common.config.coderule.utils.SerialCodeUtils;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.ExtendInfo;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.service.BaseExtendService;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.framework.client.DepartmentClient;
import com.hvisions.hiperbase.CodeRuleConsts;
import com.hvisions.hiperbase.SysBaseDTO;
import com.hvisions.hiperbase.client.SpareClient;
import com.hvisions.hiperbase.client.SpareDTO;
import com.hvisions.hiperbase.consts.EquipmentConsts;
import com.hvisions.hiperbase.dao.equipment.EquipmentMapper;
import com.hvisions.hiperbase.entity.SysBase;
import com.hvisions.hiperbase.entity.equipment.*;
import com.hvisions.hiperbase.enums.EquipmentExceptionEnum;
import com.hvisions.hiperbase.enums.EquipmentParentEnum;
import com.hvisions.hiperbase.equipment.*;
import com.hvisions.hiperbase.equipment.location.LocationDTO;
import com.hvisions.hiperbase.equipment.location.LocationMsgDTO;
import com.hvisions.hiperbase.repository.equipment.*;
import com.hvisions.hiperbase.service.equipment.EquipmentMapService;
import com.hvisions.hiperbase.service.equipment.EquipmentService;
import com.hvisions.hiperbase.service.equipment.EquipmentTypeService;
import com.hvisions.hiperbase.service.equipment.LocationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.SerializationUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title: HvEquipmentExtendServiceImp</p>
 * <p>Description: 设备属性扩展服务实现</p>
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p>
 * <p>create date: 2018/11/15</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
@Slf4j
public class EquipmentServiceImpl implements EquipmentService {

    private final EquipmentRepository equipmentRepository;
    private final EquipmentTypeRepository hvEquipmentTypeRepository;
    private final LocationRepository locationRepository;
    private final EquipmentCellRepository equipmentCellRepository;
    private final EquipmentMapper equipmentMapper;
    private final LocationService locationService;
    private final EquipmentTypeService equipmentTypeService;
    private final ApplicationContext applicationContext;
    private final EquipmentStatusRepository equipmentStatusRepository;
    private final DepartmentClient departmentClient;
    private final EquipmentSparePartRepository equipmentSparePartRepository;
    private final SpareClient spareClient;
    private final EquipmentMapService equipmentMapService;
    private final SerialCodeUtils serialCodeUtils;
    private static final Integer EQUIPMENT_TYPE = 100;

    private static final String DEFAULT_EQUIPMENT_STATE = "ready";
    @Resource(name = "equipment_extend")
    BaseExtendService equipmentExtendService;

    @Autowired
    public EquipmentServiceImpl(LocationRepository locationRepository,
                                EquipmentRepository equipmentRepository,
                                EquipmentTypeRepository hvEquipmentTypeRepository,
                                EquipmentCellRepository equipmentCellRepository,
                                EquipmentMapper equipmentMapper,
                                LocationService locationService,
                                EquipmentTypeService equipmentTypeService,
                                ApplicationContext applicationContext,
                                EquipmentStatusRepository equipmentStatusRepository,
                                DepartmentClient departmentClient,
                                EquipmentSparePartRepository equipmentSparePartRepository,
                                SpareClient spareClient,
                                EquipmentMapService equipmentMapService,
                                SerialCodeUtils serialCodeUtils) {
        this.equipmentRepository = equipmentRepository;
        this.hvEquipmentTypeRepository = hvEquipmentTypeRepository;
        this.locationRepository = locationRepository;
        this.equipmentCellRepository = equipmentCellRepository;
        this.equipmentMapper = equipmentMapper;
        this.locationService = locationService;
        this.equipmentTypeService = equipmentTypeService;
        this.applicationContext = applicationContext;
        this.equipmentStatusRepository = equipmentStatusRepository;
        this.departmentClient = departmentClient;
        this.equipmentSparePartRepository = equipmentSparePartRepository;
        this.spareClient = spareClient;
        this.equipmentMapService = equipmentMapService;
        this.serialCodeUtils = serialCodeUtils;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public EquipmentDTO create(EquipmentDTO equipmentDTO) {
        //判断是否有传入产线ID 如果为空给0 如果不为空绑定产线和设备关系
        Integer cellId = null;
        //notes: 兼容工厂建模数据
        if (equipmentDTO.getCellId() != null) {
            cellId = equipmentDTO.getCellId();
            if (locationRepository.existsById(cellId)) {
                equipmentDTO.setParentId(cellId);
                equipmentDTO.setParentType(EquipmentParentEnum.LOCATION.getType());
            } else {
                throw new BaseKnownException("建模数据异常，请检查输入的工厂模型信息");
            }
        } else if (EquipmentParentEnum.LOCATION.getType().equals(equipmentDTO.getParentType())) {
            cellId = equipmentDTO.getParentId();
        }
        //不绑定时设置为parentId为0
        if (equipmentDTO.getParentId() == null) {
            equipmentDTO.setParentId(0);
            equipmentDTO.setParentType(0);
        }
        //设置设备默认状态
        equipmentStatusRepository.findByStatusCode(DEFAULT_EQUIPMENT_STATE)
                .map(HvBmEquipmentStatus::getId)
                .ifPresent(equipmentDTO::setEquipmentStatusId);
        //如果设备编码为空则自动生成
        if (StringUtils.isEmpty(equipmentDTO.getEquipmentCode())) {
            equipmentDTO.setEquipmentCode(serialCodeUtils.generateCode(CodeRuleConsts.EQUIPMENT_CODE));
        }
        //保存设备
        HvBmEquipment equipment = equipmentRepository.saveAndFlush(DtoMapper.convert(equipmentDTO, HvBmEquipment.class));
        //设置扩展信息
        if (equipmentDTO.getExtend() != null) {
            ExtendInfo extendInfo = new ExtendInfo();
            extendInfo.setEntityId(equipment.getId());
            extendInfo.setValues(equipmentDTO.getExtend());
            equipmentExtendService.addExtendInfo(extendInfo);
        }
        if (cellId != null) {
            //绑定产线和设备关系(现在是设备和可以绑定产线和设备)
            HvBmEquipmentCell hvBmEquipmentCell = new HvBmEquipmentCell();
            hvBmEquipmentCell.setEquipmentId(equipment.getId());
            hvBmEquipmentCell.setCellId(cellId);
            equipmentCellRepository.saveAndFlush(hvBmEquipmentCell);
        }
        return findByEquipmentId(equipment.getId());
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public List<EquipmentDTO> getAllEquipment() {
        return getEquipmentList(new EquipmentQueryDTO());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Page<EquipmentDTO> getEquipmentPage(EquipmentQueryDTO pageInfo) {
        fixQuery(pageInfo);
        //转换成page
        Page<EquipmentDTO> result = PageHelperUtil.getPage(equipmentMapper::findEquipmentByQuery, pageInfo, EquipmentDTO.class);
        //获取扩展信息
        List<ExtendInfo> extendInfos = equipmentExtendService.getExtend(result.stream().map(SysBaseDTO::getId).collect(Collectors.toList()));
        //内存中拼接设备基础信息和扩展信息
        joinDTOWithExtendAndEquipmentType(result, extendInfos);
        return result;
    }


    /**
     * 根据查询条件查询列表
     *
     * @param equipmentQueryDTO 设备查询对象
     * @return 查询列表
     */
    @Override
    public List<EquipmentDTO> getEquipmentList(EquipmentQueryDTO equipmentQueryDTO) {
        fixQuery(equipmentQueryDTO);
        List<EquipmentDTO> result = equipmentMapper.findEquipmentByQuery(equipmentQueryDTO);
        //获取扩展信息
        List<ExtendInfo> extendInfos = equipmentExtendService.getExtend(result.stream().map(SysBaseDTO::getId).collect(Collectors.toList()));
        //内存中拼接设备基础信息和扩展信息
        joinDTOWithExtendAndEquipmentType(result, extendInfos);
        return result;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<EquipmentDTO> getEquipmentListByEquipmentTypeId(int id) {
        EquipmentQueryDTO queryDTO = new EquipmentQueryDTO();
        queryDTO.setEquipmentTypeId(id);
        return getEquipmentList(queryDTO);
    }

    /**
     * 根据设备Code查询设备信息
     *
     * @param equipmentCode 设备Code
     * @return 设备信息
     */
    @Override
    public EquipmentDTO getEquipmentCodeByCode(String equipmentCode) {
        HvBmEquipment entity = equipmentRepository.findByEquipmentCode(equipmentCode);
        if (entity == null) {
            return null;
        }
        return findByEquipmentId(entity.getId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Modifying
    public void updateCellEquipmentRelation(CellWithEquipmentDTO cellEquipmentDTO) {
        if (StringUtils.isEmpty(cellEquipmentDTO.getCellId())) {
            throw new BaseKnownException(EquipmentExceptionEnum.LOCATION_ID_IS_NOT_NULL);
        }
        if (!locationRepository.existsById(cellEquipmentDTO.getCellId())) {
            return;
        }
        //找出产线下绑定的
        List<HvBmEquipmentCell> allByCellId = equipmentCellRepository.getAllByCellId(cellEquipmentDTO.getCellId());
        //删除之前的关系
        equipmentCellRepository.deleteAll(allByCellId);
        List<Integer> equipmentIds = cellEquipmentDTO.getEquipmentIds();
        List<HvBmEquipmentCell> hvBmEquipmentCellList = new ArrayList<>();
        List<HvBmEquipment> equipments = new ArrayList<>();
        for (Integer eid : cellEquipmentDTO.getEquipmentIds()) {
            HvBmEquipmentCell hvBmEquipmentCell = new HvBmEquipmentCell();
            hvBmEquipmentCell.setCellId(cellEquipmentDTO.getCellId());
            hvBmEquipmentCell.setEquipmentId(eid);
            hvBmEquipmentCellList.add(hvBmEquipmentCell);
            //删除之前的绑定关系,一个设备只能绑定一个位置
            equipmentIds.add(eid);
            //修改设备台账绑定关系
            equipmentRepository.findById(eid).ifPresent(e -> {
                e.setParentId(cellEquipmentDTO.getCellId());
                e.setParentType(EquipmentParentEnum.LOCATION.getType());
                equipments.add(e);
            });
        }
        equipmentCellRepository.deleteByEquipmentIdIn(equipmentIds);
        equipmentCellRepository.flush();
        equipmentCellRepository.saveAll(hvBmEquipmentCellList);
        equipmentRepository.saveAll(equipments);
    }

    /**
     * 更新设备与cell关系
     *
     * @param cellEquipmentDTO 设备与Cell关系对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCellEquipment(CellEquipmentDTO cellEquipmentDTO) {
        if (StringUtils.isEmpty(cellEquipmentDTO.getCellId())) {
            throw new BaseKnownException(EquipmentExceptionEnum.LOCATION_ID_IS_NOT_NULL);
        }
        if (!locationRepository.existsById(cellEquipmentDTO.getCellId())) {
            return;
        }
        //获取所有设备关系
        List<HvBmEquipmentCell> relations = equipmentCellRepository.getAllByCellId(cellEquipmentDTO.getCellId());
        //把之前的关联关系删除
        equipmentCellRepository.deleteAll(relations);
        //删除之前的绑定关系,一个设备只能绑定一个位置
        List<HvBmEquipmentCell> newRelation = new ArrayList<>();
        List<Integer> equipmentIds = cellEquipmentDTO.getEquipmentIds().stream().map(EquipmentOrderNumDTO::getEquipmentId)
                .collect(Collectors.toList());
        List<HvBmEquipment> equipments = new ArrayList<>();
        for (EquipmentOrderNumDTO equipmentOrderNumDTO : cellEquipmentDTO.getEquipmentIds()) {
            HvBmEquipmentCell hvBmEquipmentCell = new HvBmEquipmentCell();
            hvBmEquipmentCell.setCellId(cellEquipmentDTO.getCellId());
            hvBmEquipmentCell.setEquipmentId(equipmentOrderNumDTO.getEquipmentId());
            hvBmEquipmentCell.setOrderNum(equipmentOrderNumDTO.getOrderNum());
            newRelation.add(hvBmEquipmentCell);
            //修改设备台账绑定关系
            equipmentRepository.findById(equipmentOrderNumDTO.getEquipmentId()).ifPresent(e -> {
                e.setParentId(cellEquipmentDTO.getCellId());
                e.setParentType(EquipmentParentEnum.LOCATION.getType());
                equipments.add(e);
            });
        }
        equipmentCellRepository.deleteByEquipmentIdIn(equipmentIds);
        equipmentCellRepository.flush();
        equipmentCellRepository.saveAll(newRelation);
        equipmentRepository.saveAll(equipments);
        equipmentRepository.flush();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCellEquipmentRelation(int equipmentId, int cellId) {
        equipmentCellRepository.deleteByCellIdAndEquipmentId(cellId, equipmentId);
        //更新设备数据
        Optional<HvBmEquipment> hvBmEquipment = equipmentRepository.findById(equipmentId);
        if (hvBmEquipment.isPresent()) {
            HvBmEquipment equipment = hvBmEquipment.get();
            equipment.setParentId(0);
            equipment.setParentType(0);
            equipmentRepository.save(equipment);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public EquipmentDTO findByEquipmentId(int id) {
        EquipmentDTO hvEquipmentDTO = equipmentMapper.findEquipmentById(id);
        Map<String, Object> map = equipmentExtendService.getExtend(id);
        hvEquipmentDTO.setExtend(map);
        //设置设备所在地,上级设备
        if (EquipmentParentEnum.LOCATION.getType().equals(hvEquipmentDTO.getParentType()) && hvEquipmentDTO.getParentId() != 0) {
            //类型为位置
            String hvBmLocationName = locationRepository.findById(hvEquipmentDTO.getParentId())
                    .map(HvBmLocation::getName).orElse(null);
            hvEquipmentDTO.setLocation(hvBmLocationName);
        }
        if (EquipmentParentEnum.EQUIPMENT.getType().equals(hvEquipmentDTO.getParentType()) && hvEquipmentDTO.getParentId() != 0) {
            //类型为设备
            String hvBmEquipmentName = Optional.of(equipmentRepository.getOne(hvEquipmentDTO.getParentId()))
                    .map(HvBmEquipment::getEquipmentName).orElse(null);
            hvEquipmentDTO.setParentEquipment(hvBmEquipmentName);
        }
        //设置设备类型父节点
        if (hvEquipmentDTO.getEquipmentTypeId() != null) {
            List<EquipmentTypeDTO> equipmentTypeDtos = equipmentTypeService.findParentTypeById(hvEquipmentDTO.getEquipmentTypeId());
            if (!CollectionUtils.isEmpty(equipmentTypeDtos)) {
                List<Integer> parentIds = new ArrayList<>();
                for (EquipmentTypeDTO equipmentTypeDto : equipmentTypeDtos) {
                    parentIds.add(equipmentTypeDto.getId());
                }
                Collections.reverse(parentIds);
                hvEquipmentDTO.setEquipmentTypeParentIds(parentIds);
            }
        } else {
            hvEquipmentDTO.setEquipmentTypeParentIds(new ArrayList<>());
        }
        //设置设备父节点id列表
        if (hvEquipmentDTO.getParentId() != null && EquipmentParentEnum.LOCATION.getType().equals(hvEquipmentDTO.getParentType())) {
            List<Integer> parentIds = findParentLocationIds(hvEquipmentDTO.getParentId(), new ArrayList<>());
            Collections.reverse(parentIds);
            hvEquipmentDTO.setEquipmentParentIds(parentIds);
        }
        if (hvEquipmentDTO.getParentId() != null && hvEquipmentDTO.getParentType() != null &&
                hvEquipmentDTO.getParentId() != 0) {
            //设置设备树
            EquipmentTreeNode equipmentTreeNode = new EquipmentTreeNode();
            try {
                EquipmentTreeNode equipmentTree = getEquipmentTree(id, new EquipmentTreeNode());
                //查找根节点
                EquipmentTreeNode root = findRootNode(equipmentTree);
                equipmentTreeNode = reverseTree(root, equipmentTree, hvEquipmentDTO.getParentId());
            } catch (Exception e) {
                log.error("设备id:{}设备树绑定错误", id, e);
            } finally {
                hvEquipmentDTO.setEquipmentTreeNodes(Lists.newArrayList(equipmentTreeNode));
            }
        }
        //部门id列表
        if (hvEquipmentDTO.getResponsePartId() != null) {
            ResultVO<List<Integer>> result = departmentClient.getParentDepartIds(hvEquipmentDTO.getResponsePartId());
            if (result.isSuccess() && !CollectionUtils.isEmpty(result.getData())) {
                hvEquipmentDTO.setResponsePartIds(result.getData());
            }
        } else {
            hvEquipmentDTO.setResponsePartIds(Lists.newArrayList());
        }
        //查看是否存在设备地图
        hvEquipmentDTO.setExistsMap(ObjectUtil.isNotNull(equipmentMapService.findByEquipmentId(id)));
        return hvEquipmentDTO;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<EquipmentDTO> getByCellId(int id) {
        EquipmentQueryDTO queryDTO = new EquipmentQueryDTO();
        queryDTO.setDirectLocationId(id);
        List<EquipmentDTO> result = getEquipmentList(queryDTO);
        result.sort(Comparator.comparing(EquipmentDTO::getOrderNum,
                Comparator.nullsLast(Integer::compareTo)));
        return result;
    }


    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public EquipmentDTO updateEquipment(EquipmentDTO equipmentDTO) {
        validEquipment(equipmentDTO);
        if (equipmentDTO.getParentId() == null) {
            equipmentDTO.setParentId(0);
            equipmentDTO.setParentType(EquipmentParentEnum.LOCATION.getType());
        }
        HvBmEquipment equipment = equipmentRepository.saveAndFlush(DtoMapper.convert(equipmentDTO, HvBmEquipment.class));
        //更新设备扩展信息
        if (equipmentDTO.getExtend() != null) {
            ExtendInfo extendInfo = new ExtendInfo();
            extendInfo.setEntityId(equipment.getId());
            extendInfo.setValues(equipmentDTO.getExtend());
            equipmentExtendService.updateExtendInfo(extendInfo);
        }
        equipmentDTO.setExtend(equipmentExtendService.getExtend(equipment.getId()));
        //父节点类型为位置更新
        if (EquipmentParentEnum.LOCATION.getType().equals(equipmentDTO.getParentType()) && equipmentDTO.getParentId() != 0) {
            HvBmEquipmentCell hvBmEquipmentCell = new HvBmEquipmentCell();
            Integer cellId = null;
            //如果绑定过区域和设备关系，则更新
            HvBmEquipmentCell equipmentCell = equipmentCellRepository.getOneByEquipmentId(equipment.getId());
            if (equipmentCell != null) {
                cellId = equipmentCell.getId();
            }
            hvBmEquipmentCell.setId(cellId);
            hvBmEquipmentCell.setEquipmentId(equipment.getId());
            hvBmEquipmentCell.setCellId(equipmentDTO.getParentId());
            equipmentCellRepository.saveAndFlush(hvBmEquipmentCell);
        } else {
            //更新建模
            equipmentCellRepository.deleteByEquipmentId(equipment.getId());
        }
        return findByEquipmentId(equipment.getId());
    }


    @Override
    public ResponseEntity<byte[]> exportEquipmentLink() throws IOException, IllegalAccessException {
        List<EquipmentExcelDto> equipmentExcelDtoList = getEquipmentExcelDtosByQuery(new EquipmentQueryDTO());
        fillParentCode(equipmentExcelDtoList);
        return ExcelUtil.generateImportFile(equipmentExcelDtoList, EquipmentConsts.EQUIPMENT_EXPORT_FILE_NAME, EquipmentExcelDto.class, equipmentExtendService.getExtendColumnInfo());
    }

    public void fillParentCode(List<EquipmentExcelDto> equipmentExcelDtoList) {
        if (CollectionUtils.isEmpty(equipmentExcelDtoList)) {
            return;
        }
        for (EquipmentExcelDto equipmentExcelDto : equipmentExcelDtoList) {
            //设置设备所在地,上级设备
            if (EquipmentParentEnum.LOCATION.getType().equals(equipmentExcelDto.getParentType()) && equipmentExcelDto.getParentId() != 0) {
                //类型为位置
                String hvBmLocationCode = locationRepository.findById(equipmentExcelDto.getParentId())
                        .map(HvBmLocation::getCode).orElse(null);
                equipmentExcelDto.setParentCode(hvBmLocationCode);
            }
            if (EquipmentParentEnum.EQUIPMENT.getType().equals(equipmentExcelDto.getParentType()) && equipmentExcelDto.getParentId() != 0) {
                //类型为设备
                String hvBmEquipmentCode = Optional.of(equipmentRepository.getOne(equipmentExcelDto.getParentId()))
                        .map(HvBmEquipment::getEquipmentCode).orElse(null);
                equipmentExcelDto.setParentCode(hvBmEquipmentCode);
            }

        }

    }

    /**
     * 导出设备信息
     *
     * @param equipmentQueryDTO 设备查询条件
     * @return 设备信息excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException 访问异常
     */
    @Override
    public ResponseEntity<byte[]> exportEquipmentLink(EquipmentQueryDTO equipmentQueryDTO) throws IOException, IllegalAccessException {
        List<EquipmentExcelDto> equipmentByQuery = getEquipmentExcelDtosByQuery(equipmentQueryDTO);
        fillParentCode(equipmentByQuery);
        return ExcelUtil.generateImportFile(equipmentByQuery, EquipmentConsts.EQUIPMENT_EXPORT_FILE_NAME, EquipmentExcelDto.class, equipmentExtendService.getExtendColumnInfo());
    }

    @Override
    public ResultVO<ExcelExportDto> exportEquipment() throws IOException, IllegalAccessException {
        List<EquipmentExcelDto> equipmentExcelDtoList = getEquipmentExcelDtosByQuery(new EquipmentQueryDTO());
        fillParentCode(equipmentExcelDtoList);
        ResponseEntity<byte[]> result =
                ExcelUtil.generateImportFile(equipmentExcelDtoList,
                        EquipmentConsts.EQUIPMENT_EXPORT_FILE_NAME,
                        EquipmentExcelDto.class,
                        equipmentExtendService.getExtendColumnInfo());
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(result.getBody());
        excelExportDto.setFileName(EquipmentConsts.EQUIPMENT_EXPORT_FILE_NAME);
        return ResultVO.success(excelExportDto);
    }

    /**
     * 导出设备信息
     *
     * @param equipmentQueryDTO 设备查询条件
     * @return 设备信息excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException 访问异常
     */
    @Override
    public ResultVO<ExcelExportDto> exportEquipment(EquipmentQueryDTO equipmentQueryDTO) throws IOException, IllegalAccessException {
        List<EquipmentExcelDto> equipmentExcelDtoList = getEquipmentExcelDtosByQuery(equipmentQueryDTO);
        fillParentCode(equipmentExcelDtoList);
        ResponseEntity<byte[]> result =
                ExcelUtil.generateImportFile(equipmentExcelDtoList,
                        EquipmentConsts.EQUIPMENT_EXPORT_FILE_NAME,
                        EquipmentExcelDto.class,
                        equipmentExtendService.getExtendColumnInfo());
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(result.getBody());
        excelExportDto.setFileName(EquipmentConsts.EQUIPMENT_EXPORT_FILE_NAME);
        return ResultVO.success(excelExportDto);
    }

    /**
     * 获取设备信息List
     *
     * @param equipmentQueryDTO 查询条件
     * @return 设备信息List
     */
    private List<EquipmentExcelDto> getEquipmentExcelDtosByQuery(EquipmentQueryDTO equipmentQueryDTO) {
        List<EquipmentExcelDto> equipmentExcelDtoList = equipmentMapper.findEquipmentExcelDto(equipmentQueryDTO);
        //获取所有的设备扩展信息
        List<ExtendInfo> extendInfos = equipmentExtendService.getAll();
        for (EquipmentExcelDto equipmentExcelDto : equipmentExcelDtoList) {
            //添加设备扩展信息
            extendInfos.stream()
                    .filter(extendInfo -> equipmentExcelDto.getId().equals(extendInfo.getEntityId()))
                    .findFirst()
                    .ifPresent(extendInfo -> equipmentExcelDto.setExtend(extendInfo.getValues()));
        }
        //填充备件数据
        fillSpareParts(equipmentExcelDtoList);
        return equipmentExcelDtoList;
    }

    /**
     * 填充备件数据
     */
    private void fillSpareParts(List<EquipmentExcelDto> equipmentExcelDtoList) {
        if (!CollectionUtils.isEmpty(equipmentExcelDtoList)) {
            equipmentExcelDtoList.forEach(e -> {
                List<HvBmEquipmentSparePart> spareParts = equipmentSparePartRepository.findByEquipmentId(e.getId());
                if (!CollectionUtils.isEmpty(spareParts)) {
                    e.setSparePartStr(spareParts.stream().map(s -> {
                                //查询备件信息
                                ResultVO<SpareDTO> spare = spareClient.getSpareById(s.getSparePartId());
                                if (spare.isSuccess() && spare.getData() != null) {
                                    return spare.getData().getSpareCode() + ":" + s.getNumber();
                                }
                                return null;
                            })
                            .filter(Objects::nonNull)
                            .collect(Collectors.joining(",")));
                }
            });
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<EquipmentDTO> findByEquipmentIdList(List<Integer> idList) {
        EquipmentQueryDTO queryDTO = new EquipmentQueryDTO();
        queryDTO.setEquipmentIdList(idList);
        return getEquipmentList(queryDTO);
    }

    /**
     * 根据设备ID查询所在位置信息（集团，工厂，车间，产线）
     *
     * @param id 设备ID
     * @return 设备所在位置信息
     */
    @Override
    public LocationMsgDTO getLocationMsgDtoByEquipmentId(int id) {
        LocationMsgDTO locationMsgDTO = new LocationMsgDTO();
        Optional<HvBmEquipment> hvBmEquipment = equipmentRepository.findById(id);
        if (hvBmEquipment.isPresent()) {
            List<HvBmEquipmentCell> allByEquipmentId = equipmentCellRepository.getAllByEquipmentId(hvBmEquipment.get().getId());
            List<Integer> collect = allByEquipmentId.stream().map(HvBmEquipmentCell::getCellId).collect(Collectors.toList());
            HvBmLocation hvBmLocation = null;

            if (collect.size() > 0) {
                Optional<HvBmLocation> cell = locationRepository.findById(Collections.min(collect));
                if (cell.isPresent()) {
                    hvBmLocation = cell.get();
                    int i = hvBmLocation.getParentId();
                    do {
                        Optional<HvBmLocation> byId = locationRepository.findById(i);
                        if (byId.isPresent()) {
                            i = byId.get().getParentId();
                        }
                    } while (i != 0);
                }

            }
            if (hvBmLocation != null) {
                locationMsgDTO.setCellDto(DtoMapper.convert(hvBmLocation, LocationDTO.class));
                Optional<HvBmLocation> byId = locationRepository.findById(hvBmLocation.getParentId());
                if (byId.isPresent()) {
                    locationMsgDTO.setAreaDto(DtoMapper.convert(byId.get(), LocationDTO.class));
                    Optional<HvBmLocation> area = locationRepository.findById(byId.get().getParentId());
                    if (area.isPresent()) {
                        locationMsgDTO.setSiteDto(DtoMapper.convert(area.get(), LocationDTO.class));
                        Optional<HvBmLocation> enterprise = locationRepository.findById(area.get().getParentId());
                        enterprise.ifPresent(bmLocation -> locationMsgDTO.setEnterpriseDto(DtoMapper.convert(bmLocation,
                                LocationDTO.class)));
                    }
                }
            }
        }
        return locationMsgDTO;
    }


    /**
     * 根据ID删除设备信息
     *
     * @param id 设备ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteEquipmentById(int id) {
        List<HvBmEquipment> allByParentId = equipmentRepository.findAllByParentIdAndParentType(id, EquipmentParentEnum.EQUIPMENT.getType());
        if (allByParentId.size() > 0) {
            throw new BaseKnownException(EquipmentExceptionEnum.EQUIPMENT_EXISTS_DATA);
        }
        List<HvBmEquipmentCell> allByEquipmentId = equipmentCellRepository.getAllByEquipmentId(id);
        if (allByEquipmentId != null && allByEquipmentId.size() > 0) {
            throw new BaseKnownException(EquipmentExceptionEnum.EQUIPMENT_HAS_BEEN_CELL);
        }
        //删除设备
        equipmentRepository.deleteById(id);
        //删除设备扩展字段
        equipmentExtendService.deleteExtendInfo(id);
        //删除设备关联属性
        applicationContext.publishEvent(new DelEquipmentEvent(id));
    }

    /**
     * 根据产线ID查询产线下所有设备和子设备
     *
     * @param cellId 产线ID
     * @return 设备列表
     */
    @Override
    public List<EquipmentDTO> getAllEquipmentByCellId(int cellId) {
        EquipmentQueryDTO queryDTO = new EquipmentQueryDTO();
        queryDTO.setLocationId(cellId);
        return getEquipmentList(queryDTO);
    }

    /**
     * 根据产线Id列表查询设备
     *
     * @param cellIdList 产线ID列表
     * @return 设备列表
     */
    @Override
    public List<EquipmentDTO> getAllEquipmentByCellIdList(List<Integer> cellIdList) {
        EquipmentQueryDTO queryDTO = new EquipmentQueryDTO();
        queryDTO.setLocationIds(cellIdList);
        return getEquipmentList(queryDTO);
    }


    /**
     * 根据父设备id查询子设备列表
     *
     * @param parentId 父设备ID
     * @return 设备列表
     */
    @Override
    public List<EquipmentDTO> findAllChildEquipmentByParentId(Integer parentId) {
        EquipmentQueryDTO queryDTO = new EquipmentQueryDTO();
        queryDTO.setParentEquipmentId(parentId);
        return getEquipmentList(queryDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void copy(CopyEquipmentDto equipmentDto) {
        equipmentRepository.findById(equipmentDto.getEquipmentId()).ifPresent(e -> {
            //源设备id
            Integer copyEquipmentId = e.getId();
            ExtendInfo extendInfo = new ExtendInfo();
            Map<String, Object> values = equipmentExtendService.getExtend(e.getId());
            //删除设备id列
            values.remove(EquipmentConsts.EQUIPMENT_ID);
            extendInfo.setValues(values);
            //深拷贝设备数据,浅拷贝会报错
            HvBmEquipment equipment = SerializationUtils.clone(e);
            equipment.setId(null);
            //设备设备默认状态
            equipmentStatusRepository.findByStatusCode(DEFAULT_EQUIPMENT_STATE)
                    .map(HvBmEquipmentStatus::getId)
                    .ifPresent(equipment::setEquipmentStatusId);
            String equipmentCode = equipmentDto.getEquipmentCode();
            if (StringUtils.isEmpty(equipmentDto.getEquipmentCode())) {
                equipmentCode = serialCodeUtils.generateCode(CodeRuleConsts.EQUIPMENT_CODE);
            }
            equipment.setEquipmentCode(equipmentCode);
            equipment.setEquipmentName(equipmentDto.getEquipmentName());
            HvBmEquipment hvBmEquipment = equipmentRepository.saveAndFlush(equipment);
            //复制扩展信息
            extendInfo.setEntityId(hvBmEquipment.getId());
            equipmentExtendService.addExtendInfo(extendInfo);
            if (EquipmentParentEnum.LOCATION.getType().equals(hvBmEquipment.getParentType()) &&
                    hvBmEquipment.getParentId() != 0) {
                //复制设备关联属性
                HvBmEquipmentCell hvBmEquipmentCell = new HvBmEquipmentCell();
                hvBmEquipmentCell.setEquipmentId(hvBmEquipment.getId());
                hvBmEquipmentCell.setCellId(hvBmEquipment.getParentId());
                equipmentCellRepository.saveAndFlush(hvBmEquipmentCell);
            }
            //发布复制设备其他数据事件
            applicationContext.publishEvent(new CopyEquipmentEvent(hvBmEquipment.getId(), copyEquipmentId));
        });
    }

    /**
     * 根据设备编码查询设备列表
     *
     * @param equipmentCodes 设备编码
     * @return 设备列表
     */
    @Override
    public List<EquipmentDTO> getAllEquipmentsByCodeIn(List<String> equipmentCodes) {
        EquipmentQueryDTO queryDTO = new EquipmentQueryDTO();
        queryDTO.setEquipmentCodeList(equipmentCodes);
        return getEquipmentList(queryDTO);
    }

    /**
     * 根据CellId查询Cell下所属的设备信息
     *
     * @param ids CellID
     * @return 设备信息列表
     */
    @Override
    public List<EquipmentDTO> getEquipmentListByCellIdList(List<Integer> ids) {
        List<EquipmentDTO> result = new ArrayList<>();
        for (Integer id : ids) {
            List<EquipmentDTO> equipment = getByCellId(id);
            result.addAll(equipment);
        }
        return result;
    }

    @Override
    public List<HvBmEquipment> getAllEquipmentByLocationId(Integer locationId) {
        return equipmentRepository.findAllByParentId(locationId);
    }

    /**
     * 根据设备id查询父节点位置
     *
     * @param id        设备id
     * @param parentIds 父节点id列表
     * @return 父节点id列表
     */
    private List<Integer> findParentLocationIds(Integer id, List<Integer> parentIds) {
        Optional<HvBmLocation> hvBmLocation = locationRepository.findById(id);
        if (hvBmLocation.isPresent()) {
            parentIds.add(hvBmLocation.get().getId());
            findParentLocationIds(hvBmLocation.get().getParentId(), parentIds);
        }
        return parentIds;
    }


    /**
     * dto拼接到
     *
     * @param hvEquipmentDTOS dto列表
     * @param extendInfos     扩展信息列表
     */
    private void joinDTOWithExtendAndEquipmentType(Iterable<EquipmentDTO> hvEquipmentDTOS,
                                                   Iterable<ExtendInfo> extendInfos) {
        //设备类型id列表
        List<Integer> equipmentTypeIds = new ArrayList<>();
        hvEquipmentDTOS.forEach(hvEquipmentDTO -> {
            if (!equipmentTypeIds.contains(hvEquipmentDTO.getEquipmentTypeId())) {
                equipmentTypeIds.add(hvEquipmentDTO.getEquipmentTypeId());
            }
        });
        //根据已有设备类型id列表查询数据
        List<HvBmEquipmentType> hvBmEquipmentTypeList = hvEquipmentTypeRepository.findAllById(equipmentTypeIds);
        ArrayList<ExtendInfo> extendInfoArrayList = Lists.newArrayList(extendInfos);
        for (EquipmentDTO hvEquipmentDTO : hvEquipmentDTOS) {
            //添加设备类型信息
            hvBmEquipmentTypeList.stream()
                    .filter(eqptType -> eqptType.getId().equals(hvEquipmentDTO.getEquipmentTypeId()))
                    .findFirst()
                    .ifPresent(hvBmEquipmentType -> hvEquipmentDTO.setEquipmentTypeName(hvBmEquipmentType.getEquipmentTypeName()));
            if (EquipmentParentEnum.LOCATION.getType().equals(hvEquipmentDTO.getParentType())
                    && hvEquipmentDTO.getParentId() != 0) {
                //类型为位置
                hvEquipmentDTO.setLocation(
                        locationRepository.findById(hvEquipmentDTO.getParentId())
                                .map(HvBmLocation::getName)
                                .orElse(""));
            }
            if (EquipmentParentEnum.EQUIPMENT.getType().equals(hvEquipmentDTO.getParentType())) {
                //类型为设备
                hvEquipmentDTO.setParentEquipment(equipmentRepository.findById(hvEquipmentDTO.getParentId())
                        .map(HvBmEquipment::getEquipmentName)
                        .orElse(""));
            }
            //添加设备扩展信息
            extendInfoArrayList.stream()
                    .filter(extendInfo -> hvEquipmentDTO.getId().equals(extendInfo.getEntityId()))
                    .findFirst()
                    .ifPresent(extendInfo -> hvEquipmentDTO.setExtend(extendInfo.getValues()));
        }
    }

    /**
     * 验证设备信息
     *
     * @param hvEquipmentDTO 设备信息
     */
    private void validEquipment(EquipmentDTO hvEquipmentDTO) {
        if (hvEquipmentDTO.getEquipmentTypeId() != null) {
            if (!hvEquipmentTypeRepository.findById(hvEquipmentDTO.getEquipmentTypeId()).isPresent()) {
                throw new BaseKnownException(EquipmentExceptionEnum.EQUIPMENT_TYPE_NOT_EXISTS);
            }
        }
        //校验设备绑定关系
        if (hvEquipmentDTO.getParentId() != null && EquipmentParentEnum.EQUIPMENT.getType()
                .equals(hvEquipmentDTO.getParentType())) {
            //校验父节点是否就是自身
            if (hvEquipmentDTO.getId().equals(hvEquipmentDTO.getParentId())) {
                throw new BaseKnownException(EquipmentExceptionEnum.EQUIPMENT_PARENT_IS_SELF);
            }
            //校验父节点是否就是子节点中的一个
            EquipmentQueryDTO queryDTO = new EquipmentQueryDTO();
            queryDTO.setParentEquipmentId(hvEquipmentDTO.getId());
            List<EquipmentDTO> equipmentList = getEquipmentList(queryDTO);
            if (!CollectionUtils.isEmpty(equipmentList)) {
                List<Integer> childIds = equipmentList.stream().map(SysBaseDTO::getId).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(childIds) && childIds.contains(hvEquipmentDTO.getParentId())) {
                    throw new BaseKnownException(EquipmentExceptionEnum.EQUIPMENT_PARENT_IS_CHILD);
                }
            }
        }
    }

    /**
     * 遍历设备树
     *
     * @param id                设备di
     * @param equipmentTreeNode 设备树
     * @return 设备树
     */
    private EquipmentTreeNode getEquipmentTree(Integer id, EquipmentTreeNode equipmentTreeNode) {
        Optional<HvBmEquipment> hvBmEquipment = equipmentRepository.findById(id);
        if (hvBmEquipment.isPresent()) {
            equipmentTreeNode.setParentId(hvBmEquipment.get().getParentId());
            equipmentTreeNode.setId(hvBmEquipment.get().getId());
            equipmentTreeNode.setType(EQUIPMENT_TYPE);
            equipmentTreeNode.setNodeName(hvBmEquipment.get().getEquipmentName());
            //父节点类型
            if (EquipmentParentEnum.EQUIPMENT.getType().equals(hvBmEquipment.get().getParentType())
                    && hvBmEquipment.get().getParentId() != null) {
                equipmentTreeNode.setEquipmentTreeNode(getEquipmentTree(hvBmEquipment.get().getParentId(), new EquipmentTreeNode()));
            } else if (EquipmentParentEnum.LOCATION.getType().equals(hvBmEquipment.get().getParentType())
                    && hvBmEquipment.get().getParentId() != null && hvBmEquipment.get().getParentId() != 0) {
                equipmentTreeNode.setEquipmentTreeNode(getLocationTree(hvBmEquipment.get().getParentId(), new EquipmentTreeNode()));
            } else {
                equipmentTreeNode.setEquipmentTreeNode(null);
            }
        }
        return equipmentTreeNode;
    }

    /**
     * 设备父节点为位置,遍历位置树
     *
     * @param id                设备id
     * @param equipmentTreeNode 设备节点
     * @return 设备树
     */
    private EquipmentTreeNode getLocationTree(Integer id, EquipmentTreeNode equipmentTreeNode) {
        Optional<HvBmLocation> hvBmLocation = locationRepository.findById(id);
        if (hvBmLocation.isPresent()) {
            equipmentTreeNode.setParentId(hvBmLocation.get().getParentId());
            equipmentTreeNode.setId(hvBmLocation.get().getId());
            equipmentTreeNode.setType(hvBmLocation.get().getType());
            equipmentTreeNode.setNodeName(hvBmLocation.get().getName());
            if (hvBmLocation.get().getParentId() == 0) {
                equipmentTreeNode.setEquipmentTreeNode(null);
            } else {
                equipmentTreeNode.setEquipmentTreeNode(getLocationTree(hvBmLocation.get().getParentId(), new EquipmentTreeNode()));
            }
        }
        return equipmentTreeNode;
    }

    /**
     * @param equipmentTreeNode 设备根节点
     * @param originData        原始树数据
     * @param parentId          父节点id,终止条件
     * @return 设备树
     */
    private EquipmentTreeNode reverseTree(EquipmentTreeNode equipmentTreeNode, EquipmentTreeNode
            originData, Integer parentId) {
        if (!parentId.equals(equipmentTreeNode.getParentId())) {
            List<EquipmentTreeNode> childNodes = findChildNode(originData, equipmentTreeNode.getId());
            equipmentTreeNode.setEquipmentTreeNodes(childNodes);
            for (EquipmentTreeNode childNode : childNodes) {
                reverseTree(childNode, originData, parentId);
            }
        }
        return equipmentTreeNode;
    }

    /**
     * 查找设备
     *
     * @param equipmentTreeNode 设备树
     * @param parentId          父节点id
     * @return 设备树
     */
    private List<EquipmentTreeNode> findChildNode(EquipmentTreeNode equipmentTreeNode, Integer parentId) {
        if (equipmentTreeNode.getParentId().equals(parentId)) {
            EquipmentTreeNode childNode = new EquipmentTreeNode();
            childNode.setId(equipmentTreeNode.getId());
            childNode.setNodeName(equipmentTreeNode.getNodeName());
            childNode.setParentId(parentId);
            childNode.setType(equipmentTreeNode.getType());
            return Lists.newArrayList(childNode);
        } else {
            return findChildNode(equipmentTreeNode.getEquipmentTreeNode(), parentId);
        }
    }

    private EquipmentTreeNode findRootNode(EquipmentTreeNode equipmentTreeNode) {
        if (equipmentTreeNode.getEquipmentTreeNode() == null) {
            return equipmentTreeNode;
        }
        return findRootNode(equipmentTreeNode.getEquipmentTreeNode());
    }


    private void fixQuery(EquipmentQueryDTO pageInfo) {
        //如果工厂建模id不是空，那么查询id对应的他自己和他的子集实体递归查询
        if (pageInfo.getLocationId() != null) {
            List<LocationDTO> allLocation = locationService.getChildLocation(pageInfo.getLocationId());
            pageInfo.setLocationIds(allLocation.stream()
                    .map(SysBaseDTO::getId)
                    .collect(Collectors.toList()));
        }
        if (pageInfo.getLocationIds() != null) {
            //根据工厂建模id找到所有的直接设备以及设备下面的所有子设备
            List<Integer> allEquipmentId = new ArrayList<>();
            findAllEquipmentIdByParentLocation(pageInfo.getLocationIds(), allEquipmentId);
            pageInfo.setEquipmentIdList(allEquipmentId);
        }
        if (pageInfo.getParentEquipmentId() != null) {
            List<Integer> allEquipmentId = new ArrayList<>();
            findAllEquipmentIdByParentEquipmentIds(Arrays.asList(pageInfo.getParentEquipmentId()), allEquipmentId);
            pageInfo.setEquipmentIdList(allEquipmentId);
        }
        if (pageInfo.getEquipmentIdList() != null && pageInfo.getEquipmentIdList().size() == 0) {
            //如果是空列表，那就是没找到设备，传递一个不能查出数据的列表进去
            pageInfo.getEquipmentIdList().add(0);
        }
        //设备类型不为空,查询设备类型以及子类型
        if (pageInfo.getEquipmentTypeId() != null) {
            List<Integer> equipmentTypeIds = Lists.newArrayList(pageInfo.getEquipmentTypeId());
            findAllTypeByParentId(pageInfo.getEquipmentTypeId(), equipmentTypeIds);
            pageInfo.setEquipmentTypeIds(equipmentTypeIds);
        }
    }

    private void findAllEquipmentIdByParentLocation(List<Integer> locationIds, List<Integer> allId) {
        List<HvBmEquipment> equipments = equipmentRepository.findAllByParentIdInAndParentType(locationIds, EquipmentParentEnum.LOCATION.getType());
        List<Integer> equipmentIds = equipments.stream().map(SysBase::getId).collect(Collectors.toList());
        allId.addAll(equipmentIds);
        findAllEquipmentIdByParentEquipmentIds(equipmentIds, allId);
    }

    private void findAllEquipmentIdByParentEquipmentIds(List<Integer> equipmentIds, List<Integer> allId) {
        List<HvBmEquipment> equipments = equipmentRepository.findAllByParentIdInAndParentType(equipmentIds, EquipmentParentEnum.EQUIPMENT.getType());
        List<Integer> ids = equipments.stream().map(SysBase::getId).collect(Collectors.toList());
        allId.addAll(ids);
        if (ids.size() > 0) {
            findAllEquipmentIdByParentEquipmentIds(ids, allId);
        }
    }


    /**
     * 根据设备类型父节点查找子节点类型
     *
     * @param parentId 父类型id
     */
    private void findAllTypeByParentId(Integer parentId, List<Integer> childIds) {
        List<HvBmEquipmentType> hvBmEquipmentTypes = hvEquipmentTypeRepository.findAllByParentId(parentId);
        if (!CollectionUtils.isEmpty(hvBmEquipmentTypes)) {
            for (HvBmEquipmentType hvBmEquipmentType : hvBmEquipmentTypes) {
                childIds.add(hvBmEquipmentType.getId());
                findAllTypeByParentId(hvBmEquipmentType.getId(), childIds);
            }
        }
    }
}


















