package com.hvisions.hiperbase.controller;

import com.hvisions.common.utils.EnumUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.schedule.dto.*;
import com.hvisions.hiperbase.schedule.enums.CrewTypeEnum;
import com.hvisions.hiperbase.service.schedule.CrewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>Title: HvBmCrewController</p>
 * <p>Description: 班组控制器</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping("/crew")
@Api(description = "班组控制器")
public class CrewController {

    @Autowired
    private CrewService crewService;

    /**
     * 添加班组
     *
     * @param crewDTO 班组信息
     * @return 班组id
     */
    @PostMapping("/createCrew")
    @ApiOperation(value = "添加班组")
    public int createCrew(@RequestBody CrewDTO crewDTO) {
        return crewService.createOrUpdate(crewDTO);
    }

    /**
     * 添加班组
     *
     * @param crewWithMemberIdsDTO 班组信息
     * @return 班组id
     */
    @PostMapping("/createOrUpdateCrewWithIds")
    @ApiOperation(value = "添加班组")
    public int createCrewWithIds(@RequestBody CrewWithMemberIdsDTO crewWithMemberIdsDTO) {
        return crewService.createOrUpdateCrewWithIds(crewWithMemberIdsDTO);
    }

    /**
     * 删除班组(会同时删除对应的排班信息)
     *
     * @param id 班组id
     */
    @DeleteMapping("/deleteCrewById/{id}")
    @ApiOperation(value = "删除班组,会同时删除对应的班组成员信息，以及对应的排班信息")
    public void deleteCrewById(@PathVariable int id) {
        crewService.delete(id);
    }

    /**
     * 通编码删除班组
     *
     * @param crewCode 班组编码
     * @param areaId   车间id
     * @param cellId   产线id
     */
    @DeleteMapping("/deleteCrewByCode")
    @ApiOperation(value = "通过编码删除班组,会同时删除对应的班组成员信息，以及对应的排班信息")
    public void deleteCrewByCode(@RequestParam String crewCode,
                                 @RequestParam(required = false, defaultValue = "0") Integer areaId,
                                 @RequestParam(required = false, defaultValue = "0") Integer cellId) {
        crewService.deleteByCode(crewCode, areaId, cellId);
    }

    /**
     * 更新班组基础信息
     *
     * @param crewDTO 班组基础信息
     * @return 班组Id
     */
    @PutMapping("/updateCrew")
    @ApiOperation(value = "更新班组基础信息")
    public int updateCrew(@RequestBody CrewDTO crewDTO) {
        return crewService.createOrUpdate(crewDTO);
    }

    /**
     * 根据车间id查询班组信息
     *
     * @param areaId 车间id
     * @param cellId 产线id
     * @return 班组信息列表
     */
    @GetMapping("/getCrewListByAreaIdAndCellId/{areaId}/{cellId}")
    @ApiOperation(value = "根据车间id,产线id查询班组,包括班组成员信息,车间id传0,返回通用车间排班，产线id传0 ，返回车间对应班组信息.")
    public List<CrewWithMemberDTO> getCrewListByAreaIdAndCellId(@PathVariable int areaId, @PathVariable int cellId) {
        return crewService.getByAreaIdAndCellId(areaId, cellId);
    }

    /**
     * 根据班组id查询详细信息
     *
     * @param id 班组id
     * @return 班组详细信息
     */
    @GetMapping("/getCrewById/{id}")
    @ApiOperation(value = "根据班组id获取班组信息")
    public CrewWithMemberDTO getCrewById(@PathVariable int id) {
        return crewService.getById(id);
    }


    /**
     * 根据班组编码获取班组信息
     *
     * @param areaId   车间id
     * @param cellId   产线id
     * @param crewCode 班组编码
     * @return 班组信息
     */
    @GetMapping("/getCrewByCode")
    @ApiOperation(value = "根据班组编码获取班组信息")
    public CrewWithMemberDTO getCrewByCode(@RequestParam String crewCode,
                                           @RequestParam(required = false, defaultValue = "0") Integer areaId,
                                           @RequestParam(required = false, defaultValue = "0") Integer cellId) {
        return crewService.getByCode(crewCode, areaId, cellId);
    }

    /**
     * 根据班组编码列表获取班组信息
     *
     * @param areaId       车间id
     * @param cellId       产线id
     * @param crewCodeList 班组编码列表
     * @return 班组信息
     */
    @GetMapping("/getByCodeList")
    @ApiOperation(value = "根据班组编码获取班组信息")
    public List<CrewWithMemberDTO> getByCodeList(@RequestParam List<String> crewCodeList,
                                                 @RequestParam(required = false, defaultValue = "0") Integer areaId,
                                                 @RequestParam(required = false, defaultValue = "0") Integer cellId) {
        return crewService.getByCodeList(crewCodeList, areaId, cellId);
    }


    /**
     * 根据人员id查询班组列表
     *
     * @param userId 人员id
     * @return 班组列表
     */
    @GetMapping("/getCrewByUserId/{userId}")
    @ApiOperation(value = "根据人员id查询班组列表")
    public List<CrewDTO> getCrewByUserId(@PathVariable int userId) {
        return crewService.getCrewByUserId(userId);
    }

    /**
     * 更新班组人员信息
     *
     * @param crewMember 班组信息
     */
    @PutMapping("/updateCrewMember")
    @ApiOperation(value = "更新班组人员信息")
    public void updateCrewMember(@RequestBody CrewMember crewMember) {
        crewService.update(crewMember);
    }

    /**
     * 根据车间产线查询班组班次信息,如果查询不到，返回车间信息，如果还没有，返回通用信息
     *
     * @param areaId 车间id
     * @param cellId 产线id
     * @return 班组班次信息
     */
    @GetMapping("/getCrewShiftByAreaIdAndCellId/{areaId}/{cellId}")
    @ApiOperation(value = "根据车间产线查询班组班次信息,如果查询不到，返回车间信息，如果还没有，返回通用信息")
    public ShiftCrewQueryResult getCrewShiftByAreaIdAndCellId(
            @PathVariable(required = false) int areaId, @PathVariable int cellId) {
        return crewService.getCrewShiftByAreaIdAndCellId(areaId, cellId);
    }

    /**
     * 获取班组类型
     *
     * @return 班组类型
     */
    @GetMapping("getCrewType")
    @ApiOperation("获取班组类型")
    public Map<Integer, String> getCrewType() {
        return EnumUtil.enumToMap(CrewTypeEnum.class);
    }


    /**
     * 根据班组编码查询班组信息
     *
     * @param crewCode 班组编号
     * @return 班组信息列表
     */
    @GetMapping("/getCrewListByAreaIdAndCellId/{crewCode}")
    @ApiOperation(value = "根据班组编码查询班组信息")
    public CrewWithMemberDTO getCrewByCrewCode(@PathVariable("crewCode") String crewCode){
        return crewService.getCrewByCrewCode(crewCode);
    }


}
