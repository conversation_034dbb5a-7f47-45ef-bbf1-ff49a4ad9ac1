package com.hvisions.hiperbase.controller;

import com.hvisions.common.dto.ExtendColumnInfo;
import com.hvisions.common.service.BaseExtendService;
import com.hvisions.hiperbase.route.dto.OperationDTO;
import com.hvisions.hiperbase.route.dto.OperationQueryDTO;
import com.hvisions.hiperbase.service.route.OperationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title: OperationController</p>
 * <p>Description: 工艺操作控制器</p>
 * <p>Company: www.h-visions.com</p>
 * <p>add date: 2018/12/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@Api(description = "工艺操作控制器")
@RequestMapping("/operation")
public class OperationController {
    @Autowired
    OperationService operationService;
    @Resource(name = "operation_extend")
    BaseExtendService operationExtendService;

    //-----------------基础接口----------------------

    /**
     * 新增工艺操作
     *
     * @param operationDTO 工艺操作
     * @return 新增的工艺操作id
     */
    @PostMapping("/addOperation")
    @ApiOperation(value = "新增工艺操作")
    public int addOperation(@Valid @RequestBody OperationDTO operationDTO) {
        return operationService.createOperation(operationDTO);

    }

    /**
     * 删除工艺操作
     *
     * @param id 工艺操作id
     */
    @DeleteMapping("/deleteOperationById/{id}")
    @ApiOperation(value = "根据工艺操作id删除工艺操作")
    public void deleteOperationId(@PathVariable int id) {
        operationService.deleteOperationById(id);
    }

    /**
     * 更新工艺操作
     *
     * @param operationDto 工艺操作dto
     * @return 工艺操作id
     */
    @PutMapping("/updateOperation")
    @ApiOperation(value = "更新工艺操作")
    public int updateOperation(@RequestBody @Valid OperationDTO operationDto) {
        return operationService.updateOperation(operationDto);
    }


    /**
     * 根据id查询工艺操作
     *
     * @param id 工艺操作id
     * @return 工艺操作DTO
     */
    @GetMapping("/getOperationById/{id}")
    @ApiOperation(value = "根据id查询工艺操作")
    public OperationDTO getOperationById(@PathVariable int id) {
        return operationService.getOperationById(id);
    }

    /**
     * 根据编码查询工艺操作
     *
     * @param code 工艺操作编码
     * @return 工艺操作DTO
     */
    @GetMapping("/getOperationByCode/{code}")
    @ApiOperation(value = "根据编码查询工艺操作")
    public OperationDTO getOperationByCode(@PathVariable String code) {
        return operationService.getOperationByCode(code);
    }

    /**
     * 根据id列表查询工艺操作
     *
     * @param ids 工艺操作编码
     * @return 工艺操作DTO列表
     */
    @GetMapping("/getOperationByIds")
    @ApiOperation(value = "根据id列表查询")
    public List<OperationDTO> getOperationByIds(@RequestParam List<Integer> ids) {
        return operationService.getOperationByIds(ids);
    }

    /**
     * 根据工艺操作名称和编码查询分页信息
     *
     * @param operationQueryDTO 查询DTO
     * @return 工艺操作分页数据
     */
    @PostMapping("/getOperationPageByCodeAndName")
    @ApiOperation(value = "根据工艺操作名称和编码查询分页信息")
    public Page<OperationDTO> getOperationPageByCodeAndName(@RequestBody OperationQueryDTO operationQueryDTO) {
        return operationService.getOperationPageByCodeAndName(operationQueryDTO);
    }

    @GetMapping("/getAllOperation")
    @ApiOperation(value = "查询所有工艺操作")
    public List<OperationDTO> getAllOperation() {
        return operationService.getAllOperation();
    }


    //-----------------扩展属性----------------------

    /**
     * 添加工艺操作扩展属性
     *
     * @param extendColumnInfo 扩展属性信息
     */
    @PostMapping("/addOperationColumn")
    @ApiOperation(value = "添加工艺操作扩展属性")
    public void addOperationColumn(@RequestBody ExtendColumnInfo extendColumnInfo) {
        operationExtendService.addExtend(extendColumnInfo);
    }

    /**
     * 删除工艺操作扩展属性
     *
     * @param columnName 扩展属性名称
     */
    @DeleteMapping("/deleteOperationColumnByColumnName/{columnName}")
    @ApiOperation(value = "删除工艺操作扩展属性")
    public void deleteOperationColumnByColumnName(@PathVariable String columnName) {
        operationExtendService.dropExtend(columnName);
    }

    /**
     * 获取工艺操作所有扩展字段信息
     *
     * @return 扩展字段信息
     */
    @GetMapping("/getOperationExtendColumnInfo")
    @ApiOperation(value = "获取工艺操作所有扩展字段信息")
    public List<ExtendColumnInfo> getOperationExtendColumnInfo() {
        return operationExtendService.getExtendColumnInfo();
    }
    //-----------------扩展属性----------------------

}









