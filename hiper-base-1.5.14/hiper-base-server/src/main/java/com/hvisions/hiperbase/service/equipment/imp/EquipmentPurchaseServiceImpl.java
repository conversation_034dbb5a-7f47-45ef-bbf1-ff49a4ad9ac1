package com.hvisions.hiperbase.service.equipment.imp;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.hiperbase.equipment.CopyEquipmentEvent;
import com.hvisions.hiperbase.equipment.DelEquipmentEvent;
import com.hvisions.hiperbase.equipment.EquipmentPurchaseDto;
import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentPurchase;
import com.hvisions.hiperbase.repository.equipment.EquipmentPurchaseRepository;
import com.hvisions.hiperbase.service.equipment.EquipmentPurchaseService;
import org.apache.commons.lang3.SerializationUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>Title: EquipmentPurchaseServiceImpl</p >
 * <p>Description: 设备采购</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/5</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Service
public class EquipmentPurchaseServiceImpl implements EquipmentPurchaseService{

    private final EquipmentPurchaseRepository equipmentPurchaseRepository;

    @Autowired
    public EquipmentPurchaseServiceImpl(EquipmentPurchaseRepository equipmentPurchaseRepository) {
        this.equipmentPurchaseRepository = equipmentPurchaseRepository;
    }

    /**
     * 修改设备采购
     *
     * @param equipmentPurchaseDto 设备采购dto
     */
    @Override
    public void save(EquipmentPurchaseDto equipmentPurchaseDto) {
        HvBmEquipmentPurchase hvBmEquipmentPurchase = DtoMapper.convert(equipmentPurchaseDto, HvBmEquipmentPurchase.class);
        equipmentPurchaseRepository.save(hvBmEquipmentPurchase);
    }

    /**
     * 根据设备id查询设备采购信息
     *
     * @param equipmentId 设备id
     * @return 设备采购信息
     */
    @Override
    public EquipmentPurchaseDto findByEquipmentId(Integer equipmentId) {
        HvBmEquipmentPurchase hvBmEquipmentPurchase = equipmentPurchaseRepository.findOneByEquipmentId(equipmentId);
        return DtoMapper.convert(hvBmEquipmentPurchase, EquipmentPurchaseDto.class);
    }

    /**
     * 复制设备事件处理
     *
     * @param event 复制设备事件
     */
    @EventListener(CopyEquipmentEvent.class)
    public void handleCopyEvent(CopyEquipmentEvent event) {
        HvBmEquipmentPurchase equipmentPurchase = equipmentPurchaseRepository.findOneByEquipmentId(event.getCopyEquipmentId());
        if (equipmentPurchase == null) {
            return;
        }
        HvBmEquipmentPurchase hvBmEquipmentPurchase = SerializationUtils.clone(equipmentPurchase);
        hvBmEquipmentPurchase.setEquipmentId(event.getEquipmentId());
        hvBmEquipmentPurchase.setId(null);
        equipmentPurchaseRepository.save(hvBmEquipmentPurchase);
    }

    /**
     * 删除设备事件处理
     *
     * @param event 删除设备事件
     */
    @Transactional(rollbackFor = Exception.class)
    @EventListener(DelEquipmentEvent.class)
    public void handleDelEvent(DelEquipmentEvent event) {
        equipmentPurchaseRepository.deleteAllByEquipmentId(event.getEquipmentId());
    }
}
