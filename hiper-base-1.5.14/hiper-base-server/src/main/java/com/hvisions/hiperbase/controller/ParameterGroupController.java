package com.hvisions.hiperbase.controller;

import com.hvisions.hiperbase.route.dto.ParameterTagDTO;
import com.hvisions.hiperbase.route.dto.ParameterGroupQuery;
import com.hvisions.hiperbase.service.route.ParameterTagService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

/**
 * <p>Title: ParameterGroupController</p>
 * <p>Description: 标签控制器</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/11/17</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@Api(description = "标签控制器")
@RequestMapping("/parameterGroup")
public class ParameterGroupController {
    private final ParameterTagService service;

    @Autowired
    public ParameterGroupController(ParameterTagService service) {
        this.service = service;
    }

    /**
     * 创建标签
     *
     * @param dto 标签信息
     * @return 分组id
     */
    @PostMapping(value = "/create")
    @ApiOperation(value = "创建标签")
    public Integer create(@RequestBody ParameterTagDTO dto) {
        return service.create(dto);
    }

    /**
     * 修改标签
     *
     * @param dto 标签信息
     */
    @PutMapping(value = "/update")
    @ApiOperation(value = "修改标签")
    public void update(@RequestBody ParameterTagDTO dto) {
        service.update(dto);
    }

    /**
     * 删除标签
     *
     * @param id 分组id
     */
    @DeleteMapping(value = "/delete/{id}")
    @ApiOperation(value = "删除标签")
    public void delete(@PathVariable Integer id) {
        service.delete(id);
    }

    /**
     * 获取标签信息
     *
     * @param id 分组id
     * @return 标签信息
     */
    @GetMapping(value = "/get/{id}")
    @ApiOperation(value = "获取标签信息")
    public ParameterTagDTO get(@PathVariable Integer id) {
        return service.get(id);
    }

    /**
     * 查询分页信息
     *
     * @param query 分页对象
     * @return 标签分页信息
     */
    @PostMapping(value = "/query")
    @ApiOperation(value = "/查询分页信息")
    public Page<ParameterTagDTO> query(@RequestBody ParameterGroupQuery query) {
        return service.query(query);
    }

}









