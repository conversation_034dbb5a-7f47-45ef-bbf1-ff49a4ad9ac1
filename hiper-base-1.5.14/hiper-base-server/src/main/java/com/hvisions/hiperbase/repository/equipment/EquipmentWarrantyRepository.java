package com.hvisions.hiperbase.repository.equipment;

import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentWarranty;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: EquipmentWarrantyRepository</p >
 * <p>Description: 设备保修信息维护</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/5</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Repository
public interface EquipmentWarrantyRepository extends JpaRepository<HvBmEquipmentWarranty, Integer> {
    /**
     * 根据设备id查询保修信息
     *
     * @param equipmentId 设备id
     * @return 保修信息列表
     */
    List<HvBmEquipmentWarranty> findByEquipmentId(Integer equipmentId);

    /**
     * 根据设备id删除保修信息
     *
     * @param equipmentId 设备id
     */
    void deleteAllByEquipmentId(Integer equipmentId);
}
