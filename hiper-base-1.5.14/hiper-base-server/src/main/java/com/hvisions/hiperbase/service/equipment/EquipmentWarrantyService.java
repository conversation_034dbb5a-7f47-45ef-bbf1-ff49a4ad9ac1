package com.hvisions.hiperbase.service.equipment;

import com.hvisions.hiperbase.equipment.EquipmentWarrantyDto;

import java.util.List;

/**
 * <p>Title: EquipmentWarrantyService</p >
 * <p>Description: 设备保修信息service</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/5</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
public interface EquipmentWarrantyService {
    /**
     * 根据设备id删除设备保修信息
     *
     * @param equipmentWarrantyDto 设备保修信息
     */
    void addWarranty(EquipmentWarrantyDto equipmentWarrantyDto);

    /**
     * 根据设备id查询设备保修信息
     *
     * @param equipmentId 设备id
     * @return 设备保修信息
     */
    List<EquipmentWarrantyDto> findAllByEquipmentId(Integer equipmentId);

    /**
     * 根据id查询设备保修信息
     *
     * @param equipmentWarrantyDto 设备保修信息
     */
    void updateWarranty(EquipmentWarrantyDto equipmentWarrantyDto);

    /**
     * 根据id删除设备保修信息
     *
     * @param id 设备保修信息id
     */
    void deleteWarranty(Integer id);
}
