package com.hvisions.hiperbase.repository.equipment;

import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentMapDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <p>Title: EquipmentMapDetailRepository</p >
 * <p>Description: </p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/6/23</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Repository
public interface EquipmentMapDetailRepository extends JpaRepository<HvBmEquipmentMapDetail, Integer> {

    /**
     * 根据设备id查询详情表数据
     *
     * @param equipmentId 设备id
     * @return 详情表数据
     */
    HvBmEquipmentMapDetail findByEquipmentId(Integer equipmentId);

    /**
     * 根据headerId删除详情表数据
     *
     * @param headerId 头表id
     */
    void deleteAllByHeaderId(Integer headerId);
}
