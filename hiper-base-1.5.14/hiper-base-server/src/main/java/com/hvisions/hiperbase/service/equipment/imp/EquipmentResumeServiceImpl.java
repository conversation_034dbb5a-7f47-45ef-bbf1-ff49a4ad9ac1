package com.hvisions.hiperbase.service.equipment.imp;

import com.hvisions.hiperbase.equipment.EquipmentOffLineRecordDto;
import com.hvisions.hiperbase.equipment.EquipmentResumeDto;
import com.hvisions.hiperbase.service.equipment.EquipmentRecordService;
import com.hvisions.hiperbase.service.equipment.EquipmentResumeService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>Title: EquipmentResumeServiceImpl</p >
 * <p>Description: </p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/5</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Service
public class EquipmentResumeServiceImpl implements EquipmentResumeService {

    private final EquipmentRecordService equipmentRecordService;

    @Autowired
    public EquipmentResumeServiceImpl(EquipmentRecordService equipmentRecordService) {
        this.equipmentRecordService = equipmentRecordService;
    }

    /**
     * 根据设备id查询设备履历记录
     *
     * @param equipmentId 设备id
     * @return 设备履历记录
     */
    @Override
    public EquipmentResumeDto findByEquipmentId(Integer equipmentId) {
        EquipmentResumeDto equipmentResumeDto = new EquipmentResumeDto();
        List<EquipmentOffLineRecordDto> record = equipmentRecordService.findEquipmentRecordByEquipmentId(equipmentId);
        if (CollectionUtils.isNotEmpty(record)) {
            equipmentResumeDto.setEquipmentOffLineRecordDtos(record);
        }
        return equipmentResumeDto;
    }
}
