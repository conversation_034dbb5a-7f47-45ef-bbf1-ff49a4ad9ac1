package com.hvisions.hiperbase.repository.schedule;

import com.hvisions.hiperbase.entity.schedule.HvBmScheduleBreakTime;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: ScheduleBreakTimeRepository</p>
 * <p>Description: 排班休息时间</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/4/7</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface ScheduleBreakTimeRepository extends JpaRepository<HvBmScheduleBreakTime, Integer> {
    /**
     * 查询排班计划的休息时间信息
     * @param scheduleId 排班id
     * @return 休息信息列表
     */
    List<HvBmScheduleBreakTime> findAllByScheduleId(Integer scheduleId);
    /**
     * 查询排班计划的休息时间信息
     * @param scheduleIdList 排班id列表
     * @return 休息信息列表
     */
    List<HvBmScheduleBreakTime> findAllByScheduleIdIn(List<Integer> scheduleIdList);
}









