package com.hvisions.hiperbase.service.transportTool;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.entity.material.HvBmFrame;
import com.hvisions.hiperbase.entity.transportTool.HvBmTransportToolType;
import com.hvisions.hiperbase.transportTool.TransportToolTypeDTO;
import com.hvisions.hiperbase.transportTool.TransportToolTypeQueryDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>Title: TransportToolTypeService</p>
 * <p>Description: 运输工具类型服务</p>
 */
public interface TransportToolTypeService extends IService<HvBmTransportToolType> {
    Page<TransportToolTypeDTO> getPage(TransportToolTypeQueryDTO queryDTO);
    List<HvBmTransportToolType> findListByCondition(HvBmTransportToolType hvBmTransportToolType);
    ResultVO importData(MultipartFile file, UserInfoDTO userInfo);
    boolean checkHvBmTransportToolTypeCodesIsUnique(String transportToolTypeCode);
}