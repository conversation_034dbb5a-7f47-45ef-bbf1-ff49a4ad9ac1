package com.hvisions.hiperbase.controller;

import com.hvisions.hiperbase.equipment.EquipmentSwotUnitDto;
import com.hvisions.hiperbase.service.equipment.EquipmentSwotUnitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: HvBmEquipmentSwotUnitController</p >
 * <p>Description: 设备读数维护</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/9</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@RestController
@RequestMapping("/equipmentSwot")
@Api(tags = "设备读数单位控制器")
public class EquipmentSwotUnitController {

    private final EquipmentSwotUnitService equipmentSwotUnitService;

    @Autowired
    public EquipmentSwotUnitController(EquipmentSwotUnitService equipmentSwotUnitService) {
        this.equipmentSwotUnitService = equipmentSwotUnitService;
    }

    @ApiOperation(value = "设备读数单位新增")
    @PostMapping("/save")
    public void save(@RequestBody EquipmentSwotUnitDto equipmentSwotUnitDto) {
        equipmentSwotUnitService.save(equipmentSwotUnitDto);
    }

    @ApiOperation(value = "设备读数单位修改")
    @PutMapping("/update")
    public void update(@RequestBody EquipmentSwotUnitDto equipmentSwotUnitDto) {
        equipmentSwotUnitService.update(equipmentSwotUnitDto);
    }


    @ApiOperation(value = "设备读数单位删除")
    @DeleteMapping("/delete/{id}")
    public void deleteById(@PathVariable("id") Integer id) {
        equipmentSwotUnitService.deleteById(id);
    }

    @ApiOperation(value = "设备读数单位查询")
    @GetMapping("/findAll")
    public List<EquipmentSwotUnitDto> findAll() {
        return equipmentSwotUnitService.findAll();
    }
}
