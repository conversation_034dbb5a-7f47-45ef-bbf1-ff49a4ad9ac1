package com.hvisions.hiperbase.repository.equipment;

import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentCell;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>Title: HvEquipmentCellRepository</p >
 * <p>Description: 设备产线关联关系仓储层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/4/12</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface EquipmentCellRepository extends JpaRepository<HvBmEquipmentCell, Integer> {

    /**
     * 根据产线ID查询产线设备关联关系
     *
     * @param cellId 产线ID
     * @return 产线设备关联关系列表
     */
    List<HvBmEquipmentCell> getAllByCellId(Integer cellId);


    /**
     * 根据设备ID和产线ID删除关联关系
     *
     * @param cellId      产线ID
     * @param equipmentId 设备ID
     */
    void deleteByCellIdAndEquipmentId(Integer cellId, Integer equipmentId);

    /**
     * 根据设备ID查询所关联的产线
     *
     * @param equipmentId 设备ID
     * @return 设备产线关联关系
     */
    List<HvBmEquipmentCell> getAllByEquipmentId(Integer equipmentId);


    /**
     * @param equipmentId 设备id
     * @return 设备产线关系
     */
    HvBmEquipmentCell getOneByEquipmentId(Integer equipmentId);

    /**
     * 删除设备产线关联关系
     *
     * @param equipmentIds 设备id
     */
    @Modifying
    @Transactional
    void deleteByEquipmentIdIn(List<Integer> equipmentIds);

    /**
     * 根据设备id删除设备产线关联关系
     *
     * @param equipmentId 设备id
     */
    void deleteByEquipmentId(Integer equipmentId);
}
