package com.hvisions.hiperbase.service.equipment;

import com.hvisions.hiperbase.equipment.equipmentclass.AddEquipmentPropertyDTO;
import com.hvisions.hiperbase.equipment.equipmentclass.EquipmentPropertyDTO;

import java.util.List;

/**
 * <p>Title: EquipmentPropertyService</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/3/25</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface EquipmentPropertyService {
    /**
     * 修改设备属性
     *
     * @param propertyDTOs 属性
     */
    void update(List<EquipmentPropertyDTO> propertyDTOs);

    /**
     * 获取设备属性
     *
     * @param id 设备id
     * @return 设备属性列表
     */
    List<EquipmentPropertyDTO> findByEquipmentId(Integer id);


    /**
     * 添加设备属性
     *
     * @param propertyDTO 设备属性
     */
    void addPropertyToEquipment(AddEquipmentPropertyDTO propertyDTO);

    /**
     * 根据Id删除设备属性
     */
    void deletePropertyById(int id);
}









