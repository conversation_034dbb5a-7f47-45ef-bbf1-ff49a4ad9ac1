package com.hvisions.hiperbase.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.annotation.EnableFilter;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.ExtendColumnInfo;
import com.hvisions.common.dto.ExtendInfo;
import com.hvisions.common.utils.EnumUtil;
import com.hvisions.hiperbase.configuration.LocationExtendServiceMapper;
import com.hvisions.hiperbase.equipment.CellWithEquipmentDTO;
import com.hvisions.hiperbase.equipment.EquipmentDTO;
import com.hvisions.hiperbase.equipment.RelationDTO;
import com.hvisions.hiperbase.equipment.eums.LocationTypeEnum;
import com.hvisions.hiperbase.equipment.location.AreaLocationDTO;
import com.hvisions.hiperbase.equipment.location.LocationDTO;
import com.hvisions.hiperbase.equipment.location.LocationQueryDTO;
import com.hvisions.hiperbase.service.equipment.EquipmentService;
import com.hvisions.hiperbase.service.equipment.LocationService;
import com.hvisions.thirdparty.common.dto.MesFactoryDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: HvBmLocationExtendController</p >
 * <p>Description: location服务扩展控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/11/16</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Api(description = "工厂建模控制器")
@RequestMapping("/location")
@RestController
@Slf4j
public class LocationExtendController {

    @Autowired
    EquipmentService equipmentService;
    private final LocationService locationService;

    private final LocationExtendServiceMapper locationExtendServiceMapper;

    @Autowired
    public LocationExtendController(LocationExtendServiceMapper locationExtendServiceMapper,
                                    LocationService locationService) {
        this.locationService = locationService;

        this.locationExtendServiceMapper = locationExtendServiceMapper;
    }

    /**
     * 添加location扩展属性
     *
     * @param extendColumnInfo 扩展属性信息
     * @param type             类型
     */
    @PostMapping("/createLocationColumn")
    @ApiOperation(value = "添加Location扩展属性")
    public void createLocationColumn(
            @RequestParam @ApiParam(value = "10=enterprise,20=site,30=area,40=cell,50=workCenter,60=post",
                    allowableValues = "10,20,30,40,50,60",
                    example = "10") int type,
            @RequestBody ExtendColumnInfo extendColumnInfo) {
        (locationExtendServiceMapper.getService(type)).addExtend(extendColumnInfo);
    }

    /**
     * 更新location扩展属性
     *
     * @param extendInfo 扩展属性信息
     * @param type       类型
     */
    @PutMapping("/updateLocationExtendInfo")
    @ApiOperation(value = "更新Location扩展属性")
    public void updateLocationExtendInfo(
            @RequestParam @ApiParam(value = "10=enterprise,20=site,30=area,40=cell,50=workCenter,60=post",
                    allowableValues = "10,20,30,40,50,60",
                    example = "10") int type,
            @RequestBody ExtendInfo extendInfo) {
        (locationExtendServiceMapper.getService(type)).updateExtendInfo(extendInfo);
    }

    /**
     * 添加location
     *
     * @param locationDTO 数据传输对象
     */
    @PostMapping("/createLocation")
    @ApiOperation(value = "新增location")
    public LocationDTO createLocation(@Valid @RequestBody LocationDTO locationDTO) {
        return locationService.create(locationDTO);
    }

    /***
     * 修改location
     * @param locationDTO 数据传输对象
     *
     */
    @PutMapping("/updateLocation")
    @ApiOperation(value = "更新location")
    public LocationDTO updateLocation(@Valid @RequestBody LocationDTO locationDTO) {
        return locationService.update(locationDTO);
    }

    /**
     * 删除扩展属性
     *
     * @param type       类型
     * @param columnName 扩展属性名称
     */
    @DeleteMapping("/deleteLocationColumn/{columnName}")
    @ApiOperation(value = "根据扩展属性名删除Location扩展属性")
    @Transactional(rollbackFor = Exception.class)
    public void deleteLocationColumn(@PathVariable String columnName,
                                     @RequestParam @ApiParam(value = "10=enterprise,20=site,30=area,40=cell,50=workCenter,60=post",
                                             allowableValues = "10,20,30,40,50,60",
                                             example = "10") int type) {
        locationExtendServiceMapper.getService(type).dropExtend(columnName);
    }

    /**
     * 根据location类型查询列表
     *
     * @param type 类型id
     * @return 类型列表
     */
    @GetMapping("/getLocationListByType/{type}")
    @ApiOperation(value = "根据type类型查询location列表")
    public List<LocationDTO> getLocationListByType(
            @PathVariable @ApiParam(value = "10=enterprise,20=site,30=area,40=cell,50=workCenter,60=post",
                    allowableValues = "10,20,30,40,50,60",
                    example = "10") int type) {
        return locationService.findLocationDtoByType(type);
    }

    /**
     * 根据location类型查询所有ID
     *
     * @param type 类型id
     * @return 类型列表
     */
    @GetMapping("/getLocationIdsByType/{type}")
    @ApiOperation(value = "根据type类型查询locationID")
    public List<Integer> getLocationIdsByType(
            @PathVariable @ApiParam(value = "10=enterprise,20=site,30=area,40=cell,50=workCenter,60=post",
                    allowableValues = "10,20,30,40,50,60",
                    example = "10") int type) {
        return locationService.getLocationIdsByType(type);
    }


    /**
     * 根据location ID查询扩展属性
     *
     * @param id 主键id
     */
    @RequestMapping(value = "/getLocationById/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "根据ID查询location信息")
    public LocationDTO getLocationById(@PathVariable int id) {
        return locationService.findLocationExtendById(id);
    }

    /***
     * 根据parent id 查询下级
     * @param parentId 父级ID
     * @param directChild 是否只查询一个层级的对象，比如车间只查产线
     * @return 下级建模信息
     */
    @GetMapping(value = "/getLocationListByParentId/{parentId}")
    @ApiOperation(value = "根据父级ID查询下级")
    public List<LocationDTO> getLocationListByParentId(@PathVariable int parentId, @RequestParam(defaultValue = "true") Boolean directChild) {
        return locationService.getLocationDtoByParentID(parentId, directChild);
    }

    /**
     * 根据 location code 查询扩展属性
     */
    @RequestMapping(value = "/getLocationByCode/{code}", method = RequestMethod.GET)
    @ApiOperation(value = "根据Code查询location及扩展信息")
    public LocationDTO getLocationByCode(@PathVariable String code) {
        return locationService.getLocationExtendByCode(code);
    }

    /**
     * 根据Code查询工厂建模
     *
     * @param code location code
     * @return 工厂建模信息
     */
    @GetMapping(value = "/getHvBmLocationByCode/{code}")
    @ApiOperation(value = "根据Code查询工厂建模")
    public LocationDTO getHvBmLocationByCode(@PathVariable String code) {
        return locationService.getHvBmLocationByCode(code);
    }


    /***
     * 删除location 包括扩展属性
     *
     */
    @DeleteMapping("/deleteLocationById/{id}")
    @ApiOperation(value = "删除location")
    public void deleteLocationById(@PathVariable int id) {
        locationService.deleteLocationById(id);
    }

    /**
     * 根据type类型查询扩展属性
     *
     * @param type type
     * @return columnInfo
     */

    @GetMapping("/getExtendColumnInfoByType/{type}")
    @ApiOperation(value = "根据Type查询扩展属性")
    public List<ExtendColumnInfo> getExtendColumnInfo(
            @PathVariable @ApiParam(value = "10=enterprise,20=site,30=area,40=cell,50=workCenter,60=post",
                    allowableValues = "10,20,30,40,50,60",
                    example = "10") int type) {
        return locationExtendServiceMapper.getService(type).getExtendColumnInfo();
    }

    /**
     * 根据CellId查询Cell下所属的设备信息
     *
     * @param id CellID
     * @return 设备信息列表
     */
    @GetMapping("/getEquipmentListByCellId/{id}")
    @ApiOperation(value = "根据cellId查询所在cell所有的设备信息")
    public List<EquipmentDTO> getEquipmentListByCellId(@PathVariable int id) {
        return equipmentService.getByCellId(id);
    }

    /**
     * 根据CellId查询Cell下所属的设备信息
     *
     * @param ids CellID
     * @return 设备信息列表
     */
    @PostMapping("/getEquipmentListByCellIdList")
    @ApiOperation(value = "根据cellId查询所在cell所有的设备信息")
    public List<EquipmentDTO> getEquipmentListByCellIdList(@RequestBody List<Integer> ids) {
        return equipmentService.getEquipmentListByCellIdList(ids);
    }

    /**
     * 添加Cell与设备关系
     *
     * @param cellEquipmentDTO 设备和Cell关系对象
     */
    @PutMapping(value = "/updateCellEquipmentRelation")
    @ApiOperation(value = "添加工厂建模Cell与设备的关系")
    public void updateCellEquipmentRelation(@RequestBody CellWithEquipmentDTO cellEquipmentDTO) {
        equipmentService.updateCellEquipmentRelation(cellEquipmentDTO);
    }

    /**
     * 删除设备的归属关系
     *
     * @param equipmentId 设备id
     */
    @ApiOperation(value = "删除设备的Location归属关系")
    @DeleteMapping(value = "/deleteCellEquipmentRelation/{equipmentId}/{cellId}")
    public void deleteCellEquipmentRelation(@PathVariable int equipmentId, @PathVariable int cellId) {
        equipmentService.deleteCellEquipmentRelation(equipmentId, cellId);
    }


    /**
     * 删除设备的归属关系
     *
     * @param cellWithEquipmentDTO 设备id列表对象 产线ID
     */
    @ApiOperation(value = "批量删除设备的Location归属关系")
    @DeleteMapping(value = "/deleteCellEquipmentRelationBatch")
    public void deleteCellEquipmentRelationBatch(@RequestBody CellWithEquipmentDTO cellWithEquipmentDTO) {
        locationService.deleteCellEquipmentRelationBatch(cellWithEquipmentDTO);
    }


    /**
     * 根据产线ID集合获取产线信息列表
     *
     * @param idList 产线ID列表
     * @return 产线信息列表
     */
    @GetMapping(value = "/getAllByCellIdList")
    @ApiOperation(value = "根据产线ID集合获取产线信息列表")
    public List<LocationDTO> getAllByCellIdList(@RequestParam List<Integer> idList) {
        return locationService.getAllByCellIdList(idList);
    }

    /**
     * 根据产线Id查询所在车间以及车间下产线信息
     *
     * @param idList 查新Id
     * @return 车间以及车间下产线信息
     */
    @PostMapping(value = "/getAllBySonId")
    @ApiOperation(value = "根据产线Id查询所在车间以及车间下产线信息")
    public List<AreaLocationDTO> getAllBySonId(@RequestBody List<Integer> idList) {
        return locationService.getAllBySonId(idList);
    }


    /**
     * 条件查询location
     *
     * @param locationQueryDTO 查询条件
     * @return location信息
     */
    @PostMapping(value = "/getLocationByQuery")
    @ApiOperation(value = "条件查询location")
    public List<LocationDTO> getLocationByQuery(@RequestBody LocationQueryDTO locationQueryDTO) {
        return locationService.getLocationByQuery(locationQueryDTO);
    }

    /**
     * 导入所有
     *
     * @param file 工厂建模信息文档
     * @throws IllegalAccessException field访问异常
     * @throws IOException            io异常
     */
    @EnableFilter
    @PostMapping(value = "/importLocation")
    @ApiOperation(value = "导入工厂建模信息，如果code存在则更新，code不存在则新增")
    public void importLocation(@RequestParam("file") MultipartFile file)
            throws IllegalAccessException, IOException {
        locationService.importLocation(file);
    }

    /**
     * 导出所有建模数据
     *
     * @return 设备信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @EnableFilter
    @PostMapping(value = "/exportLocation")
    @ApiOperation(value = "导出所有建模数据")
    public ExcelExportDto exportLocation() throws IOException, IllegalAccessException {
        return locationService.exportLocation();
    }


    /**
     * 导出所有建模数据-url
     *
     * @return 设备信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @EnableFilter
    @ApiResultIgnore
    @GetMapping(value = "/exportTemplate")
    @ApiOperation(value = "导出所有建模数据-url")
    public ResponseEntity<byte[]> exportTemplate() throws IOException, IllegalAccessException {
        return locationService.exportTemplate();
    }

    /**
     * 导出所有工厂建模基础信息
     *
     * @return 设备信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @EnableFilter
    @GetMapping(value = "/exportVoTemplate")
    @ApiOperation(value = "导出所有工厂建模基础信息")
    public ExcelExportDto exportVoTemplate() throws IOException, IllegalAccessException {
        ResponseEntity<byte[]> responseEntity = locationService.exportTemplate();
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(responseEntity.getBody());
        excelExportDto.setFileName("locations.xls");
        return excelExportDto;
    }

    /**
     * 获取所有工厂建模tree
     *
     * @return 工厂建模树
     */
    @GetMapping(value = "/getLocationTree")
    @ApiOperation(value = "获取所有工厂建模tree")
    public List<LocationDTO> getLocationTree() {
        return locationService.getLocationTree();
    }

    /**
     * 获取所有工厂建模类型
     *
     * @return 工厂建模类型
     */
    @GetMapping(value = "/getLocationTypes")
    @ApiOperation(value = "获取所有工厂建模类型")
    public Map<Integer, String> getLocationTypes() {
        return EnumUtil.enumToMap(LocationTypeEnum.class);
    }

    /**
     * 根据父级id列表查询下级列表
     *
     * @param parentIds   父级id
     * @param directChild 是否只查父级直接对应的类型,比如车间只查产线。
     * @return 下级列表
     */
    @PostMapping(value = "/getAllByParentIdList")
    @ApiOperation(value = "根据父级id列表查询下级列表")
    public List<LocationDTO> getAllByParentIdList(@RequestBody List<Integer> parentIds, @RequestParam(defaultValue = "true") Boolean directChild) {
        return locationService.getAllByParentIdList(parentIds, directChild);
    }


    /**
     * 根据产线ID集合获取产线信息列表
     *
     * @param idList 产线ID列表
     * @return 产线信息列表
     */
    @PostMapping(value = "/getAllByIdList")
    @ApiOperation(value = "根据ID集合获取信息列表")
    public List<LocationDTO> getAllByIdList(@RequestBody List<Integer> idList) {
        return locationService.getAllByCellIdList(idList);
    }


    /**
     * 对工厂建模里面的模型进行排序
     *
     * @param relationDTO 模型关系
     */
    @PutMapping(value = "/sort")
    @ApiOperation(value = "根据")
    public void sort(@RequestBody RelationDTO relationDTO) {
        locationService.sort(relationDTO);
    }

    @PostMapping("/saveLocationByReceiving")
    @ApiOperation("将接收到的工厂建模数据添加")
    public int saveLocationByReceiving(@RequestBody List<MesFactoryDTO> factoryDTOS){
        return locationService.saveLocationByReceiving(factoryDTOS);
    }

    /**
     * 根据工位编号查询产线编号
     * @param stationCode
     * @return
     */
    @ApiOperation(value = "根据工位编号查询产线编号")
    @GetMapping(value = "/getLineByStationCode/{stationCode}")
    public String getLineByStationCode(@PathVariable("stationCode") String stationCode){
        return locationService.getLineByStationCode(stationCode);
    }

    /**
     * 根据产线id查询产线信息
     * @param id
     * @return
     */
    @ApiOperation(value = "根据产线id查询产线信息")
    @GetMapping(value = "/getLineById/{id}")
    public LocationDTO getLineById(@PathVariable("id") Integer id){
        return locationService.getLineById(id);
    }

    /**
     * 根据产线编号查询产线信息
     * @param lineCode
     * @return
     */
    @ApiOperation(value = "根据产线编号查询产线信息")
    @GetMapping(value = "/getLineInfoByLineCode/{lineCode}")
    public LocationDTO getLineInfoByLineCode(@PathVariable("lineCode") String lineCode){
        return locationService.getLineInfoByLineCode(lineCode);
    }

    /**
     * 根据产线编号list查询产线信息
     *
     * @param codes
     * @return
     */
    @ApiOperation(value = "根据产线编号查询产线信息")
    @PostMapping(value = "/getLocationsByCodes")
    public List<LocationDTO> getLocationsByCodes(@RequestBody List<String> codes) {
        return locationService.getLocationsByCodes(codes);
    }

}
