package com.hvisions.hiperbase.controller;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.equipment.StereoscopicAndLineWarehouseDTO;
import com.hvisions.hiperbase.equipment.StereoscopicAndLineWarehouseQueryDTO;
import com.hvisions.hiperbase.equipment.location.LocationDTO;
import com.hvisions.hiperbase.service.equipment.LocationService;
import com.hvisions.hiperbase.service.equipment.StereoscopicAndLineWarehouseService;
import com.hvisions.wms.client.WaresLocationClient;
import com.hvisions.wms.dto.location.WaresLocationDTO;
import com.hvisions.wms.dto.location.WaresLocationQueryDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/5/22
 */
@Slf4j
@Api(tags = "立库与缓存区")
@RestController
@RequestMapping("/stereoscopicAndLineWarehouse")
public class StereoscopicAndLineWarehouseController {

    @Autowired
    private StereoscopicAndLineWarehouseService stereoscopicAndLineWarehouseService;

    @Autowired
    private LocationService locationService;

    @Autowired
    private WaresLocationClient waresLocationClient;

    /**
     * 分页查询
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/getPage")
    @ApiOperation("分页查询")
    public Page<StereoscopicAndLineWarehouseDTO> getPage(@RequestBody StereoscopicAndLineWarehouseQueryDTO queryDTO) {
        return stereoscopicAndLineWarehouseService.getPage(queryDTO);
    }

    @GetMapping("/getOtherData")
    @ApiOperation("获取其他数据")
    public Map<String, Object> getOtherData() {
        Map<String, Object> otherData = new HashMap<>();
        List<LocationDTO> locationList = locationService.findLocationDtoByType(40);
        otherData.put("locationList", locationList);
        ResultVO<List<WaresLocationDTO>> waresLocationVO = waresLocationClient.findAllByQuery(new WaresLocationQueryDTO());
        if (waresLocationVO.isSuccess()) {
            otherData.put("stereoscopicList", waresLocationVO.getData());
        }
        return otherData;
    }

    /**
     * 增加
     *
     * @param stereoscopicAndLineWarehouseDTO
     * @return
     */
    @PostMapping("/addStereoscopicAndLineWarehouse")
    @ApiOperation("新增")
    public int addStereoscopicAndLineWarehouse(@RequestBody StereoscopicAndLineWarehouseDTO stereoscopicAndLineWarehouseDTO) {
        return stereoscopicAndLineWarehouseService.addStereoscopicAndLineWarehouse(stereoscopicAndLineWarehouseDTO);
    }

    /**
     * 修改
     *
     * @param stereoscopicAndLineWarehouseDTO
     * @return
     */
    @PutMapping("/updateStereoscopicAndLineWarehouse")
    @ApiOperation("修改")
    public int updateStereoscopicAndLineWarehouse(@RequestBody StereoscopicAndLineWarehouseDTO stereoscopicAndLineWarehouseDTO) {
        return stereoscopicAndLineWarehouseService.updateStereoscopicAndLineWarehouse(stereoscopicAndLineWarehouseDTO);
    }

    /**
     * 删除
     *
     * @param id
     */
    @DeleteMapping("/deleteStereoscopicAndLineWarehouse/{id}")
    @ApiOperation("删除")
    public void deleteStereoscopicAndLineWarehouse(@PathVariable int id) {
        stereoscopicAndLineWarehouseService.deleteStereoscopicAndLineWarehouse(id);
    }
}
