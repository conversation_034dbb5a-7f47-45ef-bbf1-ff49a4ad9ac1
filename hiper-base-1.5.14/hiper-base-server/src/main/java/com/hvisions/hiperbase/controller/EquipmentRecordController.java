package com.hvisions.hiperbase.controller;

import com.hvisions.hiperbase.equipment.EquipmentOfflineDto;
import com.hvisions.hiperbase.equipment.EquipmentOnlineDto;
import com.hvisions.hiperbase.service.equipment.EquipmentRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <p>Title: EquipmentRecordController</p >
 * <p>Description: 设备状态修改</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/7</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@RestController
@RequestMapping("/equipmentRecord")
@Api(tags = "设备上下线控制器")
public class EquipmentRecordController {

    private final EquipmentRecordService equipmentRecordService;

    @Autowired
    public EquipmentRecordController(EquipmentRecordService equipmentRecordService) {
        this.equipmentRecordService = equipmentRecordService;
    }

    @ApiOperation(value = "根据设备id上线设备")
    @PostMapping("/online")
    public void onlineEquipment(@RequestBody @Valid EquipmentOnlineDto equipmentOnlineDto) {
        equipmentRecordService.onlineEquipment(equipmentOnlineDto);
    }

    @ApiOperation(value = "根据设备id上线设备")
    @PostMapping("/offline")
    public void offlineEquipment(@RequestBody @Valid EquipmentOfflineDto equipmentOfflineDto) {
        equipmentRecordService.offlineEquipment(equipmentOfflineDto);
    }
}
