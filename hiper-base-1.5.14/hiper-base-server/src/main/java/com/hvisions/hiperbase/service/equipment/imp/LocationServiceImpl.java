package com.hvisions.hiperbase.service.equipment.imp;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.ExtendInfo;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.service.BaseExtendService;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.EnumUtil;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.hiperbase.configuration.LocationExtendServiceMapper;
import com.hvisions.hiperbase.dao.equipment.LocationMapper;
import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentCell;
import com.hvisions.hiperbase.entity.equipment.HvBmLocation;
import com.hvisions.hiperbase.enums.EquipmentExceptionEnum;
import com.hvisions.hiperbase.equipment.CellWithEquipmentDTO;
import com.hvisions.hiperbase.equipment.RelationDTO;
import com.hvisions.hiperbase.equipment.eums.LocationTypeEnum;
import com.hvisions.hiperbase.equipment.location.AreaLocationDTO;
import com.hvisions.hiperbase.equipment.location.LocationDTO;
import com.hvisions.hiperbase.equipment.location.LocationQueryDTO;
import com.hvisions.hiperbase.excel.LocationExcel;
import com.hvisions.hiperbase.repository.equipment.EquipmentCellRepository;
import com.hvisions.hiperbase.repository.equipment.LocationRepository;
import com.hvisions.hiperbase.service.equipment.LocationService;
import com.hvisions.thirdparty.common.dto.MesFactoryDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <p>Title: HvBmLocationServiceImp</p >
 * <p>Description: HvBmLocation服务实现</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/11/16</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Service
@Slf4j
public class LocationServiceImpl implements LocationService {
    private static final int ROOT_PARENT_ID = 0;
    private static final String CACHE_FOR_ID = "h-visions.location:id:";
    private static final String CACHE_FOR_ENTITY = "h-visions.location:base:";
    private static final String CACHE_FOR_EXTEND = "h-visions.location:code:";

    private final LocationRepository locationRepository;
    private final LocationExtendServiceMapper locationExtendServiceMapper;
    private final EquipmentCellRepository equipmentCellRepository;
    private final LocationMapper locationMapper;
    private final ObjectMapper objectMapper;
    private final StringRedisTemplate stringRedisTemplate;

    @Autowired
    @Lazy
    public LocationServiceImpl(LocationRepository locationRepository,
                               LocationExtendServiceMapper locationExtendServiceMapper,
                               EquipmentCellRepository equipmentCellRepository,
                               LocationMapper locationMapper, StringRedisTemplate stringRedisTemplate) {
        this.locationRepository = locationRepository;
        this.locationExtendServiceMapper = locationExtendServiceMapper;
        this.equipmentCellRepository = equipmentCellRepository;
        this.locationMapper = locationMapper;
        this.objectMapper = new ObjectMapper();
        this.objectMapper.enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);
        this.objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.stringRedisTemplate = stringRedisTemplate;
    }


    /**
     * 通过父级id查询子级locationid
     *
     * @param parentId    父级ID
     * @param directChild 是否只查询一个层级的对象，比如车间只查产线
     * @return 下级建模信息
     */
    @Override
    public List<LocationDTO> getLocationDtoByParentID(int parentId, Boolean directChild) {
        Integer parentLocationType = locationRepository.findById(parentId)
                .map(HvBmLocation::getType).orElse(0);
        //根据Pid查询这一列数据
        List<HvBmLocation> locations = locationRepository.findByParentId(parentId);
        locations.sort(Comparator.comparing(HvBmLocation::getIndexNum));
        List<LocationDTO> result = DtoMapper.convertList(locations, LocationDTO.class);
        if (directChild) {
            result = result.stream().filter(t -> t.getType() == parentLocationType + 10)
                    .collect(Collectors.toList());
        }
        // 获取扩展字段信息
        if (result.size() > 0) {
            //转换成DTO
            //DTO  添加扩展字段信息
            for (LocationDTO dto : result) {
                Map<String, Object> extend = locationExtendServiceMapper.getService(dto.getType())
                        .getExtend(dto.getId());
                dto.setExtend(extend);
            }
        }
        return result;
    }


    /**
     * 通过id查询扩展信息
     *
     * @param id location id
     * @return dto
     */
    @Override
    public LocationDTO findLocationExtendById(int id) {
        String cacheKey = CACHE_FOR_ID + id;
        String json = stringRedisTemplate.opsForValue().get(cacheKey);
        //先读缓存
        if (Strings.isNotBlank(json)) {
            try {
                return objectMapper.readValue(json, LocationDTO.class);
            } catch (Exception ex) {
                log.warn("设备缓存读取异常{}", ex.getMessage());
            }
        }
        Optional<HvBmLocation> hvBmLocation = locationRepository.findById(id);
        if (!hvBmLocation.isPresent()) {
            return null;
        }
        LocationDTO dto = new LocationDTO();
        BeanUtils.copyProperties(hvBmLocation.get(), dto);
        Map<String, Object> extendInfo = (locationExtendServiceMapper.getService(dto.getType())).getExtend(id);
        dto.setExtend(extendInfo);
        try {
            json = objectMapper.writeValueAsString(dto);
            stringRedisTemplate.opsForValue().set(cacheKey, json, 10, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.warn("设备缓存存储异常：{}", e.getMessage());
        }
        return dto;

    }

    /***
     * 通过code 查询扩展信息
     */
    @Override
    public LocationDTO getLocationExtendByCode(String code) {
        String cacheKey = CACHE_FOR_EXTEND + code;
        String json = stringRedisTemplate.opsForValue().get(cacheKey);
        //先读缓存
        if (Strings.isNotBlank(json)) {
            try {
                return objectMapper.readValue(json, LocationDTO.class);
            } catch (Exception ex) {
                log.warn("设备缓存读取异常{}", ex.getMessage());
            }
        }
        HvBmLocation entity = locationRepository.findByCode(code);
        if (entity == null) {
            return null;
        }
        LocationDTO dto = new LocationDTO();
        BeanUtils.copyProperties(entity, dto);
        Map<String, Object> extendInfo = (locationExtendServiceMapper.getService(dto.getType())).getExtend(entity.getId());
        dto.setExtend(extendInfo);
        try {
            json = objectMapper.writeValueAsString(dto);
            stringRedisTemplate.opsForValue().set(cacheKey, json, 10, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.warn("设备缓存存储异常：{}", e.getMessage());
        }
        return dto;
    }

    @Override
    public List<LocationDTO> findLocationDtoByType(int type) {
        List<LocationDTO> result = DtoMapper.convertList(locationRepository.findAllByType(type), LocationDTO.class);
        List<ExtendInfo> extendInfos = locationExtendServiceMapper.getService(type).getExtend(
                result.stream().map(LocationDTO::getId).collect(Collectors.toList())
        );
        for (LocationDTO hvBmLocationDTO : result) {
            for (ExtendInfo extendInfo : extendInfos) {
                if (hvBmLocationDTO.getId().equals(extendInfo.getEntityId())) {
                    hvBmLocationDTO.setExtend(extendInfo.getValues());
                    break;
                }
            }
        }
        return result;
    }

    /**
     * 删除location
     *
     * @param id locationID
     */
    @Override
    public void deleteLocationById(int id) {
        List<LocationDTO> hvBmLocationDTOS = getLocationDtoByParentID(id, false);
        if (hvBmLocationDTOS.size() > 0) {
            throw new BaseKnownException(EquipmentExceptionEnum.LOCATION_HAVE_DATA);
        } else {
            //判断location 是否有绑定设备
            List<HvBmEquipmentCell> allByCellId = equipmentCellRepository.getAllByCellId(id);
            if (allByCellId.size() > 0) {
                throw new BaseKnownException(EquipmentExceptionEnum.LOCATION_HAVE_EQUIPMENT);
            }
            HvBmLocation location = locationRepository.getById(id);
            locationExtendServiceMapper.getService(location.getType()).deleteExtendInfo(id);
            locationRepository.deleteById(id);
            cleanCache(id, location.getCode());
        }
    }

    /**
     * 批量删除设备产线归属关系
     *
     * @param cellWithEquipmentDTO 设备ID列表 产线ID
     */
    @Override
    public void deleteCellEquipmentRelationBatch(CellWithEquipmentDTO cellWithEquipmentDTO) {
        for (Integer equipmentId : cellWithEquipmentDTO.getEquipmentIds()) {
            equipmentCellRepository.deleteByCellIdAndEquipmentId(cellWithEquipmentDTO.getCellId(), equipmentId);
        }
    }

    /**
     * 根据Code查询工厂建模
     *
     * @param code location code
     * @return 工厂建模信息
     */
    @Override
    public LocationDTO getHvBmLocationByCode(String code) {
        String cacheKey = CACHE_FOR_ENTITY + code;
        String json = stringRedisTemplate.opsForValue().get(cacheKey);
        //先读缓存
        if (Strings.isNotBlank(json)) {
            try {
                return objectMapper.readValue(json, LocationDTO.class);
            } catch (Exception ex) {
                log.warn("设备缓存读取异常{}", ex.getMessage());
            }
        }
        HvBmLocation entity = locationRepository.findByCode(code);
        if (entity == null) {
            return null;
        }
        LocationDTO result = DtoMapper.convert(entity, LocationDTO.class);
        try {
            json = objectMapper.writeValueAsString(result);
            stringRedisTemplate.opsForValue().set(cacheKey, json, 10, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.warn("设备缓存存储异常：{}", e.getMessage());
        }
        return result;
    }

    /**
     * 根据产线ID集合获取产线信息列表
     *
     * @param idList 产线ID列表
     * @return 产线信息列表
     */
    @Override
    public List<LocationDTO> getAllByCellIdList(List<Integer> idList) {
        return DtoMapper.convertList(locationRepository.getAllByIdIn(idList), LocationDTO.class);
    }

    @Override
    public List<AreaLocationDTO> getAllBySonId(List<Integer> idList) {
        //产线
        List<LocationDTO> locationDTOs = getAllByCellIdList(idList);
        List<Integer> areaId = locationDTOs.stream().map(LocationDTO::getParentId).collect(Collectors.toList());
        List<LocationDTO> allByCellIdList1 = getAllByCellIdList(areaId);
        List<AreaLocationDTO>
                areaLocationDTOS = DtoMapper.convertList(allByCellIdList1, AreaLocationDTO.class);
        for (AreaLocationDTO areaLocationDTO : areaLocationDTOS) {
            List<LocationDTO> objects = new ArrayList<>();
            for (LocationDTO locationDTO : locationDTOs) {
                if (locationDTO.getParentId().equals(areaLocationDTO.getId())) {
                    objects.add(locationDTO);
                }
            }
            areaLocationDTO.setCellDTO(objects);
        }
        return areaLocationDTOS;
    }

    /**
     * 条件查询location
     *
     * @param locationQueryDTO 查询条件
     * @return location信息
     */
    @Override
    public List<LocationDTO> getLocationByQuery(LocationQueryDTO locationQueryDTO) {
        return locationMapper.getLocationByQuery(locationQueryDTO);
    }


    /**
     * 查询所有location
     *
     * @return location信息列表
     */
    @Override
    public List<LocationDTO> findAll() {
        List<HvBmLocation> locations = locationRepository.findAll();
        locations.sort(Comparator.comparing(HvBmLocation::getIndexNum));
        return DtoMapper.convertList(locations, LocationDTO.class);
    }

    /**
     * 获取所有工厂建模tree
     *
     * @return 工厂建模树
     */
    @Override
    public List<LocationDTO> getLocationTree() {
        List<LocationDTO> locations = findAll();
        //扩展信息都查出来
        Map<Integer, String> types = EnumUtil.enumToMap(LocationTypeEnum.class);
        for (Integer typeId : types.keySet()) {
            BaseExtendService service = locationExtendServiceMapper.getService(typeId);
            List<ExtendInfo> all = service.getAll();
            for (ExtendInfo extendInfo : all) {
                locations.stream()
                        .filter(t -> t.getId().equals(extendInfo.getEntityId()))
                        .findFirst()
                        .ifPresent(t -> {
                            t.setExtend(extendInfo.getValues());
                        });
            }
        }
        for (LocationDTO location : locations) {
            location.setChildLocations(locations.stream()
                    .filter(t -> location.getId().equals(t.getParentId()))
                    .collect(Collectors.toList()));
        }
        List<LocationDTO> result = locations.stream().filter(t -> t.getParentId() == 0).collect(Collectors.toList());
        //设置所有result的父级id列表,用于前端渲染界面使用
        for (LocationDTO locationDTO : result) {
            setParentIds(locationDTO, locations);
        }
        return result;
    }

    /**
     * 设置父级id
     * 递归执行，从树根节点遍历，如果父级节点0.就设置自己，其他的设置父级列表，加上自己的id。
     *
     * @param locationDTO 工厂建模模型
     * @param locations   所有的模型信息
     */
    private void setParentIds(LocationDTO locationDTO, List<LocationDTO> locations) {
        if (locationDTO.getParentId() == 0) {
            locationDTO.setParentIdList(Collections.emptyList());
        } else {
            List<Integer> ids = locations.stream()
                    .filter(t -> t.getId().equals(locationDTO.getParentId()))
                    .findFirst()
                    .map(LocationDTO::getParentIdList)
                    .orElse(Collections.emptyList());
            List<Integer> idList = new ArrayList<>(ids);
            idList.add(locationDTO.getParentId());
            locationDTO.setParentIdList(idList);
        }
        if (locationDTO.getChildLocations() != null && locationDTO.getChildLocations().size() > 0) {
            for (LocationDTO childLocation : locationDTO.getChildLocations()) {
                setParentIds(childLocation, locations);
            }
        }
    }

    /**
     * 导入所有
     *
     * @param file 工厂建模信息文档
     * @throws IllegalAccessException field访问异常
     * @throws IOException            io异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importLocation(MultipartFile file) throws IOException, IllegalAccessException {
        List<LocationExcel> entityList = ExcelUtil.getEntityList(file, 0, LocationExcel.class);
        for (LocationExcel locationExcel : entityList) {
            Integer parentId;
            LocationDTO oldLocation = getHvBmLocationByCode(locationExcel.getCode());
            if (Strings.isBlank(locationExcel.getParentCode())) {
                parentId = 0;
            } else {
                LocationDTO location = getHvBmLocationByCode(locationExcel.getParentCode());
                if (location == null) {
                    throw new BaseKnownException(10000, "导入数据异常：无法获取父级节点:" + locationExcel.getParentCode());
                }
                parentId = location.getId();
            }
            if (oldLocation == null) {
                LocationDTO dto = new LocationDTO();
                dto.setCode(locationExcel.getCode());
                dto.setName(locationExcel.getName());
                dto.setType(locationExcel.getType());
                dto.setParentId(parentId);
                dto.setIndexNum(1);
                create(dto);
            } else {
                oldLocation.setName(locationExcel.getName());
                oldLocation.setType(locationExcel.getType());
                update(oldLocation);
                cleanCache(oldLocation.getId(), oldLocation.getCode());
            }
        }
    }

    /**
     * 导出所有建模数据
     *
     * @return 设备信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @Override
    public ExcelExportDto exportLocation() throws IOException, IllegalAccessException {
        List<LocationExcel> locationList = getAllLocationExcel();
        locationList.sort(Comparator.comparing(LocationExcel::getType));
        ResponseEntity<byte[]> responseEntity = ExcelUtil.generateImportFile(locationList, "locations.xls", LocationExcel.class);
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(responseEntity.getBody());
        excelExportDto.setFileName("locations.xls");
        return excelExportDto;
    }


    /**
     * 导出所有工厂建模基础信息
     *
     * @return 设备信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @Override
    public ResponseEntity<byte[]> exportTemplate() throws IOException, IllegalAccessException {
        List<LocationExcel> locationList = new ArrayList<>();
        return ExcelUtil.generateImportFile(locationList, "locations.xls", LocationExcel.class);
    }

    /**
     * 获取location所有的子集
     *
     * @param locationId 主键
     * @return location所有子集
     */
    @Override
    public List<LocationDTO> getChildLocation(Integer locationId) {
        List<LocationDTO> allDto = findAll();
        Assert.notNull(locationId, "不能传递null");
        List<LocationDTO> child = getChild(locationId, allDto, 0);
        if (locationId != 0) {
            LocationDTO dto = findLocationExtendById(locationId);
            if (dto == null) {
                return new ArrayList<>();
            }
            //把自己进去
            child.add(dto);
        }
        return child;
    }

    /**
     * 根据父级id列表查询下级列表
     *
     * @param parentIds   父级id
     * @param directChild 是否只查父级直接对应的类型,比如车间只查产线。
     * @return 下级列表
     */
    @Override
    public List<LocationDTO> getAllByParentIdList(List<Integer> parentIds, Boolean directChild) {
        List<HvBmLocation> allParents = locationRepository.findAllById(parentIds);
        Integer types = allParents.stream().map(HvBmLocation::getType)
                .distinct()
                .findFirst()
                .orElse(0);
        List<LocationDTO> allChildren = DtoMapper.convertList(locationRepository.getAllByParentIdIn(parentIds), LocationDTO.class);
        if (directChild) {
            allChildren = allChildren.stream()
                    .filter(t -> t.getType().equals(types + 10))
                    .collect(Collectors.toList());
        }
        return allChildren;
    }

    /**
     * 对工厂建模里面的模型进行排序
     *
     * @param relationDTO 模型关系
     */
    @Override
    public void sort(RelationDTO relationDTO) {
        HvBmLocation parentLocation = locationRepository.findById(relationDTO.getParentId())
                .orElseThrow(() -> new BaseKnownException(10000, "建模信息不存在，请检查父级节点信息"));
        List<HvBmLocation> childLocation = locationRepository.findByParentId(parentLocation.getId());
        //找到对应的建模信息，更新index值，并保存
        for (HvBmLocation location : childLocation) {
            relationDTO.getChildIds().stream()
                    .filter(t -> location.getId().equals(t.getLocationId()))
                    .findFirst()
                    .ifPresent(t -> location.setIndexNum(t.getIndex()));
        }
        locationRepository.saveAll(childLocation);
    }

    @Override
    public List<Integer> getLocationIdsByType(int type) {
        return locationMapper.findIdsByType(type);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveLocationByReceiving(List<MesFactoryDTO> factoryDTOS) {
        int i = 0;
        int index = 1;
        for (MesFactoryDTO factoryDTO : factoryDTOS) {
            if (factoryDTO.getLocationType() != 10) {
                throw new BaseKnownException("最大父级类型不为集团类型");
            }
            i = i + saveLocationRound(factoryDTO, 0, index, 10);
            index++;
        }
        return i;
    }

    /**
     * 调用自身添加location
     * @param mesFactoryDTO
     * @param parentId
     * @return
     */
    int saveLocationRound(MesFactoryDTO mesFactoryDTO,int parentId,int indexNum,int type){
        int i = 0;
        HvBmLocation location = new HvBmLocation();
        location.setCode(mesFactoryDTO.getLocationCode());
        location.setName(mesFactoryDTO.getLocationName());
        if(type!=mesFactoryDTO.getLocationType()) {
            throw new BaseKnownException("输入的类型与父子级关系不匹配，请检查类型输入是否有误");
        }
        location.setType(mesFactoryDTO.getLocationType());
        location.setParentId(parentId);
        location.setIndexNum(indexNum);
        HvBmLocation hvBmLocation = locationRepository.findByCode(mesFactoryDTO.getLocationCode());
        //判断编码是否已存在
        if(hvBmLocation ==null) {
            //不存在，插入数据
            hvBmLocation = locationRepository.saveAndFlush(location);
        }else if (hvBmLocation !=null && mesFactoryDTO.getLocationName() !=null){
            //存在，只修改名称
            hvBmLocation.setName(mesFactoryDTO.getLocationName());
            updateByCode(hvBmLocation);
        }

        i++;
        if(mesFactoryDTO.getChildrenList().size()>0){
            int index = 1;
            type+=10;
            for (MesFactoryDTO factoryDTO : mesFactoryDTO.getChildrenList()) {
                i = i + saveLocationRound(factoryDTO, hvBmLocation.getId(),index,type);
                index++;
            }
        }
        return i;
    }


    /**
     * 新增设备信息
     *
     * @param locationDTO 设备信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public LocationDTO create(LocationDTO locationDTO) {
        locationDTO.setId(null);
        if (locationDTO.getIndexNum() == null) {
            locationDTO.setIndexNum(0);
        }
        HvBmLocation location = new HvBmLocation();
        //转化成DTO
        BeanUtils.copyProperties(locationDTO, location);
        LocationDTO result = DtoMapper.convert(location, LocationDTO.class);
        if (location.getParentId() == null) {
            location.setParentId(ROOT_PARENT_ID);
        }
        if (location.getParentId() != ROOT_PARENT_ID) {
            HvBmLocation parentLocation = locationRepository.getById(location.getParentId());
            if (parentLocation == null) {
                throw new BaseKnownException("父节点查询异常，无法找到对应的父节点。父节点id：" + location.getParentId());
            }
            if (parentLocation.getType() >= location.getType()) {
                throw new BaseKnownException("父节点类型不匹配，不能反向关联节点信息，节点编码：" + location.getCode());
            }
        }
        locationRepository.save(location);
        //判断获取到扩展字段信息不为空 则添加
        if (locationDTO.getExtend() != null) {
            ExtendInfo info = new ExtendInfo();
            info.setEntityId(location.getId());
            info.setValues(locationDTO.getExtend());
            (locationExtendServiceMapper.getService(locationDTO.getType())).addExtendInfo(info);
            result.setExtend(info.getValues());
        }
        result.setId(location.getId());
        return result;
    }

    /***
     * 更新设备信息
     *
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public LocationDTO update(LocationDTO locationDTO) {
        HvBmLocation entity = locationRepository.findById(locationDTO.getId())
                .orElseThrow(() -> new BaseKnownException(10000, "数据不存在"));
        entity.setName(locationDTO.getName());
        entity.setIndexNum(locationDTO.getIndexNum());
        entity.setParentId(locationDTO.getParentId());
        if (locationDTO.getParentId() != ROOT_PARENT_ID) {
            HvBmLocation parentLocation = locationRepository.getById(locationDTO.getParentId());
            if (parentLocation == null) {
                throw new BaseKnownException("父节点查询异常，无法找到对应的父节点。父节点id：" + locationDTO.getParentId());
            }
            if (parentLocation.getType() >= entity.getType()) {
                throw new BaseKnownException("父节点类型不匹配，不能反向关联节点信息，节点编码：" + locationDTO.getCode());
            }
        }
        locationRepository.save(entity);
        ExtendInfo info = new ExtendInfo();
        info.setEntityId(entity.getId());
        info.setValues(locationDTO.getExtend());
        (locationExtendServiceMapper.getService(locationDTO.getType())).updateExtendInfo(info);
        LocationDTO result = DtoMapper.convert(entity, LocationDTO.class);
        result.setExtend(info.getValues());
        //清理缓存信息
        cleanCache(entity.getId(), entity.getCode());
        return result;
    }


    /**
     * 清理缓存
     *
     * @param id   实体id
     * @param code 实体编码
     */
    private void cleanCache(Integer id, String code) {
        try {
            String cacheKey = CACHE_FOR_ID + id;
            stringRedisTemplate.delete(cacheKey);
            cacheKey = CACHE_FOR_EXTEND + code;
            stringRedisTemplate.delete(cacheKey);
            cacheKey = CACHE_FOR_ENTITY + code;
            stringRedisTemplate.delete(cacheKey);
        } catch (Exception ex) {
            log.error("redis 清理出现问题:{}", ex.getMessage());
        }
    }

    private List<LocationExcel> getAllLocationExcel() {
        List<HvBmLocation> all = locationRepository.findAll();
        return all.stream()
                .map(t -> {
                    LocationExcel excel = new LocationExcel();
                    excel.setCode(t.getCode());
                    excel.setName(t.getName());
                    excel.setType(t.getType());
                    excel.setParentCode(all.stream()
                            .filter(l -> l.getId().equals(t.getParentId()))
                            .map(HvBmLocation::getCode)
                            .findFirst()
                            .orElse(""));
                    return excel;
                }).collect(Collectors.toList());
    }

    /**
     * 获取所有的子集信息
     *
     * @param id          主键
     * @param allDto      所有的工厂建模信息
     * @param currentTime 当前时间
     * @return 所有的子集信息
     */
    private List<LocationDTO> getChild(Integer id, List<LocationDTO> allDto, Integer currentTime) {
        List<LocationDTO> result = new ArrayList<>();
        if (currentTime > 10) {
            log.warn("设备工厂建模出现数据错误，请检查是否有循环配置的数据");
            return result;
        }
        List<LocationDTO> dtos = allDto.stream()
                .filter(t -> t.getParentId().equals(id))
                .collect(Collectors.toList());
        if (dtos.size() == 0) {
            return result;
        } else {
            result.addAll(dtos);
            for (LocationDTO dto : dtos) {
                result.addAll(getChild(dto.getId(), allDto, currentTime + 1));
            }
            return result;
        }
    }

    @Override
    public String getLineByStationCode(String stationCode) {
        return locationMapper.getLineByStationCode(stationCode);
    }


    @Override
    public Integer updateByCode(HvBmLocation hvBmLocation) {
        return locationMapper.updateByCode(hvBmLocation);
    }


    @Override
    public LocationDTO getLineById(Integer id) {
        return DtoMapper.convert(locationRepository.getById(id), LocationDTO.class);
    }

    @Override
    public LocationDTO getLineInfoByLineCode(String lineCode) {
        return DtoMapper.convert(locationRepository.findByCode(lineCode), LocationDTO.class);
    }

    /**
     * 根据产线编号查询产线信息
     *
     * @param codes 产线编号集合
     * @return
     */
    @Override
    public List<LocationDTO> getLocationsByCodes(List<String> codes) {
        List<HvBmLocation> locationList = locationRepository.findByCodeIn(codes);
        List<LocationDTO> result = new ArrayList<>();
        for (HvBmLocation hvBmLocation : locationList) {
            LocationDTO locationDTO = new LocationDTO();
            BeanUtils.copyProperties(hvBmLocation, locationDTO);
            result.add(locationDTO);
        }
        return result;
    }
}

