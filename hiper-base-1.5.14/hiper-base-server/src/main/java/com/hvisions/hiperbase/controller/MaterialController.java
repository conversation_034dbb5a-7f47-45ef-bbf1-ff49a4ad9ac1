package com.hvisions.hiperbase.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.config.coderule.utils.SerialCodeUtils;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.ExtendColumnInfo;
import com.hvisions.common.dto.ExtendInfo;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.service.BaseExtendService;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.CodeRuleConsts;
import com.hvisions.hiperbase.bom.dto.BomMaterialDTO;
import com.hvisions.hiperbase.consts.MaterialConst;
import com.hvisions.hiperbase.materials.dto.*;
import com.hvisions.hiperbase.service.material.MaterialService;
import com.hvisions.thirdparty.common.dto.MesMaterialDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <p>Title: HvBmMaterialsExtendController</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/11/20</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/material")
@Slf4j
@Api(description = "物料主数据控制器")
public class MaterialController {

    @Autowired
    MaterialService materialService;


    @Resource(name = "material_extend")
    BaseExtendService materialExtendService;

    @Autowired
    SerialCodeUtils serialCodeUtils;


    @GetMapping("/getMaterialCode")
    @ApiOperation(value = "获取物料编码")
    public String getMaterialCode() {
        return serialCodeUtils.generateCode(CodeRuleConsts.MATERIAL_CODE);
    }

    /**
     * 添加物料信息
     *
     * @param materialDTO 物料信息
     * @return 添加后的物料id
     */
    @PostMapping("/createMaterial")
    @ApiOperation(value = "添加物料信息")
    public int createMaterial(@RequestBody BaseMaterialDTO materialDTO) {
        return materialService.createMaterial(materialDTO);
    }

    /**
     * 更新物料信息
     *
     * @param materialDTO 传入DTO对象
     * @return materialType id
     */
    @PutMapping("/updateMaterial")
    @ApiOperation(value = "更新物料信息")
    public int updateMaterial(@RequestBody MaterialDTO materialDTO) {

        return materialService.updateMaterial(materialDTO);
    }

    /**
     * 删除物料信息
     *
     * @param id 物料主id
     */
    @DeleteMapping(value = "/deleteMaterial/{id}")
    @ApiOperation(value = "删除物料信息")
    @Transactional(rollbackFor = Exception.class)
    public void deleteMaterial(@PathVariable int id) {
        materialService.deleteMaterialById(id);
    }


    /**
     * 查询物料信息
     *
     * @param materialQueryDTO 物料查询条件
     * @return 物料分页信息
     */
    @PostMapping(value = "/getMaterial")
    @ApiOperation(value = "查询物料")
    public Page<MaterialDTO> getMaterial(@RequestBody QueryDTO materialQueryDTO) {
        return materialService.getMaterial(materialQueryDTO);
    }

    /**
     * 通过ID查询materials信息
     *
     * @param id materialsID
     * @return MaterialDTO
     */
    @GetMapping(value = "/getMaterialById/{id}")
    @ApiOperation(value = "通过ID获取物料信息")
    public MaterialDTO getMaterialById(@PathVariable int id) {
        return materialService.getMaterialById(id);
    }

    /**
     * 根据ID列表查询物料信息
     *
     * @param listId id列表
     * @return 物料信息列表
     */
    @PostMapping(value = "/getMaterialsByIdList")
    @ApiOperation(value = "根据ID列表查询物料信息")
    public List<MaterialDTO> getMaterialsByIdList(@RequestBody List<Integer> listId) {
        return materialService.getMaterialsByIdList(listId);
    }


    //物料扩展

    /**
     * 添加物料扩展属性
     *
     * @param extendColumnInfo 扩展属性信息
     */
    @PostMapping("/createMaterialExtend")
    @ApiOperation(value = "添加物料属性")
    public void createMaterialExtend(@RequestBody ExtendColumnInfo extendColumnInfo) {
        materialExtendService.addExtend(extendColumnInfo);
    }

    /**
     * 更新物料扩展属性
     *
     * @param extendInfo 扩展信息
     */
    @PutMapping("/updateExtendInfo")
    @ApiOperation(value = "添加物料属性")
    public void updateExtendInfo(@RequestBody ExtendInfo extendInfo) {
        materialExtendService.updateExtendInfo(extendInfo);
    }

    /**
     * 删除物料扩展属性
     *
     * @param columnName 扩展属性名称
     */
    @DeleteMapping("/deleteMaterialExtend/{columnName}")
    @ApiOperation(value = "删除扩展属性")
    @Transactional(rollbackFor = Exception.class)
    public void deleteMaterialExtend(@PathVariable String columnName) {
        materialExtendService.dropExtend(columnName);
    }

    /**
     * 获取所有物料扩展字段信息
     *
     * @return 料扩展字段信息
     */
    @GetMapping(value = "/getAllMaterialExtend")
    @ApiOperation(value = "获取所有物料扩展字段信息")
    public List<ExtendColumnInfo> getAllMaterialExtend() {
        return materialExtendService.getExtendColumnInfo();
    }

    /**
     * 获取导入模板
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/getMaterialsImportTemplateLink")
    @ApiOperation(value = "获取导入模板 支持超链接")
    public ResponseEntity<byte[]> getImportTemplate() throws IOException, IllegalAccessException {
        return ExcelUtil.generateImportFile(MaterialDTO.class, MaterialConst.MATERIAL_EXPORT_FILE_NAME, materialExtendService.getExtendColumnInfo());
    }

    /**
     * 导出所有物料信息
     *
     * @return 物料信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/exportMaterialsLink")
    @ApiOperation(value = "导出 支持超链接")
    public ResponseEntity<byte[]> export() throws IOException, IllegalAccessException {
        return materialService.export();
    }

    /**
     * 导入所有物料信息
     *
     * @param file 物料信息文档
     * @return 导入信息详情
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    @PostMapping(value = "/importMaterial")
    @ApiOperation(value = "导入物料信息(code存在则更新，不存在则新增)")
    public ImportResult importMaterial(@RequestParam("file") MultipartFile file) throws IllegalAccessException, ParseException, IOException {
        return materialService.importMaterials(file);
    }


    /**
     * 导出所有物料信息
     * @param queryDTO
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    @ApiResultIgnore
    @PostMapping(value = "/exportMaterials")
    @ApiOperation(value = "导出所有物料信息 ")
    public ResultVO<ExcelExportDto> exportMaterials(@RequestBody MaterialQueryDTO queryDTO) throws IOException, IllegalAccessException {
        return materialService.exportMaterials(DtoMapper.convert(queryDTO, QueryDTO.class));
    }

    /**
     * 获取导入模板
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/getMaterialsImportTemplate")
    @ApiOperation(value = "获取物料导入模板 ")
    public ResultVO<ExcelExportDto> getMaterialsImportTemplate() throws IOException, IllegalAccessException {
        return materialService.getMaterialsImportTemplate();
    }


    /**
     * 绑定物料与BOM关联关系
     *
     * @param bomMaterialDTO dto
     * @return id
     */
    @PostMapping(value = "/createBomMaterial")
    @ApiOperation(value = "绑定物料与BOM关联关系")
    public Integer createBomMaterial(@RequestBody BomMaterialDTO bomMaterialDTO) {
        return materialService.createBomMaterial(bomMaterialDTO);
    }

    /**
     * 删除物料BOM关联关系
     *
     * @param materialId 物料ID
     */
    @DeleteMapping(value = "/deleteBomMaterialByMaterialId/{materialId}")
    @ApiOperation(value = "删除物料BOM关联关系")
    @Transactional(rollbackFor = Exception.class)
    public void deleteBomMaterialByMaterialId(@PathVariable int materialId) {
        materialService.deleteBomMaterialByMaterialId(materialId);
    }

    /**
     * 更新转换设置
     *
     * @param parseSettingDTO 转换设置
     */
    @ApiOperation(value = "更新转换设置")
    @PostMapping(value = "/updateSetting")
    public void updateSetting(@RequestBody ParseSettingDTO parseSettingDTO) {
        materialService.updateSetting(parseSettingDTO);
    }

    /**
     * 查询转换设置
     *
     * @return 转换设置"
     */
    @ApiOperation(value = "查询转换设置")
    @GetMapping(value = "/getSetting")
    public ParseSettingDTO getSetting() {
        return materialService.getSetting();
    }

    /**
     * 根据批次号匹配对应物料
     *
     * @param batchNum 批次号
     * @return 物料信息
     */
    @ApiOperation(value = "根据批次号匹配物料")
    @GetMapping(value = "/regularBatchNum/{batchNum}")
    public MaterialDTO regularBatchNum(@PathVariable String batchNum) {
        return materialService.findMaterialByBatch(batchNum);
    }

    /**
     * 根据批次号匹配对应物料
     *
     * @param batchNum 批次号
     * @return 物料信息
     */
    @ApiOperation(value = "根据批次号匹配物料")
    @PutMapping(value = "/regularBatchNumPut/{batchNum}")
    public MaterialDTO regularBatchNumPut(@PathVariable String batchNum) {
        return materialService.findMaterialByBatch(batchNum);
    }


    /**
     * 查询所有成品半成品物料信息
     *
     * @return 物料信息
     */
    @ApiOperation(value = "查询所有成品半成品物料信息(无条件)")
    @PostMapping(value = "/getAllProductAndByProduct")
    public List<MaterialDTO> getAllProduct() {
        return materialService.getAllProductAndByProduct();
    }

    /**
     * 根据物料类型查询物料
     *
     * @param typeId 物料类型id
     * @return 物料信息
     */
    @GetMapping(value = "/getMaterialByTypeId/{typeId}")
    @ApiOperation(value = "根据物料类型查询物料")
    public List<MaterialDTO> getMaterialByTypeId(@PathVariable int typeId) {
        return materialService.getMaterialByTypeId(typeId);
    }

    /**
     * 根据物料编码和特征值查询物料
     *
     * @param eigenvalueDTO 查询条件
     * @return 物料信息
     */
    @PostMapping(value = "/getMaterialByMaterialQuery")
    @ApiOperation(value = "根据物料编码和特征值查询物料")
    public MaterialDTO getMaterialByMaterialQuery(@RequestBody MaterialCodeAndEigenvalueDTO eigenvalueDTO) {
        return materialService.getMaterialByMaterialCode(eigenvalueDTO.getMaterialCode(), eigenvalueDTO.getEigenvalue());
    }

    /**
     * 根据物料编码批量查询物料
     *
     * @param materialCodes 多个物料号
     * @return 物料信息
     */
    @PostMapping(value = "/findByMaterialCodeIn")
    @ApiOperation(value = "根据物料编码批量查询物料")
    public List<MaterialDTO> findByMaterialCodeIn(@RequestBody Collection<String> materialCodes) {
        return materialService.findByMaterialCodeIn(materialCodes);
    }


    /**
     * 查询所有成品半成品物料信息
     *
     * @param materialQueryDTO 物料查询条件
     * @return 物料分页信息
     */
    @PostMapping(value = "/getAllProductAndByProductByQuery")
    @ApiOperation(value = "查询所有成品半成品物料信息")
    @Deprecated
    public Page<MaterialDTO> getAllProductAndByProduct(@RequestBody QueryDTO materialQueryDTO) {
        List<String> types = new ArrayList<>();
        types.add("Product");
        types.add("Semi_Product");
        materialQueryDTO.setMaterialTypes(types);
        return materialService.getMaterial(materialQueryDTO);
    }

    /**
     * 分页查询
     *
     * @param materialQueryDTO 查询条件对象
     * @return 分页信息
     */
    @Deprecated
    @PostMapping(value = "/findByQuery")
    @ApiOperation(value = "分页查询")
    public Page<MaterialDTO> findByQuery(@RequestBody MaterialQueryDTO materialQueryDTO) {
        return materialService.getMaterial(DtoMapper.convert(materialQueryDTO, QueryDTO.class));
    }

    /**
     * 根据物料编码和特征值查询物料ID
     *
     * @param eigenvalue   特征值
     * @param materialCode 物料编码
     * @return 物料ID
     */
    @Deprecated
    @GetMapping(value = "/getMaterialIdByMaterialCode/{materialCode},{eigenvalue}")
    @ApiOperation(value = "根据物料编码和特征值查询物料ID")
    public Integer getMaterialIdByMaterialCodeAndEigenvalue(@PathVariable String materialCode, @PathVariable String eigenvalue) {
        return materialService.getMaterialIdByMaterialCode(materialCode, eigenvalue);
    }

    /**
     * 根据物料编码和特征值查询物料ID
     *
     * @param eigenvalue   特征值
     * @param materialCode 物料编码
     * @return 物料ID
     */
    @GetMapping(value = "/getIdByCode")
    @ApiOperation(value = "根据物料编码和特征值查询物料ID")
    public Integer getIdByCode(@RequestParam String materialCode, @RequestParam(defaultValue = "1") String eigenvalue) {
        return materialService.getMaterialIdByMaterialCode(materialCode, eigenvalue);
    }

    /**
     * 根据物料编码和特征值查询物料
     *
     * @param eigenvalue   特征值
     * @param materialCode 物料编码
     * @return 物料ID
     */
    @GetMapping(value = "/getByCode")
    @ApiOperation(value = "根据物料编码和特征值查询物料")
    public MaterialDTO getByCode(@RequestParam String materialCode, @RequestParam(defaultValue = "1") String eigenvalue) {
        return materialService.getMaterialByMaterialCode(materialCode, eigenvalue);
    }

    /**
     * 根据物料编码和特征值查询物料
     *
     * @param eigenvalue   特征值
     * @param materialCode 物料编码
     * @return 物料ID
     */
    @Deprecated
    @GetMapping(value = "/getMaterialByMaterialCodeAndEigenvalue/{materialCode},{eigenvalue}")
    @ApiOperation(value = "根据物料编码和特征值查询物料")
    public MaterialDTO getMaterialByMaterialCodeAndEigenvalue(@PathVariable String materialCode, @PathVariable String eigenvalue) {
        return materialService.getMaterialByMaterialCode(materialCode, eigenvalue);
    }


    /**
     * 根据编码及特征值模糊查询
     *
     * @param materialCodeQueryDTO 传入DTo
     * @return 物料分页信息
     */
    @Deprecated
    @PostMapping(value = "/getHvBmMaterialByMaterialCodeLike")
    @ApiOperation(value = "根据编码及特征值模糊查询")
    public List<MaterialDTO> getHvBmMaterialByMaterialCodeLike(@RequestBody MaterialCodeQueryDTO materialCodeQueryDTO) {
        return materialService.getHvBmMaterialByMaterialCodeLike(materialCodeQueryDTO);
    }

    /**
     * 根据编码及名称模糊查询
     *
     * @param queryDTO 传入DTo
     * @return 物料分页信息
     */
    @Deprecated
    @PostMapping(value = "/getMaterialByNameOrCode")
    @ApiOperation(value = "根据物料编码及名称获取物料信息")
    public Page<MaterialDTO> getMaterialByNameOrCode(@RequestBody MaterialQueryDTO queryDTO) {
        return materialService.getMaterial(DtoMapper.convert(queryDTO, QueryDTO.class));
    }


    /**
     * 根据编码及名称模糊查询
     *
     * @param queryDTO 传入DTo
     * @return 物料分页信息
     */
    @Deprecated
    @PostMapping(value = "/getAllByCodeOrName")
    @ApiOperation(value = "根据物料编码或者名称获取物料信息(即查编码又查名称)")
    public Page<MaterialDTO> getAllByCodeOrName(@RequestBody MaterialNameCodeDTO queryDTO) {
        QueryDTO query = DtoMapper.convert(queryDTO, QueryDTO.class);
        query.setKeyWord(queryDTO.getNameOrCode());
        return materialService.getMaterial(query);
    }

    /**
     * 查询全部
     * @return 列表
     */
    @ApiOperation(value = "获取所有物料")
    @GetMapping(value = "/getAll")
    public List<MaterialDTO> getAll(){
        return materialService.getAll();
    }


    /**
     * 根据物料编码查询物料
     *
     * @param materialCode 物料编码
     * @return 物料ID
     */
    @Deprecated
    @GetMapping(value = "/getByMaterialCode")
    @ApiOperation(value = "根据物料编码和特征值查询物料")
    public MaterialDTO getByMaterialCode(@RequestParam String materialCode) {
        return materialService.getByMaterialCode(materialCode);
    }

    /**
     * 根据物料分组ID查询物料
     *
     * @param materialGroupId 物料分组ID
     * @return 物料集合
     */
    @GetMapping(value = "/getMaterialIdByMaterialGroupId/{materialGroupId}")
    public List<Integer> getMaterialIdByMaterialGroupId(@PathVariable("materialGroupId") Integer materialGroupId){
        return materialService.getMaterialIdByMaterialGroupId(materialGroupId);
    }


    @GetMapping(value = "/getGroupByMaterialCode/{materialCode}")
    public Integer getGroupByMaterialCode(@PathVariable("materialCode") String materialCode) {
        return materialService.getGroupByMaterialCode(materialCode);
    }

    /**
     * 将接收到的物料主数据入库
     * @param materialList
     * @return
     */
    @PostMapping("/saveMaterialByReceiving")
    @ApiOperation("将接收到的物料主数据入库")
    public void saveMaterialByReceiving(@RequestBody List<MesMaterialDTO> materialList){
         materialService.saveMaterialByReceiving(materialList);
    }

    /**
     *根据物料号查询物料信息(查询多个)
     * @param materialCodes
     * @return
     */
    @PostMapping("/getMaterialListByMaterialCodes")
    @ApiOperation("根据物料号查询物料信息")
    public List<MaterialDTO> getMaterialListByMaterialCodes(@RequestBody List<String> materialCodes){
        return materialService.getMaterialListByMaterialCodes(materialCodes);
    }

    @GetMapping("/getOneByMaterialCode/{materialCode}")
    @ApiOperation("只根据物料编码查询一条物料信息")
    public MaterialInfoDTO getOneByMaterialCode(@PathVariable("materialCode")  String materialCode){
        return materialService.getOneByMaterialCode(materialCode);
    }

    /**
     * 按钮手动下发物料主数据给堆场或者海康
     * @return
     */
    @PostMapping("/resendMaterialToStockyardOrIwms/{target}")
    @ApiOperation("按钮手动下发物料主数据给堆场或者海康")
    public ResultVO<?>  resendMaterialToStockyardOrIwms(@PathVariable("target") String target){
       return materialService.resendMaterialToStockyardOrIwms(target);
    }
}
