package com.hvisions.hiperbase.service.equipment;

import com.hvisions.hiperbase.equipment.equipmentclass.EquipmentClassPropertyDTO;

import java.util.List;

/**
 * <p>Title: EquipmentClassPropertyService</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/3/25</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface EquipmentClassPropertyService {
    /**
     * 创建属性
     *
     * @param propertyDTO 属性
     * @return 主键
     */
    Integer create(EquipmentClassPropertyDTO propertyDTO) ;

    /**
     * 更新属性
     *
     * @param propertyDTO 属性
     */
    void update(EquipmentClassPropertyDTO propertyDTO);

    /**
     * 删除属性
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 获取属性
     *
     * @param id 设备类型id
     * @return 设备属性类型属性列表
     */
    List<EquipmentClassPropertyDTO> findByEquipmentClassId(Integer id);
}

    
    
    
    