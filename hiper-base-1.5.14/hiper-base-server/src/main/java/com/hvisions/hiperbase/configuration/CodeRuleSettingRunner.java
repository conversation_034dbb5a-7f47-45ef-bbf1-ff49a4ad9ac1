package com.hvisions.hiperbase.configuration;

import com.hvisions.common.config.coderule.enums.CodeRuleSerializeEnum;
import com.hvisions.common.config.coderule.enums.ExpireUnitEnum;
import com.hvisions.common.config.coderule.serialize.DateRuleValue;
import com.hvisions.common.config.coderule.serialize.KvRuleValue;
import com.hvisions.common.config.coderule.serialize.SerialNumRuleValue;
import com.hvisions.common.dto.CodeRuleDetailDto;
import com.hvisions.common.dto.CodeRuleDto;
import com.hvisions.common.dto.FullCodeRuleDto;
import com.hvisions.common.runner.SafetyCommandLineRunner;
import com.hvisions.framework.client.CodeRuleClient;
import com.hvisions.hiperbase.CodeRuleConsts;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

/**
 * <p>Title: CodeRuleSettingRunner</p >
 * <p>Description: 编码规则配置</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2023-02-10</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Component
@Slf4j
public class CodeRuleSettingRunner extends SafetyCommandLineRunner {

    private final CodeRuleClient codeRuleClient;

    @Autowired
    public CodeRuleSettingRunner(CodeRuleClient codeRuleClient) {
        this.codeRuleClient = codeRuleClient;
    }

    @Override
    public void callRunner(String... args) {
        log.info("编码规则配置检测");
        FullCodeRuleDto fullCodeRuleDto = new FullCodeRuleDto();
        fullCodeRuleDto.setStorageName("hiper-base");
        // 1: 系统规则; 2: 自定义规则
        fullCodeRuleDto.setStorageType(1);
        List<CodeRuleDto> codeRuleDtos = buildCodeRuleDtos(buildMaterialCodeRule(), buildEqptCodeRule());
        fullCodeRuleDto.setCodeRuleDtoList(codeRuleDtos);
        // 保存编码规则
        codeRuleClient.saveFullCodeRule(fullCodeRuleDto);
        log.info("编码规则配置检测完毕");
    }

    private List<CodeRuleDto> buildCodeRuleDtos(CodeRuleDto... codeRuleDto) {
        return new ArrayList<>(Arrays.asList(codeRuleDto));
    }

    /**
     * 设置物料编码规则
     *
     * @return 编码规则
     */
    private CodeRuleDto buildMaterialCodeRule() {
        CodeRuleDto codeRuleDto = new CodeRuleDto();
        codeRuleDto.setRuleName("物料编码");
        codeRuleDto.setRuleCode(CodeRuleConsts.MATERIAL_CODE);
        codeRuleDto.setUsed(true);
        List<CodeRuleDetailDto> detailDtos = buildMaterialCodeDetail();
        codeRuleDto.setCodeRuleDetailDtos(detailDtos);
        return codeRuleDto;
    }

    /**
     * 设置编码规则段
     *
     * @return 编码规则段列表
     */
    private List<CodeRuleDetailDto> buildMaterialCodeDetail() {
        List<CodeRuleDetailDto> detailDtos = new LinkedList<>();
        // 1.常量
        CodeRuleDetailDto constDetail = new CodeRuleDetailDto();
        constDetail.setRuleType(CodeRuleSerializeEnum.CONSTANT.getType());
        KvRuleValue kvRuleValue = new KvRuleValue();
        kvRuleValue.setValue("M");
        constDetail.setRuleValue(kvRuleValue);
        // 2.日期
        CodeRuleDetailDto dateDetail = new CodeRuleDetailDto();
        dateDetail.setRuleType(CodeRuleSerializeEnum.DATE.getType());
        DateRuleValue dateRuleValue = new DateRuleValue();
        dateRuleValue.setFormat("yyyyMMdd");
        dateDetail.setRuleValue(dateRuleValue);
        // 3.序列号
        CodeRuleDetailDto serialDetail = new CodeRuleDetailDto();
        serialDetail.setRuleType(CodeRuleSerializeEnum.SERIAL_NUMBER.getType());
        SerialNumRuleValue serialRuleValue = new SerialNumRuleValue();
        serialRuleValue.setFirstValue(0);
        serialRuleValue.setStep(1);
        serialRuleValue.setResetType(ExpireUnitEnum.DAY.getCode());
        serialRuleValue.setFill(0);
        serialDetail.setRuleValue(serialRuleValue);

        detailDtos.add(constDetail);
        detailDtos.add(dateDetail);
        detailDtos.add(serialDetail);
        return detailDtos;
    }

    /**
     * 设置设备编码规则
     *
     * @return 编码规则
     */
    private CodeRuleDto buildEqptCodeRule() {
        CodeRuleDto codeRuleDto = new CodeRuleDto();
        codeRuleDto.setRuleName("设备编码");
        codeRuleDto.setRuleCode(CodeRuleConsts.EQUIPMENT_CODE);
        codeRuleDto.setUsed(true);
        List<CodeRuleDetailDto> detailDtos = buildEqptCodeDetail();
        codeRuleDto.setCodeRuleDetailDtos(detailDtos);
        return codeRuleDto;
    }

    /**
     * 设置编码规则段
     *
     * @return 编码规则段列表
     */
    private List<CodeRuleDetailDto> buildEqptCodeDetail() {
        List<CodeRuleDetailDto> detailDtos = new LinkedList<>();
        // 1.常量
        CodeRuleDetailDto constDetail = new CodeRuleDetailDto();
        constDetail.setRuleType(CodeRuleSerializeEnum.CONSTANT.getType());
        KvRuleValue kvRuleValue = new KvRuleValue();
        kvRuleValue.setValue("EQPT");
        constDetail.setRuleValue(kvRuleValue);
        // 2.日期
        CodeRuleDetailDto dateDetail = new CodeRuleDetailDto();
        dateDetail.setRuleType(CodeRuleSerializeEnum.DATE.getType());
        DateRuleValue dateRuleValue = new DateRuleValue();
        dateRuleValue.setFormat("yyyyMMdd");
        dateDetail.setRuleValue(dateRuleValue);
        // 3.序列号
        CodeRuleDetailDto serialDetail = new CodeRuleDetailDto();
        serialDetail.setRuleType(CodeRuleSerializeEnum.SERIAL_NUMBER.getType());
        SerialNumRuleValue serialRuleValue = new SerialNumRuleValue();
        serialRuleValue.setFirstValue(0);
        serialRuleValue.setStep(1);
        serialRuleValue.setResetType(ExpireUnitEnum.DAY.getCode());
        serialRuleValue.setFill(0);
        serialDetail.setRuleValue(serialRuleValue);

        detailDtos.add(constDetail);
        detailDtos.add(dateDetail);
        detailDtos.add(serialDetail);
        return detailDtos;
    }
}
