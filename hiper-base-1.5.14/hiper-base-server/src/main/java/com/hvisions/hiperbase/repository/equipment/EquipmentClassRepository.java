package com.hvisions.hiperbase.repository.equipment;

import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentClass;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <p>Title: EquipmentClassRepository</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/3/25</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface EquipmentClassRepository extends JpaRepository<HvBmEquipmentClass, Integer> {
    /**
     * 根据编码查询
     *
     * @param code 类型编码
     * @return 属性类型
     */
    HvBmEquipmentClass findByCode(String code);

    /**
     * 查询分页
     * @param code 编码
     * @param name 名称
     * @param pageable 分页信息
     * @return 分页数据
     */
    Page<HvBmEquipmentClass> findAllByCodeContainsOrNameContaining(String code, String name, Pageable pageable);
}









