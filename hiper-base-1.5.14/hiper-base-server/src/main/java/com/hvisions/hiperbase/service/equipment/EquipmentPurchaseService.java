package com.hvisions.hiperbase.service.equipment;

import com.hvisions.hiperbase.equipment.EquipmentPurchaseDto;

/**
 * <p>Title: EquipmentPurchaseService</p >
 * <p>Description: 设备采购service</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/5</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
public interface EquipmentPurchaseService {
    /**
     * 新增设备采购
     * @param equipmentPurchaseDto 设备采购dto
     */
    void save(EquipmentPurchaseDto equipmentPurchaseDto);

    /**
     * 根据设备id查询设备采购信息
     * @param id 设备id
     * @return 设备采购信息
     */
    EquipmentPurchaseDto findByEquipmentId(Integer id);
}
