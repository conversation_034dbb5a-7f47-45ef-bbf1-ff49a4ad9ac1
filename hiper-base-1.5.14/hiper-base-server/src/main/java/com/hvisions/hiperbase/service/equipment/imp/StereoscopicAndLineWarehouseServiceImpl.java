package com.hvisions.hiperbase.service.equipment.imp;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.hiperbase.dao.equipment.StereoscopicAndLineWarehouseMapper;
import com.hvisions.hiperbase.entity.equipment.HvBmStereoscopicAndLineWarehouse;
import com.hvisions.hiperbase.equipment.StereoscopicAndLineWarehouseDTO;
import com.hvisions.hiperbase.equipment.StereoscopicAndLineWarehouseQueryDTO;
import com.hvisions.hiperbase.repository.equipment.StereoscopicAndLineWarehouseRepository;
import com.hvisions.hiperbase.service.equipment.StereoscopicAndLineWarehouseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @Date 2024/5/22
 */
@Service
@Slf4j
public class StereoscopicAndLineWarehouseServiceImpl implements StereoscopicAndLineWarehouseService {

    @Autowired
    private StereoscopicAndLineWarehouseMapper stereoscopicAndLineWarehouseMapper;

    @Autowired
    private StereoscopicAndLineWarehouseRepository stereoscopicAndLineWarehouseRepository;

    @Override
    public Page<StereoscopicAndLineWarehouseDTO> getPage(StereoscopicAndLineWarehouseQueryDTO queryDTO) {
        return PageHelperUtil.getPage(stereoscopicAndLineWarehouseMapper::getPage,queryDTO);
    }

    @Override
    public int addStereoscopicAndLineWarehouse(StereoscopicAndLineWarehouseDTO stereoscopicAndLineWarehouseDTO) {
        HvBmStereoscopicAndLineWarehouse hvBmStereoscopicAndLineWarehouse = stereoscopicAndLineWarehouseRepository.saveAndFlush(DtoMapper.convert(stereoscopicAndLineWarehouseDTO, HvBmStereoscopicAndLineWarehouse.class));
        return hvBmStereoscopicAndLineWarehouse.getId();
    }

    @Override
    public int updateStereoscopicAndLineWarehouse(StereoscopicAndLineWarehouseDTO stereoscopicAndLineWarehouseDTO) {
        HvBmStereoscopicAndLineWarehouse hvBmStereoscopicAndLineWarehouse = stereoscopicAndLineWarehouseRepository.save(DtoMapper.convert(stereoscopicAndLineWarehouseDTO, HvBmStereoscopicAndLineWarehouse.class));
        return hvBmStereoscopicAndLineWarehouse.getId();
    }

    @Override
    public void deleteStereoscopicAndLineWarehouse(int id) {
        stereoscopicAndLineWarehouseRepository.deleteById(id);
    }
}
