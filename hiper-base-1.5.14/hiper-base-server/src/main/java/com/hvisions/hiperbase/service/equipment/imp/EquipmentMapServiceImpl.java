package com.hvisions.hiperbase.service.equipment.imp;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.hiperbase.dao.equipment.EquipmentMapMapper;
import com.hvisions.hiperbase.entity.equipment.HvBmEquipment;
import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentMapDetail;
import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentMapHeader;
import com.hvisions.hiperbase.equipment.map.*;
import com.hvisions.hiperbase.repository.equipment.EquipmentMapDetailRepository;
import com.hvisions.hiperbase.repository.equipment.EquipmentMapHeaderRepository;
import com.hvisions.hiperbase.repository.equipment.EquipmentRepository;
import com.hvisions.hiperbase.service.equipment.EquipmentMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>Title: EquipmentMapServiceImpl</p >
 * <p>Description: </p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/6/23</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Service
public class EquipmentMapServiceImpl implements EquipmentMapService {

    private final EquipmentMapHeaderRepository headerRepository;

    private final EquipmentMapDetailRepository detailRepository;

    private final EquipmentMapMapper equipmentMapMapper;

    private final EquipmentRepository equipmentRepository;

    private final ObjectMapper objectMapper;

    @Autowired
    public EquipmentMapServiceImpl(EquipmentMapHeaderRepository headerRepository,
                                   EquipmentMapDetailRepository detailRepository,
                                   EquipmentMapMapper equipmentMapMapper,
                                   EquipmentRepository equipmentRepository, ObjectMapper objectMapper) {
        this.headerRepository = headerRepository;
        this.detailRepository = detailRepository;
        this.equipmentMapMapper = equipmentMapMapper;
        this.equipmentRepository = equipmentRepository;
        this.objectMapper = objectMapper;
    }

    /**
     * 根据id删除对应的头表数据
     *
     * @param id 头表id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteHeader(Integer id) {
        headerRepository.deleteById(id);
        //删除详情表数据
        detailRepository.deleteAllByHeaderId(id);
    }

    /**
     * 根据关键字查询符合的头表数据
     *
     * @param query 查询对象
     * @return 头表数据列表
     */
    @Override
    public List<EquipmentMapHeaderDto> findAllHeaderByQuery(EquipmentHeaderQuery query) {
        return equipmentMapMapper.findAllHeaderByQuery(query);
    }

    /**
     * 根据地图头表id查询地图详情数据
     *
     * @param headerId 头表id
     * @return 详情数据
     */
    @Override
    public EquipmentMapDto findDetailByHeaderId(Integer headerId) {
        return equipmentMapMapper.findByHeaderId(headerId);
    }

    /**
     * 保存头表数据
     *
     * @param equipmentMapDto 地图详情数据
     * @param forceUpdate     是否强制更新
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(EquipmentMapDto equipmentMapDto, Boolean forceUpdate) throws JsonProcessingException {
        //校验设备数据
        validateEquipments(equipmentMapDto.getEquipmentMapDetailDtos());
        Integer headerId = equipmentMapDto.getId();
        HvBmEquipmentMapHeader bmEquipmentMapHeader = DtoMapper.convert(equipmentMapDto, HvBmEquipmentMapHeader.class);
        //id为空,新增数据
        if (headerId == null) {
            headerId = headerRepository.saveAndFlush(bmEquipmentMapHeader).getId();
        }
        List<HvBmEquipmentMapDetail> bmEquipmentMapDetails;
        //不是强制更新,检查数据合法性,如果错误信息不为空,需要确认是否强制执行,或者校验通过在保存
        if (!forceUpdate) {
            //校验设备是否挂载到其他地图
            List<EquipmentCheckMessageDto> checkMsgs = validateEquipmentDetail(equipmentMapDto);
            //如果一个或多个设备在其他地图挂载,这需要返回确认是否强制执行
            if (!CollectionUtils.isEmpty(checkMsgs)) {
                throw new BaseKnownException(9999, objectMapper.writeValueAsString(checkMsgs));
            }
            //校验通过,直接保存新的数据
            bmEquipmentMapDetails = DtoMapper.convertList(equipmentMapDto.getEquipmentMapDetailDtos(), HvBmEquipmentMapDetail.class);
            Integer id = headerId;
            bmEquipmentMapDetails.forEach(c -> c.setHeaderId(id));
        } else {
            //强制执行,已挂载地图设备会迁移至新的地图
            bmEquipmentMapDetails = forceUpdateMapDetail(headerId, equipmentMapDto.getEquipmentMapDetailDtos());
        }
        //页面没有单独的删除功能,所以删除原有的地图详情数据重新保存
        detailRepository.deleteAllByHeaderId(headerId);
        detailRepository.flush();
        //保存头表数据
        headerRepository.saveAndFlush(bmEquipmentMapHeader);
        //重现保存新的地图详情数据
        detailRepository.saveAll(bmEquipmentMapDetails);
    }

    /**
     * 校验设备是否挂载到其他地图,并返回这些设备的名称
     *
     * @param equipmentMapDto 设备地图数据
     * @return 设备名称如果是多个, 用, 隔开
     */
    private List<EquipmentCheckMessageDto> validateEquipmentDetail(EquipmentMapDto equipmentMapDto) {
        List<EquipmentMapDetailDto> equipmentMapDetailDtos = equipmentMapDto.getEquipmentMapDetailDtos();
        if (CollectionUtil.isNotEmpty(equipmentMapDetailDtos)) {
            List<EquipmentCheckMessageDto> errorMessages = new ArrayList<>();
            equipmentMapDetailDtos.forEach(e -> {
                HvBmEquipmentMapDetail bmEquipmentMapDetail = detailRepository.findByEquipmentId(e.getEquipmentId());
                //设备已在其他地图挂载
                if (ObjectUtil.isNotNull(bmEquipmentMapDetail) && !bmEquipmentMapDetail.getHeaderId().equals(equipmentMapDto.getId())) {
                    //根据设备id查询设备名称和挂载到其他地图的名称
                    equipmentRepository.findById(e.getEquipmentId())
                            .ifPresent(c -> headerRepository.findById(bmEquipmentMapDetail.getHeaderId())
                                    .ifPresent(
                                            d->{
                                                EquipmentCheckMessageDto checkMessageDto = new EquipmentCheckMessageDto();
                                                checkMessageDto.setEquipmentName(c.getEquipmentName());
                                                checkMessageDto.setMapName(d.getMapName());
                                                checkMessageDto.setEquipmentCode(c.getEquipmentCode());
                                                errorMessages.add(checkMessageDto);
                                            }
                                    ));
                }
            });
            return errorMessages;
        }
        return null;
    }

    /**
     * 获取修正之后的数据
     *
     * @param headerId               头表id
     * @param equipmentMapDetailDtos 设备地图详情数据
     * @return 经过矫正的详情数据, 不存在的新增, 存在的修正为现在的
     */
    public List<HvBmEquipmentMapDetail> forceUpdateMapDetail(Integer headerId, List<EquipmentMapDetailDto> equipmentMapDetailDtos) {
        //如果是强制更新，则会覆盖之前的数据
        List<HvBmEquipmentMapDetail> bmEquipmentMapDetails = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(equipmentMapDetailDtos)) {
            equipmentMapDetailDtos.forEach(e -> {
                HvBmEquipmentMapDetail bmEquipmentMapDetail = detailRepository.findByEquipmentId(e.getEquipmentId());
                if (ObjectUtil.isNull(bmEquipmentMapDetail)) {
                    //不存在,新增数据
                    bmEquipmentMapDetail = new HvBmEquipmentMapDetail();
                }
                //存在直接修改数据
                bmEquipmentMapDetail.setXAxis(e.getXAxis());
                bmEquipmentMapDetail.setYAxis(e.getYAxis());
                bmEquipmentMapDetail.setHeaderId(headerId);
                bmEquipmentMapDetails.add(bmEquipmentMapDetail);
            });
        }
        return bmEquipmentMapDetails;
    }

    /**
     * 查询所有地图数据
     *
     * @return 地图数据列表
     */
    @Override
    public List<EquipmentMapDto> findAll() {
        List<EquipmentMapDto> equipmentMapDtos = new ArrayList<>();
        List<HvBmEquipmentMapHeader> equipmentMapHeaders = headerRepository.findAll();
        if (CollectionUtil.isNotEmpty(equipmentMapHeaders)) {
            for (HvBmEquipmentMapHeader equipmentMapHeader : equipmentMapHeaders) {
                //根据头表id查询详情数据
                EquipmentMapDto equipmentMapDto = equipmentMapMapper.findByHeaderId(equipmentMapHeader.getId());
                equipmentMapDtos.add(equipmentMapDto);
            }
        }
        return equipmentMapDtos;
    }

    @Override
    public EquipmentLocationDto findByEquipmentId(Integer equipmentId) {
        EquipmentLocationDto equipmentLocationDto = null;
        HvBmEquipmentMapDetail mapDetail = detailRepository.findByEquipmentId(equipmentId);
        if (ObjectUtil.isNotNull(mapDetail)) {
            equipmentLocationDto = DtoMapper.convert(mapDetail, EquipmentLocationDto.class);
            //查询地图头表数据
            Optional<HvBmEquipmentMapHeader> equipmentMapHeader = headerRepository.findById(mapDetail.getHeaderId());
            if (equipmentMapHeader.isPresent()) {
                //设置地图文件id
                equipmentLocationDto.setMapFileId(equipmentMapHeader.get().getMapFileId());
            }
        }
        return equipmentLocationDto;
    }

    /**
     * 校验设备是否重复
     *
     * @param equipmentMapDetailDtos 设备数据
     */
    private void validateEquipments(List<EquipmentMapDetailDto> equipmentMapDetailDtos) {
        if (!CollectionUtils.isEmpty(equipmentMapDetailDtos)) {
            Map<Integer, Long> equipmentCounts = equipmentMapDetailDtos.stream()
                    .collect(Collectors.groupingBy(EquipmentMapDetailDto::getEquipmentId, Collectors.counting()));
            List<Integer> repeatIds = new ArrayList<>();
            //查询重复的设备id
            equipmentCounts.forEach((equipmentId, equipmentCount) -> {
                if (equipmentCount > 1) {
                    repeatIds.add(equipmentId);
                }
            });
            //重复的设备查询设备信息,返回给前端
            if (!CollectionUtils.isEmpty(repeatIds)) {
                List<String> equipmentNames = equipmentRepository.findAllById(repeatIds).stream()
                        .map(HvBmEquipment::getEquipmentName)
                        .collect(Collectors.toList());
                String repeatEquipmentNames = String.join(",", equipmentNames);
                String errorMessage = String.format("设备名称不能重复，重复的设备名称为：[%s]", repeatEquipmentNames);
                throw new BaseKnownException(1000, errorMessage);
            }
        }
    }
}
