package com.hvisions.hiperbase.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.EasyExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.entity.transportTool.HvBmTransportTool;
import com.hvisions.hiperbase.entity.transportTool.HvBmTransportToolType;
import com.hvisions.hiperbase.equipment.RepairAreaQueryDTO;
import com.hvisions.hiperbase.service.transportTool.TransportToolService;
import com.hvisions.hiperbase.service.transportTool.TransportToolTypeService;
import com.hvisions.hiperbase.transportTool.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>Title: TransportToolController</p>
 * <p>Description: 运输工具主数据控制器</p>
 */
@RestController
@RequestMapping("/transportTool")
@Api(tags = "运输工具管理")
public class TransportToolController {

    @Autowired
    private TransportToolService transportToolService;
    @Autowired
    private TransportToolTypeService transportToolTypeService;

    @GetMapping("getTransportToolList")
    @ApiOperation("获取运输工具列表")
    public List<TransportToolListDTO> getTransportToolList() {
        return transportToolService.getTransportToolList();
    }

    @PostMapping("getTransportToolPage")
    @ApiOperation("获取运输工具分页模糊查询")
    public Page<TransportToolDTO> getTransportToolPage(@RequestBody TransportToolQueryDTO transportToolQueryDTO) {
        return transportToolService.getPage(transportToolQueryDTO);
    }

    @PostMapping("getTransportToolStatusPage")
    @ApiOperation("获取运输工具状态分页模糊查询")
    public Page<TransportToolStatusDTO> getTransportToolStatusPage(@RequestBody TransportToolStatusQueryDTO queryDTO) {
        return transportToolService.getStatusPage(queryDTO);
    }

    @GetMapping("getTransportToolById/{id}")
    @ApiOperation("根据运输工具编号获取运输工具")
    public TransportToolDTO getTransportToolById(@PathVariable @ApiParam("运输工具编号") String id) {
        HvBmTransportTool one = transportToolService.getOne(new LambdaQueryWrapper<HvBmTransportTool>().eq(HvBmTransportTool::getTransportToolCode, id));
        return DtoMapper.convert(one, TransportToolDTO.class);
    }

    @PostMapping("createTransportTool")
    @ApiOperation("添加运输工具")
    public ResultVO createTransportTool(@RequestBody @ApiParam("运输工具信息") TransportToolDTO transportToolDTO, @UserInfo @ApiIgnore UserInfoDTO userInfo) {
        if (transportToolService.checkHvBmTransportToolCodesIsUnique(transportToolDTO.getTransportToolCode())) {
            return ResultVO.error(500, "运输工具编号" + transportToolDTO.getTransportToolCode() + "已存在");
        }
        if (!transportToolTypeService.checkHvBmTransportToolTypeCodesIsUnique(transportToolDTO.getTransportToolTypeCode())) {
            return ResultVO.error(500, "运输工具类型编号" + transportToolDTO.getTransportToolTypeCode() + "不存在");
        }
        transportToolDTO.setCreateTime(LocalDateTime.now());
        transportToolDTO.setCreatorId(userInfo.getUserName());
        transportToolDTO.setUpdateTime(LocalDateTime.now());
        transportToolDTO.setUpdaterId(userInfo.getUserName());
        return ResultVO.success(transportToolService.save(DtoMapper.convert(transportToolDTO,HvBmTransportTool.class)));
    }

    @PutMapping("updateTransportTool")
    @ApiOperation("更新运输工具")
    public ResultVO updateTransportTool(@RequestBody @ApiParam("运输工具信息") TransportToolDTO transportToolDTO, @UserInfo @ApiIgnore UserInfoDTO userInfo) {
        if (!transportToolService.checkHvBmTransportToolCodesIsUnique(transportToolDTO.getTransportToolCode())) {
            return ResultVO.error(500, "运输工具编号" + transportToolDTO.getTransportToolCode() + "不存在");
        }
        if (!transportToolTypeService.checkHvBmTransportToolTypeCodesIsUnique(transportToolDTO.getTransportToolTypeCode())) {
            return ResultVO.error(500, "运输工具类型编号" + transportToolDTO.getTransportToolTypeCode() + "不存在");
        }
        transportToolDTO.setUpdateTime(LocalDateTime.now());
        transportToolDTO.setUpdaterId(userInfo.getUserName());
        return ResultVO.success(transportToolService.update(DtoMapper.convert(transportToolDTO,HvBmTransportTool.class), new LambdaQueryWrapper<HvBmTransportTool>().eq(HvBmTransportTool::getTransportToolCode, transportToolDTO.getTransportToolCode())));
    }

    @DeleteMapping("deleteTransportTool/{id}")
    @ApiOperation("删除运输工具")
    public ResultVO deleteTransportTool(@PathVariable @ApiParam("运输工具ID") String id) {
        if (!transportToolService.checkHvBmTransportToolCodesIsUnique(id)) {
            return ResultVO.error(500, "运输工具编号" + id + "不存在");
        }
        return ResultVO.success(transportToolService.remove(new LambdaQueryWrapper<HvBmTransportTool>().eq(HvBmTransportTool::getTransportToolCode, id)));
    }

    /**
     * 下载模版
     *
     */
    @ApiOperation(value = "下载模版")
    @PostMapping(value = "/downloadTemplate")
    @ApiResultIgnore
    public ResultVO<ExcelExportDto> downloadTemplate() {
        List<HvBmTransportTool> list = new ArrayList<>();
        return ResultVO.success(EasyExcelUtil.getExcel(list, HvBmTransportTool.class, System.currentTimeMillis() + "运输工具主数据导入模版.xlsx"));
    }

    /**
     * 导出
     *
     * @param transportToolDTO
     */
    @ApiOperation(value = "导出")
    @PostMapping(value = "/exportData")
    @ApiResultIgnore
    public ResultVO<ExcelExportDto> exportData(@RequestBody TransportToolDTO transportToolDTO) {
        List<HvBmTransportTool> list =  transportToolService.findListByCondition(DtoMapper.convert(transportToolDTO,HvBmTransportTool.class));
        return ResultVO.success(EasyExcelUtil.getExcel(list, HvBmTransportTool.class, System.currentTimeMillis() + "运输工具主数据.xlsx"));
    }

    /**
     * 导出运输工具状态
     *
     * @param transportToolStatusQueryDTO
     */
    @ApiOperation(value = "导出状态")
    @PostMapping(value = "/exportStatusData")
    @ApiResultIgnore
    public ResultVO<ExcelExportDto> exportData(@RequestBody TransportToolStatusQueryDTO transportToolStatusQueryDTO) {
        return ResultVO.success(EasyExcelUtil.getExcel(transportToolService.findStatusListByCondition(transportToolStatusQueryDTO),
                TransportToolStatusDTO.class, System.currentTimeMillis() + "运输工具状态数据.xlsx"));
    }

    /**
     * 导入
     *
     * @param file 文件
     */
    @PostMapping("/import")
    @ApiOperation("导入")
    public ResultVO importData(MultipartFile file, @UserInfo @ApiIgnore UserInfoDTO userInfo) {
        return transportToolService.importData(file, userInfo);
    }




}