package com.hvisions.hiperbase.service.schedule.imp;

import cn.hutool.core.collection.CollectionUtil;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.interfaces.IConverter;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.EasyExcelUtil;
import com.hvisions.hiperbase.entity.SysBase;
import com.hvisions.hiperbase.entity.schedule.*;
import com.hvisions.hiperbase.equipment.eums.LocationTypeEnum;
import com.hvisions.hiperbase.equipment.location.LocationDTO;
import com.hvisions.hiperbase.excel.ScheduleExport;
import com.hvisions.hiperbase.repository.schedule.*;
import com.hvisions.hiperbase.schedule.dto.*;
import com.hvisions.hiperbase.schedule.enums.ScheduleExceptionEnum;
import com.hvisions.hiperbase.service.equipment.LocationService;
import com.hvisions.hiperbase.service.schedule.CrewService;
import com.hvisions.hiperbase.service.schedule.ScheduleService;
import com.hvisions.hiperbase.service.schedule.ShiftService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title: ScheduleServiceImp</p>
 * <p>Description: 排产信息服务实现</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Slf4j
@Service
public class ScheduleServiceImpl implements ScheduleService {

    @Value("${spring.jackson.time-zone}")
    String timeZone;

    private final ScheduleRepository scheduleRepository;
    private final CrewRepository crewRepository;
    private final ShiftRepository shiftRepository;
    private final HolidayRepository holidayRepository;
    private final ScheduleBreakTimeRepository scheduleBreakTimeRepository;
    private final BreakRepository breakRepository;
    private final LocationService locationService;
    private final CrewService crewService;
    private final ShiftService shiftService;

    @Autowired
    public ScheduleServiceImpl(ScheduleRepository scheduleRepository, CrewRepository crewRepository,
                               ShiftRepository shiftRepository,
                               HolidayRepository holidayRepository, ScheduleBreakTimeRepository scheduleBreakTimeRepository, BreakRepository breakRepository, LocationService locationService, CrewService crewService, ShiftService shiftService) {
        this.scheduleRepository = scheduleRepository;
        this.crewRepository = crewRepository;
        this.shiftRepository = shiftRepository;
        this.holidayRepository = holidayRepository;
        this.scheduleBreakTimeRepository = scheduleBreakTimeRepository;
        this.breakRepository = breakRepository;
        this.locationService = locationService;
        this.crewService = crewService;
        this.shiftService = shiftService;
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public ScheduleWithInfoDTO getScheduleById(int id) {
        //获取排班信息
        HvBmSchedule hvBmSchedule = scheduleRepository.getOne(id);
        //转换类型
        ScheduleWithInfoDTO scheduleWithInfoDTO = DtoMapper.convert(hvBmSchedule, ScheduleWithInfoDTO.class);
        //添加班组名称
        scheduleWithInfoDTO.setCrewName(crewRepository.getOne(scheduleWithInfoDTO.getCrewId()).getCrewName());
        //添加班次名称
        scheduleWithInfoDTO.setShiftName(shiftRepository.getOne(scheduleWithInfoDTO.getShiftId()).getShiftName());
        return scheduleWithInfoDTO;
    }


    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ScheduleDailyDTO createOrUpdateScheduleDay(ScheduleDailyDTO scheduleDailyInfoDTO) {
        if (CollectionUtil.isEmpty(scheduleDailyInfoDTO.getCrewShiftDtoS())) {
            scheduleDailyInfoDTO.setCrewShiftDtoS(Collections.emptyList());
        }
        List<Integer> dailyCrewId =
                scheduleDailyInfoDTO.getCrewShiftDtoS().stream().map(CrewShiftDTO::getCrewId).collect(Collectors.toList());
        List<Integer> shiftIds =
                scheduleDailyInfoDTO.getCrewShiftDtoS().stream().map(CrewShiftDTO::getShiftId).collect(Collectors.toList());
        validScheduleParam(dailyCrewId, shiftIds, scheduleDailyInfoDTO.getAreaId(), scheduleDailyInfoDTO.getCellId());
        List<CrewShiftDTO> crewShiftDtoS = scheduleDailyInfoDTO.getCrewShiftDtoS();
        if (crewShiftDtoS == null || crewShiftDtoS.size() == 0) {
            scheduleDailyInfoDTO.setCrewShiftDtoS(new ArrayList<>());
        }
        List<HvBmShift> shifts = shiftRepository.findAllById(scheduleDailyInfoDTO.getCrewShiftDtoS().stream()
                .map(CrewShiftDTO::getShiftId)
                .collect(Collectors.toList()));
        //清除当天的排班计划
        deleteScheduleInfoAndScheduleBreakTime(scheduleDailyInfoDTO.getAreaId(),
                scheduleDailyInfoDTO.getCellId(),
                scheduleDailyInfoDTO.getData(),
                scheduleDailyInfoDTO.getData());
        //清除当天的假期设置
        List<HvBmHoliday> holidays = holidayRepository.findAllByAreaIdAndCellIdAndHoliday(scheduleDailyInfoDTO.getAreaId(),
                scheduleDailyInfoDTO.getCellId(),
                scheduleDailyInfoDTO.getData());
        holidayRepository.deleteAll(holidays);
        holidayRepository.flush();
        //实际休息时间信息
        List<HvBmScheduleBreakTime> scheduleBreakTimes = new ArrayList<>();
        //如果是假期,创建假期信息
        if (scheduleDailyInfoDTO.getIsHoliday()) {
            HvBmHoliday holiday = new HvBmHoliday(scheduleDailyInfoDTO.getData());
            holiday.setAreaId(scheduleDailyInfoDTO.getAreaId());
            holiday.setCellId(scheduleDailyInfoDTO.getCellId());
            holidayRepository.save(holiday);
            scheduleDailyInfoDTO.setCrewShiftDtoS(new ArrayList<>());
        }
        //如果不是假期,创建排班信息
        else {
            //新增排班计划
            for (CrewShiftDTO dto : scheduleDailyInfoDTO.getCrewShiftDtoS()) {
                HvBmShift shift = shifts.stream().filter(t -> t.getId().equals(dto.getShiftId()))
                        .findFirst()
                        .orElseThrow(() -> new BaseKnownException(10000, "班次信息查询错误，请检查班次id信息"));
                HvBmSchedule schedule = new HvBmSchedule();
                schedule.setCellId(scheduleDailyInfoDTO.getCellId());
                schedule.setAreaId(scheduleDailyInfoDTO.getAreaId());
                schedule.setCrewId(dto.getCrewId());
                schedule.setShiftId(dto.getShiftId());
                //支持手动调整，如果没有手动录入的值，那么设置未班次默认的值
                if (dto.getStartTime() == null) {
                    schedule.setStartTime(shift.getStartTime().atDate(scheduleDailyInfoDTO.getData()));
                } else {
                    schedule.setStartTime(dto.getStartTime().atDate(scheduleDailyInfoDTO.getData()));
                }
                if (dto.getEndTime() == null) {
                    schedule.setEndTime(shift.getEndTime().atDate(scheduleDailyInfoDTO.getData()));
                } else {
                    schedule.setEndTime(dto.getEndTime().atDate(scheduleDailyInfoDTO.getData()));
                }
                //如果结束时间晚于开始时间，说明跨天了。加1天
                if (schedule.getEndTime().isBefore(schedule.getStartTime())) {
                    schedule.setEndTime(schedule.getEndTime().plusDays(1));
                }
                schedule.setPlanQuantity(dto.getPlanQuantity());
                schedule.setScheduleDate(scheduleDailyInfoDTO.getData());
                scheduleRepository.save(schedule);
                dto.setId(schedule.getId());
                //创建休息时间
                if (dto.getBreakTimes() != null && dto.getBreakTimes().size() > 0) {
                    List<HvBmScheduleBreakTime> entitys = dto.getBreakTimes().stream()
                            .map(t -> {
                                HvBmBreakTime breakTime = new HvBmBreakTime();
                                breakTime.setBeginTime(t.getStartTime());
                                breakTime.setEndTime(t.getEndTime());
                                breakTime.setDescription(t.getDescription());
                                return getBreakByScheduleAndBreakTime(schedule, breakTime);
                            })
                            .collect(Collectors.toList());
                    scheduleBreakTimes.addAll(entitys);
                }
            }
            //保存所有的休息时间
            scheduleBreakTimeRepository.saveAll(scheduleBreakTimes);
        }

        return scheduleDailyInfoDTO;
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public ShiftInfoRelatedDTO getShiftReaTime(LocalDateTime time, int areaId, int cellId) {
        //返回信息存放DTO
        ShiftInfoRelatedDTO result = new ShiftInfoRelatedDTO();
        //获取当前的排班信息
        List<HvBmSchedule> schedules = scheduleRepository.findByTime(time, areaId, cellId);
        List<ShiftInfoDTO> infos = mapToInfo(schedules);
        if (infos.size() > 0) {
            result.setShiftInfoDTO(infos.get(0));
        }
        //获取前五天的排班信息
        List<HvBmSchedule> before = scheduleRepository.findByTimes(time.plusDays(-5), time, areaId, cellId);
        List<ShiftInfoDTO> infosBefore = mapToInfo(before);
        infosBefore.sort(Comparator.comparing(ShiftInfoDTO::getStartTime));
        result.setBefore(infosBefore);
        //获取后五天的排班信息
        List<HvBmSchedule> after = scheduleRepository.findByTimes(time, time.plusDays(5), areaId, cellId);
        List<ShiftInfoDTO> infosAfter = mapToInfo(after);
        infosAfter.sort(Comparator.comparing(ShiftInfoDTO::getStartTime));
        result.setAfter(infosAfter);
        return result;
    }

    /**
     * 填充数据
     *
     * @param schedules 实体
     * @return 排班信息
     */
    private List<ShiftInfoDTO> mapToInfo(List<HvBmSchedule> schedules) {
        if (CollectionUtil.isEmpty(schedules)) {
            return CollectionUtil.newArrayList();
        }
        List<HvBmShift> shifts = shiftRepository.findAllById(schedules.stream()
                .map(HvBmSchedule::getShiftId).collect(Collectors.toList()));
        List<HvBmCrew> crews = crewRepository.findAllById(schedules.stream()
                .map(HvBmSchedule::getCrewId).collect(Collectors.toList()));
        List<ShiftInfoDTO> result = DtoMapper.convertList(schedules, new IConverter<HvBmSchedule, ShiftInfoDTO>() {
            @Override
            public ShiftInfoDTO convert(HvBmSchedule hvBmSchedule) {
                ShiftInfoDTO dto = DtoMapper.convert(hvBmSchedule, ShiftInfoDTO.class);
                dto.setScheduleDay(hvBmSchedule.getScheduleDate());
                return dto;
            }
        });

        for (ShiftInfoDTO shiftInfoDTO : result) {
            Optional<HvBmCrew> crew = crews.stream()
                    .filter(t -> t.getId().equals(shiftInfoDTO.getCrewId()))
                    .findFirst();
            Optional<HvBmShift> shift = shifts.stream()
                    .filter(t -> t.getId().equals(shiftInfoDTO.getShiftId()))
                    .findFirst();
            shiftInfoDTO.setCrewName(crew.map(HvBmCrew::getCrewName).orElse(""));
            shiftInfoDTO.setCrewCode(crew.map(HvBmCrew::getCrewCode).orElse(""));
            shiftInfoDTO.setShiftCode(shift.map(HvBmShift::getShiftCode).orElse(""));
            shiftInfoDTO.setShiftName(shift.map(HvBmShift::getShiftName).orElse(""));
        }
        return result;
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public List<ScheduleDailyInfoDTO> getScheduleListByAreaIdAndCellIdAndTime(int areaId, int cellId, LocalDate beginTime, LocalDate endTime) {
        //根据时间查询所有的排班信息
        List<HvBmSchedule> scheduleInfos = scheduleRepository.getAllByAreaIdAndCellIdAndScheduleDateBetween(areaId, cellId, beginTime, endTime);
        //根据时间查询所有的休息时间数据
        List<HvBmScheduleBreakTime> breakTimes = scheduleBreakTimeRepository.findAllByScheduleIdIn(scheduleInfos
                .stream()
                .map(SysBase::getId)
                .collect(Collectors.toList()));
        //根据时间查询所有的假期信息
        List<HvBmHoliday> hvBmHolidays = holidayRepository.findDistinctByCellIdAndAreaIdAndHolidayBetween(cellId, areaId, beginTime, endTime)
                .stream().distinct().collect(Collectors.toList());
        //获取车间班组信息
        List<HvBmCrew> crews = crewRepository.findAllByAreaIdAndCellId(areaId, cellId);
        //获取车间班次信息
        List<HvBmShift> shifts = shiftRepository.findAllByAreaIdAndCellId(areaId, cellId);
        //创建返回结果
        List<ScheduleDailyInfoDTO> result = new ArrayList<>();
        //按照时间分组
        Map<LocalDate, List<HvBmSchedule>> scheduleGroup =
                scheduleInfos.stream().collect(Collectors.groupingBy(HvBmSchedule::getScheduleDate));
        //遍历，创建排班信息
        for (LocalDate date : scheduleGroup.keySet()) {
            ScheduleDailyInfoDTO scheduleDailyInfoDTO = new ScheduleDailyInfoDTO();
            //设置时间
            scheduleDailyInfoDTO.setScheduleDate(date);
            //设置是否为假期
            if (hvBmHolidays.stream().anyMatch(t -> t.getHoliday().equals(date))) {
                scheduleDailyInfoDTO.setIsHoliday(true);
            }
            //设置排班信息
            List<HvBmSchedule> schedules = scheduleGroup.get(date);
            List<CrewShiftWithNameDTO> crewShiftWithNameDTOS = DtoMapper.convertList(schedules,
                    CrewShiftWithNameDTO.class);
            //添加排班的班次，班组名称,编码
            for (CrewShiftWithNameDTO crewShiftWithNameDTO : crewShiftWithNameDTOS) {
                Optional<HvBmCrew> crew = crews.stream().filter(t -> t.getId().equals(crewShiftWithNameDTO.getCrewId())).findFirst();
                crew.ifPresent(crewEntity -> {
                    crewShiftWithNameDTO.setCrewName(crewEntity.getCrewName());
                    crewShiftWithNameDTO.setCrewCode(crewEntity.getCrewCode());
                });
                Optional<HvBmShift> shift = shifts.stream().filter(t -> t.getId().equals(crewShiftWithNameDTO.getShiftId())).findFirst();
                shift.ifPresent(shiftEntity -> {
                    crewShiftWithNameDTO.setShiftName(shiftEntity.getShiftName());
                    crewShiftWithNameDTO.setShiftCode(shiftEntity.getShiftCode());
                });
                //找到对应的休息数据
                List<ActualBreakDTO> collect = breakTimes
                        .stream()
                        .filter(t -> t.getScheduleId().equals(crewShiftWithNameDTO.getId()))
                        .map(t -> {
                            ActualBreakDTO actualBreakDTO = new ActualBreakDTO();
                            actualBreakDTO.setDescription(t.getDescription());
                            actualBreakDTO.setEndTime(t.getEndTime().toLocalTime());
                            actualBreakDTO.setStartTime(t.getStartTime().toLocalTime());
                            return actualBreakDTO;
                        })
                        .collect(Collectors.toList());
                crewShiftWithNameDTO.setBreakDTOList(collect);
            }
            scheduleDailyInfoDTO.setCrewShift(crewShiftWithNameDTOS);
            //对当天内的班次进行排序
            scheduleDailyInfoDTO.crewShiftSort();
            result.add(scheduleDailyInfoDTO);
        }
        //如果有些假期没有排班。也要返回给前端
        hvBmHolidays.stream()
                .filter(t -> result.stream().noneMatch(j -> j.getScheduleDate().equals(t.getHoliday())))
                .map(HvBmHoliday::getHoliday)
                .forEach(t -> {
                    ScheduleDailyInfoDTO scheduleDailyInfoDTO = new ScheduleDailyInfoDTO();
                    scheduleDailyInfoDTO.setScheduleDate(t);
                    scheduleDailyInfoDTO.setIsHoliday(true);
                    result.add(scheduleDailyInfoDTO);
                });
        //按照时间排序
        return result.stream().sorted(Comparator.comparing(ScheduleDailyInfoDTO::getScheduleDate))
                .collect(Collectors.toList());
    }

    /**
     * 用来判断是否有时区问题的方法
     *
     * @return 展示数据
     */
    @Override
    public Map<String, Object> showDate() {
        Map<String, Object> map = new HashMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        map.put("3 服务器中创建出来的时间new Date()", sdf.format(new Date()));

        map.put("4 当前时区的配置", timeZone);
        return map;
    }

    /**
     * 获取开始时间，收工时间"
     *
     * @param query 日期
     * @return 开始结束时间
     */
    @Override
    public WorkTime getWorkTime(WorkTimeQuery query) {
        List<HvBmSchedule> schedules =
                scheduleRepository.findAllByCellIdAndAreaIdAndScheduleDate(query.getCellId(), query.getAreaId(), query.getDate());
        if (schedules.size() == 0) {
            return null;
        }
        WorkTime result = new WorkTime();
        LocalDateTime minStartTime = null;
        LocalDateTime maxEndTime = null;
        for (HvBmSchedule schedule : schedules) {
            if (minStartTime == null) {
                minStartTime = schedule.getStartTime();
            }
            if (maxEndTime == null) {
                maxEndTime = schedule.getEndTime();
            }
            if (minStartTime.isAfter(schedule.getStartTime())) {
                minStartTime = schedule.getStartTime();
            }
            if (maxEndTime.isBefore(schedule.getEndTime())) {
                maxEndTime = schedule.getEndTime();
            }
        }
        result.setBeginTime(minStartTime);
        result.setEndTime(maxEndTime);
        result.setDate(query.getDate());
        return result;
    }

    /**
     * 根据时间查询班次信息，以及上五次和下五次的班次信息
     *
     * @param time   时间
     * @param areaId 车间
     * @param cellId 产线
     * @return 班次信息
     */
    @Override
    public ShiftInfoWithBreakTimeDTO getCurrentSchedule(LocalDateTime time, Integer areaId, Integer cellId) {
        if (time == null) {
            time = LocalDateTime.now();
        }
        //获取当前的排班信息
        List<HvBmSchedule> schedules = scheduleRepository.findByTime(time, areaId, cellId);
        List<ShiftInfoDTO> infos = mapToInfo(schedules);
        if (infos == null || infos.size() <= 0) {
            return null;
        }
        ShiftInfoDTO shiftInfoDTO = infos.get(0);
        ShiftInfoWithBreakTimeDTO result = DtoMapper.convert(shiftInfoDTO, ShiftInfoWithBreakTimeDTO.class);
        //获取排班中的休息时间
        List<HvBmScheduleBreakTime> breakTimes = scheduleBreakTimeRepository.findAllByScheduleId(result.getId());
        if (breakTimes != null && breakTimes.size() > 0) {
            result.setBreakTimes(DtoMapper.convertList(breakTimes, ScheduleBreakTimeDTO.class));
        }
        return result;
    }


    /**
     * 导出排班信息
     *
     * @param areaId    车间
     * @param cellId    产线
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 排班计划信息
     */
    @Override
    public ExcelExportDto exportScheduleForFront(Integer areaId, Integer cellId, LocalDate startTime, LocalDate endTime) {
        String areaName;
        if (0 == areaId) {
            areaName = "通用车间";
        } else {
            LocationDTO location = locationService.findLocationExtendById(areaId);

            if (location == null) {
                throw new BaseKnownException(10000, "获取车间信息错误，找不到对应的车间信息:" + areaId);
            }
            if (location.getType() != 30) {
                throw new BaseKnownException(10000, "获取车间信息错误，传递参数异常:不是车间。id为：" + areaId);
            }
            areaName = location.getCode();
        }
        String cellName;
        if (0 == cellId) {
            cellName = "通用产线";
        } else {
            LocationDTO location = locationService.findLocationExtendById(cellId);

            if (location == null) {
                throw new BaseKnownException(10000, "获取产线信息错误，找不到对应的车间信息:" + cellId);
            }
            if (location.getType() != 40) {
                throw new BaseKnownException(10000, "获取产线信息错误，传递参数异常:不是产线。id为：" + cellId);
            }
            cellName = location.getCode();
        }
        List<ScheduleDailyInfoDTO> schedule = getScheduleListByAreaIdAndCellIdAndTime(areaId, cellId, startTime, endTime);
        String fileName = "排班信息.xlsx";
        List<ScheduleExport> scheduleInfo = new ArrayList<>();
        for (ScheduleDailyInfoDTO infoDTO : schedule) {
            for (CrewShiftWithNameDTO shift : infoDTO.getCrewShift()) {
                ScheduleExport export = new ScheduleExport();
                export.setAreaCode(areaName);
                export.setPlanQuantity(shift.getPlanQuantity());
                export.setScheduleDate(infoDTO.getScheduleDate());
                export.setShiftCode(shift.getShiftCode());
                export.setShiftEndTime(shift.getEndTime().toLocalTime());
                export.setShiftStartTime(shift.getStartTime().toLocalTime());
                export.setCrewCode(shift.getCrewCode());
                export.setCellCode(cellName);
                List<ActualBreakDTO> breaks = shift.getBreakDTOList().stream()
                        .filter(Objects::nonNull).collect(Collectors.toList());
                if (breaks.size() > 5) {
                    export.setBreakDescription6(breaks.get(5).getDescription());
                    export.setBreakEndTime6(breaks.get(5).getEndTime());
                    export.setBreakStartTime6(breaks.get(5).getStartTime());
                }
                if (breaks.size() > 4) {
                    export.setBreakDescription5(breaks.get(4).getDescription());
                    export.setBreakEndTime5(breaks.get(4).getEndTime());
                    export.setBreakStartTime5(breaks.get(4).getStartTime());
                }
                if (breaks.size() > 3) {
                    export.setBreakDescription4(breaks.get(3).getDescription());
                    export.setBreakEndTime4(breaks.get(3).getEndTime());
                    export.setBreakStartTime4(breaks.get(3).getStartTime());
                }
                if (breaks.size() > 2) {
                    export.setBreakDescription3(breaks.get(2).getDescription());
                    export.setBreakEndTime3(breaks.get(2).getEndTime());
                    export.setBreakStartTime3(breaks.get(2).getStartTime());
                }
                if (breaks.size() > 1) {
                    export.setBreakDescription2(breaks.get(1).getDescription());
                    export.setBreakEndTime2(breaks.get(1).getEndTime());
                    export.setBreakStartTime2(breaks.get(1).getStartTime());
                }
                if (breaks.size() > 0) {
                    export.setBreakDescription1(breaks.get(0).getDescription());
                    export.setBreakEndTime1(breaks.get(0).getEndTime());
                    export.setBreakStartTime1(breaks.get(0).getStartTime());
                }
                scheduleInfo.add(export);
            }
        }
        return EasyExcelUtil.getExcel(scheduleInfo, ScheduleExport.class, fileName);
    }

    @Override
    public ExcelExportDto scheduleExportTemplate() {
        String fileName = "排班信息导入模板.xlsx";
        return EasyExcelUtil.getExcel(Collections.emptyList(), ScheduleExport.class, fileName);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importSchedule(MultipartFile file) {
        List<ScheduleExport> data = EasyExcelUtil.getImport(file, ScheduleExport.class);
        List<LocationDTO> allLocations = locationService.findAll();
        //需要根据日期，area，cell分组
        Map<List<Object>, List<ScheduleExport>> schedule = data.stream()
                .collect(Collectors.groupingBy(t -> Arrays.asList(t.getScheduleDate(), t.getAreaCode(), t.getCellCode())));
        for (List<Object> key : schedule.keySet()) {
            //获取车间产线信息
            String areaCode = (String) (key.get(1));
            String cellCode = (String) (key.get(2));
            Integer areaId;
            Integer cellId;
            if(areaCode.equals("通用车间")){
                areaId = 0;
            }else{
                LocationDTO area = allLocations.stream()
                        .filter(t -> t.getCode().equals(areaCode) && LocationTypeEnum.AREA.getCode().equals(t.getType()))
                        .findFirst()
                        .orElseThrow(() -> new BaseKnownException("找不到对应的车间信息，请检查车间编码：" + areaCode));
                areaId = area.getId();
            }
            if(cellCode.equals("通用产线")){
                cellId = 0;
            }else{
                LocationDTO cell = allLocations.stream()
                        .filter(t -> t.getCode().equals(cellCode) && LocationTypeEnum.CELL.getCode().equals(t.getType()))
                        .findFirst()
                        .orElseThrow(() -> new BaseKnownException("找不到对应的产线信息，请检查产线编码：" + areaCode));
                cellId = cell.getId();
            }
            List<CrewWithMemberDTO> crewInfoList = crewService.getByAreaIdAndCellId(areaId,cellId);
            List<ShiftDTO> shiftInfoList = shiftService.getShiftListByAreaIdAndCellId(areaId,cellId);
            ScheduleDailyDTO daySchedule = new ScheduleDailyDTO();
            daySchedule.setData((LocalDate) (key.get(0)));
            daySchedule.setAreaId(areaId);
            daySchedule.setCellId(cellId);
            daySchedule.setIsHoliday(false);
            List<CrewShiftDTO> crewShiftSchedule = new ArrayList<>();
            for (ScheduleExport crewShiftInfo : schedule.get(key)) {
                CrewShiftDTO dto = new CrewShiftDTO();
                dto.setPlanQuantity(crewShiftInfo.getPlanQuantity());
                dto.setStartTime(crewShiftInfo.getShiftStartTime());
                dto.setEndTime(crewShiftInfo.getShiftEndTime());
                dto.setCrewId(crewInfoList.stream().filter(t -> t.getCrewCode().equals(crewShiftInfo.getCrewCode())).findFirst()
                        .orElseThrow(() -> new BaseKnownException("班组信息异常，请检查班组编码是否正确:" + crewShiftInfo.getCellCode()))
                        .getId());
                dto.setShiftId(shiftInfoList.stream().filter(t -> t.getShiftCode().equals(crewShiftInfo.getShiftCode())).findFirst()
                        .orElseThrow(() -> new BaseKnownException("班次信息异常，请检查班次编码是否正确:" + crewShiftInfo.getShiftCode()))
                        .getId());
                List<ActualBreakDTO> actualBreakDTOS = parseBreakList(crewShiftInfo);
                dto.setBreakTimes(actualBreakDTOS);
                crewShiftSchedule.add(dto);
            }
            daySchedule.setCrewShiftDtoS(crewShiftSchedule);
            createOrUpdateScheduleDay(daySchedule);
        }

    }

    /**
     * 转换休息时间
     *
     * @param crewShiftInfo 导入的信息
     * @return 休息时间列表
     */
    private List<ActualBreakDTO> parseBreakList(ScheduleExport crewShiftInfo) {
        List<ActualBreakDTO> result = new ArrayList<>();
        if (crewShiftInfo.getBreakStartTime1() != null && crewShiftInfo.getBreakEndTime1() != null) {
            ActualBreakDTO dto = new ActualBreakDTO(crewShiftInfo.getBreakStartTime1(),
                    crewShiftInfo.getBreakEndTime1(),
                    crewShiftInfo.getBreakDescription1());
            result.add(dto);
        }
        if (crewShiftInfo.getBreakStartTime2() != null && crewShiftInfo.getBreakEndTime2() != null) {
            ActualBreakDTO dto = new ActualBreakDTO(crewShiftInfo.getBreakStartTime2(),
                    crewShiftInfo.getBreakEndTime2(),
                    crewShiftInfo.getBreakDescription2());
            result.add(dto);
        }
        if (crewShiftInfo.getBreakStartTime3() != null && crewShiftInfo.getBreakEndTime3() != null) {
            ActualBreakDTO dto = new ActualBreakDTO(crewShiftInfo.getBreakStartTime3(),
                    crewShiftInfo.getBreakEndTime3(),
                    crewShiftInfo.getBreakDescription3());
            result.add(dto);
        }
        if (crewShiftInfo.getBreakStartTime4() != null && crewShiftInfo.getBreakEndTime4() != null) {
            ActualBreakDTO dto = new ActualBreakDTO(crewShiftInfo.getBreakStartTime4(),
                    crewShiftInfo.getBreakEndTime4(),
                    crewShiftInfo.getBreakDescription4());
            result.add(dto);
        }
        if (crewShiftInfo.getBreakStartTime5() != null && crewShiftInfo.getBreakEndTime5() != null) {
            ActualBreakDTO dto = new ActualBreakDTO(crewShiftInfo.getBreakStartTime5(),
                    crewShiftInfo.getBreakEndTime5(),
                    crewShiftInfo.getBreakDescription5());
            result.add(dto);
        }
        if (crewShiftInfo.getBreakStartTime6() != null && crewShiftInfo.getBreakEndTime6() != null) {
            ActualBreakDTO dto = new ActualBreakDTO(crewShiftInfo.getBreakStartTime6(),
                    crewShiftInfo.getBreakEndTime6(),
                    crewShiftInfo.getBreakDescription6());
            result.add(dto);
        }
        return result;
    }


    /**
     * 班次班组排班验证参数
     *
     * @param crewIds 班组id列表
     * @param shifts  班次id列表
     * @param areaId  车间id
     * @param cellId  产线id
     */
    private void validScheduleParam(List<Integer> crewIds, List<Integer> shifts, Integer areaId, Integer cellId) {
        List<HvBmShift> shiftList = shiftRepository.findAllByAreaIdAndCellId(areaId, cellId);
        List<Integer> shiftIdList = shiftList.stream().map(SysBase::getId).collect(Collectors.toList());
        boolean isShift = shiftIdList.containsAll(shifts);
        if (!isShift) {
            throw new BaseKnownException(10000, "排班班次数据出错");
        }
        List<CrewWithMemberDTO> crewWithMemberDTOS = crewService.getByAreaIdAndCellId(areaId, cellId);
        List<Integer> crewIdList = crewWithMemberDTOS.stream().map(t -> t.getId()).collect(Collectors.toList());
        boolean isCrew = crewIdList.containsAll(crewIds);
        if (!isCrew) {
            throw new BaseKnownException(10000, "排班班组数据出错");
        }
    }

    /**
     * 根据开始结束时间自动排班
     *
     * @param scheduleInfo 排班信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createAutomaticScheduling(AutomaticSchedulingDTO scheduleInfo) {
        //之前的业务选中的最后一天不会进行处理，这里加了一天
        scheduleInfo.setEndTime(scheduleInfo.getEndTime().plusDays(1));
        validScheduleParam(scheduleInfo.getCrewIds(), scheduleInfo.getShiftIds(), scheduleInfo.getAreaId(),
                scheduleInfo.getCellId());
        if (CollectionUtil.isEmpty(scheduleInfo.getShiftIds())) {
            throw new BaseKnownException(10000, "班次以及产量信息不能为空");
        }
        if (CollectionUtil.isEmpty(scheduleInfo.getCrewIds())) {
            throw new BaseKnownException(10000, "班组信息不能为空");
        }
        //获取shift信息
        List<HvBmShift> shifts = shiftRepository.findAllByAreaIdAndCellId(scheduleInfo.getAreaId(), scheduleInfo.getCellId());

        //获取shift对应的休息时间信息
        List<HvBmBreakTime> breakEntities = breakRepository.findAllByShiftIdIn(scheduleInfo.getShiftIds());
        //用map存下，后面方面查询用
        Map<Integer, HvBmShift> shiftMap = new HashMap<>();
        for (HvBmShift shift : shifts) {
            shiftMap.put(shift.getId(), shift);
        }
        //获取时间段内的假期
        List<HvBmHoliday> holidays = holidayRepository.findDistinctByCellIdAndAreaIdAndHolidayBetween(scheduleInfo.getCellId(),
                scheduleInfo.getAreaId(),
                scheduleInfo.getStartTime(),
                scheduleInfo.getEndTime());
        //删除时间段内的所有排班,休息时间
        deleteScheduleInfoAndScheduleBreakTime(
                scheduleInfo.getAreaId(),
                scheduleInfo.getCellId(),
                scheduleInfo.getStartTime(),
                scheduleInfo.getEndTime());
        //休息时间信息
        List<HvBmScheduleBreakTime> breakTimes = new ArrayList<>();
        //取出开始时间并定义变量
        LocalDate workDay = scheduleInfo.getStartTime();
        //获取班组最大数量
        int crewIdSize = scheduleInfo.getCrewIds().size();
        int crewIdFlag = 0;
        //循环时间并增加天数
        while (workDay.isBefore(scheduleInfo.getEndTime())) {
            LocalDate finalWorkDay = workDay;
            //后推一天
            workDay = workDay.plusDays(1);
            //如果不是假期
            if (holidays.stream().noneMatch(t -> t.getHoliday().equals(finalWorkDay))) {
                //----------------------------------
                //2.5.2版本已经去掉了周六周日，改为了工作日。这里的代码是为了兼容之前的版本
                //如果时间为周六，并且周末不工作，跳过日期
                if (finalWorkDay.getDayOfWeek() == DayOfWeek.SATURDAY && !scheduleInfo.getWorkOvertimeOnSaturday()) {
                    continue;
                }
                //如果时间为周日，并且周末不工作，跳过日期
                if (finalWorkDay.getDayOfWeek() == DayOfWeek.SUNDAY && !scheduleInfo.getWorkOvertimeOnSunday()) {
                    continue;
                }
                //----------------------------------
                //如果不在工作时间内
                if (!scheduleInfo.getWorkOfDay().contains(finalWorkDay.getDayOfWeek().getValue())) {
                    continue;
                }
                //循环班次
                for (Integer shiftId : scheduleInfo.getShiftIds()) {
                    HvBmShift bmShift = shiftMap.get(shiftId);
                    if (bmShift == null) {
                        throw new BaseKnownException(10000, "查询班次信息异常,请检查输入的班次id是否正确");
                    }
                    //创建排班信息
                    HvBmSchedule schedule = new HvBmSchedule();
                    schedule.setCrewId(scheduleInfo.getCrewIds().get(crewIdFlag));
                    schedule.setShiftId(shiftId);
                    schedule.setPlanQuantity(shiftMap.get(shiftId).getPlanQuantity());
                    schedule.setAreaId(scheduleInfo.getAreaId());
                    schedule.setCellId(scheduleInfo.getCellId());
                    schedule.setScheduleDate(finalWorkDay);
                    schedule.setPlanQuantity(bmShift.getPlanQuantity());
                    schedule.setStartTime(bmShift.getStartTime().atDate(finalWorkDay));
                    schedule.setEndTime(bmShift.getEndTime().atDate(finalWorkDay));
                    //如果结束时间比开始时间小，那么说明跨天了。要增加一天
                    if (schedule.getEndTime().isBefore(schedule.getStartTime())) {
                        schedule.setEndTime(schedule.getEndTime().plusDays(1));
                    }
                    //班组的id前移
                    crewIdFlag++;
                    //当班次到达结尾 则重新排班
                    if (crewIdFlag == crewIdSize) {
                        crewIdFlag = 0;
                    }
                    scheduleRepository.save(schedule);
                    //创建休息信息
                    List<HvBmScheduleBreakTime> entities = breakEntities.stream()
                            .filter(t -> t.getShiftId().equals(shiftId))
                            .map(t -> getBreakByScheduleAndBreakTime(schedule, t))
                            .collect(Collectors.toList());
                    breakTimes.addAll(entities);
                }
            }
        }
        //统一添加排班休息信息
        scheduleBreakTimeRepository.saveAll(breakTimes);
    }

    /**
     * 清理时间段内的排班数据
     *
     * @param areaId 车间id
     * @param cellId 产线id
     * @param begin  开始时间
     * @param end    结束时间
     */
    private void deleteScheduleInfoAndScheduleBreakTime(Integer areaId, Integer cellId, LocalDate begin, LocalDate
            end) {
        //清除当天的排班计划
        List<HvBmSchedule> schedules = scheduleRepository.findAllByDate(areaId,
                cellId,
                begin,
                end);
        //清除所有对应的休息时间
        List<HvBmScheduleBreakTime> breakTimes = scheduleBreakTimeRepository.findAllByScheduleIdIn(schedules
                .stream()
                .map(SysBase::getId)
                .collect(Collectors.toList()));
        scheduleBreakTimeRepository.deleteAll(breakTimes);
        scheduleRepository.deleteAll(schedules);
        scheduleRepository.flush();
    }


    /**
     * 验证班组id信息
     *
     * @param areaId 车间id
     * @param cellId 产线id
     * @param crewId 班组id
     */
    private void validCrewId(int areaId, int cellId, int crewId) {
        HvBmCrew hvBmCrew1 = crewRepository.getOne(crewId);
        if (hvBmCrew1.getAreaId() != areaId || hvBmCrew1.getCellId() != cellId) {
            throw new BaseKnownException(ScheduleExceptionEnum.CREW_NOT_EXISTS);
        } else if (hvBmCrew1.getIsEnable() == null || !hvBmCrew1.getIsEnable()) {
            throw new BaseKnownException(ScheduleExceptionEnum.CREW_IS_NOT_ENABLE);
        }

    }

    /**
     * 验证班次合规性
     *
     * @param areaId  车间id
     * @param cellId  产线id
     * @param shiftId 班次id
     */
    private void validShiftId(int areaId, int cellId, int shiftId) {
        HvBmShift hvBmShift = shiftRepository.getOne(shiftId);
        if (hvBmShift.getAreaId() != areaId || hvBmShift.getCellId() != cellId) {
            throw new BaseKnownException(ScheduleExceptionEnum.SHIFT_NOT_EXISTS);
        } else if (hvBmShift.getIsEnable() == null || !hvBmShift.getIsEnable()) {
            throw new BaseKnownException(ScheduleExceptionEnum.SHIFT_NOT_ENABLE);
        }

    }

    private HvBmScheduleBreakTime getBreakByScheduleAndBreakTime(HvBmSchedule schedule, HvBmBreakTime breakTime) {
        HvBmScheduleBreakTime entity = new HvBmScheduleBreakTime();
        entity.setScheduleId(schedule.getId());
        entity.setStartTime(breakTime.getBeginTime().atDate(schedule.getScheduleDate()));
        entity.setEndTime(breakTime.getEndTime().atDate(schedule.getScheduleDate()));
        //休息时间跨天，加进去
        if (entity.getEndTime().isBefore(entity.getStartTime())) {
            entity.setEndTime(entity.getEndTime().plusDays(1));
        }
        //如果休息时间再开始时间前面，认为是跨天了。再加1天
        if (entity.getStartTime().isBefore(schedule.getStartTime())) {
            entity.setStartTime(entity.getStartTime().plusDays(1));
            entity.setEndTime(entity.getEndTime().plusDays(1));
        }
        entity.setDescription(breakTime.getDescription());
        return entity;
    }
}