package com.hvisions.hiperbase.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.equipment.EquipmentTypeBaseDTO;
import com.hvisions.hiperbase.equipment.EquipmentTypeDTO;
import com.hvisions.hiperbase.equipment.EquipmentTypeImportDTO;
import com.hvisions.hiperbase.equipment.EquipmentTypeQueryDTO;
import com.hvisions.hiperbase.consts.EquipmentConsts;
import com.hvisions.hiperbase.service.equipment.EquipmentTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**
 * <p>Title: HvEquipmentTypeController</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/13</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@Api(description = "设备类型接口")
@RequestMapping("/equipmentType")
public class EquipmentTypeController {
    private EquipmentTypeService equipmentTypeService;

    @Autowired
    public EquipmentTypeController(EquipmentTypeService equipmentTypeService) {
        this.equipmentTypeService = equipmentTypeService;
    }

    /**
     * 添加设备类型
     *
     * @param equipmentTypeDTO 设备类型信息
     * @return 设备类型主键
     */
    @PostMapping("/createEquipmentType")
    @ApiOperation(value = "添加设备类型")
    public EquipmentTypeDTO createEquipmentType(@Valid @RequestBody EquipmentTypeDTO equipmentTypeDTO) {
        return equipmentTypeService.createEquipmentType(equipmentTypeDTO);
    }

    /**
     * 根据设备类型id删除设备类型
     *
     * @param id 设备类型id
     */

    @DeleteMapping("/deleteEquipmentTypeById/{id}")
    @ApiOperation(value = "删除设备类型")
    public Integer deleteEquipmentTypeById(@PathVariable Integer id) {
        return equipmentTypeService.deleteEquipmentTypeById(id);
    }


    /**
     * 更新设备类型信息
     *
     * @param equipmentTypeDTO 设备类型信息
     * @return 设备类型主键
     */
    @PutMapping("/updateEquipmentType")
    @ApiOperation(value = "修改设备类型")
    public EquipmentTypeDTO updateEquipmentType(@Valid @RequestBody EquipmentTypeBaseDTO equipmentTypeDTO) {
        return equipmentTypeService.updateEquipmentType(equipmentTypeDTO);
    }

    /**
     * 获取所有设备类型信息
     *
     * @return 设备类型信息
     */
    @GetMapping("/getAllEquipmentTypeList")
    @ApiOperation(value = "获取所有设备类型")
    public List<EquipmentTypeDTO> getAllEquipmentTypeList() {
        return equipmentTypeService.getAllEquipmentType();
    }

    /**
     * 分页查询
     *
     * @param equipmentTypeQueryDto 分页查询条件
     * @return 分页信息
     */
    @PostMapping(value = "/getEquimentTypeQuery")
    @ApiOperation(value = "设备类型分页查询")
    public Page<EquipmentTypeDTO> getEquipmentTypeQuery(@RequestBody EquipmentTypeQueryDTO equipmentTypeQueryDto) {
        return equipmentTypeService.getEquipmentTypeQuery(equipmentTypeQueryDto);
    }

    /**
     * 根据父级设备类型ID查询设备类型信息
     *
     * @param parentId 父级设备类型ID
     * @return 设备类型列表信息
     */
    @ApiOperation(value = "根据父级设备类型ID查询设备类型信息")
    @GetMapping(value = "/findAllByParentId/{parentId}")
    public List<EquipmentTypeDTO> findAllByParentId(@PathVariable Integer parentId) {
        return equipmentTypeService.findAllByParentId(parentId);
    }

    /**
     * 根据设备类型id查询父级设备类型列表
     *
     * @param id 设备类型id
     * @return 父级设备类型列表
     */
    @ApiOperation(value = "根据设备类型id查询父级设备类型列表")
    @GetMapping(value = "/findParentTypeById/{id}")
    public List<EquipmentTypeDTO> findParentTypeById(@PathVariable Integer id) {
        return equipmentTypeService.findParentTypeById(id);
    }

    /**
     * 根据类型编码查询设备类型信息
     *
     * @param typeCode 设备类型编码
     * @return 设备类型信息
     */
    @GetMapping(value = "/getEquipmentTypeByTypeCode")
    @ApiOperation(value = "根据类型编码查询设备类型信息")
    public EquipmentTypeDTO getEquipmentTypeByTypeCode(@RequestParam String typeCode) {
        return equipmentTypeService.getEquipmentTypeByCode(typeCode);
    }

    /**
     * 根据设备类型编码查询设备类型信息列表
     *
     * @param codeIn 设备类型编码列表
     * @return 设备类型信息列表
     */
    @PostMapping(value = "/getAllByEquipmentTypeCodeIn")
    @ApiOperation(value = "根据设备类型编码列表查询设备类型信息列表")
    public List<EquipmentTypeDTO> getAllByEquipmentTypeCodeIn(@RequestBody List<String> codeIn) {
        return equipmentTypeService.getAllByEquipmentTypeCodeIn(codeIn);
    }

    /**
     * 根据ID查询设备类型信息
     *
     * @param id id
     * @return 设备类型信息
     */
    @GetMapping(value = "/getHvEquipmentTypeDTOById/{id}")
    @ApiOperation(value = "根据ID查询设备类型信息")
    public EquipmentTypeDTO getHvEquipmentTypeDTOById(@PathVariable int id) {
        return equipmentTypeService.getEquipmentTypeById(id);
    }


    /**
     * 根据ID列表查询设备类型
     *
     * @param idList id列表
     * @return 设备类型列表
     */
    @PostMapping(value = "/getTypeAllByIdIn")
    @ApiOperation(value = "根据ID列表查询设备类型")
    public List<EquipmentTypeDTO> getTypeAllByIdIn(@RequestBody List<Integer> idList) {
        return equipmentTypeService.getAllByIdIn(idList);
    }

    /**
     * 获取设备类型导入模板
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/getEquipmentTypeImportTemplateLink")
    @ApiOperation(value = "获取设备类型导入模板,支持超链接")
    public ResponseEntity<byte[]> getEquipmentImportTemplateLink() throws IOException, IllegalAccessException {
        return ExcelUtil.generateImportFile(EquipmentTypeImportDTO.class,
                EquipmentConsts.EQUIPMENT_TYPE_IMPORT_TABLE_NAME);
    }

    /**
     * 获取导入模板
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/getEquipmentTypeImportTemplate")
    @ApiOperation(value = "获取设备导入模板")
    public ResultVO<ExcelExportDto> getEquipmentImportTemplate() throws IOException, IllegalAccessException {
        ResponseEntity<byte[]> result =
                ExcelUtil.generateImportFile(EquipmentTypeImportDTO.class,
                        EquipmentConsts.EQUIPMENT_TYPE_IMPORT_TABLE_NAME);
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(result.getBody());
        excelExportDto.setFileName(EquipmentConsts.EQUIPMENT_TYPE_IMPORT_TABLE_NAME);
        return ResultVO.success(excelExportDto);
    }

    /**
     * 导入所有设备类型信息
     *
     * @param file 设备信息文档
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    @PostMapping(value = "/importEquipmentType")
    @ApiOperation(value = "导入设备类型信息，如果code存在则更新，code不存在则新增")
    public ImportResult importEquipment(@RequestParam("file") MultipartFile file)
            throws IllegalAccessException, ParseException, IOException {
        return equipmentTypeService.importEquipmentType(file);
    }

    /**
     * 导出设备类型信息
     *
     * @return 设备信息excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException 访问异常
     */
    @ApiResultIgnore
    @ApiOperation(value = "导出设备类型信息(超链接)")
    @GetMapping(value = "/exportEquipmentTypeLink")
    public ResponseEntity<byte[]> exportEquipmentTypeLink() throws IOException, IllegalAccessException {
        return equipmentTypeService.exportEquipmentTypeLink();
    }

    /**
     * 导出设备信息
     *
     * @return 设备信息excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException 访问异常
     */
    @ApiResultIgnore
    @ApiOperation(value = "导出设备类型信息")
    @GetMapping(value = "/exportEquipmentType")
    public ResultVO<ExcelExportDto> exportEquipmentType() throws IOException, IllegalAccessException {
        return equipmentTypeService.exportEquipmentType();
    }

    @GetMapping("/findAllChildTypeByParentId/{parentId}")
    @ApiOperation(value = "根据父节点下的所有子类型(包含子类型的子类型)")
    public List<EquipmentTypeDTO> findAllChildTypeByParentId(@PathVariable(value = "parentId") Integer parentId) {
        return equipmentTypeService.findAllChildTypeByParentId(parentId);
    }


    @GetMapping("/findAllparentTypeByChildId/{childId}")
    @ApiOperation(value = "根据父节点下的所有子类型(包含子类型的子类型)")
    public List<EquipmentTypeDTO> findAllparentTypeByChildId(@PathVariable(value = "childId") Integer childId) {
        return equipmentTypeService.findAllByChildId(childId);
    }

}
















