package com.hvisions.hiperbase.component;

import com.hvisions.common.runner.SafetyCommandLineRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcOperations;

/**
 * 修复所有没有工艺操作类型的数据，用于兼容之前的版本
 *
 * <AUTHOR>
 */
@Configuration
public class StartUpRunner extends SafetyCommandLineRunner {
    @Autowired
    JdbcOperations jdbcOperations;

    @Override
    public void callRunner(String... args) throws Exception {
        // 找到目前所有操作类型为null的记录，修改为0
        String sql = "UPDATE hv_bm_operation set operation_type = 0 where operation_type is null";
        // 执行插入语句
        jdbcOperations.execute(sql);
    }

}