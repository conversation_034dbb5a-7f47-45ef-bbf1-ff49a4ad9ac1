package com.hvisions.hiperbase.service.equipment.imp;

import cn.hutool.json.JSONObject;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.hiperbase.equipment.EquipmentDTO;
import com.hvisions.hiperbase.equipment.EquipmentTypeDTO;
import com.hvisions.hiperbase.export.AuthorityDTO;
import com.hvisions.hiperbase.export.EquipmentExportDTO;
import com.hvisions.hiperbase.export.EquipmentMessageDTO;
import com.hvisions.hiperbase.export.EquipmentTypeMessageDTO;
import com.hvisions.hiperbase.enums.EquipmentExceptionEnum;
import com.hvisions.hiperbase.service.equipment.EquipmentService;
import com.hvisions.hiperbase.service.equipment.EquipmentTypeService;
import com.hvisions.hiperbase.service.equipment.ExportService;
import com.hvisions.hiperbase.utils.AESTools;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>Title: ExportServiceImpl</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/6/5</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
@Slf4j
public class ExportServiceImpl implements ExportService {

    @Autowired
    EquipmentService equipmentService;
    @Autowired
    EquipmentTypeService equipmentTypeService;
    @Value("${h-visions.tag.tagLimit:1000}")
    private Integer tagLimit;
    @Value("${h-visions.tag.siteNum:10}")
    private String siteNum;
    @Value("${h-visions.tag.tagTime:2020-05-21 12:00:00}")
    private String timeLimit;
    @Value("${h-visions.tag.password:hvisionsenergyba}")
    private String password;

    /**
     * {@inheritDoc}
     */
    @Override
    public void export(HttpServletResponse response) {
        EquipmentExportDTO equipmentExportDTO = getEquipmentExportDTO();
        //调用AES加密方法 对要导出的对象进行加密转换为json格式
        byte[] encrypt;
        try {
            String info = new JSONObject(equipmentExportDTO).toString();
            encrypt = AESTools.encryptString(info, password);
        } catch (Exception ex) {
            log.error(ex.getMessage());
            return;
        }
        OutputStream os;
        response.setContentType("multipart/form-data");
        response.addHeader("Content-Length", "" + encrypt.length);
        try {
            response.setHeader("Content-Disposition", "attachment;filename=equipment.info");
            os = response.getOutputStream();
            os.write(encrypt, 0, encrypt.length);
            os.flush();
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new BaseKnownException(EquipmentExceptionEnum.EXPORT_ERROR);
        }
    }

    /**
     * 获取数据
     *
     * @return 设备信息实体
     */
    private EquipmentExportDTO getEquipmentExportDTO() {
        //导出的数据模型
        EquipmentExportDTO equipmentExportDTO = new EquipmentExportDTO();
        equipmentExportDTO.setAuthority(getAuthInfo());
        //获取设备信息
        equipmentExportDTO.setEquipments(getAllEquipment());
        //获取设备类型ID
        List<Integer> typeIdList = equipmentExportDTO.getEquipments()
                .stream().map(EquipmentMessageDTO::getEquipmentTypeId)
                .collect(Collectors.toList());
        if (typeIdList.size() > 0) {
            List<EquipmentTypeDTO> typeAllByIdIn = equipmentTypeService.getAllByIdIn(typeIdList);
            equipmentExportDTO.setEquipmentTypes(DtoMapper.convertList(typeAllByIdIn, EquipmentTypeMessageDTO.class));
            return equipmentExportDTO;
        }
        return equipmentExportDTO;
    }

    /**
     * 获取授权信息
     *
     * @return 授权信息
     */
    private AuthorityDTO getAuthInfo() {

        AuthorityDTO authorityDTO = new AuthorityDTO();
        //拼接授权信息对象 从yml文件配置中取值
        authorityDTO.setTagLimit(tagLimit);
        authorityDTO.setSiteNum(siteNum);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            authorityDTO.setTimeLimit(simpleDateFormat.parse(timeLimit));
        } catch (ParseException e) {
            authorityDTO.setTimeLimit(null);
        }
        return authorityDTO;
    }

    /**
     * 设备信息，转换成传输的信息
     *
     * @return 传输的信息
     */
    private List<EquipmentMessageDTO> getAllEquipment() {
        List<EquipmentDTO> list = equipmentService.getAllEquipment();
        log.info("设备及其设备类型信息" + list.toString());
        return DtoMapper.convertList(list, EquipmentMessageDTO.class);
    }

}









