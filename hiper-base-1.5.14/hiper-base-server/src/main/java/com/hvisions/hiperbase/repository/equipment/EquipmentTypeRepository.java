package com.hvisions.hiperbase.repository.equipment;

import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: HvEquipmentTypeRepository</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/13</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface EquipmentTypeRepository extends JpaRepository<HvBmEquipmentType, Integer> {
    /**
     * 根据设备类型编码查询设备类型信息
     *
     * @param code 设备类型编码
     * @return 设备类型信息
     */
    HvBmEquipmentType findByEquipmentTypeCode(String code);


    /**
     * 根据设备类型编码查询设备类型信息列表
     *
     * @param codeIn 设备类型编码列表
     * @return 设备类型信息列表
     */
    List<HvBmEquipmentType> getAllByEquipmentTypeCodeIn(List<String> codeIn);

    /**
     * 根据父级设备类型ID查询设备类型信息
     *
     * @param parentId 父级设备类型ID
     * @return 设备类型列表信息
     */
    List<HvBmEquipmentType> findAllByParentId(Integer parentId);


    /**
     * 根据ID列表查询设备类型
     *
     * @param idList id列表
     * @return 设备类型信息
     */
    List<HvBmEquipmentType> getAllByIdIn(List<Integer> idList);
}

    
    
    
    
    
    
    
    
    
    
    