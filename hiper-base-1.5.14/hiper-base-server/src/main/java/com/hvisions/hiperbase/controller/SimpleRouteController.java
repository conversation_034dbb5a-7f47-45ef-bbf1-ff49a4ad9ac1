package com.hvisions.hiperbase.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.route.dto.Node;
import com.hvisions.hiperbase.route.dto.simple.OperationNodeData;
import com.hvisions.hiperbase.route.dto.simple.SimpleRouteDTO;
import com.hvisions.hiperbase.service.route.SimpleRouteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**
 * <p>Title: SimpleRouteController</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2022/4/20</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@Api(description = "简单工艺控制器")
@RequestMapping(value = "/simple_route")
public class SimpleRouteController {

    @Autowired
    SimpleRouteService simpleRouteService;

    /**
     * 创建简单工艺
     *
     * @param simpleRouteDTO 工艺对象
     */
    @ApiOperation(value = "创建简单工艺")
    @PostMapping(value = "/createSimpleRoute")
    public Integer createSimpleRoute(@RequestBody SimpleRouteDTO simpleRouteDTO) {
        return simpleRouteService.createSimpleRoute(simpleRouteDTO);
    }


    /**
     * 更新简单工艺
     *
     * @param routeDTO 工艺对象
     */
    @ApiOperation(value = "更新简单工艺")
    @PutMapping(value = "/updateSimpleRoute")
    public void updateSimpleRoute(@RequestBody SimpleRouteDTO routeDTO) {
        simpleRouteService.updateSimpleRoute(routeDTO);
    }

    /**
     * 创建工序
     *
     * @param operationNodeData 工序节点数据
     */
    @ApiOperation(value = "创建工序")
    @PostMapping(value = "/createOperation")
    public void createOperation(@RequestBody OperationNodeData operationNodeData) {
        simpleRouteService.createOperation(operationNodeData);
    }

    /**
     * 更新工序
     *
     * @param operationNodeData 工序节点数据
     */
    @ApiOperation(value = "创建工序")
    @PutMapping(value = "/updateOperation")
    public void updateOperation(@RequestBody OperationNodeData operationNodeData) {
        simpleRouteService.updateOperation(operationNodeData);
    }

    /**
     * 删除工序 节点编码
     *
     * @param routeId  工艺id
     * @param nodeCode 节点编码
     */
    @DeleteMapping(value = "/deleteOperation")
    @ApiOperation(value = "删除工序")
    public void deleteOperation(@RequestParam Integer routeId, @RequestParam String nodeCode) {
        simpleRouteService.deleteOperation(routeId, nodeCode);
    }

    /**
     * 工序排序
     *
     * @param nodes 工序
     */
    @PostMapping(value = "/sortOperation")
    @ApiOperation(value = "工序排序")
    public void sortOperation(@RequestBody List<Node> nodes, @RequestParam Integer routeId) {
        simpleRouteService.sortOperation(nodes, routeId);
    }

    /**
     * 导入简单工艺路径 并转化成流程图方式
     *
     * @param file 导入文件
     * @return 导入结果
     */
    @PostMapping(value = "/importVisioRoute")
    @ApiOperation(value = "导入简单工艺路径 并转化成流程图方式")
    public ImportResult importVisioRoute(MultipartFile file) throws IllegalAccessException, ParseException,
            IOException {
        return simpleRouteService.importVisioRoute(file);
    }

    /**
     * 获取导入模板
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/getRouteImportTemplate")
    @ApiOperation(value = "获取导入模板")
    public ResultVO<ExcelExportDto> getRouteImportTemplate() throws IOException, IllegalAccessException {
        return simpleRouteService.getRouteImportTemplate();
    }

    /**
     * 导出
     *
     * @param routeId
     * @return
     */
    @ApiResultIgnore
    @PostMapping(value = "/exportRoute")
    @ApiOperation(value = "导出")
    public ResultVO<ExcelExportDto> exportRoute(@RequestParam Integer routeId) throws IllegalAccessException, IOException {
        return simpleRouteService.exportRoute(routeId);
    }

}