package com.hvisions.hiperbase.repository.route;

import com.hvisions.hiperbase.entity.route.HvBmOperationTemplate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <p>Title: OperationTemplateRepository</p>
 * <p>Description: 工艺操作模板仓储类</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/8/31</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface OperationTemplateRepository extends JpaRepository<HvBmOperationTemplate, Integer> {
    /**
     * 获取工艺操作的模板
     * @param operationId 工艺操作id
     * @param platform    平台
     * @return 模板信息
     */
    HvBmOperationTemplate findByOperationIdAndPlatform(Integer operationId, Integer platform);
}









