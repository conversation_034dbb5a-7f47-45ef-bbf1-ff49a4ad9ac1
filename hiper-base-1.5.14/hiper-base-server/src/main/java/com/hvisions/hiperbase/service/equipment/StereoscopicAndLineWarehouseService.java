package com.hvisions.hiperbase.service.equipment;

import com.hvisions.hiperbase.equipment.StereoscopicAndLineWarehouseDTO;
import com.hvisions.hiperbase.equipment.StereoscopicAndLineWarehouseQueryDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/5/22
 */
public interface StereoscopicAndLineWarehouseService {

    /**
     * 分页查询
     * @return
     */
    Page<StereoscopicAndLineWarehouseDTO> getPage(StereoscopicAndLineWarehouseQueryDTO queryDTO);

    /**
     * 添加
     * @param stereoscopicAndLineWarehouseDTO
     * @return
     */
    int addStereoscopicAndLineWarehouse(StereoscopicAndLineWarehouseDTO stereoscopicAndLineWarehouseDTO);

    /**
     * 修改
     * @param stereoscopicAndLineWarehouseDTO
     * @return
     */
    int updateStereoscopicAndLineWarehouse(StereoscopicAndLineWarehouseDTO stereoscopicAndLineWarehouseDTO);

    /**
     * 删除
     * @param id
     */
    void deleteStereoscopicAndLineWarehouse(int id);
}
