package com.hvisions.hiperbase.controller;

import com.hvisions.common.dto.ExtendColumnInfo;
import com.hvisions.common.service.BaseExtendService;
import com.hvisions.hiperbase.bom.dto.BaseSubstituteItemDTO;
import com.hvisions.hiperbase.bom.dto.SubstituteItemDTO;
import com.hvisions.hiperbase.service.material.SubstituteItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title: SubstituteItemController</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/11/29</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@RequestMapping("/SubstituteItem")
@Slf4j
@Api(description = "SubstituteItem控制器")
public class SubstituteItemController {

    private final SubstituteItemService substituteItemService;
    @Resource(name = "substitute_item_extend")
    BaseExtendService substituteItemExtendService;


    @Autowired
    public SubstituteItemController(SubstituteItemService substituteItemService) {
        this.substituteItemService = substituteItemService;
    }

    /**
     * 添加SubstituteItem信息
     *
     * @param substituteItemDTO 传入DTO对象
     * @return id
     */
    @PostMapping("/createSubstituteItem")
    @ApiOperation(value = "添加SubstituteItem")
    public int createSubstituteItem(@Valid @RequestBody BaseSubstituteItemDTO substituteItemDTO) {
        return substituteItemService.createSubstituteItem(substituteItemDTO);
    }

    /**
     * 添加SubstituteItem扩展属性
     *
     * @param extendColumnInfo 扩展属性信息
     */
    @PostMapping("/createSubstituteItemColumn")
    @ApiOperation(value = "添加SubstituteItem扩展属性")
    public void createSubstituteItemColumn(@Valid @RequestBody ExtendColumnInfo extendColumnInfo) {
        substituteItemExtendService.addExtend(extendColumnInfo);
    }

    /**
     * 获取所有SubstituteItem扩展字段信息
     *
     * @return SubstituteItem扩展字段信息
     */
    @GetMapping(value = "/getAllSubstituteItemColumnExtend")
    @ApiOperation(value = "获取所有SubstituteItem扩展字段信息")
    public List<ExtendColumnInfo> getAllSubstituteItemColumnExtend() {
        return substituteItemExtendService.getExtendColumnInfo();
    }

    /**
     * 更新SubstituteItem信息
     *
     * @param substituteItemDTO 传入DTO对象
     * @return id
     */
    @PutMapping("/updateSubstituteItem")
    @ApiOperation(value = "更新SubstituteItem")
    public int updateSubstituteItem(@RequestBody BaseSubstituteItemDTO substituteItemDTO) {
        return substituteItemService.updateSubstituteItem(substituteItemDTO);
    }

    /**
     * 删除SubstituteItem信息
     *
     * @param id SubstituteItemId
     */

    @DeleteMapping("/deleteSubstituteItemById/{id}")
    @ApiOperation(value = "删除SubstituteItem信息   ")
    @Transactional(rollbackFor = Exception.class)
    public void deleteSubstituteItemById(@PathVariable int id) {
        substituteItemService.deleteSubstituteItem(id);
    }


    /**
     * 删除SubstituteItem扩展属性
     *
     * @param columnName 扩展属性名称
     */
    @DeleteMapping("/deleteSubstituteItemColumn/{columnName}")
    @ApiOperation(value = "删除SubstituteItem扩展属性")
    @Transactional(rollbackFor = Exception.class)
    public void deleteSubstituteItemColumn(@PathVariable String columnName) {
        substituteItemExtendService.dropExtend(columnName);
    }


    /**
     * 通过ID查询SubstituteItem
     *
     * @param id SubstituteItemId
     * @return SubstituteItem信息
     */
    @GetMapping("/getSubstituteItemById/{id}")
    @ApiOperation(value = "通过id查询SubstituteItem信息")
    public SubstituteItemDTO getSubstituteItemById(@PathVariable int id) {
        return substituteItemService.getAllById(id);
    }

    /**
     * 通过bomItemId查询SubstituteItem
     *
     * @param bomItemId bomItemId
     * @return SubstituteItem信息
     */
    @GetMapping("/getSubstituteItemByBomItemId/{bomItemId}")
    @ApiOperation(value = "通过bomItemId查询SubstituteItem信息")
    public List<SubstituteItemDTO> getSubstituteItemByBomItemId(@PathVariable int bomItemId) {
        return substituteItemService.getAllByBomItemId(bomItemId);
    }

}
