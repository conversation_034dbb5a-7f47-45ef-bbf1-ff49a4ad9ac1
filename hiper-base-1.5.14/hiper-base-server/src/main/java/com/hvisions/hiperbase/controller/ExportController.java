package com.hvisions.hiperbase.controller;

import com.hvisions.hiperbase.service.equipment.ExportService;
import com.hvisions.hiperbase.utils.AESTools;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>Title: ExportController</p>
 * <p>Description: 信息导出控制器</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/6/5</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping("/export")
@Api(description = "设备信息导出控制器")
public class ExportController {
    @Autowired
    ExportService exportService;
    @Value("${h-visions.tag.password:hvisionsenergyba}")
    private String password;

    /**
     * 设备导出数据
     */

    @GetMapping(value = "/export")
    @ApiOperation(value = "设备导出数据")
    public void export(HttpServletResponse response) {
        exportService.export(response);
    }

    /**
     * 解析数据
     */
    @PostMapping(value = "/explain")
    @ApiOperation(value = "解析数据")
    public String explain(MultipartFile file) {
        try {
            return AESTools.decryptString(file.getBytes(), password);
        } catch (Exception ex) {
            return "";
        }
    }
}









