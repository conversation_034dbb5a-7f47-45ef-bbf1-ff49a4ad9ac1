package com.hvisions.hiperbase.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.EasyExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.entity.material.HvBmTaoMaterialFile;
import com.hvisions.hiperbase.materials.dto.HvBmTaoMaterialFileDTO;
import com.hvisions.hiperbase.service.material.HvBmTaoMaterialFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import java.time.LocalDateTime;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-22 11:17
 */
@RestController
@RequestMapping(value = "/taoMaterialFile")
@Api(description = "型材套料配置")
public class TaoMaterialFileController {
    @Autowired
    private HvBmTaoMaterialFileService hvBmTaoMaterialFileService;
    /**
     * 分页查询
     */
    @ApiOperation("分页查询")
    @PostMapping("/list")
    public Page<HvBmTaoMaterialFile> list(@RequestBody HvBmTaoMaterialFile condition,
                                          @RequestParam(name="pageNum", defaultValue="1") Integer pageNo,
                                          @RequestParam(name="pageSize", defaultValue="10") Integer pageSize)
    {

        QueryWrapper<HvBmTaoMaterialFile> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(condition.getModel()),"model",condition.getModel())
                .eq(StringUtils.isNotEmpty(condition.getSegmentationCode()),"segmentation_code",condition.getSegmentationCode());

        Page<HvBmTaoMaterialFile> page = new Page<>(pageNo,pageSize);
        return hvBmTaoMaterialFileService.page(page,queryWrapper);
    }

    /**
     * 导入
     *
     * @param file 文件
     */
    @PostMapping("/import")
    @ApiOperation("导入")
    public void importData(MultipartFile file, @UserInfo @ApiIgnore UserInfoDTO userInfo) {
        List<HvBmTaoMaterialFile> HvBmTaoMaterialFiles = EasyExcelUtil.getImport(file, HvBmTaoMaterialFile.class);
        for (HvBmTaoMaterialFile hvBmTaoMaterialFile : HvBmTaoMaterialFiles) {

            try {
                if(hvBmTaoMaterialFile.getQuantity() <= 0){
                    throw new BaseKnownException("数量必须为大于0的整数");
                }
            }catch (Exception e){
                throw new BaseKnownException("数量必须为大于0的整数");
            }

            try {
                if(Double.parseDouble(hvBmTaoMaterialFile.getLength()) <= 0){
                    throw new BaseKnownException("长度必须为大于0的整数或小数");
                }
            }catch (Exception e){
                throw new BaseKnownException("长度必须为大于0的整数或小数");
            }

            try {
                if(Double.parseDouble(hvBmTaoMaterialFile.getWeight()) <= 0){
                    throw new BaseKnownException("重量必须为大于0的整数或小数");
                }
            }catch (Exception e){
                throw new BaseKnownException("重量必须为大于0的整数或小数");
            }
            hvBmTaoMaterialFile.setCreatorId(userInfo.getId());
        }
        hvBmTaoMaterialFileService.saveBatch(HvBmTaoMaterialFiles);

    }


    /**
     * 导出
     *
     * @param HvBmMaterialTaoPicDTO hvBmMaterialTaoPicDTO
     */
    @ApiOperation(value = "导出")
    @PostMapping(value = "/exportData")
    @ApiResultIgnore
    public ResultVO<ExcelExportDto> exportData(@RequestBody HvBmTaoMaterialFile condition) {
        QueryWrapper<HvBmTaoMaterialFile> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(condition.getModel()),"model",condition.getModel())
                .eq(StringUtils.isNotEmpty(condition.getSegmentationCode()),"segmentation_code",condition.getSegmentationCode());
        List<HvBmTaoMaterialFile> list = hvBmTaoMaterialFileService.list(queryWrapper);
        return ResultVO.success(EasyExcelUtil.getExcel(list, HvBmTaoMaterialFile.class, System.currentTimeMillis() + "型材套料配置.xlsx"));

    }

    /**
     * 下载模版
     *
     * @param
     */
    @ApiOperation(value = "下载模版")
    @PostMapping(value = "/downloadTemplate")
    @ApiResultIgnore
    public ResultVO<ExcelExportDto> downloadTemplate() {
        List<HvBmTaoMaterialFile> list = new ArrayList<>();
        return ResultVO.success(EasyExcelUtil.getExcel(list, HvBmTaoMaterialFile.class, System.currentTimeMillis() + "型材套料配置导入模版.xlsx"));

    }


    /**
     * 添加
     *
     * @param HvBmTaoMaterialFileDTO hvBmTaoMaterialFileDTO
     */
    @ApiOperation(value = "添加")
    @PostMapping(value = "/add")
    public void add(@RequestBody HvBmTaoMaterialFileDTO hvBmTaoMaterialFileDTO, @UserInfo @ApiIgnore UserInfoDTO userInfo) {
        HvBmTaoMaterialFile convert = DtoMapper.convert(hvBmTaoMaterialFileDTO, HvBmTaoMaterialFile.class);
        convert.setCreateTime(LocalDateTime.now());
        convert.setCreatorId(userInfo.getId());
        hvBmTaoMaterialFileService.save(convert);

    }

    /**
     * 删除
     *
     * @param id 主键
     */
    @ApiOperation(value = "删除")
    @DeleteMapping(value = "/delete/{id}")
    public void delete(@PathVariable long id) {
        hvBmTaoMaterialFileService.removeById(id);
    }

    /**
     * 修改
     *
     * @param HvBmTaoMaterialFileDTO hvBmTaoMaterialFileDTO
     */
    @ApiOperation(value = "修改")
    @PutMapping(value = "/update")
    public void update(@RequestBody HvBmTaoMaterialFileDTO hvBmTaoMaterialFileDTO,@UserInfo @ApiIgnore UserInfoDTO userInfo) {
        HvBmTaoMaterialFile convert = DtoMapper.convert(hvBmTaoMaterialFileDTO, HvBmTaoMaterialFile.class);
        convert.setUpdateTime(LocalDateTime.now());
        convert.setUpdaterId(userInfo.getId());
        hvBmTaoMaterialFileService.updateById(DtoMapper.convert(hvBmTaoMaterialFileDTO,HvBmTaoMaterialFile.class));
    }

    /**
     * 根据id获取
     *
     * @param id 主键
     * @return HvBmMaterialTaoPicDTO
     */
    @ApiOperation(value = "根据id获取")
    @GetMapping(value = "/get/{id}")
    public HvBmTaoMaterialFileDTO getList(@PathVariable long id) {
        return DtoMapper.convert(hvBmTaoMaterialFileService.getById(id),HvBmTaoMaterialFileDTO.class);
    }

    /**
     * 查询全部
     * @return 列表
     */
    @ApiOperation(value = "获取所有列表")
    @GetMapping(value = "/getAll")
    public List<HvBmTaoMaterialFileDTO> getAll(){
        return DtoMapper.convertList(hvBmTaoMaterialFileService.list(),HvBmTaoMaterialFileDTO.class);

    }

    @ApiOperation(value = "根据船型和分段查询")
    @PostMapping(value = "/getListByModelAndSegmentation")
    public List<HvBmTaoMaterialFile> getListByModelAndSegmentation(@RequestBody HvBmTaoMaterialFileDTO hvBmTaoMaterialFileDTO){
        QueryWrapper<HvBmTaoMaterialFile> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(hvBmTaoMaterialFileDTO.getModel()),"model",hvBmTaoMaterialFileDTO.getModel())
                .eq(StringUtils.isNotEmpty(hvBmTaoMaterialFileDTO.getSegmentationCode()),"segmentation_code",hvBmTaoMaterialFileDTO.getSegmentationCode());
        return hvBmTaoMaterialFileService.list(queryWrapper);
    }
}
