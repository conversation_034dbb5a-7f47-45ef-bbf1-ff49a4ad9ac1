package com.hvisions.hiperbase.repository.schedule;

import com.hvisions.hiperbase.entity.schedule.HvBmCrewMember;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: HvBmCrewMemberRepository</p>
 * <p>Description: 班组成员</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface CrewMemberRepository extends JpaRepository<HvBmCrewMember, Integer> {

    /**
     * find by Crew id
     *
     * @param crewId crew id
     * @return list of crew
     */
    List<HvBmCrewMember> findAllByCrewId(int crewId);

    /**
     * 删除所有班组成员
     *
     * @param crewId 班组id
     */
    @Modifying
    @Query(value = "delete from HvBmCrewMember h where h.crewId = ?1")
    void deleteByCrewId(int crewId);

    /**
     * 根据用户id查询班组
     *
     * @param userId 用户id
     * @return 班组信息
     */
    List<HvBmCrewMember> findAllByUserId(int userId);
}
