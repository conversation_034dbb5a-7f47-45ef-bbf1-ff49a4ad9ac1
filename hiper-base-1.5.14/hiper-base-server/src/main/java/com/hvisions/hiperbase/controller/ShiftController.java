package com.hvisions.hiperbase.controller;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.schedule.dto.ShiftDTO;
import com.hvisions.hiperbase.service.schedule.ShiftService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: HvBmShiftController</p>
 * <p>Description: 班次控制器</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/23</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@Api(description = "班次控制器")
@RequestMapping("/shift")
public class ShiftController {

    @Autowired
    private ShiftService shiftService;

    /**
     * 创建班次
     *
     * @param shiftDTO 班次信息
     * @return 创建的班次id
     */
    @PostMapping("/createShift")
    @ApiOperation(value = "/创建班次")
    public int createShift(@RequestBody ShiftDTO shiftDTO) {
        return shiftService.createOrUpdateShift(shiftDTO);
    }

    /**
     * 根据班次id删除班次信息,会同时删除对应的排班信息
     *
     * @param id 班次id
     */
    @DeleteMapping("/deleteShiftById/{id}")
    @ApiOperation(value = "/根据班次id删除班次,会同时删除对应的排班信息")
    public void deleteShiftById(@PathVariable int id) {
        shiftService.deleteShiftById(id);
    }

    /**
     * 更新班次信息
     *
     * @param shiftDTO 班次信息
     * @return id
     */
    @PutMapping("/updateShift")
    @ApiOperation(value = "更新班次信息")
    public int updateShift(@RequestBody ShiftDTO shiftDTO) {
        return shiftService.createOrUpdateShift(shiftDTO);
    }

    /**
     * 根据车间id查询班次信息
     *
     * @param areaId 车间id
     * @param cellId 产线id
     * @return 班次信息列表
     */
    @GetMapping("/getShiftListByAreaIdAndCellId/{areaId}/{cellId}")
    @ApiOperation(value = "根据车间id,产线id查询班次信息,车间id为0返回通用车间班次，产线id为0返回车间班次")
    public List<ShiftDTO> getShiftListByAreaIdAndCellId(@PathVariable int areaId, @PathVariable int cellId) {
        return shiftService.getShiftListByAreaIdAndCellId(areaId, cellId);
    }

    /**
     * 根据班次id查询班次信息
     *
     * @param shiftId 班次id
     * @return 班次信息
     */
    @GetMapping("/getShiftListById/{shiftId}")
    @ApiOperation(value = "根据班次id查询班次信息")
    public ShiftDTO getShiftById(@PathVariable int shiftId) {
        return shiftService.getShiftById(shiftId);
    }

    /**
     * 根据班次编码查询班次信息
     *
     * @param areaId    车间id
     * @param cellId    产线id
     * @param shiftCode 班次编码
     * @return 班次信息
     */
    @GetMapping("/getShiftByShiftCode")
    @ApiOperation(value = "根据班次编码查询班次信息")
    public ShiftDTO getShiftByShiftCode(@RequestParam String shiftCode,
                                        @RequestParam(required = false, defaultValue = "0") Integer areaId,
                                        @RequestParam(required = false, defaultValue = "0") Integer cellId) {
        return shiftService.getShiftByShiftCode(shiftCode, areaId, cellId);
    }

    /**
     * 根据班次编码查询班次信息
     *
     * @param areaId        车间id
     * @param cellId        产线id
     * @param shiftCodeList 班次编码列表
     * @return 班次信息集合
     */
    @GetMapping("/getShiftByShiftCodeList")
    @ApiOperation(value = "根据班次编码列表查询班次信息")
    public List<ShiftDTO> getShiftByShiftCodeList(@RequestParam List<String> shiftCodeList,
                                                  @RequestParam(required = false, defaultValue = "0") Integer areaId,
                                                  @RequestParam(required = false, defaultValue = "0") Integer cellId) {
        return shiftService.getShiftByShiftCodeList(shiftCodeList, areaId, cellId);
    }

    /**
     * 通过编码删除班次信息
     *
     * @param shiftCode 班次信息
     * @param areaId    车间id
     * @param cellId    产线id
     */
    @DeleteMapping(value = "/deleteShiftByShiftCode")
    @ApiOperation(value = "通过编码删除班次信息")
    public void deleteShiftByShiftCode(@RequestParam String shiftCode,
                                       @RequestParam(required = false, defaultValue = "0") Integer areaId,
                                       @RequestParam(required = false, defaultValue = "0") Integer cellId) {
        shiftService.deleteShiftByShiftCode(shiftCode, areaId, cellId);
    }


    /**
     * 根据班次编码查询班次信息
     *
     * @param shiftCode 班次编码
     * @return 班次信息
     */
    @GetMapping("/getInfoShiftByShiftCode")
    @ApiOperation(value = "根据班次编码查询班次信息")
    public ShiftDTO getInfoShiftByShiftCode(@RequestParam("shiftCode") String shiftCode){
        return shiftService.getShiftInfoByShiftCode(shiftCode);
    }
}
