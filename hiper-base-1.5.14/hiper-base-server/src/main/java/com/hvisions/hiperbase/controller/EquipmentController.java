package com.hvisions.hiperbase.controller;

import com.google.common.collect.Maps;
import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.annotation.EnableFilter;
import com.hvisions.common.config.coderule.utils.SerialCodeUtils;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.ExtendColumnInfo;
import com.hvisions.common.dto.ExtendInfo;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.service.BaseExtendService;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.CodeRuleConsts;
import com.hvisions.hiperbase.entity.equipment.HvBmEquipment;
import com.hvisions.hiperbase.equipment.*;
import com.hvisions.hiperbase.equipment.location.LocationMsgDTO;
import com.hvisions.hiperbase.consts.EquipmentConsts;
import com.hvisions.hiperbase.service.equipment.EquipmentExcelService;
import com.hvisions.hiperbase.service.equipment.EquipmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: HvEquipmentExtendController</p>
 * <p>Description: 设备扩展属性</p>
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p>
 * <p>create date: 2018/10/29</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@Slf4j
@RequestMapping(value = "/equipment")
@Api(description = "设备接口")
public class EquipmentController {


    private final EquipmentService equipmentService;

    private final SerialCodeUtils serialCodeUtils;

    @Resource(name = "equipment_extend")
    BaseExtendService equipmentExtendService;

    private final EquipmentExcelService equipmentExcelService;

    @Autowired
    public EquipmentController(EquipmentService equipmentService,
                               SerialCodeUtils serialCodeUtils,
                               EquipmentExcelService equipmentExcelService) {
        this.equipmentService = equipmentService;
        this.serialCodeUtils = serialCodeUtils;
        this.equipmentExcelService = equipmentExcelService;
    }

    @GetMapping("/getEquipmentCode")
    @ApiOperation(value = "生成设备编码")
    public String getEquipmentCode() {
        return serialCodeUtils.generateCode(CodeRuleConsts.EQUIPMENT_CODE);
    }

    /**
     * 添加设备扩展属性
     *
     * @param extendColumnInfo 扩展属性信息
     */
    @EnableFilter
    @PostMapping("/createEquipmentColumn")
    @ApiOperation(value = "添加扩展属性")
    public void createEquipmentColumn(@RequestBody ExtendColumnInfo extendColumnInfo) {
        equipmentExtendService.addExtend(extendColumnInfo);
    }


    /**
     * 更新扩展属性
     *
     * @param extendInfo 扩展属性信息
     */
    @PostMapping("/updateEquipmentExtendInfo")
    @ApiOperation(value = "更新扩展属性")
    public void updateEquipmentExtendInfo(@RequestBody ExtendInfo extendInfo) {
        equipmentExtendService.updateExtendInfo(extendInfo);
    }

    /**
     * 删除设备扩展属性
     *
     * @param columnName 扩展属性名称
     */
    @EnableFilter
    @DeleteMapping("/deleteEquipmentColumnByColumnName/{columnName}")
    @ApiOperation(value = "删除扩展属性")
    public void deleteEquipmentColumnByColumnName(@PathVariable String columnName) {
        equipmentExtendService.dropExtend(columnName);
    }

    /**
     * 添加设备信息
     *
     * @param hvEquipmentDTO 设备信息
     * @return 添加后的设备id
     */
    @EnableFilter
    @PostMapping("/createEquipment")
    @ApiOperation(value = "添加设备")
    public EquipmentDTO createEquipment(@Valid @RequestBody EquipmentDTO hvEquipmentDTO) {
        return equipmentService.create(hvEquipmentDTO);
    }

    /**
     * 获取设备所有扩展字段信息
     *
     * @return 设备扩展字段信息
     */
    @EnableFilter
    @GetMapping("/getEquipmentExtendColumnInfo")
    @ApiOperation(value = "获取所有设备扩展字段信息")
    public List<ExtendColumnInfo> getEquipmentExtendColumnInfo() {
        return equipmentExtendService.getExtendColumnInfo();
    }

    /**
     * 删除设备信息
     *
     * @param id 设备id
     */
    @DeleteMapping("/deleteEquipmentById/{id}")
    @ApiOperation(value = "根据设备id删除设备")
    @Transactional(rollbackFor = Exception.class)
    public void deleteEquipmentById(@PathVariable int id) {
        equipmentService.deleteEquipmentById(id);
    }


    /**
     * 更新设备信息
     *
     * @param hvEquipmentDTO 设备信息
     * @return 设备id
     */
    @EnableFilter
    @PutMapping("/updateEquipment")
    @ApiOperation(value = "更新设备")
    public EquipmentDTO updateEquipment(@Valid @RequestBody EquipmentDTO hvEquipmentDTO) {
        return equipmentService.updateEquipment(hvEquipmentDTO);
    }
    /**
     * 根据设备code，以及设备名称模糊查询分页信息
     *
     * @param queryDTO 查询对象
     * @return 设备分页信息
     */
    @PostMapping("/getEquipmentPage")
    @ApiOperation(value = "根据设备名称或者设备编码还有设备类型id获取设备信息,如果名称，编码都传空值，那么默认获取根级别设备")
    public Page<EquipmentDTO> getEquipmentPage(@RequestBody EquipmentQueryDTO queryDTO) {
        return equipmentService.getEquipmentPage(queryDTO);
    }

    /**
     * 根据设备code，以及设备名称模糊查询分页信息
     *
     * @param queryDTO 查询对象
     * @return 设备分页信息
     */
    @Deprecated
    @PostMapping("/getEquipmentPageByNameOrCodeAndEquipmentTypeId")
    @ApiOperation(value = "根据设备名称或者设备编码还有设备类型id获取设备信息,如果名称，编码都传空值，那么默认获取根级别设备")
    public Page<EquipmentDTO> getEquipmentPageByNameOrCodeAndEquipmentTypeId(@RequestBody EquipmentQueryDTO queryDTO) {
        return equipmentService.getEquipmentPage(queryDTO);
    }

    /**
     * 分页查询
     *
     * @param equipmentQueryDTO 查询条件
     * @return 返回信息
     */
    @PostMapping("/getEquipmentListByQuery")
    @ApiOperation(value = "设备台账条件查询")
    public List<EquipmentDTO> getEquipmentListByQuery(@RequestBody EquipmentQueryDTO equipmentQueryDTO) {
        return equipmentService.getEquipmentList(equipmentQueryDTO);
    }

    /**
     * 分页查询
     *
     * @param equipmentQueryDTO 查询条件
     * @return 返回信息
     */
    @PostMapping("/getEquipmentDtoByQuery")
    @Deprecated
    @ApiOperation(value = "设备台账条件查询")
    public Page<EquipmentDTO> getEquipmentDtoByQuery(@RequestBody EquipmentQueryDTO equipmentQueryDTO) {
        return equipmentService.getEquipmentPage(equipmentQueryDTO);
    }

    /**
     * 查询所有设备信息
     *
     * @return 设备信息列表
     */
    @Deprecated
    @ApiOperation(value = "查询所有设备信息")
    @GetMapping(value = "/getAllEquipment")
    public List<EquipmentDTO> getAllEquipment() {
        return equipmentService.getAllEquipment();
    }


    /**
     * 根据设备code查询设备
     *
     * @param equipmentCode 设备编码
     * @return 设备信息
     */
    @ApiOperation(value = "根据设备code查询设备")
    @GetMapping(value = "/getEquipmentByCode/{equipmentCode}")
    public EquipmentDTO getEquipmentByCode(@PathVariable String equipmentCode) {
        return equipmentService.getEquipmentCodeByCode(equipmentCode);
    }

    /**
     * 根据设备父级id查询子级设备信息列表
     *
     * @param parentId 父级id
     * @return 设备信息列表
     */
    @Deprecated
    @GetMapping("/getEquipmentListByParentId/{parentId}")
    @ApiOperation(value = "根据设备父级id查询设备列表")
    public List<EquipmentDTO> getEquipmentListByParentId(@PathVariable int parentId) {
        EquipmentQueryDTO queryDTO = new EquipmentQueryDTO();
        queryDTO.setParentEquipmentId(parentId);
        return getEquipmentListByQuery(queryDTO);
    }

    /**
     * 根据设备id查询设备明细
     *
     * @param id 设备id
     * @return 设备信息
     */
    @Deprecated
    @GetMapping("/getEquipmentById/{id}")
    @ApiOperation(value = "根据设备id获取设备信息")
    public EquipmentDTO getEquipmentById(@PathVariable int id) {
        return equipmentService.findByEquipmentId(id);
    }


    /**
     * 根据设备id列表查询设备明细
     *
     * @param idList 设备id列表
     * @return 设备信息
     */
    @Deprecated
    @PostMapping("/getEquipmentByIdList")
    @ApiOperation(value = "根据设备id获取设备信息")
    public List<EquipmentDTO> getEquipmentById(@RequestBody List<Integer> idList) {
        return equipmentService.findByEquipmentIdList(idList);
    }

    /**
     * 根据设备类型查询设备信息列表
     *
     * @param id 设备类型id
     * @return 设备信息列表
     */
    @Deprecated
    @GetMapping("/getEquipmentListByEquipmentTypeId/{id}")
    @ApiOperation(value = "根据设备类型查询设备列表")
    public List<EquipmentDTO> getEquipmentListByEquipmentTypeId(@PathVariable int id) {
        return equipmentService.getEquipmentListByEquipmentTypeId(id);
    }

    /**
     * 根据CellId查询Cell下所属的设备信息
     *
     * @param id CellID
     * @return 设备信息列表
     */
    @Deprecated
    @GetMapping("/getEquipmentListByCellId/{id}")
    @ApiOperation(value = "根据cellId查询所在cell所有的设备信息")
    public List<EquipmentDTO> getEquipmentListByCellId(@PathVariable int id) {
        return equipmentService.getByCellId(id);
    }

    /**
     * 获取导入模板
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */

    @ApiResultIgnore
    @GetMapping(value = "/getEquipmentImportTemplateLink")
    @ApiOperation(value = "获取设备导入模板,支持超链接")
    public ResponseEntity<byte[]> getEquipmentImportTemplateLink() throws IOException, IllegalAccessException {
        return ExcelUtil.generateImportFile(EquipmentExcelDto.class,
                EquipmentConsts.EQUIPMENT_EXPORT_FILE_NAME,
                equipmentExtendService.getExtendColumnInfo());
    }

    /**
     * 获取导入模板
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @EnableFilter
    @ApiResultIgnore
    @GetMapping(value = "/getEquipmentImportTemplate")
    @ApiOperation(value = "获取设备导入模板")
    public ResultVO<ExcelExportDto> getEquipmentImportTemplate() throws IOException, IllegalAccessException {
        ResponseEntity<byte[]> result =
                ExcelUtil.generateImportFile(EquipmentExcelDto.class,
                        EquipmentConsts.EQUIPMENT_EXPORT_FILE_NAME,
                        equipmentExtendService.getExtendColumnInfo());
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(result.getBody());
        excelExportDto.setFileName(EquipmentConsts.EQUIPMENT_EXPORT_FILE_NAME);
        return ResultVO.success(excelExportDto);
    }

    /**
     * 导出所有设备信息
     *
     * @return 设备信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @Deprecated
    @GetMapping(value = "/exportEquipmentLink")
    @ApiOperation(value = "导出所有设备信息,支持超链接")
    public ResponseEntity<byte[]> exportEquipmentLink() throws IOException, IllegalAccessException {
        return equipmentService.exportEquipmentLink();
    }

    /**
     * 导出所有设备信息
     *
     * @return 设备信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @PostMapping(value = "/exportEquipmentLink")
    @ApiOperation(value = "根据条件导出所有设备信息,支持超链接")
    public ResponseEntity<byte[]> exportEquipmentLink(@RequestBody EquipmentQueryDTO equipmentQueryDTO) throws IOException,
            IllegalAccessException {
        return equipmentService.exportEquipmentLink(equipmentQueryDTO);
    }

    /**
     * 导出所有设备信息
     *
     * @return 设备信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @Deprecated
    @GetMapping(value = "/exportEquipment")
    @ApiOperation(value = "导出所有设备信息")
    public ResultVO<ExcelExportDto> exportEquipment() throws IOException, IllegalAccessException {
        return equipmentService.exportEquipment();
    }

    /**
     * 导出所有设备信息
     *
     * @return 设备信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @PostMapping(value = "/exportEquipment")
    @ApiOperation(value = "根据条件导出所有设备信息")
    public ResultVO<ExcelExportDto> exportEquipment(@RequestBody EquipmentQueryDTO equipmentQueryDTO) throws IOException, IllegalAccessException {
        return equipmentService.exportEquipment(equipmentQueryDTO);
    }


    /**
     * 添加Cell与设备关系
     *
     * @param cellEquipmentDTO 设备和Cell关系对象
     */
    @PutMapping(value = "/updateCellEquipmentRelation")
    @ApiOperation(value = "添加工厂建模Cell与设备的关系")
    public void updateCellEquipmentRelation(@RequestBody CellWithEquipmentDTO cellEquipmentDTO) {
        equipmentService.updateCellEquipmentRelation(cellEquipmentDTO);
    }

    /**
     * 更新Cell与设备关系
     *
     * @param cellEquipmentDTO 设备和Cell关系对象
     */
    @PutMapping(value = "/updateCellEquipment")
    @ApiOperation(value = "更新Cell与设备关系(带排序字段)")
    public void updateCellEquipment(@RequestBody CellEquipmentDTO cellEquipmentDTO) {
        equipmentService.updateCellEquipment(cellEquipmentDTO);
    }

    /**
     * 设备的归属关系
     *
     * @param equipmentId 设备id
     */
    @ApiOperation(value = "删除设备的Location归属关系")
    @DeleteMapping(value = "/deleteCellEquipmentRelation/{equipmentId}/{cellId}")
    public void deleteCellEquipmentRelation(@PathVariable int equipmentId, @PathVariable int cellId) {
        equipmentService.deleteCellEquipmentRelation(equipmentId, cellId);
    }

    /**
     * 导入所有
     *
     * @param file 设备信息文档
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    @PostMapping(value = "/importEquipment")
    @ApiOperation(value = "导入设备信息，如果code存在则更新，code不存在则新增")
    public ImportResult importEquipment(@RequestParam("file") MultipartFile file) throws IOException, ParseException, IllegalAccessException {
        return equipmentExcelService.importEquipment(file);
    }

    /**
     * 根据设备ID查询所在位置信息（集团，工厂，车间，产线）
     *
     * @param id 设备ID
     * @return 设备所在位置信息
     */
    @GetMapping(value = "/getLocationMsgDtoByEquipmentId/{id}")
    @ApiOperation(value = "根据设备ID查询所在位置信息（集团，工厂，车间，产线）")
    public LocationMsgDTO getLocationMsgDtoByEquipmentId(@PathVariable int id) {
        return equipmentService.getLocationMsgDtoByEquipmentId(id);
    }

    /**
     * 根据设备ID查询所在位置信息（集团，工厂，车间，产线）
     *
     * @param ids 设备Id列表
     * @return 设备所在位置信息列表
     */
    @PostMapping(value = "/getLocationMsgDtoListByEquipmentIds")
    @ApiOperation(value = "根据设备ID列表查询所在位置信息（集团，工厂，车间，产线）")
    public Map<Integer, LocationMsgDTO> getLocationMsgDtoListByEquipmentIds(@RequestBody List<Integer> ids) {
        HashMap<Integer, LocationMsgDTO> result = Maps.newHashMap();
        for (Integer id : ids) {
            LocationMsgDTO locationMsgDTO = equipmentService.getLocationMsgDtoByEquipmentId(id);
            result.put(id, locationMsgDTO);
        }
        return result;
    }


    /**
     * 根据产线ID查询产线下所有设备和子设备
     *
     * @param cellId 产线ID
     * @return 设备列表
     */
    @Deprecated
    @GetMapping(value = "/getAllEquipmentByCellId/{cellId}")
    @ApiOperation(value = "根据产线ID查询产线下所有设备和子设备")
    public List<EquipmentDTO> getAllEquipmentByCellId(@PathVariable int cellId) {
        return equipmentService.getAllEquipmentByCellId(cellId);
    }

    /**
     * 根据产线Id列表查询设备
     *
     * @param cellIdList 产线ID列表
     * @return 设备列表
     */
    @Deprecated
    @PostMapping(value = "/getAllEquipmentByCellIdList")
    @ApiOperation(value = "根据产线Id列表查询设备和子设备")
    public List<EquipmentDTO> getAllEquipmentByCellIdList(@RequestBody List<Integer> cellIdList) {
        return equipmentService.getAllEquipmentByCellIdList(cellIdList);
    }

    @PostMapping(value = "/copy")
    @ApiOperation(value = "复制设备")
    public void copy(@RequestBody CopyEquipmentDto equipmentDTO) {
        equipmentService.copy(equipmentDTO);
    }

    @PostMapping(value = "/getAllEquipmentsByCodeIn")
    @Deprecated
    @ApiOperation(value = "根据设备编码列表查询设备")
    public List<EquipmentDTO> getAllEquipmentsByCodeIn(@RequestBody List<String> equipmentCodes){
        return equipmentService.getAllEquipmentsByCodeIn(equipmentCodes);
    }

    @ApiOperation(value = "获取对应位置的设备")
    @GetMapping(value = "/getAllEquipmentByLocationId")
    public List<HvBmEquipment> getAllEquipmentByLocationId(@RequestParam Integer locationId){
       return equipmentService.getAllEquipmentByLocationId(locationId);
    }
}
