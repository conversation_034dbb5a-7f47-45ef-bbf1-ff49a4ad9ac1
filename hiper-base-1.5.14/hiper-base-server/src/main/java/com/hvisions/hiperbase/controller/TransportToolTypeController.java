package com.hvisions.hiperbase.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.EasyExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.entity.transportTool.HvBmTransportToolType;
import com.hvisions.hiperbase.service.transportTool.TransportToolTypeService;
import com.hvisions.hiperbase.transportTool.TransportToolTypeDTO;
import com.hvisions.hiperbase.transportTool.TransportToolTypeQueryDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>Title: TransportToolTypeController</p>
 * <p>Description: 运输工具类型控制器</p>
 */
@RestController
@RequestMapping("/transportToolType")
@Api(tags = "运输工具类型管理")
public class TransportToolTypeController {

    @Autowired
    private TransportToolTypeService transportToolTypeService;

    @GetMapping("/getTransportTypeList")
    @ApiOperation("获取所有运输工具类型列表")
    public List<HvBmTransportToolType> getTransportTypeList() {
        return transportToolTypeService.list();
    }

    @PostMapping("/getTransportTypePage")
    @ApiOperation("获取运输工具类型列表分页")
    public Page<TransportToolTypeDTO> getTransportTypePage(@RequestBody @ApiParam("查询参数") TransportToolTypeQueryDTO query) {
        return transportToolTypeService.getPage(query);
    }

    @GetMapping("getTransportToolTypeById/{id}")
    @ApiOperation("根据运输工具类型编号获取运输工具类型")
    public TransportToolTypeDTO getTransportToolTypeById(@PathVariable @ApiParam("运输工具类型编号") String id) {
        HvBmTransportToolType one = transportToolTypeService.getOne(new LambdaQueryWrapper<HvBmTransportToolType>().eq(HvBmTransportToolType::getTransportToolTypeCode, id));

        return DtoMapper.convert(one, TransportToolTypeDTO.class);
    }

    @PostMapping("createTransportType")
    @ApiOperation("创建运输工具类型")
    public ResultVO createTransportType(@RequestBody @ApiParam("运输工具类型信息") TransportToolTypeDTO transportToolTypeDTO, @UserInfo @ApiIgnore UserInfoDTO userInfo) {
        if (transportToolTypeService.checkHvBmTransportToolTypeCodesIsUnique(transportToolTypeDTO.getTransportToolTypeCode())) {
            return ResultVO.error(500, "运输工具类型编号" + transportToolTypeDTO.getTransportToolTypeCode() + "已存在");
        }
        System.out.println("=======================这里是用户信息"+userInfo);
        transportToolTypeDTO.setCreateTime(LocalDateTime.now());
        transportToolTypeDTO.setCreatorId(userInfo.getUserName());
        transportToolTypeDTO.setUpdateTime(LocalDateTime.now());
        transportToolTypeDTO.setUpdaterId(userInfo.getUserName());
        return ResultVO.success(transportToolTypeService.save(DtoMapper.convert(transportToolTypeDTO, HvBmTransportToolType.class)));
    }

    @PutMapping("/updateTransportToolType")
    @ApiOperation("更新运输工具类型")
    public ResultVO updateTransportToolType(@RequestBody @ApiParam("运输工具类型信息") TransportToolTypeDTO transportToolTypeDTO, @UserInfo @ApiIgnore UserInfoDTO userInfo) {
        if (!transportToolTypeService.checkHvBmTransportToolTypeCodesIsUnique(transportToolTypeDTO.getTransportToolTypeCode())) {
            return ResultVO.error(500, "运输工具类型编号" + transportToolTypeDTO.getTransportToolTypeCode() + "不存在");
        }
        transportToolTypeDTO.setUpdateTime(LocalDateTime.now());
        transportToolTypeDTO.setUpdaterId(userInfo.getUserName());
        return ResultVO.success(transportToolTypeService.update(DtoMapper.convert(transportToolTypeDTO, HvBmTransportToolType.class), new LambdaQueryWrapper<HvBmTransportToolType>().eq(HvBmTransportToolType::getTransportToolTypeCode,transportToolTypeDTO.getTransportToolTypeCode())));
    }

    @DeleteMapping("/deleteTransportToolType/{id}")
    @ApiOperation("删除运输工具类型")
    public ResultVO deleteTransportToolType(@PathVariable @ApiParam("运输工具类型ID") String id) {
        if (!transportToolTypeService.checkHvBmTransportToolTypeCodesIsUnique(id)) {
            return ResultVO.error(500, "运输工具类型编号" + id + "不存在");
        }
        return ResultVO.success(transportToolTypeService.remove(new LambdaQueryWrapper<HvBmTransportToolType>().eq(HvBmTransportToolType::getTransportToolTypeCode, id)));
    }

    /**
     * 下载模版
     *
     */
    @ApiOperation(value = "下载模版")
    @PostMapping(value = "/downloadTemplate")
    @ApiResultIgnore
    public ResultVO<ExcelExportDto> downloadTemplate() {
        List<HvBmTransportToolType> list = new ArrayList<>();
        return ResultVO.success(EasyExcelUtil.getExcel(list, HvBmTransportToolType.class, System.currentTimeMillis() + "运输工具类型主数据导入模版.xlsx"));

    }

    /**
     * 导出
     *
     * @param transportToolTypeDTO
     */
    @ApiOperation(value = "导出")
    @PostMapping(value = "/exportData")
    @ApiResultIgnore
    public ResultVO<ExcelExportDto> exportData(@RequestBody TransportToolTypeDTO transportToolTypeDTO) {
        List<HvBmTransportToolType> list =  transportToolTypeService.findListByCondition(DtoMapper.convert(transportToolTypeDTO,HvBmTransportToolType.class));
        return ResultVO.success(EasyExcelUtil.getExcel(list, HvBmTransportToolType.class, System.currentTimeMillis() + "运输工具类型主数据.xlsx"));
    }

    /**
     * 导入
     *
     * @param file 文件
     */
    @PostMapping("/import")
    @ApiOperation("导入")
    public ResultVO importData(MultipartFile file, @UserInfo @ApiIgnore UserInfoDTO userInfo) {

        return transportToolTypeService.importData(file, userInfo);

    }

}