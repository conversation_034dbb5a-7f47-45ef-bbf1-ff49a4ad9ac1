package com.hvisions.hiperbase.repository.equipment;

import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * <p>Title: EquipmentStatusRepository</p >
 * <p>Description: 设备状态</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/6</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Repository
public interface EquipmentStatusRepository extends JpaRepository<HvBmEquipmentStatus, Integer> {
    /**
     * 根据设备编号查询设备状态
     *
     * @param state 设备状态
     * @return 设备状态
     */
    Optional<HvBmEquipmentStatus> getOneByStatusCode(String state);

    /**
     * 根据状态类型查询设备状态
     *
     * @param type 状态类型
     * @return 设备状态列表
     */
    List<HvBmEquipmentStatus> findAllByType(Integer type);

    /**
     * 根据设备状态编码查询状态id
     *
     * @param defaultEquipmentState 默认设备状态编码
     * @return 设备状态
     */
    Optional<HvBmEquipmentStatus> findByStatusCode(String defaultEquipmentState);
}
