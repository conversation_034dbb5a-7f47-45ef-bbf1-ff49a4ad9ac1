package com.hvisions.hiperbase.configuration;

import com.hvisions.common.runner.SafetyCommandLineRunner;
import com.hvisions.hiperbase.entity.equipment.HvBmLocation;
import com.hvisions.hiperbase.repository.equipment.LocationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title: DataFix</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/11/19</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
public class DataFix extends SafetyCommandLineRunner {
    @Autowired
    private LocationRepository locationRepository;

    /**
     * Callback used to run the bean.
     *
     * @param args incoming main method arguments
     * @throws Exception on error
     */
    @Override
    public void callRunner(String... args) throws Exception {
        List<HvBmLocation> entities = locationRepository.findAllByIndexNumIsNull();
        for (HvBmLocation entity : entities) {
            entity.setIndexNum(1);
        }
        locationRepository.saveAll(entities);
    }
}









