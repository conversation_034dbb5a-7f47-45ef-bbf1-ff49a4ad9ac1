package com.hvisions.hiperbase.service.equipment;

import com.hvisions.hiperbase.equipment.EquipmentFileDTO;

import java.util.List;

/**
 * <p>Title: EquipmentFileService</p >
 * <p>Description: </p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2019/7/2</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface EquipmentFileService {


    /**
     * 新增设备文件关联
     *
     * @param hvEquipmentFileDTO 设备文件关联关系
     */
    void createEquipmentFile(EquipmentFileDTO hvEquipmentFileDTO);


    /**
     * 根据ID删除设备文件关联关系
     *
     * @param id 主键ID
     */
    void deleteEquipmentFileById(int id);

    /**
     * 根据设备ID删除设备文件关联关系
     *
     * @param hvEquipmentFileDTO 文件关联关系
     */
    void updateEquipmentFile(EquipmentFileDTO hvEquipmentFileDTO);

    /**
     * 根据设备id查询设备文件关联关系
     * @param equipmentId 设备ID
     * @return 设备文件关联关系
     */
    List<EquipmentFileDTO> findAllByEquipmentId(Integer equipmentId);
}
