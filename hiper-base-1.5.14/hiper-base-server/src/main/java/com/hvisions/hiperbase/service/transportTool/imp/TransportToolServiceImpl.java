package com.hvisions.hiperbase.service.transportTool.imp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.EasyExcelUtil;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.dao.transportTool.TransportToolMapper;
import com.hvisions.hiperbase.dao.transportTool.TransportToolTypeMapper;
import com.hvisions.hiperbase.entity.transportTool.HvBmTransportTool;
import com.hvisions.hiperbase.entity.transportTool.HvBmTransportToolType;
import com.hvisions.hiperbase.service.transportTool.TransportToolService;
import com.hvisions.hiperbase.transportTool.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <p>Title: TransportToolServiceImpl</p>
 * <p>Description: 运输工具主数据服务实现</p>
 */
@Service
public class TransportToolServiceImpl extends ServiceImpl<TransportToolMapper, HvBmTransportTool> implements TransportToolService {

    @Autowired
    private TransportToolMapper transportToolMapper;

    @Autowired
    private TransportToolTypeMapper transportToolTypeMapper;

    @Override
    public Page<TransportToolDTO> getPage(TransportToolQueryDTO transportToolQueryDTO) {
        return PageHelperUtil.getPage(transportToolMapper::getPage, transportToolQueryDTO);
    }

    @Override
    public Page<TransportToolStatusDTO> getStatusPage(TransportToolStatusQueryDTO queryDTO) {
        return PageHelperUtil.getPage(transportToolMapper::getStatusPage, queryDTO);
    }


    @Override
    public List<HvBmTransportTool> findListByCondition(HvBmTransportTool hvBmTransportTool) {
        return transportToolMapper.findListByCondition(hvBmTransportTool);
    }

    @Override
    public List<TransportToolStatusDTO> findStatusListByCondition(TransportToolStatusQueryDTO queryDTO) {
        return transportToolMapper.findStatusListByCondition(queryDTO);
    }

    @Override
    public ResultVO importData(MultipartFile file, UserInfoDTO userInfo) {
        List<HvBmTransportTool> HvBmTransportTools = EasyExcelUtil.getImport(file, HvBmTransportTool.class);
        //存储需要更新的数据
        List<HvBmTransportTool> modifyList = new ArrayList<>();
        //存储需要插入的数据
        List<HvBmTransportTool> addList = new ArrayList<>();

        for (HvBmTransportTool hvBmTransportTool : HvBmTransportTools) {
            if (hvBmTransportTool.getTransportToolCode() == null || hvBmTransportTool.getTransportToolCode().isEmpty()){
                break;
            }
//            检查运输工具编号是否存在
            boolean transportToolCodeIsExist = transportToolMapper.exists(new LambdaQueryWrapper<HvBmTransportTool>().eq(HvBmTransportTool::getTransportToolCode, hvBmTransportTool.getTransportToolCode()));
            if (transportToolCodeIsExist) {
//                其对应的运输工具类型是否存在
                boolean transportToolTypeCodeIsExist = transportToolTypeMapper.exists(new LambdaQueryWrapper<HvBmTransportToolType>().eq(HvBmTransportToolType::getTransportToolTypeCode, hvBmTransportTool.getTransportToolTypeCode()));
                if (!transportToolTypeCodeIsExist) {
                    return ResultVO.error(500, "导入的运输工具类型编号" + hvBmTransportTool.getTransportToolTypeCode() + "不存在");
                }
//                hvBmTransportTool.setUpdaterId(userInfo.getUserName());
//                hvBmTransportTool.setUpdateTime(LocalDateTime.now());
                modifyList.add(hvBmTransportTool);
            }else {
//                hvBmTransportTool.setCreatorId(userInfo.getUserName());
//                hvBmTransportTool.setCreateTime(LocalDateTime.now());
                addList.add(hvBmTransportTool);
            }
        }
//        批量插入
        this.saveBatch(addList);
//        循环更新
        for (HvBmTransportTool hvBmTransportTool : modifyList) {
            this.update(hvBmTransportTool, new UpdateWrapper<HvBmTransportTool>().eq("transport_tool_code", hvBmTransportTool.getTransportToolCode()));
        }

        return ResultVO.success("导入成功");
    }

    @Override
    public boolean checkHvBmTransportToolCodesIsUnique(String transportToolCode) {
        return transportToolMapper.exists(new LambdaQueryWrapper<HvBmTransportTool>().eq(HvBmTransportTool::getTransportToolCode, transportToolCode));
    }

    @Override
    public List<TransportToolListDTO> getTransportToolList() {
        List<TransportToolListDTO> transportToolListDTOS = new ArrayList<>();
        List<HvBmTransportTool> list = this.list();
        for (HvBmTransportTool hvBmTransportTool : list) {
            TransportToolListDTO transportToolListDTO = DtoMapper.convert(hvBmTransportTool, TransportToolListDTO.class);
            HvBmTransportToolType hvBmTransportToolType = transportToolTypeMapper.selectOne(new LambdaQueryWrapper<HvBmTransportToolType>().eq(HvBmTransportToolType::getTransportToolTypeCode, hvBmTransportTool.getTransportToolTypeCode()));
            transportToolListDTO.setTransportToolTypeDesc(hvBmTransportToolType.getTransportToolTypeDesc());
            transportToolListDTOS.add(transportToolListDTO);
        }
        return transportToolListDTOS;
    }
}