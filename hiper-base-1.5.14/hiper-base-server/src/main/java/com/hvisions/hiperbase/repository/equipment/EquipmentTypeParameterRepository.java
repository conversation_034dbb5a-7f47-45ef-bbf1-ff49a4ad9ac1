package com.hvisions.hiperbase.repository.equipment;

import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentTypeParameter;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: EquipmentParameterRepository</p >
 * <p>Description: 设备参数仓储层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/4/22</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface EquipmentTypeParameterRepository extends JpaRepository<HvBmEquipmentTypeParameter, Integer> {

    /**
     * 根据ID列表删除设备类型参数
     *
     * @param idList ID列表
     */
    void deleteByIdIn(List<Integer> idList);
}
