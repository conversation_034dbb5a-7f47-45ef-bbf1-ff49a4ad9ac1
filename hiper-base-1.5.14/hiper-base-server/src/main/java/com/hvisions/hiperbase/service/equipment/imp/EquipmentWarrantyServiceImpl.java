package com.hvisions.hiperbase.service.equipment.imp;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.hiperbase.dao.equipment.EquipmentWarrantyMapper;
import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentWarranty;
import com.hvisions.hiperbase.enums.WarrantTimeEnum;
import com.hvisions.hiperbase.enums.WarrantTypeEnum;
import com.hvisions.hiperbase.equipment.CopyEquipmentEvent;
import com.hvisions.hiperbase.equipment.DelEquipmentEvent;
import com.hvisions.hiperbase.equipment.EquipmentWarrantyDto;
import com.hvisions.hiperbase.repository.equipment.EquipmentWarrantyRepository;
import com.hvisions.hiperbase.service.equipment.EquipmentWarrantyService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>Title: EquipmentWarrantyServiceImpl</p >
 * <p>Description: </p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/5</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Service
public class EquipmentWarrantyServiceImpl implements EquipmentWarrantyService {

    private final EquipmentWarrantyRepository equipmentWarrantyRepository;
    private final EquipmentWarrantyMapper equipmentWarrantyMapper;

    @Autowired
    public EquipmentWarrantyServiceImpl(EquipmentWarrantyRepository equipmentWarrantyRepository, EquipmentWarrantyMapper equipmentWarrantyMapper) {
        this.equipmentWarrantyRepository = equipmentWarrantyRepository;
        this.equipmentWarrantyMapper = equipmentWarrantyMapper;
    }

    /**
     * 根据设备id删除设备保修信息
     *
     * @param equipmentWarrantyDto 设备保修信息
     */
    @Override
    public void addWarranty(EquipmentWarrantyDto equipmentWarrantyDto) {
        HvBmEquipmentWarranty hvBmEquipmentWarranty = DtoMapper.convert(equipmentWarrantyDto, HvBmEquipmentWarranty.class);
        equipmentWarrantyRepository.save(hvBmEquipmentWarranty);
    }

    /**
     * 根据设备id查询设备保修信息
     *
     * @param equipmentId 设备id
     * @return 设备保修信息
     */
    @Override
    public List<EquipmentWarrantyDto> findAllByEquipmentId(Integer equipmentId) {
        return equipmentWarrantyMapper.findWarentysByEquipmentId(equipmentId).stream()
                .peek(e -> {
                    e.setTypeName(WarrantTypeEnum.getValueByType(e.getType()));
                    e.setWarrantyTypeName(WarrantTimeEnum.getValueByType(e.getWarrantyType()));
                })
                .sorted(Comparator.comparing(EquipmentWarrantyDto::getCreateTime).reversed())
                .collect(Collectors.toList());
    }

    /**
     * 根据id查询设备保修信息
     *
     * @param equipmentWarrantyDto 设备保修信息
     */
    @Override
    public void updateWarranty(EquipmentWarrantyDto equipmentWarrantyDto) {
        HvBmEquipmentWarranty hvBmEquipmentWarranty = DtoMapper.convert(equipmentWarrantyDto, HvBmEquipmentWarranty.class);
        equipmentWarrantyRepository.save(hvBmEquipmentWarranty);
    }

    /**
     * 根据id删除设备保修信息
     *
     * @param id 设备保修信息id
     */
    @Override
    public void deleteWarranty(Integer id) {
        equipmentWarrantyRepository.deleteById(id);
    }

    /**
     * 复制设备事件处理
     *
     * @param event 复制设备事件
     */
    @EventListener(CopyEquipmentEvent.class)
    public void handleCopyEvent(CopyEquipmentEvent event) {
        List<HvBmEquipmentWarranty> equipmentWarranties = equipmentWarrantyRepository.findByEquipmentId(event.getCopyEquipmentId());
        if (CollectionUtils.isEmpty(equipmentWarranties)) {
            return;
        }
        List<HvBmEquipmentWarranty> copyWarranties = new ArrayList<>();
        for (HvBmEquipmentWarranty equipmentWarranty : equipmentWarranties) {
            HvBmEquipmentWarranty copyWarranty = SerializationUtils.clone(equipmentWarranty);
            copyWarranty.setId(null);
            copyWarranty.setEquipmentId(event.getEquipmentId());
            copyWarranties.add(copyWarranty);
        }
        equipmentWarrantyRepository.saveAll(copyWarranties);
    }

    /**
     * 删除设备事件处理
     *
     * @param event 删除设备事件
     */
    @Transactional(rollbackFor = Exception.class)
    @EventListener(DelEquipmentEvent.class)
    public void handleDelEvent(DelEquipmentEvent event) {
        equipmentWarrantyRepository.deleteAllByEquipmentId(event.getEquipmentId());
    }
}
