package com.hvisions.hiperbase.configuration;

import com.hvisions.common.runner.SafetyCommandLineRunner;
import com.hvisions.hiperbase.service.material.UnitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <p>Title: UnitInitialStarter</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/8/31</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
@Slf4j
public class UnitInitialStarter extends SafetyCommandLineRunner {
    @Autowired
    UnitService unitService;

    /**
     * Callback used to run the bean.
     *
     * @param args incoming main method arguments
     * @throws Exception on error
     */
    @Override
    public void callRunner(String... args) throws Exception {
        log.info("初始化物料单位开始");
        unitService.initialUnits();
        log.info("初始化物料单位结束");
    }
}









