package com.hvisions.hiperbase.service.schedule.imp;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.hiperbase.entity.schedule.HvBmBreakTime;
import com.hvisions.hiperbase.entity.schedule.HvBmSchedule;
import com.hvisions.hiperbase.entity.schedule.HvBmShift;
import com.hvisions.hiperbase.repository.schedule.BreakRepository;
import com.hvisions.hiperbase.repository.schedule.ScheduleRepository;
import com.hvisions.hiperbase.repository.schedule.ShiftRepository;
import com.hvisions.hiperbase.schedule.dto.BreakDTO;
import com.hvisions.hiperbase.schedule.dto.ShiftDTO;
import com.hvisions.hiperbase.schedule.dto.SysBaseDTO;
import com.hvisions.hiperbase.schedule.enums.ScheduleExceptionEnum;
import com.hvisions.hiperbase.service.schedule.ShiftService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>Title: ShiftServiceImp</p>
 * <p>Description: 班次实现类</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
public class ShiftServiceImpl implements ShiftService {
    private final ShiftRepository shiftRepository;
    private final ScheduleRepository scheduleRepository;
    private final BreakRepository breakRepository;

    @Autowired
    public ShiftServiceImpl(ShiftRepository shiftRepository,
                            ScheduleRepository scheduleRepository, BreakRepository breakRepository) {
        this.shiftRepository = shiftRepository;
        this.scheduleRepository = scheduleRepository;
        this.breakRepository = breakRepository;
    }


    /**
     * 新增或者创建一个班次信息
     *
     * @param shiftDTO 班次信息
     * @return 新建的班次id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int createOrUpdateShift(ShiftDTO shiftDTO) {
        HvBmShift shiftEntity = DtoMapper.convert(shiftDTO, HvBmShift.class);
        shiftEntity.setStartTime(shiftDTO.getStartTime());
        shiftEntity.setEndTime(shiftDTO.getEndTime());
        if (shiftDTO.getAreaId() == 0) {
            shiftEntity.setCellId(0);
        }
        HvBmShift save = shiftRepository.save(shiftEntity);
        //找到所有的班次信息。删除
        List<HvBmBreakTime> breaks = breakRepository.findAllByShiftId(save.getId());
        breakRepository.deleteAll(breaks);
        //班次信息重新生成
        if (shiftDTO.getBreakTimes() != null) {
            List<HvBmBreakTime> breakEntityList = DtoMapper.convertList(shiftDTO.getBreakTimes(), HvBmBreakTime.class);
            //设置班次id
            breakEntityList.forEach(t -> t.setShiftId(save.getId()));
            breakRepository.saveAll(breakEntityList);
        }

        return save.getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteShiftById(int shiftId) {
        //通过班次查询排班信息
        List<HvBmSchedule> schedules = scheduleRepository.findAllByShiftId(shiftId);
        if (schedules == null || schedules.size() != 0) {
            //当前班次已经使用删除失败
            throw new BaseKnownException(ScheduleExceptionEnum.CURRENT_TEAM_HAS_FAILED_TO_USE_DELETE);
        }
        //删除班次信息
        shiftRepository.deleteById(shiftId);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<ShiftDTO> getShiftListByAreaIdAndCellId(int areaId, int cellId) {
        List<HvBmShift> hvBmShifts = shiftRepository.findAllByAreaIdAndCellId(areaId, cellId);
        List<ShiftDTO> shiftDTOS = new ArrayList<>();
        for (HvBmShift bmShift : hvBmShifts) {
            ShiftDTO shiftDTO = DtoMapper.convert(bmShift, ShiftDTO.class);
            shiftDTOS.add(shiftDTO);
        }
        fillBreakInfo(shiftDTOS);
        //根据时间进行倒序输出
        return shiftDTOS.stream().sorted(Comparator.comparing(ShiftDTO::getStartTime)).collect(Collectors.toList());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ShiftDTO getShiftById(int shiftId) {
        return shiftRepository.findById(shiftId)
                .map(t -> DtoMapper.convert(shiftRepository.getOne(shiftId), ShiftDTO.class))
                .orElse(null);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ShiftDTO getShiftByShiftCode(String shiftCode, Integer areaId, Integer cellId) {
        HvBmShift shift = shiftRepository.findByShiftCodeAndAreaIdAndCellId(shiftCode, areaId, cellId);
        if (shift == null) {
            return null;
        }

        ShiftDTO result = this.getShiftById(shift.getId());
        fillBreakInfo(result);
        return result;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<ShiftDTO> getShiftByShiftCodeList(List<String> shiftCodeList, Integer areaId, Integer cellId) {
        List<ShiftDTO> shiftDTOS = new ArrayList<>();
        if (shiftCodeList == null || shiftCodeList.size() <= 0) {
            return shiftDTOS;
        }
        for (String code : shiftCodeList) {
            ShiftDTO shiftByShiftCode = this.getShiftByShiftCode(code, areaId, cellId);
            if (shiftByShiftCode != null) {
                shiftDTOS.add(shiftByShiftCode);
            }
        }
        fillBreakInfo(shiftDTOS);
        return shiftDTOS;
    }

    /**
     * 通过编码删除班次信息
     *
     * @param shiftCode 班次信息
     * @param areaId    车间id
     * @param cellId    产线id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteShiftByShiftCode(String shiftCode, Integer areaId, Integer cellId) {
        ShiftDTO shiftByShiftCode = this.getShiftByShiftCode(shiftCode, areaId, cellId);
        if (shiftByShiftCode == null) {
            return;
        }
        this.deleteShiftById(shiftByShiftCode.getId());
    }

    @Override
    public ShiftDTO getShiftInfoByShiftCode(String shiftCode) {
        return DtoMapper.convert(shiftRepository.findByShiftCode(shiftCode),ShiftDTO.class);
    }

    /**
     * 填充休息信息
     *
     * @param shiftDTO 班次信息
     */
    private void fillBreakInfo(ShiftDTO shiftDTO) {
        shiftDTO.setBreakTimes(new ArrayList<>());
        shiftDTO.getBreakTimes().addAll(breakRepository.findAllByShiftId(shiftDTO.getId())
                .stream().map(t -> DtoMapper.convert(t, BreakDTO.class))
                .collect(Collectors.toList()));
    }

    /**
     * 填充休息信息
     *
     * @param shiftDTOS 班次信息
     */
    private void fillBreakInfo(List<ShiftDTO> shiftDTOS) {
        List<HvBmBreakTime> breakTimes = breakRepository.findAllByShiftIdIn(
                shiftDTOS.stream()
                        .map(SysBaseDTO::getId)
                        .collect(Collectors.toList()));
        for (ShiftDTO shiftDTO : shiftDTOS) {
            shiftDTO.setBreakTimes(new ArrayList<>());
            List<BreakDTO> breaks = breakTimes.stream()
                    .filter(t -> t.getShiftId().equals(shiftDTO.getId()))
                    .map(t -> DtoMapper.convert(t, BreakDTO.class))
                    .collect(Collectors.toList());
            shiftDTO.getBreakTimes().addAll(breaks);
        }
    }
}
















