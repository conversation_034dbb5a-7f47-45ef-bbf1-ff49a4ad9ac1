package com.hvisions.hiperbase.service.equipment.imp;

import cn.hutool.core.lang.Assert;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.hiperbase.entity.equipment.HvBmEquipment;
import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentClass;
import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentClassProperty;
import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentProperty;
import com.hvisions.hiperbase.equipment.EquipmentPropertyDataType;
import com.hvisions.hiperbase.equipment.equipmentclass.EquipmentClassPropertyDTO;
import com.hvisions.hiperbase.repository.equipment.EquipmentClassPropertyRepository;
import com.hvisions.hiperbase.repository.equipment.EquipmentClassRepository;
import com.hvisions.hiperbase.repository.equipment.EquipmentPropertyRepository;
import com.hvisions.hiperbase.service.equipment.EquipmentClassPropertyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <p>Title: EquipmentClassPropertyServiceImpl</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/3/25</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
public class EquipmentClassPropertyServiceImpl implements EquipmentClassPropertyService {
    private EquipmentClassPropertyRepository propertyRepository;
    private EquipmentClassRepository classRepository;
    private EquipmentPropertyRepository equipmentPropertyRepository;

    @Autowired
    public EquipmentClassPropertyServiceImpl(EquipmentClassPropertyRepository propertyRepository, EquipmentClassRepository classRepository, EquipmentPropertyRepository equipmentPropertyRepository) {
        this.propertyRepository = propertyRepository;
        this.classRepository = classRepository;
        this.equipmentPropertyRepository = equipmentPropertyRepository;
    }

    /**
     * 创建属性
     *
     * @param propertyDTO 属性
     * @return 主键
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer create(EquipmentClassPropertyDTO propertyDTO) {
        Assert.isNull(propertyDTO.getId(), "新增不能传递id");
        Assert.notNull(propertyDTO.getDateType(), "数据类型不能为空");
        Assert.notNull(propertyDTO.getClassId(), "属性类型信息不能为空");
        HvBmEquipmentClass clazz = classRepository.findById(propertyDTO.getClassId())
                        .orElseThrow(()->new BaseKnownException("属性类型不存在"));
        Assert.isTrue(clazz.getProperties().stream().noneMatch(t -> t.getCode().equals(propertyDTO.getCode())),
                "属性已经存在");
        //如果类型存在，那么这个类型的属性添加这个属性
        HvBmEquipmentClassProperty property = DtoMapper.convert(propertyDTO, HvBmEquipmentClassProperty.class);
        setValue(property);
        property.setClazz(clazz);
        //所有配置了类型的设备，都需要添加这个属性
        for (HvBmEquipment equipment : clazz.getEquipments()) {
            HvBmEquipmentProperty equipmentProperty = DtoMapper.convert(property, HvBmEquipmentProperty.class);
            equipmentProperty.setId(null);
            equipmentProperty.setClassCode(clazz.getCode());
            equipmentProperty.setClassId(clazz.getId());
            equipmentProperty.setEquipment(equipment);
            equipmentPropertyRepository.save(equipmentProperty);
        }
        propertyRepository.save(property);
        return property.getId();
    }

    /**
     * 更新属性
     *
     * @param propertyDTO 属性
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(EquipmentClassPropertyDTO propertyDTO) {
        Assert.notNull(propertyDTO.getId(), "新增不能传递id");
        Optional<HvBmEquipmentClassProperty> pro = propertyRepository.findById(propertyDTO.getId());
        if (pro.isPresent()) {
            HvBmEquipmentClassProperty t = pro.get();
            t.setName(propertyDTO.getName());
            t.setValue(propertyDTO.getValue());
            setValue(t);
            propertyRepository.save(t);
            //如果是常量类型，需要更新所有配置了这个属性的设备属性
            List<HvBmEquipmentProperty> propertyList
                    = equipmentPropertyRepository.findAllByCodeAndClassCode(t.getCode(), t.getClazz().getCode());
            for (HvBmEquipmentProperty equipmentProperty : propertyList) {
                equipmentProperty.setName(propertyDTO.getName());
                if (t.getIsConst()) {
                    equipmentProperty.setValue(t.getValue());
                    equipmentProperty.setFloatValue(t.getFloatValue());
                    equipmentProperty.setStringValue(t.getStringValue());
                    equipmentProperty.setLongValue(t.getLongValue());
                    equipmentProperty.setName(t.getName());
                } else {
                    equipmentProperty.setName(t.getName());
                }
            }
            equipmentPropertyRepository.saveAll(propertyList);
        }
    }


    /**
     * 设置属性值
     *
     * @param propertyEntity 实体对象
     */
    private void setValue(HvBmEquipmentClassProperty propertyEntity) {
        switch (EquipmentPropertyDataType.getByCode(propertyEntity.getDateType())) {
            case STRING:
                propertyEntity.setStringValue(propertyEntity.getValue());
                break;
            case LONG:
                try {
                    propertyEntity.setLongValue(Long.parseLong(propertyEntity.getValue()));
                } catch (Exception ex) {
                    propertyEntity.setLongValue(0L);
                }
                break;
            case FLOAT:
                try {
                    propertyEntity.setFloatValue(Float.parseFloat(propertyEntity.getValue()));
                } catch (Exception ex) {
                    propertyEntity.setFloatValue(0F);
                }
                break;
            default:
                break;
        }
    }

    /**
     * 删除属性
     *
     * @param id 主键
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Integer id) {

        //删除属性。并且一并删除所有配置了这个属性的设备属性
        HvBmEquipmentClassProperty property = propertyRepository.findById(id)
                .orElse(null);
        if (property == null) {
            return;
        }
        HvBmEquipmentClass equipmentClass = classRepository.findById(property.getClazz().getId())
                .orElseThrow(() -> new BaseKnownException("数据异常，找不到属性对应的属性类型"));
        equipmentClass.getProperties().remove(property);
        List<HvBmEquipmentProperty> allByCodeAndClassCode = equipmentPropertyRepository.findAllByCodeAndClassCode(property.getCode(),
                property.getClazz().getCode());

        for (HvBmEquipmentProperty hvBmEquipmentProperty : allByCodeAndClassCode) {
            hvBmEquipmentProperty.getEquipment().getProperties().remove(hvBmEquipmentProperty);
        }
        equipmentPropertyRepository.deleteAll(allByCodeAndClassCode);
        propertyRepository.deleteById(property.getId());

    }

    /**
     * 获取属性
     *
     * @param id 设备类型id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<EquipmentClassPropertyDTO> findByEquipmentClassId(Integer id) {
        Optional<HvBmEquipmentClass> clazz = classRepository.findById(id);
        List<EquipmentClassPropertyDTO> result = new ArrayList<>();
        clazz.ifPresent(t -> {
            result.addAll(DtoMapper.convertList(t.getProperties(), EquipmentClassPropertyDTO.class));
        });
        return result;
    }
}









