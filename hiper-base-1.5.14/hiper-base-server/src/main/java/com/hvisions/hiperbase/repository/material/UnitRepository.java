package com.hvisions.hiperbase.repository.material;

import com.hvisions.hiperbase.entity.material.HvBmUnit;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: UnitRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/12/3</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface UnitRepository extends JpaRepository<HvBmUnit, Integer> {

    /**
     * 判断单位描述是否存在
     *
     * @param description 单位描述
     * @return true or false
     */
    boolean existsByDescription(String description);


    /**
     * 判断计量单位符号是否存在
     *
     * @param symbol 计量单位符号
     * @return true or false
     */
    boolean existsBySymbol(String symbol);

    /**
     * 根据Id获取计量单位信息
     *
     * @param id id
     * @return 计量单位信息
     */
    HvBmUnit getById(Integer id);

    /**
     * 根据计量单位符号获取计量单位信息
     *
     * @param symbol 计量单位符号
     * @return 计量单位信息
     */
    HvBmUnit getBySymbol(String symbol);

    /**
     * 根据Id列表查询计量单位信息
     *
     * @param idList Id列表
     * @return 计量单位信息列表
     */
    List<HvBmUnit> getAllByIdIn(List<Integer> idList);
}
