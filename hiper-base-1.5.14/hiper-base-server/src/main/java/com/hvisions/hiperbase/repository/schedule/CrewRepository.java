package com.hvisions.hiperbase.repository.schedule;

import com.hvisions.hiperbase.entity.schedule.HvBmCrew;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: HvBmCrewRepository</p>
 * <p>Description: 班组repo</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface CrewRepository extends JpaRepository<HvBmCrew, Integer> {

    /**
     * 根据车间代码查询所有班组
     *
     * @param areaId 车间id
     * @param cellId 产线id
     * @return 班组信息
     */
    List<HvBmCrew> findAllByAreaIdAndCellId(int areaId, int cellId);

    /**
     * 通过编码获取班组信息
     * @param crewCode 班组编码
     * @param areaId 产线
     * @param cellId 车间
     * @return 班组信息
     */
    HvBmCrew findByCrewCodeAndAreaIdAndAndCellId(String crewCode, Integer areaId, Integer cellId);

    /**
     * 通过编码获取班组信息
     * @return
     */
    HvBmCrew findByCrewCode(String crewCode);

}
