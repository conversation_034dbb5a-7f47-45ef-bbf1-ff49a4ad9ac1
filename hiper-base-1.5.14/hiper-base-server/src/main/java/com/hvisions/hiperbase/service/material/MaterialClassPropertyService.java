package com.hvisions.hiperbase.service.material;

import com.hvisions.hiperbase.materials.classdto.MaterialClassPropertyDTO;

import java.util.List;

/**
 * <p>Title: MaterialClassPropertyService</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/4/7</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
public interface MaterialClassPropertyService {

    /**
     * 创建属性
     *
     * @param propertyDTO 属性
     * @return 主键
     */
    Integer create(MaterialClassPropertyDTO propertyDTO);

    /**
     * 更新属性
     *
     * @param propertyDTO 属性
     */
    void update(MaterialClassPropertyDTO propertyDTO);

    /**
     * 删除属性
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 获取属性
     *
     * @param id 物料类型id
     * @return 物料属性类型属性列表
     */
    List<MaterialClassPropertyDTO> findByMaterialClassId(Integer id);
}