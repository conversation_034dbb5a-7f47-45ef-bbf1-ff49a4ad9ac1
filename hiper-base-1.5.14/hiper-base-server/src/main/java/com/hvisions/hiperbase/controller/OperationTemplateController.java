package com.hvisions.hiperbase.controller;

import com.hvisions.hiperbase.route.dto.Relation;
import com.hvisions.hiperbase.route.dto.TemplateDTO;
import com.hvisions.hiperbase.service.route.OperationTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>Title: OperationSkillController</p >
 * <p>Description:工艺操作模板 </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/08/31</p >
 *
 * <AUTHOR> leiming
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/operationTemplate")
@Api(description = "工艺操作模板")
public class OperationTemplateController {


    @Autowired
    OperationTemplateService service;

    /**
     * 绑定工艺操作模板
     *
     * @param relation 工序技能绑定对象信息
     */
    @PostMapping(value = "/bind")
    @ApiOperation(value = "绑定工艺操作模板")
    public void bind(@RequestBody @Valid Relation relation) {
        service.bind(relation);
    }


    /**
     * 根据关联关系删除模板
     *
     * @param relation 关联关系
     */
    @ApiOperation(value = "根据关联关系删除模板")
    @DeleteMapping(value = "/delete")
    public void deleteById(@RequestBody @Valid Relation relation) {
        service.deleteById(relation);
    }

    /**
     * 根据工艺操作ID查询工序模板
     *
     * @param operationId 工艺操作ID
     * @param platform    平台
     * @return 模板
     */
    @ApiOperation(value = "获取工艺操作的模板")
    @GetMapping(value = "/getOperationTemplate")
    public TemplateDTO getOperationTemplate(@RequestParam int operationId,
                                            @RequestParam int platform) {
        return service.getTemplate(operationId, platform);
    }

}