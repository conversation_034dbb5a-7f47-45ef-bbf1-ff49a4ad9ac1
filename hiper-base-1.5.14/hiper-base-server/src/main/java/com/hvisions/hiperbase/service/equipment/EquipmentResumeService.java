package com.hvisions.hiperbase.service.equipment;

import com.hvisions.hiperbase.equipment.EquipmentResumeDto;

/**
 * <p>Title: EquipmentResumeService</p >
 * <p>Description: 设备履历记录服务</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/5</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
public interface EquipmentResumeService {
    /**
     * 根据设备id查询设备履历记录
     *
     * @param equipmentId 设备id
     * @return 设备履历记录
     */
    EquipmentResumeDto findByEquipmentId(Integer equipmentId);
}
