package com.hvisions.hiperbase.controller;

import com.hvisions.common.dto.ExtendColumnInfo;
import com.hvisions.common.service.BaseExtendService;
import com.hvisions.hiperbase.bom.dto.BomItemDTO;
import com.hvisions.hiperbase.bom.dto.BomItemIncreaseDTO;
import com.hvisions.hiperbase.service.material.BomItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title: BomItemController</p >
 * <p>Description: bomItem控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/11/28</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@Slf4j
@RequestMapping(value = "/bomItem")
@Api(description = "BomItem控制器")
public class BomItemController {

    private final BomItemService bomItemService;

    @Resource(name = "bom_item_extend")
    BaseExtendService bomItemExtendService;

    @Autowired
    public BomItemController(BomItemService bomitemService) {
        this.bomItemService = bomitemService;
    }

    /**
     * 添加bomItem信息
     *
     * @param bomItemDTO 传入DTO对象
     * @return bomItem id
     */
    @PostMapping(value = "/createBomItem")
    @ApiOperation(value = "添加bomItem信息")
    public int createBomItem(@Valid @RequestBody BomItemDTO bomItemDTO) {
        return bomItemService.createBomItem(bomItemDTO);
    }

    /**
     * 修改bomItem信息
     *
     * @param bomItemDTO 传入DTO对象
     * @return bomItem id
     */
    @PutMapping(value = "/updateBomItem")
    @ApiOperation(value = "修改bomItem信息")
    public int updateBomItem(@Valid @RequestBody BomItemDTO bomItemDTO) {
        return bomItemService.updateBomItem(bomItemDTO);
    }

    /**
     * 添加bomItem扩展属性
     *
     * @param extendColumnInfo 扩展属性信息
     */
    @PostMapping("/createBomItemColumn")
    @ApiOperation(value = "添加bomItem扩展属性")
    public void createBomItemColumn(@RequestBody ExtendColumnInfo extendColumnInfo) {
        bomItemExtendService.addExtend(extendColumnInfo);
    }

    /**
     * 获取所有bomItem扩展字段信息
     *
     * @return bomItem扩展字段信息
     */
    @GetMapping(value = "/getAllBomItemExtend")
    @ApiOperation(value = "获取所有bomItem扩展字段信息")
    public List<ExtendColumnInfo> getAllBomItemExtend() {
        return bomItemExtendService.getExtendColumnInfo();
    }


    /**
     * 通过bomId查询bom信息
     *
     * @param bomId bomId
     * @return bomItem数据
     */
    @GetMapping("/getAllByBomId/{bomId}")
    @ApiOperation(value = "通过bomId查询bomItem信息")
    public List<BomItemIncreaseDTO> getAllByBomId(@PathVariable int bomId) {
        return bomItemService.getAllByBomId(bomId);
    }

    /**
     * 通过Id查询bomItem信息
     *
     * @param id bomItemId
     * @return bomItem数据
     */
    @GetMapping("/getBomItemById/{id}")
    @ApiOperation(value = "通过id查询bomItem信息")
    public BomItemIncreaseDTO getBomItemById(@PathVariable int id) {
        return bomItemService.getBomItemById(id);
    }


    /**
     * 删除bomItem信息
     *
     * @param id bomItemId
     */

    @DeleteMapping("/deleteBomItemById/{id}")
    @ApiOperation(value = "删除bomItem信息")
    @Transactional(rollbackFor = Exception.class)
    public void deleteBomItemById(@PathVariable int id) {
        bomItemService.deleteBomItem(id);
    }

    /**
     * 删除bomItem扩展属性
     *
     * @param columnName 扩展属性名称
     */
    @DeleteMapping("/deleteBomItemColumn/{columnName}")
    @ApiOperation(value = "删除BomItem扩展属性")
    @Transactional(rollbackFor = Exception.class)
    public void deleteBomItemColumn(@PathVariable String columnName) {
        bomItemExtendService.dropExtend(columnName);
    }


}
