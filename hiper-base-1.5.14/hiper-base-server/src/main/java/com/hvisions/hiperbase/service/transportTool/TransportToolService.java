package com.hvisions.hiperbase.service.transportTool;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.entity.transportTool.HvBmTransportTool;
import com.hvisions.hiperbase.entity.transportTool.HvBmTransportToolType;
import com.hvisions.hiperbase.equipment.RepairAreaQueryDTO;
import com.hvisions.hiperbase.transportTool.*;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>Title: TransportToolService</p>
 * <p>Description: 运输工具主数据服务</p>
 */
public interface TransportToolService extends IService<HvBmTransportTool> {
    Page<TransportToolDTO> getPage(TransportToolQueryDTO transportToolQueryDTO);
    Page<TransportToolStatusDTO> getStatusPage(TransportToolStatusQueryDTO queryDTO);
    List<HvBmTransportTool> findListByCondition(HvBmTransportTool hvBmTransportTool);
    List<TransportToolStatusDTO> findStatusListByCondition(TransportToolStatusQueryDTO queryDTO);
    ResultVO importData(MultipartFile file, UserInfoDTO userInfo);
    boolean checkHvBmTransportToolCodesIsUnique(String transportToolCode);
    List<TransportToolListDTO> getTransportToolList();
}