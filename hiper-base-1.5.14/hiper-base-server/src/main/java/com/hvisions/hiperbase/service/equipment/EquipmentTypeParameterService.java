package com.hvisions.hiperbase.service.equipment;

import com.hvisions.hiperbase.equipment.EquipmentTypeParameterDTO;
import com.hvisions.hiperbase.equipment.TypeParameterQueryDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <p>Title: EquipmentTypeParameterService</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/4/22</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface EquipmentTypeParameterService {


    /**
     * 新增设备类型参数
     *
     * @param parameterDTO 设备类型参数DTO
     * @return 设备类型参数ID
     */
    int createEquipmentParameter(EquipmentTypeParameterDTO parameterDTO);


    /**
     * 更新设备类型参数
     *
     * @param parameterDTO 设备类型参数DTO
     * @return 设备类型参数ID
     */
    int updateEquipmentParameter(EquipmentTypeParameterDTO parameterDTO);


    /**
     * 删除设备类型参数根据ID列表
     *
     * @param idList 设备类型参数ID列表
     */
    void deleteEquipmentParameter(List<Integer> idList);

    /**
     * 根据设备参数类型ID列表查询设备参数类型
     *
     * @param idList id列表
     * @return 设备参数类型列表
     */
    List<EquipmentTypeParameterDTO> getEquipmentParameterByIdList(List<Integer> idList);


    /**
     * 根据设备类型ID查询设备类型参数列表
     *
     * @param typeParameterQueryDTO 设备类型查询对象
     * @return 设备类型参数list
     */
    Page<EquipmentTypeParameterDTO> getEquipmentParameterByTypeId(TypeParameterQueryDTO typeParameterQueryDTO);


}
