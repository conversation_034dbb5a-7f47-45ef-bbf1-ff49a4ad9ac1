package com.hvisions.hiperbase.service.transportTool.imp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.EasyExcelUtil;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.dao.transportTool.TransportToolTypeMapper;
import com.hvisions.hiperbase.entity.material.HvBmFrame;
import com.hvisions.hiperbase.entity.transportTool.HvBmTransportToolType;
import com.hvisions.hiperbase.excel.HvBmFrameDownloadTemplate;
import com.hvisions.hiperbase.materials.dto.FrameTypeDTO;
import com.hvisions.hiperbase.service.transportTool.TransportToolTypeService;
import com.hvisions.hiperbase.transportTool.TransportToolTypeDTO;
import com.hvisions.hiperbase.transportTool.TransportToolTypeQueryDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <p>Title: TransportToolTypeServiceImpl</p>
 * <p>Description: 运输工具类型服务实现</p>
 */
@Service
public class TransportToolTypeServiceImpl extends ServiceImpl<TransportToolTypeMapper, HvBmTransportToolType>implements TransportToolTypeService {

    @Autowired
    TransportToolTypeMapper transportToolTypeMapper;

    @Override
    public Page<TransportToolTypeDTO> getPage(TransportToolTypeQueryDTO queryDTO) {
        return PageHelperUtil.getPage(transportToolTypeMapper::getPage, queryDTO);
    }

    @Override
    public List<HvBmTransportToolType> findListByCondition(HvBmTransportToolType hvBmTransportToolType) {
        return transportToolTypeMapper.findListByCondition(hvBmTransportToolType);
    }

    @Override
    public ResultVO importData(MultipartFile file, UserInfoDTO userInfo) {
        List<HvBmTransportToolType> HvBmTransportToolTypes = EasyExcelUtil.getImport(file, HvBmTransportToolType.class);
        //存储需要更新的数据
        List<HvBmTransportToolType> modifyList = new ArrayList<>();
        //存储需要插入的数据
        List<HvBmTransportToolType> addList = new ArrayList<>();

        for (HvBmTransportToolType hvBmTransportToolType : HvBmTransportToolTypes) {
            if (hvBmTransportToolType.getTransportToolTypeCode() == null || hvBmTransportToolType.getTransportToolTypeCode().isEmpty()){
                break;
            }
            boolean transportToolTypeCodeIsExist = transportToolTypeMapper.exists(new LambdaQueryWrapper<HvBmTransportToolType>().eq(HvBmTransportToolType::getTransportToolTypeCode, hvBmTransportToolType.getTransportToolTypeCode()));
            if (transportToolTypeCodeIsExist) {
                hvBmTransportToolType.setUpdaterId(userInfo.getUserName());
                hvBmTransportToolType.setUpdateTime(LocalDateTime.now());
                modifyList.add(hvBmTransportToolType);
            } else {
                hvBmTransportToolType.setCreatorId(userInfo.getUserName());
                hvBmTransportToolType.setCreateTime(LocalDateTime.now());
                addList.add(hvBmTransportToolType);
            }
        }
//        批量插入
        this.saveBatch(addList);
//        循环更新
        for (HvBmTransportToolType hvBmTransportToolType : modifyList) {
            this.update(hvBmTransportToolType, new LambdaQueryWrapper<HvBmTransportToolType>().eq(HvBmTransportToolType::getTransportToolTypeCode,hvBmTransportToolType.getTransportToolTypeCode()));
        }

        return ResultVO.success("导入成功");
    }

    @Override
    public boolean checkHvBmTransportToolTypeCodesIsUnique(String transportToolTypeCode) {
        return transportToolTypeMapper.exists(new LambdaQueryWrapper<HvBmTransportToolType>().eq(HvBmTransportToolType::getTransportToolTypeCode, transportToolTypeCode));
    }

}