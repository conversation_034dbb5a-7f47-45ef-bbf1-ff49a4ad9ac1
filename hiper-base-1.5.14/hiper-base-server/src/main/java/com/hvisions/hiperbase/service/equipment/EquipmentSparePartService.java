package com.hvisions.hiperbase.service.equipment;

import com.hvisions.hiperbase.equipment.EquipmentSparePartDto;
import com.hvisions.hiperbase.equipment.EquipmentSparePartQuery;

import java.util.List;

/**
 * <p>Title: EquipmentSparePartService</p >
 * <p>Description: 设备备件</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/9</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
public interface EquipmentSparePartService {
    /**
     * 新增设备备件
     *
     * @param equipmentSparePartDto 设备备件信息
     */
    void addSparePart(EquipmentSparePartDto equipmentSparePartDto);

    /**
     * 根据设备id查询设备备件
     *
     * @param query 查询条件
     * @return 设备备件列表
     */
    List<EquipmentSparePartDto> findAllByEquipmentId(EquipmentSparePartQuery query);

    /**
     * 修改设备备件
     *
     * @param equipmentSparePartDto 设备备件信息
     */
    void updateSparePart(EquipmentSparePartDto equipmentSparePartDto);

    /**
     * 删除设备备件
     *
     * @param id 设备备件id
     */
    void deleteSparePart(Integer id);

    /**
     * 批量新增备件
     *
     * @param equipmentSparePartDtos 备件信息
     * @param equipmentId            设备id
     */

    void batchAddSparePart(Integer equipmentId, List<EquipmentSparePartDto> equipmentSparePartDtos);
}
