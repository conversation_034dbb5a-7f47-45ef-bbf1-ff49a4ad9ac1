package com.hvisions.hiperbase.repository.schedule;

import com.hvisions.hiperbase.entity.schedule.HvBmSchedule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>Title: HvBmScheduleRepository</p>
 * <p>Description: 排产信息repo</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface ScheduleRepository extends JpaRepository<HvBmSchedule, Integer> {


    /**
     * 通过班组id 查询
     *
     * @param crewId 班组id
     * @return 排班计划信息
     */
    List<HvBmSchedule> findAllByCrewId(Integer crewId);


    /**
     * 通过班次获取排班信息
     *
     * @param shiftId 班次信息
     * @return 排班信息
     */
    List<HvBmSchedule> findAllByShiftId(Integer shiftId);


    /**
     * 获取当天所有的排班
     *
     * @param areaId 车间
     * @param cellId 产线
     * @param date   日期
     * @return 排班信息
     */
    List<HvBmSchedule> findAllByCellIdAndAreaIdAndScheduleDate(int areaId, int cellId, LocalDate date);

    /**
     * 根据车间和开始结束时间查询
     *
     * @param areaId    车间id
     * @param cellId    产线id
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 排班计划信息
     */
    List<HvBmSchedule> getAllByAreaIdAndCellIdAndScheduleDateBetween(int areaId, int cellId, LocalDate beginTime, LocalDate endTime);

    /**
     * 删除车间时间段内的所有排班信息
     *
     * @param areaId    车间id
     * @param cellId    产线id
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 排班信息列表
     */
    @Query(value = "select * from hv_bm_schedule  where area_id = ?1 and cell_id = ?2 and schedule_date between ?3 " +
            "and ?4",
            nativeQuery = true)
    List<HvBmSchedule> findAllByDate(int areaId, int cellId, LocalDate beginTime, LocalDate endTime);

    /**
     * 查询当时的排班计划
     *
     * @param time   时间
     * @param areaId 车间id
     * @param cellId 产线id
     * @return 排班计划信息
     */
    @Query(value = "select * from hv_bm_schedule where start_time <= ?1 and end_time > ?1 and area_id = ?2 and cell_id = ?3 ", nativeQuery = true)
    List<HvBmSchedule> findByTime(LocalDateTime time, int areaId, int cellId);

    /**
     * 根据时间段查询
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param areaId    车间id
     * @param cellId    产线id
     * @return 排班信息
     */
    @Query(value = "select *  from hv_bm_schedule  where (start_time between ?1 and ?2) and area_id = ?3 and cell_id = ?4", nativeQuery = true)
    List<HvBmSchedule> findByTimes(LocalDateTime startTime, LocalDateTime endTime, int areaId, int cellId);
}
