package com.hvisions.hiperbase.repository.route;

import com.hvisions.hiperbase.entity.route.HvBmOperation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: OperationRepository</p>
 * <p>Description: 操作步骤仓储</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/12/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface OperationRepository extends JpaRepository<HvBmOperation, Integer> {

    /**
     * 查询分页数据
     *
     * @param code     编码
     * @param name     名称
     * @param pageable 分页信息
     * @return 分页数据
     */
    Page<HvBmOperation> findAllByOperationCodeContainingAndOperationNameContaining(String code, String name, Pageable pageable);


    /**
     * 工艺操作编码查询
     *
     * @param operationCode 工艺操作编码
     * @return 工艺操作
     */
    HvBmOperation findByOperationCodeEquals(String operationCode);

    /**
    * 根据工艺类型id查询
     *
     * @param type  节点
     * @return 工艺操作
     */
    List<HvBmOperation> findAllByOperationType(int type);

    /**
     * 根据编码，名称分页查询
     * @param code 编码
     * @param name 名称
     * @return 分页数据
     */
    Page<HvBmOperation> findAllByOperationCodeContainsAndOperationNameContains(String code, String name, Pageable pageable);

    /**
     * 查询分页数据
     *
     * @param code     编码
     * @param name     名称
     * @param type     名称
     * @param pageable 分页信息
     * @return 分页数据
     */
    Page<HvBmOperation> findAllByOperationCodeContainingAndOperationNameContainingAndOperationTypeEquals(String code, String name, int type, Pageable pageable);
}









