package com.hvisions.hiperbase.controller;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.hiperbase.bom.dto.UnitDTO;
import com.hvisions.hiperbase.materials.dto.UnitQuery;
import com.hvisions.hiperbase.service.material.UnitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title: UnitController</p >
 * <p>Description: 计量单位控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/12/3</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/unit")
@Slf4j
@Api(description = "计量单位控制器")
public class UnitController {

    private final UnitService unitService;

    @Autowired
    public UnitController(UnitService unitService) {
        this.unitService = unitService;
    }

    /**
     * 获取所有计量单位信息
     *
     * @return 查询信息
     */
    @GetMapping(value = "/getAllUnit")
    @ApiOperation(value = "获取所有计量单位信息")
    public List<UnitDTO> getAllUnit() {
        return unitService.findAll();
    }

    /**
     * 获取所有计量单位信息
     *
     * @return 查询信息
     */
    @GetMapping(value = "/allUnit")
    @ApiOperation(value = "获取所有计量单位信息")
    public List<UnitDTO> getAllUnits() {
        List<UnitDTO> unitList = unitService.findAll();
        return DtoMapper.convertList(unitList, UnitDTO.class);
    }

    /**
     * 新增计量单位
     *
     * @param unitDTO 传入对象
     * @return ID
     */
    @PostMapping(value = "/createUnit")
    @ApiOperation(value = "新增计量单位")
    public int createUnit(@Valid @RequestBody UnitDTO unitDTO) {
        return unitService.createUnit(unitDTO);
    }

    /**
     * 更新计量单位
     *
     * @param unitDTO 传入对象
     * @return ID
     */
    @PutMapping(value = "/updateUnit")
    @ApiOperation(value = "更新计量单位")
    public int updateUnit(@Valid @RequestBody UnitDTO unitDTO) {

        return unitService.updateUnit(unitDTO);
    }

    /**
     * 删除计量单位
     *
     * @param id 计量单位id
     */
    @DeleteMapping(value = "/deleteUnit/{id}")
    @ApiOperation(value = "删除计量单位")
    public void deleteUnit(@PathVariable Integer id) {
        unitService.deleteUnit(id);
    }

    /**
     * 根据ID查询unit
     *
     * @param id id
     * @return 数据
     */
    @GetMapping(value = "/getUnitById/{id}")
    @ApiOperation(value = "根据ID查询unit")
    public UnitDTO getUnitById(@PathVariable Integer id) {
        return unitService.getUnitById(id);
    }


    /**
     * 根据单位符号查询计量单位信息
     *
     * @param unitDTO 计量单位对象
     * @return 分页信息
     */
    @PostMapping(value = "/findUnitBySymbolOrDescription")
    @ApiOperation("根据单位符号查询计量单位信息")
    public Page<UnitDTO> findUnitBySymbolOrDescription(@RequestBody UnitQuery unitDTO) {
        return unitService.findUnitBySymbolOrDescription(unitDTO);
    }

}
