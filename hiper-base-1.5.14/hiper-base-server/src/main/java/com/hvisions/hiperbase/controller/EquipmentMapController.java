package com.hvisions.hiperbase.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.hvisions.hiperbase.equipment.map.EquipmentHeaderQuery;
import com.hvisions.hiperbase.equipment.map.EquipmentLocationDto;
import com.hvisions.hiperbase.equipment.map.EquipmentMapDto;
import com.hvisions.hiperbase.equipment.map.EquipmentMapHeaderDto;
import com.hvisions.hiperbase.service.equipment.EquipmentMapService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title: EquipmentMapController</p >
 * <p>Description: 设备地图</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/6/23</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Api(tags = "设备地图")
@RequestMapping("/equipmentMap")
@RestController
public class EquipmentMapController {

    private final EquipmentMapService equipmentMapService;

    @Autowired
    public EquipmentMapController(EquipmentMapService equipmentMapService) {
        this.equipmentMapService = equipmentMapService;
    }

    @DeleteMapping("/deleteHeader/{id}")
    @ApiOperation("删除设备地图数据")
    public void deleteHeader(@PathVariable(value = "id") Integer id) {
        equipmentMapService.deleteHeader(id);
    }

    @PostMapping("/findAllHeaderByQuery")
    @ApiOperation("根据关键字查询头表数据")
    public List<EquipmentMapHeaderDto> findAllHeaderByQuery(@RequestBody EquipmentHeaderQuery query) {
        return equipmentMapService.findAllHeaderByQuery(query);
    }

    @GetMapping("/findDetailByHeaderId/{headerId}")
    @ApiOperation("根据地id查询地图详情")
    public EquipmentMapDto findDetailByHeaderId(@PathVariable(value = "headerId") Integer headerId) {
        return equipmentMapService.findDetailByHeaderId(headerId);
    }

    @PostMapping("/save")
    @ApiOperation("修改地图")
    public void save(@Valid @RequestBody EquipmentMapDto equipmentMapDto,
                     @RequestParam(value = "forceUpdate", defaultValue = "false", required = false) Boolean forceUpdate)
            throws JsonProcessingException {
        equipmentMapService.save(equipmentMapDto, forceUpdate);
    }

    @GetMapping("/findAll")
    @ApiOperation("查询所有地图数据")
    public List<EquipmentMapDto> findAll() {
        return equipmentMapService.findAll();
    }

    @GetMapping("/findByEquipmentId/{equipmentId}")
    @ApiOperation("根据设备id查询地图数据")
    public EquipmentLocationDto findByEquipmentId(@PathVariable(value = "equipmentId") Integer equipmentId) {
        return equipmentMapService.findByEquipmentId(equipmentId);
    }
}
