package com.hvisions.hiperbase.controller;

import com.hvisions.hiperbase.equipment.EquipmentStatusDto;
import com.hvisions.hiperbase.service.equipment.EquipmentStatusService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: EquipmentStastusController</p >
 * <p>Description: 设备状态</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/10</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@RestController
@RequestMapping("/equipmentStatus")
@Api(tags = "设备状态控制器")
public class EquipmentStatusController {

    private final EquipmentStatusService equipmentStatusService;

    @Autowired
    public EquipmentStatusController(EquipmentStatusService equipmentStatusService) {
        this.equipmentStatusService = equipmentStatusService;
    }

    @ApiOperation(value = "根据状态类型获取设备状态")
    @GetMapping("/getByType/{type}")
    public List<EquipmentStatusDto> getByType(@PathVariable Integer type) {
        return equipmentStatusService.getByType(type);
    }

    @ApiOperation(value = "查询所有设备状态")
    @GetMapping("/getAll")
    public List<EquipmentStatusDto> getAll() {
        return equipmentStatusService.getAll();
    }

    @ApiOperation(value = "新增设备状态")
    @PostMapping("/add")
    public void addEquipmentStatus(@RequestBody EquipmentStatusDto equipmentStatusDto) {
        equipmentStatusService.addEquipmentStatus(equipmentStatusDto);
    }

    @ApiOperation(value = "修改设备状态")
    @PutMapping("/update")
    public void updateEquipmentStatus(@RequestBody EquipmentStatusDto equipmentStatusDto) {
        equipmentStatusService.updateEquipmentStatus(equipmentStatusDto);
    }

    @ApiOperation(value = "删除设备状态")
    @DeleteMapping("/delete/{id}")
    public void deleteEquipmentStatus(@PathVariable Integer id) {
        equipmentStatusService.deleteEquipmentStatus(id);
    }
}
