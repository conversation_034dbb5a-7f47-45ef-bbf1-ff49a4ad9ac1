package com.hvisions.hiperbase.controller;

import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.dao.material.MaterialPointAreaMapper;
import com.hvisions.hiperbase.entity.material.HvBmMaterialPointArea;
import com.hvisions.hiperbase.materials.dto.MaterialPointAreaDTO;
import com.hvisions.hiperbase.materials.dto.MaterialPointAreaQueryDTO;
import com.hvisions.hiperbase.materials.dto.MaterialPointDTO;
import com.hvisions.hiperbase.service.material.MaterialPointAreaService;
import com.hvisions.hiperbase.service.material.MaterialPointService;
import com.hvisions.thirdparty.common.dto.LineSchedulingDTO;
import com.hvisions.wms.client.WaresLocationClient;
import com.hvisions.wms.client.WaresLocationMaterialPointClient;
import com.hvisions.wms.dto.location.WaresLocationMaterialPointSynchronizationDTO;
import com.hvisions.wms.enums.PageControllerTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.data.domain.Page;
import springfox.documentation.annotations.ApiIgnore;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/materialPointArea")
@Api(description = "料点区域")
public class MaterialPointAreaController {

    private MaterialPointAreaService materialPointAreaService;

    private MaterialPointAreaMapper materialPointAreaMapper;

    @Autowired
    private WaresLocationMaterialPointClient waresLocationMaterialPointClient;
    @Autowired
    private MaterialPointService materialPointService;

    @Autowired
    public MaterialPointAreaController(MaterialPointAreaService materialPointAreaService,
                                       MaterialPointAreaMapper materialPointAreaMapper) {
        this.materialPointAreaService = materialPointAreaService;
        this.materialPointAreaMapper = materialPointAreaMapper;
    }

    /**
     * 查询料点区域
     *
     * @return 结果
     */
    @ApiOperation(value = "获取所有料点区域")
    @GetMapping("/getAllMaterialPointArea")
    public List<MaterialPointAreaDTO> getAllMaterialPointArea() {
        return materialPointAreaService.list().stream()
                .map(t -> DtoMapper.convert(t, MaterialPointAreaDTO.class))
                .collect(Collectors.toList());
    }

    /**
     * 分页查询料点区域
     *
     * @param dto 查询信息
     * @return 分页结果
     */
    @ApiOperation(value = "分页条件查询料点区域")
    @PostMapping("/queryAllMaterialPointArea")
    public Page<MaterialPointAreaDTO> queryMaterialPointArea(@RequestBody MaterialPointAreaQueryDTO dto) {
        return PageHelperUtil.getPage(this.materialPointAreaMapper::queryMaterialPointArea, dto);
    }

    /**
     * 通过ID查询料点区域信息
     *
     * @param materialPointAreaId 料点区域id
     * @return 料点区域信息
     */
    @GetMapping(value = "/getMaterialPointArea/{id}")
    @ApiOperation(value = "通过id查询料点区域信息")
    public MaterialPointAreaDTO getMaterialPointAreaById(@PathVariable("id") int materialPointAreaId) {
        return materialPointAreaService.getMaterialPointAreaById(materialPointAreaId);
    }

    /**
     * 新增料点区域信息
     *
     * @param materialPointAreaDTO 传入的对象
     * @return 添加后的实体Id
     */
    @ApiOperation(value = "新增料点区域")
    @PostMapping(value = "/createMaterialPointArea")
    @Transactional(rollbackFor = Exception.class)
    public boolean createMaterialPointArea(
            @Validated @RequestBody final MaterialPointAreaDTO materialPointAreaDTO,
            @UserInfo @ApiIgnore UserInfoDTO userInfo) {
        HvBmMaterialPointArea materialPointArea = DtoMapper.convert(materialPointAreaDTO, HvBmMaterialPointArea.class);
        materialPointArea.setCreatorId(userInfo.getId());
        materialPointArea.setCreateTime(LocalDateTime.now());
        //同步至wms模块
        WaresLocationMaterialPointSynchronizationDTO waresLocationMaterialPointSynchronizationDTO = new WaresLocationMaterialPointSynchronizationDTO();
        waresLocationMaterialPointSynchronizationDTO.setWarehouseAreaCode(materialPointAreaDTO.getPointAreaCode());
        waresLocationMaterialPointSynchronizationDTO.setWarehouseAreaName(materialPointAreaDTO.getPointAreaName());
        List<WaresLocationMaterialPointSynchronizationDTO> dtos = new ArrayList<>();
        dtos.add(waresLocationMaterialPointSynchronizationDTO);
        ResultVO<Boolean> resultVO = waresLocationMaterialPointClient.synchronizationBaseModule(dtos, PageControllerTypeEnum.ADDorEDIT.getCode());
        if(!resultVO.isSuccess()){
            throw new BaseKnownException("同步base模块异常,请联系管理员");
        }
        return materialPointAreaService.save(DtoMapper.convert(materialPointArea, HvBmMaterialPointArea.class));
    }

    /**
     * 修改料点区域
     *
     * @param materialPointAreaDTO 传入的对象
     * @return 添加后的实体Id
     */
    @ApiOperation(value = "更新料点区域信息")
    @PutMapping(value = "/updateMaterialPointArea")
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMaterialPointArea(
            @Validated @RequestBody final MaterialPointAreaDTO materialPointAreaDTO,
            @UserInfo @ApiIgnore UserInfoDTO userInfo) {

        HvBmMaterialPointArea materialPointArea = DtoMapper.convert(materialPointAreaDTO, HvBmMaterialPointArea.class);
        materialPointArea.setUpdaterId(userInfo.getId());
        materialPointArea.setUpdateTime(LocalDateTime.now());
        //同步至wms模块
        WaresLocationMaterialPointSynchronizationDTO waresLocationMaterialPointSynchronizationDTO = new WaresLocationMaterialPointSynchronizationDTO();
        waresLocationMaterialPointSynchronizationDTO.setWarehouseAreaCode(materialPointAreaDTO.getPointAreaCode());
        waresLocationMaterialPointSynchronizationDTO.setWarehouseAreaName(materialPointAreaDTO.getPointAreaName());
        List<WaresLocationMaterialPointSynchronizationDTO> dtos = new ArrayList<>();
        dtos.add(waresLocationMaterialPointSynchronizationDTO);
        ResultVO<Boolean> resultVO = waresLocationMaterialPointClient.synchronizationBaseModule(dtos, PageControllerTypeEnum.ADDorEDIT.getCode());
        if(!resultVO.isSuccess()){
            throw new BaseKnownException("同步base模块异常,请联系管理员");
        }
        return materialPointAreaService.updateById(materialPointArea);
    }

    /**
     * 删除料点区域
     *
     * @param materialPointAreaId 料点区域id
     */
    @ApiOperation(value = "删除料点区域")
    @DeleteMapping(value = "/deleteMaterialPointAreaById/{id}")
    @Transactional(rollbackFor = Exception.class)
    public void deleteMaterialPointAreaById(@PathVariable("id") int materialPointAreaId) {
        //判断料点区域下面没有绑定料点数据
        HvBmMaterialPointArea materialPointArea = materialPointAreaService.getById(materialPointAreaId);
        MaterialPointDTO materialPointDTO = materialPointService.getMaterialPointByPointAreaCode(materialPointArea.getPointAreaCode());
        if(materialPointDTO != null){
            throw new BaseKnownException("请先删除该料点区域绑定的料点");
        }
        WaresLocationMaterialPointSynchronizationDTO waresLocationMaterialPointSynchronizationDTO = new WaresLocationMaterialPointSynchronizationDTO();
        waresLocationMaterialPointSynchronizationDTO.setWarehouseAreaCode(materialPointArea.getPointAreaCode());
        List<WaresLocationMaterialPointSynchronizationDTO> dtos = new ArrayList<>();
        dtos.add(waresLocationMaterialPointSynchronizationDTO);
        ResultVO<Boolean> resultVO = waresLocationMaterialPointClient.synchronizationBaseModule(dtos, PageControllerTypeEnum.DELETE.getCode());
        if(!resultVO.isSuccess()){
            throw new BaseKnownException("同步base模块异常,请联系管理员");
        }
        materialPointAreaService.removeById(materialPointAreaId);
    }


    /**
     * 通过工位编号查询料点区域信息
     *
     * @param stationCode 工位编号
     * @return 料点区域信息
     */
    @GetMapping(value = "/getMaterialPointAreaByStationCode")
    @ApiOperation(value = "通过工位编号查询料点区域信息")
    public MaterialPointAreaDTO getMaterialPointAreaByStationCode(@RequestParam("stationCode") String stationCode) {
        return materialPointAreaService.getMaterialPointAreaByStationCode(stationCode);
    }


    /**
     * 满框调度根据工位编号返回料点区域
     *
     * @param lineSchedulingDTO
     * @return
     */
    @PostMapping(value = "/getMaterialPointArea")
    @ApiOperation(value = "满框调度根据工位编号返回料点区域")
    public MaterialPointAreaDTO getMaterialPointArea(@RequestBody LineSchedulingDTO lineSchedulingDTO) {
        return materialPointAreaService.getMaterialPointArea(lineSchedulingDTO);
    }


    /**
     * 区域编号找料点区域
     *
     * @param areaCode
     * @return
     */
    @GetMapping(value = "/getByCode")
    @ApiOperation(value = "区域编号找料点区域")
    public MaterialPointAreaDTO getByCode(@RequestParam String areaCode) {
        return materialPointAreaService.getByCode(areaCode);
    }


    /**
     * id找料点区域
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/getById")
    @ApiOperation(value = "id找料点区域")
    public HvBmMaterialPointArea getById(@RequestParam Integer id) {
        return materialPointAreaService.getById(id);
    }


    @GetMapping(value = "/findLineCodeByMaterialPointCode/{materialPointCode}")
    @ApiOperation(value = "根据料点编号查询线体编号")
    public String findLineCodeByMaterialPointCode(@PathVariable("materialPointCode") String materialPointCode){
        return materialPointAreaService.findLineCodeByMaterialPointCode(materialPointCode);
    }

}
