package com.hvisions.hiperbase.service.equipment;

import com.hvisions.hiperbase.equipment.equipmentclass.EquipmentClassDTO;
import com.hvisions.hiperbase.equipment.equipmentclass.EquipmentClassProperty;
import com.hvisions.hiperbase.equipment.equipmentclass.EquipmentClassQuery;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <p>Title: EquipmentClassService</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/3/25</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface EquipmentClassService {
    /**
     * 创建设备属性类型
     *
     * @param classDTO 属性类型
     * @return 主键
     */
    Integer create(EquipmentClassDTO classDTO);

    /**
     * 修改设备属性类型
     *
     * @param classDTO 属性类型
     */
    void update(EquipmentClassDTO classDTO);

    /**
     * 获取设备属性类型
     *
     * @param id 属性类型主键
     * @return 属性类型
     */
    EquipmentClassDTO findById(Integer id);

    /**
     * 获取设备属性类型
     *
     * @param code 属性类型编码
     * @return 属性类型
     */
    EquipmentClassDTO findByCode(String code);

    /**
     * 获取设备属性类型分页数据
     *
     * @param query 属性类型编码
     * @return 属性类型分页数据
     */
    Page<EquipmentClassDTO> findPage(EquipmentClassQuery query);

    /**
     * 删除设备属性类型
     *
     * @param id 属性类型
     */
    void deleteById(Integer id);

    /**
     * 根据设备id查询设备属性列表
     *
     * @param id 设备di
     * @return 设备属性类型列表
     */
    List<EquipmentClassDTO> findByEquipmentId(Integer id);

    /**
     * 向设备添加设备属性类型
     *
     * @param equipmentId 设备di
     * @param classId     属性类型id
     */
    void addClassToEquipment(Integer equipmentId, Integer classId);

    /**
     * 删除设备的设备属性类型
     *
     * @param equipmentId 设备di
     * @param classId     属性类型id
     */
    void removeClassToEquipment(Integer equipmentId, Integer classId);

    /**
     * 新增设备属性
     *
     * @param property 设备属性
     */
    void addEquipmentClass(EquipmentClassProperty property);
}

    
    
    
    