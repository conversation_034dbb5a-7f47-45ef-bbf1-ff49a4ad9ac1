package com.hvisions.hiperbase.service.equipment;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.equipment.EquipmentTypeBaseDTO;
import com.hvisions.hiperbase.equipment.EquipmentTypeDTO;
import com.hvisions.hiperbase.equipment.EquipmentTypeQueryDTO;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**
 * <p>Title: EquipmentTypeService</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/13</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface EquipmentTypeService {
    /**
     * 添加设备类型
     *
     * @param hvEquipmentTypeDTO 设备类型信息
     * @return 设备类型主键
     */
    EquipmentTypeDTO createEquipmentType(EquipmentTypeDTO hvEquipmentTypeDTO);

    /**
     * 更新设备类型信息
     *
     * @param hvEquipmentTypeDTO 设备类型信息
     * @return 设备类型主键
     */
    EquipmentTypeDTO updateEquipmentType(EquipmentTypeBaseDTO hvEquipmentTypeDTO);

    /**
     * 获取所有设备类型信息
     *
     * @return 设备类型信息
     */
    List<EquipmentTypeDTO> getAllEquipmentType();

    /**
     * 根据设备类型id删除设备类型
     *
     * @param id 设备类型id
     */
    Integer deleteEquipmentTypeById(Integer id);

    /**
     * 根据设备类型id获取设备类型
     *
     * @param id 设备类型id
     * @return 设备类型信息
     */
    EquipmentTypeDTO getEquipmentTypeById(Integer id);

    /**
     * 根据设备类型名称获取设备类型
     *
     * @param code 设备类型名称
     * @return 设备类型信息
     */
    EquipmentTypeDTO getEquipmentTypeByCode(String code);


    /**
     * 根据设备类型编码查询设备类型信息列表
     *
     * @param codeIn 设备类型编码列表
     * @return 设备类型信息列表
     */
    List<EquipmentTypeDTO> getAllByEquipmentTypeCodeIn(List<String> codeIn);

    /**
     * 根据父级设备类型ID查询设备类型信息
     *
     * @param parentId 父级设备类型ID
     * @return 设备类型列表信息
     */
    List<EquipmentTypeDTO> findAllByParentId(Integer parentId);

    /**
     * 根据设备类型id查询父级设备类型列表
     *
     * @param id 设备类型id
     * @return 父级设备类型列表
     */
    List<EquipmentTypeDTO> findParentTypeById(Integer id);

    /**
     * 根据ID列表查询设备类型
     *
     * @param idList id列表
     * @return 设备类型信息
     */
    List<EquipmentTypeDTO> getAllByIdIn(List<Integer> idList);

    /**
     * 分页查询
     *
     * @param equipmentTypeQueryDto 分页条件
     * @return 设备类型分页信息
     */
    Page<EquipmentTypeDTO> getEquipmentTypeQuery(EquipmentTypeQueryDTO equipmentTypeQueryDto);

    /**
     * 导入设备类型信息
     *
     * @param file 导入文件
     * @return 导入信息
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    ImportResult importEquipmentType(MultipartFile file) throws IllegalAccessException, ParseException, IOException;


    /**
     * 导出设备信息
     *
     * @return 设备信息excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException 访问异常
     */
    ResponseEntity<byte[]> exportEquipmentTypeLink() throws IOException, IllegalAccessException;

    /**
     * 导出设备信息
     *
     * @return 设备信息excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException 访问异常
     */
    ResultVO<ExcelExportDto> exportEquipmentType() throws IOException, IllegalAccessException;

    /**
     * 根据指定节点id遍历其子节点
     *
     * @param parentId 父节点id
     * @return 所有子节点及子节点的子节点
     */
    List<EquipmentTypeDTO> findAllChildTypeByParentId(Integer parentId);

    /**
     * 根据指定节点id遍历其父节点
     *
     * @param childId 子节点id
     * @return 父节点以及父节点的父节点
     */
    List<EquipmentTypeDTO> findAllByChildId(Integer childId);
}

    
    
    
    
    
    
    
    
    
    
    