package com.hvisions.hiperbase.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.EasyExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.entity.material.HvBmSpecialMaterial;
import com.hvisions.hiperbase.materials.dto.HvBmMaterialTaoPicDTO;
import com.hvisions.hiperbase.materials.dto.HvBmSpecialMaterialDTO;
import com.hvisions.hiperbase.materials.dto.MaterialPointAreaDTO;
import com.hvisions.hiperbase.materials.dto.MaterialPointDTO;
import com.hvisions.hiperbase.service.material.HvBmSpecialMaterialService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import java.time.LocalDateTime;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-04-17 9:24
 */
@RestController
@RequestMapping(value = "/specialMaterial")
@Api(description = "特殊零件")
public class SpecialMaterialController {
    @Autowired
    private HvBmSpecialMaterialService hvBmSpecialMaterialService;

    /**
     * 分页查询
     */
    @ApiOperation("分页查询")
    @PostMapping("/list")
    public Page<HvBmSpecialMaterial> list(@RequestBody HvBmSpecialMaterial condition,
                                          @RequestParam(name="pageNum", defaultValue="1") Integer pageNo,
                                          @RequestParam(name="pageSize", defaultValue="10") Integer pageSize)
    {
        Page<HvBmSpecialMaterial> page = new Page<>(pageNo,pageSize);
        return hvBmSpecialMaterialService.pageList(page,condition);
    }

    /**
     * 导入
     *
     * @param file 文件
     */
    @PostMapping("/import")
    @ApiOperation("导入")
    public void importData(MultipartFile file, @UserInfo @ApiIgnore UserInfoDTO userInfo) {
        hvBmSpecialMaterialService.importData(file,userInfo);
    }


    /**
     * 导出
     *
     * @param condition
     */
    @ApiOperation(value = "导出")
    @PostMapping(value = "/exportData")
    @ApiResultIgnore
    public ResultVO<ExcelExportDto> exportData(@RequestBody HvBmSpecialMaterial condition) {
        List<HvBmSpecialMaterial> list = hvBmSpecialMaterialService.exportData(condition);
        return ResultVO.success(EasyExcelUtil.getExcel(list, HvBmSpecialMaterial.class, System.currentTimeMillis() + "特殊零件.xlsx"));

    }

    /**
     * 下载模版
     *
     * @param
     */
    @ApiOperation(value = "下载模版")
    @PostMapping(value = "/downloadTemplate")
    @ApiResultIgnore
    public ResultVO<ExcelExportDto> downloadTemplate() {
        List<HvBmSpecialMaterialDTO> list = new ArrayList<>();
        return ResultVO.success(EasyExcelUtil.getExcel(list, HvBmSpecialMaterialDTO.class, System.currentTimeMillis() + "特殊零件导入模版.xlsx"));

    }


    /**
     * 添加
     *
     * @param hvBmSpecialMaterial
     */
    @ApiOperation(value = "添加")
    @PostMapping(value = "/add")
    public void add(@RequestBody HvBmSpecialMaterial hvBmSpecialMaterial,@UserInfo @ApiIgnore UserInfoDTO userInfo) {
        boolean isExist = hvBmSpecialMaterialService.checkSpecialMaterialIsExist(hvBmSpecialMaterial);
        if(isExist){
            throw new BaseKnownException("该特殊零件已经存在");
        }else{
            hvBmSpecialMaterial.setCreatorId(userInfo.getId());
            hvBmSpecialMaterial.setCreateTime(LocalDateTime.now());
            hvBmSpecialMaterialService.save(hvBmSpecialMaterial);
        }

    }

    /**
     * 删除
     *
     * @param id 主键
     */
    @ApiOperation(value = "删除")
    @DeleteMapping(value = "/delete/{id}")
    public void deleteById(@PathVariable long id) {
        hvBmSpecialMaterialService.removeById(id);
    }

    /**
     * 修改
     *
     * @param hvBmSpecialMaterial
     */
    @ApiOperation(value = "修改")
    @PutMapping(value = "/update")
    public void updateById(@RequestBody HvBmSpecialMaterial hvBmSpecialMaterial,@UserInfo @ApiIgnore UserInfoDTO userInfo) {
        hvBmSpecialMaterial.setUpdaterId(userInfo.getId());
        hvBmSpecialMaterial.setUpdateTime(LocalDateTime.now());
        hvBmSpecialMaterialService.updateById(hvBmSpecialMaterial);
    }

    /**
     * 根据id获取
     *
     * @param id 主键
     * @return HvBmMaterialTaoPicDTO
     */
    @ApiOperation(value = "根据id获取")
    @GetMapping(value = "/get/{id}")
    public HvBmSpecialMaterial getList(@PathVariable long id) {
        return hvBmSpecialMaterialService.getById(id);
    }

    /**
     * 查询全部
     * @return 列表
     */
    @ApiOperation(value = "获取所有列表")
    @GetMapping(value = "/getAll")
    public List<HvBmSpecialMaterial> getAll(){
        return hvBmSpecialMaterialService.list();

    }

    /**
     * 根据物料编号和线体ID查询料点区域
     * @return 列表
     */
    @ApiOperation(value = "根据物料编号和线体ID查询料点区域")
    @PostMapping(value = "/findMaterialPointDTOByMaterialCodeAndLineId")
    public MaterialPointAreaDTO findMaterialPointDTOByMaterialCodeAndLineId(@RequestBody Map<String,Object> map){
        return hvBmSpecialMaterialService.findMaterialPointDTOByMaterialCodeAndLineId(map);
    }


}
