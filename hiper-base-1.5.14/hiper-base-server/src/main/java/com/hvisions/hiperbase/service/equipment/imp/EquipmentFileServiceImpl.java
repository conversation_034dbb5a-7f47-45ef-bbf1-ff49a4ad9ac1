package com.hvisions.hiperbase.service.equipment.imp;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.hiperbase.equipment.CopyEquipmentEvent;
import com.hvisions.hiperbase.equipment.DelEquipmentEvent;
import com.hvisions.hiperbase.equipment.EquipmentFileDTO;
import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentFile;
import com.hvisions.hiperbase.repository.equipment.EquipmentFileRepository;
import com.hvisions.hiperbase.service.equipment.EquipmentFileService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.SerializationUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>Title: EquipmentFileServiceImpl</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/2</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Service
@Slf4j
public class EquipmentFileServiceImpl implements EquipmentFileService  {


    private final EquipmentFileRepository equipmentFileRepository;

    @Autowired
    public EquipmentFileServiceImpl(EquipmentFileRepository equipmentFileRepository) {
        this.equipmentFileRepository = equipmentFileRepository;
    }


    /**
     * 新增设备文件关联
     *
     * @param hvEquipmentFileDTO 设备文件关联关系
     */
    @Override
    public void createEquipmentFile(EquipmentFileDTO hvEquipmentFileDTO) {
        HvBmEquipmentFile hvBmEquipmentFile = DtoMapper.convert(hvEquipmentFileDTO, HvBmEquipmentFile.class);
        equipmentFileRepository.save(hvBmEquipmentFile);
    }

    /**
     * 根据ID删除设备文件关联关系
     *
     * @param id 主键ID
     */
    @Override
    public void deleteEquipmentFileById(int id) {
        equipmentFileRepository.deleteById(id);
    }

    /**
     * 根据设备ID删除设备文件关联关系
     *
     * @param hvEquipmentFileDTO 文件关联关系
     */
    @Override
    public void updateEquipmentFile(EquipmentFileDTO hvEquipmentFileDTO) {
        HvBmEquipmentFile hvBmEquipmentFile = DtoMapper.convert(hvEquipmentFileDTO, HvBmEquipmentFile.class);
        equipmentFileRepository.save(hvBmEquipmentFile);
    }

    /**
     * 根据设备ID查询设备文件关联关系
     *
     * @param equipmentId 设备ID
     * @return 设备文件关联关系
     */
    @Override
    public List<EquipmentFileDTO> findAllByEquipmentId(Integer equipmentId) {
        List<HvBmEquipmentFile> hvBmEquipmentFiles = equipmentFileRepository.findAllByEquipmentId(equipmentId);
        return DtoMapper.convertList(hvBmEquipmentFiles, EquipmentFileDTO.class);
    }

    /**
     * 复制设备事件处理
     *
     * @param event 复制设备事件
     */
    @EventListener(CopyEquipmentEvent.class)
    @Transactional(rollbackFor = Exception.class)
    public void handleCopyEvent(CopyEquipmentEvent event) {
        Integer equipmentId = event.getEquipmentId();
        Integer copyEquipmentId = event.getCopyEquipmentId();
        //取出源设备id
        List<HvBmEquipmentFile> hvBmEquipmentFiles = equipmentFileRepository.findAllByEquipmentId(copyEquipmentId);
        if (CollectionUtils.isEmpty(hvBmEquipmentFiles)) {
            return;
        }
        List<HvBmEquipmentFile> copyFiles = new ArrayList<>();
        for (HvBmEquipmentFile hvBmEquipmentFile : hvBmEquipmentFiles) {
            //深拷贝复制
            HvBmEquipmentFile equipmentFile = SerializationUtils.clone(hvBmEquipmentFile);
            equipmentFile.setEquipmentId(equipmentId);
            equipmentFile.setId(null);
            copyFiles.add(equipmentFile);
        }
        //保存复制后的数据
        equipmentFileRepository.saveAll(copyFiles);
    }

    /**
     * 删除设备事件处理
     *
     * @param event 删除设备事件
     */
    @Transactional(rollbackFor = Exception.class)
    @EventListener(DelEquipmentEvent.class)
    public void handleDelEvent(DelEquipmentEvent event) {
        equipmentFileRepository.deleteAllByEquipmentId(event.getEquipmentId());
    }
}
