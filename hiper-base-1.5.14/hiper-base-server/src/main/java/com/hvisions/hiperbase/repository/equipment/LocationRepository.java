package com.hvisions.hiperbase.repository.equipment;


import com.hvisions.hiperbase.entity.equipment.HvBmLocation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: HvBmLocationRepository</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/3</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface LocationRepository extends JpaRepository<HvBmLocation, Integer> {
    /***
     * 根据父级id查询列表
     * @param parentId 父id
     * @return location列表
     */

    List<HvBmLocation> findByParentId(int parentId);

    /***
     * 根据id
     * @param id id
     * @return location信息
     */
    HvBmLocation getById(Integer id);

    /***
     * 根据代码查询
     * @param code 代码
     * @return 位置信息
     */
    HvBmLocation findByCode(String code);

    /**
     * 根据type查询
     *
     * @param type type类型
     * @return 列表
     */
    List<HvBmLocation> findAllByType(int type);

    /**
     * 根据ID列表查询 产线信息
     *
     * @param idList Id列表
     * @return 产线信息列表 Idl
     */
    List<HvBmLocation> getAllByIdIn(List<Integer> idList);

    /**
     * 根据父级id列表查询下级列表
     *
     * @param parentIds 父级id
     * @return 下级列表
     */
    List<HvBmLocation> getAllByParentIdIn(List<Integer> parentIds);


    /**
     * 修复异常数据
     */
    List<HvBmLocation> findAllByIndexNumIsNull();


    HvBmLocation findFirstByCode(String parentCode);


    List<HvBmLocation> findByCodeIn(List<String> codeList);
}