package com.hvisions.hiperbase.repository.equipment;

import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentOnlineRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: EquipmentOnlineRepository</p >
 * <p>Description: </p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/7</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Repository
public interface EquipmentOnlineRepository extends JpaRepository<HvBmEquipmentOnlineRecord, Integer> {
    /**
     * 根据设备编号查询设备在线记录
     *
     * @param equipmentId 设备编号
     * @return 设备在线记录
     */
    List<HvBmEquipmentOnlineRecord> findAllByEquipmentId(Integer equipmentId);

    /**
     * 根据设备id删除设备在线记录
     *
     * @param equipmentId 设备id
     */
    void deleteAllByEquipmentId(int equipmentId);
}
