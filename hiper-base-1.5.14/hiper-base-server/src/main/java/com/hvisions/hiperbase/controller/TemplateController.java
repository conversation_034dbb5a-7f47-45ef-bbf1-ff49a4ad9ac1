package com.hvisions.hiperbase.controller;

import com.hvisions.hiperbase.route.dto.TemplateDTO;
import com.hvisions.hiperbase.route.dto.TemplateQueryDTO;
import com.hvisions.hiperbase.service.route.TemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

/**
 * <p>Title: TemplateController</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/3/17</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@Api(description = "工艺模版控制器")
@RequestMapping(value = "/template")
public class TemplateController {


    private final TemplateService templateService;

    @Autowired
    public TemplateController(TemplateService templateService) {
        this.templateService = templateService;
    }


    /**
     * 创建模版
     *
     * @param templateDTO 模版对象
     */
    @ApiOperation(value = "创建模版")
    @PostMapping(value = "/createTemplate")
    public void createTemplate(@RequestBody TemplateDTO templateDTO) {
        templateService.createTemplate(templateDTO);
    }

    /**
     * 更新模版
     *
     * @param templateDTO 模版对象
     */
    @ApiOperation(value = "更新模版")
    @PutMapping(value = "updateTemplate")
    public void updateTemplate(@RequestBody TemplateDTO templateDTO) {
        templateService.updateTemplate(templateDTO);
    }


    /**
     * 删除
     *
     * @param id 模版id
     */
    @ApiOperation(value = "删除")
    @DeleteMapping(value = "/deleteById/{id}")
    public void deleteById(@PathVariable int id) {
        templateService.deleteById(id);
    }


    /**
     * 分页查询模版信息
     *
     * @return 模版信息分页列表
     */
    @ApiOperation(value = "分页查询模版信息")
    @PostMapping(value = "/getAllByQuery")
    public Page<TemplateDTO> getAllByQuery(@RequestBody TemplateQueryDTO templateQueryDTO) {
        return templateService.getAllByQuery(templateQueryDTO);
    }
}