package com.hvisions.hiperbase.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.EasyExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.entity.material.HvBmMaterialPoint;
import com.hvisions.hiperbase.entity.path.HvBmPath;
import com.hvisions.hiperbase.equipment.EquipmentDTO;
import com.hvisions.hiperbase.equipment.EquipmentQueryDTO;
import com.hvisions.hiperbase.path.HvBmPathDTO;
import com.hvisions.hiperbase.path.HvBmPathQueryDTO;
import com.hvisions.hiperbase.service.material.MaterialPointService;
import com.hvisions.hiperbase.service.path.HvBmPathService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;


/**
 * <p>Title: PathController</p>
 * <p>Description: 路径调度控制器</p>
 */
@RestController
@RequestMapping("/path")
@Api(tags = "路径调度管理")
public class PathController {

    @Autowired
    private HvBmPathService hvBmPathService;
    @Autowired
    private MaterialPointService materialPointService;

    @PostMapping("getPathPage")
    @ApiOperation("获取路径分页模糊查询")
    public Page<HvBmPathDTO> getPathPage(@RequestBody HvBmPathQueryDTO hvBmPathQueryDTO) {
        return hvBmPathService.getPage(hvBmPathQueryDTO);
    }

    @GetMapping("getPathById/{id}")
    @ApiOperation("根据id获取路径")
    public ResultVO getPathById(@PathVariable String id) {
        HvBmPath one = hvBmPathService.getOne(new LambdaQueryWrapper<HvBmPath>().eq(HvBmPath::getPathCode, id));
        if (one == null) {
            return ResultVO.error(500, "路径编号" + id + "不存在");
        }
//        HvBmMaterialPoint startPoint = materialPointService.getOne(new LambdaQueryWrapper<HvBmMaterialPoint>().eq(HvBmMaterialPoint::getPointCode, one.getStartPoint()));
//        HvBmMaterialPoint endPoint = materialPointService.getOne(new LambdaQueryWrapper<HvBmMaterialPoint>().eq(HvBmMaterialPoint::getPointCode, one.getEndPoint()));
//        if (startPoint == null || endPoint == null){
//            return ResultVO.error(500, "路径编号" + id + "的起始点或目标点不存在");
//        }
        HvBmPathDTO hvBmPathDTO = DtoMapper.convert(one, HvBmPathDTO.class);
//        hvBmPathDTO.setStartPointName(startPoint.getPointName());
//        hvBmPathDTO.setEndPointName(endPoint.getPointName());
        return ResultVO.success(hvBmPathDTO);
    }

    @PostMapping("createPath")
    @ApiOperation("添加路径")
    public ResultVO createPath(@RequestBody @ApiParam("路径信息") HvBmPathDTO hvBmPathDTO, @UserInfo @ApiIgnore UserInfoDTO userInfo) {
        if (hvBmPathService.isExist(hvBmPathDTO.getPathCode())) {
            return ResultVO.error(500, "路径编号" + hvBmPathDTO.getPathCode() + "已存在");
        }
        hvBmPathDTO.setCreatorId(userInfo.getUserName());
        hvBmPathDTO.setCreateTime(LocalDateTime.now());
        hvBmPathDTO.setUpdateTime(LocalDateTime.now());
        hvBmPathDTO.setUpdaterId(userInfo.getUserName());
        return ResultVO.success(hvBmPathService.save(DtoMapper.convert(hvBmPathDTO, HvBmPath.class)));
    }

    @PutMapping("updatePath")
    @ApiOperation("更新路径")
    public ResultVO updatePath(@RequestBody @ApiParam("路径信息") HvBmPathDTO hvBmPathDTO, @UserInfo @ApiIgnore UserInfoDTO userInfo) {
        if (!hvBmPathService.isExist(hvBmPathDTO.getPathCode())) {
            return ResultVO.error(500, "路径编号" + hvBmPathDTO.getPathCode() + "不存在");
        }
        hvBmPathDTO.setUpdateTime(LocalDateTime.now());
        hvBmPathDTO.setUpdaterId(userInfo.getUserName());
        return ResultVO.success(hvBmPathService.update(DtoMapper.convert(hvBmPathDTO, HvBmPath.class), new LambdaQueryWrapper<HvBmPath>().eq(HvBmPath::getPathCode, hvBmPathDTO.getPathCode())));
    }

    @DeleteMapping("deletePath/{id}")
    @ApiOperation("删除路径")
    public ResultVO deletePath(@PathVariable @ApiParam("路径编号") String id) {
        if (!hvBmPathService.isExist(id)) {
            return ResultVO.error(500, "路径编号" + id + "不存在");
        }
        return ResultVO.success(hvBmPathService.remove(new LambdaQueryWrapper<HvBmPath>().eq(HvBmPath::getPathCode, id)));
    }

    /**
     * 下载模版
     *
     */
    @ApiOperation(value = "下载模版")
    @PostMapping(value = "/downloadTemplate")
    @ApiResultIgnore
    public ResultVO<ExcelExportDto> downloadTemplate() {
        List<HvBmPath> list = new ArrayList<>();
        return ResultVO.success(EasyExcelUtil.getExcel(list, HvBmPath.class, System.currentTimeMillis() + "调度路径主数据导入模版.xlsx"));
    }

    /**
     * 导出
     *
     * @param hvBmPathDTO
     */
    @ApiOperation(value = "导出")
    @PostMapping(value = "/exportData")
    @ApiResultIgnore
    public ResultVO<ExcelExportDto> exportData(@RequestBody HvBmPathDTO hvBmPathDTO) {
        List<HvBmPath> list =  hvBmPathService.findListByCondition(hvBmPathDTO);
        return ResultVO.success(EasyExcelUtil.getExcel(list, HvBmPath.class, System.currentTimeMillis() + "调度路径主数据.xlsx"));
    }

    /**
     * 导入
     *
     * @param file 文件
     */
    @PostMapping("/import")
    @ApiOperation("导入")
    public ResultVO importData(MultipartFile file, @UserInfo @ApiIgnore UserInfoDTO userInfo) {

        return hvBmPathService.importData(file, userInfo);

    }
    @PostMapping("/getHvBmPath")
    @ApiOperation(value = "路径条件查询")
    public List<HvBmPathDTO> getHvBmPath(@RequestBody HvBmPathDTO hvBmPathDTO) {
        List<HvBmPathDTO> list =  hvBmPathService.getHvBmPath(hvBmPathDTO);
        return list;
    }

}
