package com.hvisions.hiperbase.service.equipment.imp;

import com.hvisions.common.config.coderule.utils.SerialCodeUtils;
import com.hvisions.common.dto.ExtendInfo;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.service.BaseExtendService;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.hiperbase.CodeRuleConsts;
import com.hvisions.hiperbase.client.SpareClient;
import com.hvisions.hiperbase.client.SpareDTO;
import com.hvisions.hiperbase.dao.equipment.EquipmentMapper;
import com.hvisions.hiperbase.entity.SysBase;
import com.hvisions.hiperbase.entity.equipment.*;
import com.hvisions.hiperbase.enums.EquipmentParentEnum;
import com.hvisions.hiperbase.equipment.EquipmentExcelDto;
import com.hvisions.hiperbase.repository.equipment.*;
import com.hvisions.hiperbase.service.equipment.EquipmentExcelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>Title: EquipmentExcelServiceImpl</p >
 * <p>Description: </p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/7</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Service
@Slf4j
public class EquipmentExcelServiceImpl implements EquipmentExcelService {
    @Resource(name = "equipment_extend")
    BaseExtendService equipmentExtendService;

    private final EquipmentRepository equipmentRepository;

    private final EquipmentStatusRepository equipmentStatusRepository;

    private final EquipmentTypeRepository equipmentTypeRepository;

    private final LocationRepository locationRepository;
    private final EquipmentMapper equipmentMapper;
    private final EquipmentSparePartRepository equipmentSparePartRepository;
    private final SpareClient spareClient;
    private final SerialCodeUtils serialCodeUtils;
    private static final String DEFAULT_STATE = "ready";

    @Autowired
    public EquipmentExcelServiceImpl(EquipmentRepository equipmentRepository,
                                     EquipmentStatusRepository equipmentStatusRepository,
                                     EquipmentTypeRepository equipmentTypeRepository,
                                     LocationRepository locationRepository,
                                     EquipmentMapper equipmentMapper,
                                     EquipmentSparePartRepository equipmentSparePartRepository,
                                     SpareClient spareClient,
                                     SerialCodeUtils serialCodeUtils) {
        this.equipmentRepository = equipmentRepository;
        this.equipmentStatusRepository = equipmentStatusRepository;
        this.equipmentTypeRepository = equipmentTypeRepository;
        this.locationRepository = locationRepository;
        this.equipmentMapper = equipmentMapper;
        this.equipmentSparePartRepository = equipmentSparePartRepository;
        this.spareClient = spareClient;
        this.serialCodeUtils = serialCodeUtils;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(EquipmentExcelDto equipmentExcelDto) {
        if (StringUtils.isEmpty(equipmentExcelDto.getEquipmentName())) {
            throw new BaseKnownException(1000, "设备名称不能为空");
        }
        String equipmentCode = equipmentExcelDto.getEquipmentCode();
        //如果设备编码为空，自动生成设备编码
        if (StringUtils.isBlank(equipmentCode)) {
            equipmentCode = serialCodeUtils.generateCode(CodeRuleConsts.EQUIPMENT_CODE);
            equipmentExcelDto.setEquipmentCode(equipmentCode);
        }
        HvBmEquipment hvBmEquipment = equipmentRepository.findByEquipmentCode(equipmentCode);
        if (hvBmEquipment == null) {
            HvBmEquipment equipment = new HvBmEquipment();
            Optional<Integer> statusId = equipmentStatusRepository.getOneByStatusCode(DEFAULT_STATE).map(HvBmEquipmentStatus::getId);
            statusId.ifPresent(equipment::setEquipmentStatusId);
            //新建设备
            HvBmEquipment newEquipment = saveEquipmentInfo(equipmentExcelDto, equipment);
            equipmentRepository.saveAndFlush(newEquipment);
            //添加扩展信息
            ExtendInfo extendInfo = new ExtendInfo();
            extendInfo.setEntityId(newEquipment.getId());
            extendInfo.setValues(equipmentExcelDto.getExtend());
            equipmentExtendService.addExtendInfo(extendInfo);
            //保存备件信息
            if (StringUtils.isNotBlank(equipmentExcelDto.getSparePartStr())) {
                saveEquipmentSpareParts(equipment.getId(), equipmentExcelDto.getSparePartStr());
            }
        } else {
            //更新设备
            HvBmEquipment equipment = DtoMapper.convert(hvBmEquipment, HvBmEquipment.class);
            saveEquipmentInfo(equipmentExcelDto, equipment);
            if (validEquipment(equipment)) {
                equipmentRepository.saveAndFlush(equipment);
                ExtendInfo extendInfo = new ExtendInfo();
                extendInfo.setEntityId(hvBmEquipment.getId());
                extendInfo.setValues(equipmentExcelDto.getExtend());
                equipmentExtendService.updateExtendInfo(extendInfo);
                //保存备件信息
                if (StringUtils.isNotBlank(equipmentExcelDto.getSparePartStr())) {
                    saveEquipmentSpareParts(equipment.getId(), equipmentExcelDto.getSparePartStr());
                }
            } else {
                throw new BaseKnownException(1000, "设备不能挂载到自身和子设备上");
            }
        }
    }

    /**
     * 保存设备备件信息
     *
     * @param equipmentId  设备id
     * @param sparePartStr 备件字符串
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveEquipmentSpareParts(Integer equipmentId, String sparePartStr) {
        try {
            List<HvBmEquipmentSparePart> spareParts = parseSparePart(equipmentId, sparePartStr);
            if (!CollectionUtils.isEmpty(spareParts)) {
                //清除之前的设备备件信息
                equipmentSparePartRepository.deleteAllByEquipmentId(equipmentId);
                equipmentSparePartRepository.saveAll(spareParts);
            }
        } catch (Exception e) {
            throw new BaseKnownException(e.getMessage());
        }
    }

    /**
     * 解析备件信息
     *
     * @param equipmentId  设备id
     * @param sparePartStr 备件字符串
     * @return 备件信息
     */
    private List<HvBmEquipmentSparePart> parseSparePart(Integer equipmentId, String sparePartStr) {
        String[] sparePartArr = sparePartStr.split(",");
        List<HvBmEquipmentSparePart> equipmentSpareParts = new ArrayList<>();
        if (sparePartArr.length == 0) {
            throw new BaseKnownException(1000, "设备备件解析错误,请检查符号");
        }
        for (String sparePartDto : sparePartArr) {
            String[] sparePart = sparePartDto.split(":");
            if (sparePart.length != 2) {
                throw new BaseKnownException(1000, "设备备件解析错误,请检查符号: " + sparePartDto);
            }
            HvBmEquipmentSparePart hvBmEquipmentSparePart = new HvBmEquipmentSparePart();
            hvBmEquipmentSparePart.setEquipmentId(equipmentId);
            Integer spareId = Optional.ofNullable(spareClient.getSpareBySpareCode(sparePart[0]).getData())
                    .map(SpareDTO::getId).orElseThrow(() -> new BaseKnownException(1000, "备件编码不存在:  " + sparePart[0]));
            hvBmEquipmentSparePart.setSparePartId(spareId);
            hvBmEquipmentSparePart.setNumber(new BigDecimal(sparePart[1]));
            equipmentSpareParts.add(hvBmEquipmentSparePart);
        }
        return equipmentSpareParts;
    }

    /**
     * @param equipmentExcelDto 导入数据
     * @param hvBmEquipment     设备信息
     * @return 填充后的设备数据
     */
    public HvBmEquipment saveEquipmentInfo(EquipmentExcelDto equipmentExcelDto, HvBmEquipment hvBmEquipment) {
        hvBmEquipment.setEquipmentCode(equipmentExcelDto.getEquipmentCode());
        hvBmEquipment.setEquipmentName(equipmentExcelDto.getEquipmentName());
        hvBmEquipment.setParentType(equipmentExcelDto.getParentType());
        //设置设备类型
        Optional.ofNullable(equipmentTypeRepository.findByEquipmentTypeCode(equipmentExcelDto.getEquipmentTypeCode())).
                map(HvBmEquipmentType::getId).ifPresent(hvBmEquipment::setEquipmentTypeId);
        //设置parentId
        if (hvBmEquipment.getParentType() == null || StringUtils.isBlank(equipmentExcelDto.getParentCode())) {
            hvBmEquipment.setParentId(0);
            hvBmEquipment.setParentType(EquipmentParentEnum.LOCATION.getType());
        } else if (EquipmentParentEnum.EQUIPMENT.getType().equals(equipmentExcelDto.getParentType()) &&
                StringUtils.isNotEmpty(equipmentExcelDto.getParentCode())) {
            //父节点为设备
            Integer parentId = Optional.ofNullable(equipmentRepository.findByEquipmentCode(equipmentExcelDto.getParentCode()))
                    .map(HvBmEquipment::getId)
                    .orElseThrow(() -> new BaseKnownException(1000, "父节点设备不存在"));
            hvBmEquipment.setParentId(parentId);
        } else if (EquipmentParentEnum.LOCATION.getType().equals(equipmentExcelDto.getParentType()) &&
                StringUtils.isNotEmpty(equipmentExcelDto.getParentCode())) {
            //父节点为位置
            Integer parentId = Optional.ofNullable(locationRepository.findFirstByCode(equipmentExcelDto.getParentCode()))
                    .map(HvBmLocation::getId)
                    .orElseThrow(() -> new BaseKnownException(1000, "父节点位置不存在"));
            hvBmEquipment.setParentId(parentId);
        }
        //设置负责人id
        Optional.ofNullable(equipmentMapper.findUserIdByAccount(equipmentExcelDto.getResponserAccount()))
                .ifPresent(hvBmEquipment::setResponserId);
        //设置负责部门id
        Optional.ofNullable(equipmentMapper.findDepartmentIdByCode(equipmentExcelDto.getResponsePartCode()))
                .ifPresent(hvBmEquipment::setResponsePartId);
        return hvBmEquipment;
    }

    @Override
    public ImportResult importEquipment(MultipartFile file) throws IOException, ParseException, IllegalAccessException {
        return ExcelUtil.importEntity(file,
                EquipmentExcelDto.class,
                this,
                equipmentExtendService.getExtendColumnInfo());
    }


    public Boolean validEquipment(HvBmEquipment equipment) {
        if (equipment.getId() == null) {
            return true;
        }
        //校验设备绑定关系
        if (equipment.getParentId() != null && EquipmentParentEnum.EQUIPMENT.getType()
                .equals(equipment.getParentType())) {
            //校验父节点是否就是自身
            if (equipment.getId().equals(equipment.getParentId())) {
                return false;
            }
            //校验父节点是否就是子节点中的一个
            List<HvBmEquipment> hvBmEquipments = findAllByParentId(equipment.getId(), new ArrayList<>());
            if (!CollectionUtils.isEmpty(hvBmEquipments)) {
                List<Integer> childIds = hvBmEquipments.stream().map(SysBase::getId).collect(Collectors.toList());
                return !childIds.contains(equipment.getParentId());
            }
        }
        return true;
    }

    List<HvBmEquipment> findAllByParentId(Integer equipmentId, List<HvBmEquipment> equipmentDtos) {
        List<HvBmEquipment> childEquipments = equipmentRepository.findAllByParentIdAndParentType(equipmentId,EquipmentParentEnum.EQUIPMENT.getType())
                .stream().filter(e -> EquipmentParentEnum.EQUIPMENT.getType().equals(e.getParentType())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(childEquipments)) {
            for (HvBmEquipment childEquipment : childEquipments) {
                equipmentDtos.add(childEquipment);
                findAllByParentId(childEquipment.getId(), equipmentDtos);
            }
        }
        return equipmentDtos;
    }
}
