package com.hvisions.hiperbase.service.equipment;

import com.hvisions.hiperbase.equipment.EquipmentOffLineRecordDto;
import com.hvisions.hiperbase.equipment.EquipmentOfflineDto;
import com.hvisions.hiperbase.equipment.EquipmentOnlineDto;

import java.util.List;

/**
 * <p>Title: EquipmentRecordService</p >
 * <p>Description: 设备上下线记录</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/7</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
public interface EquipmentRecordService {
    /**
     * 上线设备
     *
     * @param equipmentOnlineDto 设备上线信息
     */
    void onlineEquipment(EquipmentOnlineDto equipmentOnlineDto);

    /**
     * 下线设备
     *
     * @param equipmentOfflineDto 设备下线信息
     */
    void offlineEquipment(EquipmentOfflineDto equipmentOfflineDto);

    /**
     * 根据设备编号查询设备上下线记录
     *
     * @param equipmentId 设备编号
     * @return 设备上下线记录
     */
    List<EquipmentOffLineRecordDto> findEquipmentRecordByEquipmentId(Integer equipmentId);
}
