package com.hvisions.hiperbase.repository.material;

import com.hvisions.hiperbase.entity.material.HvBmMaterialClass;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <p>Title: MaterialClassRepository</p >
 * <p>Description: 物料类型属性仓储层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/4/7</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Repository
public interface MaterialClassRepository extends JpaRepository<HvBmMaterialClass, Integer> {

    /**
     * 根据编码查询
     *
     * @param code 类型编码
     * @return 属性类型
     */
    HvBmMaterialClass findByCode(String code);

    /**
     * 查询分页
     *
     * @param code     编码
     * @param name     名称
     * @param pageable 分页信息
     * @return 分页数据
     */
    Page<HvBmMaterialClass> findAllByCodeContainsOrNameContaining(String code, String name, Pageable pageable);

}