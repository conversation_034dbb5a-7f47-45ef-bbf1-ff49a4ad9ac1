package com.hvisions.hiperbase.service.equipment;

import com.hvisions.hiperbase.equipment.EquipmentStatusDto;

import java.util.List;

/**
 * <p>Title: EquipmentStatusService</p >
 * <p>Description: 设备状态</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/10</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
public interface EquipmentStatusService {
    /**
     * 根据状态类型查询设备状态
     *
     * @param type 状态类型
     * @return 设备状态列表
     */
    List<EquipmentStatusDto> getByType(Integer type);

    /**
     * 新增设备状态
     *
     * @param equipmentStatusDto 设备状态dto
     */
    void addEquipmentStatus(EquipmentStatusDto equipmentStatusDto);

    /**
     * 修改设备状态
     *
     * @param equipmentStatusDto 设备状态dto
     */
    void updateEquipmentStatus(EquipmentStatusDto equipmentStatusDto);

    /**
     * 删除设备状态
     *
     * @param id 设备状态id
     */
    void deleteEquipmentStatus(Integer id);

    /**
     * 查询所有设备状态
     *
     * @return 设备状态列表
     */
    List<EquipmentStatusDto> getAll();
}
