package com.hvisions.hiperbase.service.equipment;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.hiperbase.entity.equipment.HvBmLocation;
import com.hvisions.hiperbase.equipment.CellWithEquipmentDTO;
import com.hvisions.hiperbase.equipment.RelationDTO;
import com.hvisions.hiperbase.equipment.location.AreaLocationDTO;
import com.hvisions.hiperbase.equipment.location.LocationDTO;
import com.hvisions.hiperbase.equipment.location.LocationQueryDTO;
import com.hvisions.thirdparty.common.dto.MesFactoryDTO;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * <p>Title: LocationService</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/3</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */

public interface LocationService {


    /**
     * 根据父级id查询子级id
     *
     * @param parentId    父级ID
     * @param directChild 是否只查询一个层级的对象，比如车间只查产线
     * @return 下级建模信息
     */
    List<LocationDTO> getLocationDtoByParentID(int parentId, Boolean directChild);

    /***
     * 根据location id 查询扩展属性
     * @param id location id
     * @return dto
     */
    LocationDTO findLocationExtendById(int id);

    /**
     * 添加location 设备信息
     *
     * @param hvBmLocationDTO 建模位置
     * @return 建模位置
     */

    LocationDTO create(LocationDTO hvBmLocationDTO);

    /**
     * 更新location 信息
     *
     * @param hvBmLocationDTO 工厂建模位置
     * @return 工厂建模信息
     */
    LocationDTO update(LocationDTO hvBmLocationDTO);

    /**
     * 根据 location code 查询扩展属性
     *
     * @param code 编码
     * @return 位置信息
     */
    LocationDTO getLocationExtendByCode(String code);

    /**
     * 根据location type查询location列表
     *
     * @param type type类型
     * @return 位置列表
     */
    List<LocationDTO> findLocationDtoByType(int type);


    /**
     * 删除location
     *
     * @param id locationID
     */
    void deleteLocationById(int id);

    /**
     * 批量删除设备产线归属关系
     *
     * @param cellWithEquipmentDTO 设备ID列表 产线ID
     */
    void deleteCellEquipmentRelationBatch(CellWithEquipmentDTO cellWithEquipmentDTO);


    /**
     * 根据Code查询工厂建模
     *
     * @param code location code
     * @return 工厂建模信息
     */
    LocationDTO getHvBmLocationByCode(String code);

    /**
     * 根据产线ID集合获取产线信息列表
     *
     * @param idList 产线ID列表
     * @return 产线信息列表
     */
    List<LocationDTO> getAllByCellIdList(List<Integer> idList);

    /**
     * 根据产线Id查询所在车间以及车间下产线信息
     *
     * @param idList 查新Id
     * @return 车间以及车间下产线信息
     */
    List<AreaLocationDTO> getAllBySonId(List<Integer> idList);

    /**
     * 条件查询location
     *
     * @param locationQueryDTO 查询条件
     * @return location信息
     */
    List<LocationDTO> getLocationByQuery(LocationQueryDTO locationQueryDTO);



    /**
     * 查询所有location
     *
     * @return location信息列表
     */
    List<LocationDTO> findAll();

    /**
     * 获取所有工厂建模tree
     *
     * @return 工厂建模树
     */
    List<LocationDTO> getLocationTree();

    /**
     * 导入所有
     *
     * @param file 工厂建模信息文档
     * @throws IllegalAccessException field访问异常
     * @throws IOException            io异常
     */
    void importLocation(MultipartFile file) throws IOException, IllegalAccessException;

    /**
     * 导出所有建模数据
     *
     * @return 设备信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    ExcelExportDto exportLocation() throws IOException, IllegalAccessException;

    /**
     * 导出所有工厂建模基础信息
     *
     * @return 设备信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    ResponseEntity<byte[]> exportTemplate() throws IOException, IllegalAccessException;

    /**
     * 获取location所有的子集
     *
     * @param locationId 主键
     * @return location所有子集
     */
    List<LocationDTO> getChildLocation(Integer locationId);

    /**
     * 根据父级id列表查询下级列表
     *
     * @param parentIds   父级id
     * @param directChild 是否只查父级直接对应的类型,比如车间只查产线。
     * @return 下级列表
     */
    List<LocationDTO> getAllByParentIdList(List<Integer> parentIds, Boolean directChild);


    /**
     * 对工厂建模里面的模型进行排序
     *
     * @param relationDTO 模型关系
     */
    void sort(RelationDTO relationDTO);

    List<Integer> getLocationIdsByType(int type);

    int saveLocationByReceiving(List<MesFactoryDTO> factoryDTOS);

    String getLineByStationCode(String stationCode);

    //只修改名称
    Integer updateByCode(HvBmLocation hvBmLocation);

    /**
     *
     * 根据产线id查询产线信息
     * @param id
     * @return
     */
    LocationDTO getLineById(Integer id);

    /**
     * 根据产线编号查询产线信息
     * @param lineCode
     * @return
     */
    LocationDTO getLineInfoByLineCode(String lineCode);

    /**
     * 根据产线编号查询产线信息
     *
     * @param codes 产线编号集合
     * @return
     */
    List<LocationDTO> getLocationsByCodes(List<String> codes);

}
