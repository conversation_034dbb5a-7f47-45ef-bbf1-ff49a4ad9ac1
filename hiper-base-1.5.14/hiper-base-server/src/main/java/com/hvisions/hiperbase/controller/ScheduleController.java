package com.hvisions.hiperbase.controller;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.hiperbase.schedule.dto.*;
import com.hvisions.hiperbase.service.schedule.CrewService;
import com.hvisions.hiperbase.service.schedule.ScheduleService;
import com.hvisions.hiperbase.service.schedule.ShiftService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.time.DayOfWeek;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>Title: HvBmScheduleController</p>
 * <p>Description: 排班控制器</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/23</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Api(description = "排班控制器")
@RestController
@RequestMapping("/schedule")
public class ScheduleController {

    private final ScheduleService scheduleService;

    private final CrewService crewService;

    private final ShiftService shiftService;

    @Autowired
    public ScheduleController(ScheduleService scheduleService, CrewService crewService, ShiftService shiftService) {
        this.scheduleService = scheduleService;
        this.crewService = crewService;
        this.shiftService = shiftService;
    }

    @GetMapping("/getDayInfo")
    @ApiOperation(value = "获取每周的日期信息")
    public Map<String, Integer> getDayInfo() {
        String[] days = new String[]{"周一", "周二", "周三", "周四", "周五", "周六", "周日"};
        Map<String, Integer> result = new LinkedHashMap<>();
        int a = 1;
        for (String day : days) {
            result.put(day, a++);
        }
        return result;
    }

    /**
     * 根据id获取排班计划
     *
     * @param id 排班计划id
     * @return 排班计划信息
     */
    @GetMapping("/getScheduleById/{id}")
    @ApiOperation(value = "根据排班计划id获取排班计划")
    public ScheduleWithInfoDTO getScheduleById(@PathVariable int id) {
        return scheduleService.getScheduleById(id);
    }

    /**
     * 根据车间，开始日期，结束日期获取排班计划信息
     *
     * @param query 查询条件
     * @return 排班计划信息
     */
    @PostMapping("/getScheduleListByAreaIdAndCellIdAndTime")
    @ApiOperation(value = "根据车间，开始时间，结束时间获取排班计划信息")
    public List<ScheduleDailyInfoDTO> getScheduleListByAreaIdAndCellIdAndTime(@RequestBody ScheduleQuery query) {
        return scheduleService.getScheduleListByAreaIdAndCellIdAndTime(
                query.getAreaId(), query.getCellId(), query.getStartTime(), query.getEndTime());
    }

    /**
     * 排班信息导出模板
     *
     * @return 排班计划信息
     */
    @GetMapping("/scheduleExportTemplate")
    @ApiOperation(value = "排班信息导出模板")
    public ExcelExportDto scheduleExportTemplate() {
        return scheduleService.scheduleExportTemplate();
    }

    /**
     * 排班信息导入
     */
    @PostMapping("/importSchedule")
    @ApiOperation(value = "根据车间，开始时间，结束时间获取排班计划信息")
    public void importSchedule(MultipartFile file) {
        scheduleService.importSchedule(file);
    }


    /**
     * 导出排班信息
     *
     * @param query 查询条件
     * @return 排班计划信息
     */
    @PostMapping("/exportScheduleForFront")
    @ApiOperation(value = "根据车间，开始时间，结束时间获取排班计划信息")
    public ExcelExportDto exportScheduleForFront(@RequestBody ScheduleQuery query) {
        return scheduleService.exportScheduleForFront(
                query.getAreaId(), query.getCellId(), query.getStartTime(), query.getEndTime());
    }

    /**
     * 批量创建假期，排班计划，通用排班
     *
     * @param scheduleBatchDTO 批量创建参数
     */
    @PostMapping("/createOrUpdateScheduleBatch")
    @ApiOperation(value = "批量创建或更新车间排班计划")
    public void createOrUpdateScheduleBatch(
            @RequestBody ScheduleBatchDTO scheduleBatchDTO) {
        if (scheduleBatchDTO.getWorkOfDay() == null) {
            scheduleBatchDTO.setWorkOfDay(Arrays.stream(DayOfWeek.values()).map(DayOfWeek::getValue).collect(Collectors.toList()));
        }
        if (scheduleBatchDTO.getSundayWork() == null) {
            scheduleBatchDTO.setSundayWork(true);
        }
        if (scheduleBatchDTO.getSaturdayWork() == null) {
            scheduleBatchDTO.setSaturdayWork(true);
        }
        AutomaticSchedulingDTO schedulingDTO = new AutomaticSchedulingDTO();
        schedulingDTO.setEndTime(scheduleBatchDTO.getEndTime());
        schedulingDTO.setStartTime(scheduleBatchDTO.getStartTime());
        schedulingDTO.setCellId(scheduleBatchDTO.getCellId());
        schedulingDTO.setAreaId(scheduleBatchDTO.getAreaId());
        schedulingDTO.setWorkOfDay(scheduleBatchDTO.getWorkOfDay());
        schedulingDTO.setWorkOvertimeOnSaturday(scheduleBatchDTO.getSaturdayWork());
        schedulingDTO.setWorkOvertimeOnSunday(scheduleBatchDTO.getSundayWork());
        schedulingDTO.setShiftIds(scheduleBatchDTO.getCrewShiftDTOS().stream().map(CrewAndShift::getShiftId).collect(Collectors.toList()));
        schedulingDTO.setCrewIds(scheduleBatchDTO.getCrewShiftDTOS().stream().map(CrewAndShift::getCrewId).collect(Collectors.toList()));
        scheduleService.createAutomaticScheduling(schedulingDTO);
    }

    /**
     * 根据车间ID获取班次班组信息
     *
     * @param areaId 车间ID
     * @param cellId 产线ID
     * @return 班组班次信息
     */
    @GetMapping("/getCrewAndShiftInfoByAreaIdAndCellId/{areaId}/{cellId}")
    @ApiOperation(value = "根据车间ID获取班次班组信息")
    public AreaCrewShiftDTO getCrewAndShiftInfoByAreaIdCellId(
            @PathVariable int areaId, @PathVariable int cellId) {
        //获取车间班组信息
        List<CrewWithMemberDTO> crewWithMemberDTOS = crewService.getByAreaIdAndCellId(areaId, areaId);
        //获取车间班次信息
        List<ShiftDTO> shiftDTOS = shiftService.getShiftListByAreaIdAndCellId(areaId, cellId);
        //组合成DTO返回
        return new AreaCrewShiftDTO(crewWithMemberDTOS, shiftDTOS);
    }

    /**
     * 新增或者创建一天的排班计划
     *
     * @param scheduleDailyInfoDTO 一天的排班计划
     * @return 新增排班计划
     */
    @PostMapping("/createOrUpdateScheduleDay")
    @ApiOperation(value = "新增或者创建一天的排班计划")
    public ScheduleDailyDTO createOrUpdateScheduleDay(
            @RequestBody ScheduleDailyDTO scheduleDailyInfoDTO) {
        return scheduleService.createOrUpdateScheduleDay(scheduleDailyInfoDTO);
    }

    /**
     * 根据时间查询班次信息，以及上五次和下五次的班次信息
     *
     * @param query 查询条件
     * @return 班次信息
     */
    @PostMapping("/getShiftRealTime")
    @ApiOperation(value = "根据时间查询班次信息，以及上五次和下五次的班次信息")
    public ShiftInfoRelatedDTO getShiftRealTime(@RequestBody ScheduleTimeQuery query) {
        return scheduleService.getShiftReaTime(query.getTime(), query.getAreaId(), query.getCellId());
    }


    /**
     * 根据时间查询班次信息，以及上五次和下五次的班次信息
     *
     * @param query 查询条件
     * @return 班次信息
     */
    @PostMapping("/getCurrentSchedule")
    @ApiOperation(value = "根据时间查询当前排班信息")
    public ShiftInfoWithBreakTimeDTO getCurrentSchedule(@RequestBody ScheduleTimeQuery query) {
        return scheduleService.getCurrentSchedule(query.getTime(), query.getAreaId(), query.getCellId());
    }

    /**
     * 根据开始结束时间循环排班
     *
     * @param automaticSchedulingDTO 排班信息
     */
    @PostMapping("/createAutomaticScheduling")
    @ApiOperation(value = "根据开始结束时间循环排班")
    public void createAutomaticScheduling(
            @Valid @RequestBody AutomaticSchedulingDTO automaticSchedulingDTO) {

        if (automaticSchedulingDTO.getWorkOfDay() == null) {
            automaticSchedulingDTO.setWorkOfDay(Arrays.stream(DayOfWeek.values()).map(DayOfWeek::getValue).collect(Collectors.toList()));
        }
        if (automaticSchedulingDTO.getWorkOvertimeOnSunday() == null) {
            automaticSchedulingDTO.setWorkOvertimeOnSunday(true);
        }
        if (automaticSchedulingDTO.getWorkOvertimeOnSaturday() == null) {
            automaticSchedulingDTO.setWorkOvertimeOnSaturday(true);
        }
        scheduleService.createAutomaticScheduling(automaticSchedulingDTO);
    }


    /**
     * 用来判断是否有时区问题的方法
     *
     * @return map
     */
    @PostMapping("/showDate")
    @ApiOperation(value = "验证时区问题的方法")
    public Map<String, Object> showDate() {
        return scheduleService.showDate();
    }

    /**
     * 获取当天开始时间，收工时间"
     *
     * @param date 日期
     * @return 开始结束时间
     */
    @PostMapping("/getWorkTime")
    @ApiOperation(value = "获取当天开始时间，收工时间")
    public WorkTime getWorkTime(@RequestBody WorkTimeQuery date) {
        return scheduleService.getWorkTime(date);
    }


}