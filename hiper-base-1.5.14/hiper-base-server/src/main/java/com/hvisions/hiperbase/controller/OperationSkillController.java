package com.hvisions.hiperbase.controller;

import com.hvisions.hiperbase.route.dto.skill.OperationSkillDTO;
import com.hvisions.hiperbase.service.route.OperationSkillService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: OperationSkillController</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/4/16</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/operationSkill")
@Api(description = "工艺操作技能")
public class OperationSkillController {


    @Autowired
    OperationSkillService operationSkillService;

    /**
     * 绑定工艺操作技能
     *
     * @param operationSkillDTOS 工序技能绑定对象信息
     */
    @PostMapping(value = "/bindOperationSkill")
    @ApiOperation(value = "绑定工艺操作技能")
    public void bindOperationSkill(@RequestBody List<OperationSkillDTO> operationSkillDTOS) {
        operationSkillService.bindOperationSkill(operationSkillDTOS);
    }

    /**
     * 更新工艺操作技能
     *
     * @param operationSkillDTOS 工序技能绑定对象信息
     */
    @ApiOperation(value = "更新工艺操作技能")
    @PutMapping(value = "/updateOperationSkill")
    public void updateOperationSkill(@RequestBody List<OperationSkillDTO> operationSkillDTOS) {
        operationSkillService.updateOperationSkill(operationSkillDTOS);
    }


    /**
     * 根据Id删除工艺操作技能关系
     *
     * @param id 工序技能关系
     */
    @ApiOperation(value = "根据Id删除工艺操作技能关系")
    @DeleteMapping(value = "/deleteById/{id}")
    public void deleteById(@PathVariable int id) {
        operationSkillService.deleteById(id);
    }


    /**
     * 根据工艺操作ID查询工序技能
     *
     * @param operationId 工艺操作ID
     * @return 工序技能
     */
    @ApiOperation(value = "根据工艺操作ID查询工序技能")
    @GetMapping(value = "/getAllByOperationId/{operationId}")
    public List<OperationSkillDTO> getAllByOperationId(@PathVariable int operationId) {
        return operationSkillService.getAllByOperationId(operationId);
    }
}