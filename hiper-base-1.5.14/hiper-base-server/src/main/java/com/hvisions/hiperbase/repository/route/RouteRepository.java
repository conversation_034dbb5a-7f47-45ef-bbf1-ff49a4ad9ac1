package com.hvisions.hiperbase.repository.route;

import com.hvisions.hiperbase.entity.route.HvBmRoute;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * <p>Title: RouteRepository</p>
 * <p>Description: 工艺路线仓储</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/12/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface RouteRepository extends JpaRepository<HvBmRoute, Integer> {

    /**
     * 获取物料对应的工艺
     *
     * @param productId 物料id
     * @return 工艺列表
     */
    List<HvBmRoute> findAllByProductId(Integer productId);

    /**
     * 根据物料列表和工艺类型查询
     *
     * @param productIds 物料id列表
     * @param routeType  工艺路线类型
     * @return 对应的数据列表
     */
    List<HvBmRoute> findAllByProductIdInAndRouteType(List<Integer> productIds, Integer routeType);

    Optional<HvBmRoute> findByDestinationLikeAndProductIdIsNull(String destination);
}




    
    
    
    