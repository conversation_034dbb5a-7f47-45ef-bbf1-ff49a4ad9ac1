package com.hvisions.hiperbase.service.equipment.imp;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.hiperbase.dao.equipment.EquipmentSwotUnitMapper;
import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentSwotUnit;
import com.hvisions.hiperbase.equipment.EquipmentSwotUnitDto;
import com.hvisions.hiperbase.repository.equipment.EquipmentSwotUnitRepository;
import com.hvisions.hiperbase.service.equipment.EquipmentSwotUnitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>Title: EquipmentSwotUnitImpl</p >
 * <p>Description: </p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/9</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Service
public class EquipmentSwotUnitImpl implements EquipmentSwotUnitService {

    private final EquipmentSwotUnitRepository equipmentSwotUnitRepository;

    private final EquipmentSwotUnitMapper equipmentSwotUnitMapper;

    @Autowired
    public EquipmentSwotUnitImpl(EquipmentSwotUnitRepository equipmentSwotUnitRepository,
                                 EquipmentSwotUnitMapper equipmentSwotUnitMapper) {
        this.equipmentSwotUnitRepository = equipmentSwotUnitRepository;
        this.equipmentSwotUnitMapper = equipmentSwotUnitMapper;
    }

    /**
     * 新增设备读数单位
     *
     * @param equipmentSwotUnitDto 设备读数单位dto
     */
    @Override
    public void save(EquipmentSwotUnitDto equipmentSwotUnitDto) {
        HvBmEquipmentSwotUnit hvBmEquipmentSwotUnit = DtoMapper.convert(equipmentSwotUnitDto, HvBmEquipmentSwotUnit.class);
        equipmentSwotUnitRepository.save(hvBmEquipmentSwotUnit);

    }

    /**
     * 修改设备读数单位
     *
     * @param equipmentSwotUnitDto 设备读数单位dto
     */
    @Override
    public void update(EquipmentSwotUnitDto equipmentSwotUnitDto) {
        HvBmEquipmentSwotUnit hvBmEquipmentSwotUnit = DtoMapper.convert(equipmentSwotUnitDto, HvBmEquipmentSwotUnit.class);
        equipmentSwotUnitRepository.save(hvBmEquipmentSwotUnit);
    }

    /**
     * 删除设备读数单位
     *
     * @param id 设备读数单位id
     */
    @Override
    public void deleteById(Integer id) {
        equipmentSwotUnitRepository.deleteById(id);
    }

    /**
     * 查询设备读数单位
     *
     * @return 设备读数单位dto
     */
    @Override
    public List<EquipmentSwotUnitDto> findAll() {
        return equipmentSwotUnitMapper.findAll();
    }
}


