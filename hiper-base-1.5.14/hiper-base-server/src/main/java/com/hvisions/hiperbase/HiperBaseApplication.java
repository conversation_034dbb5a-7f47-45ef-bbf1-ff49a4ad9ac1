package com.hvisions.hiperbase;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

/**
 * <p>Title: DemoApplication</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/7</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@SpringBootApplication
@EnableDiscoveryClient
@MapperScan("com.hvisions.hiperbase.dao")
@EnableFeignClients(basePackages = {"com.hvisions.framework.client",
        "com.hvisions.hiperbase.client", "com.hvisions.pms.client", "com.hvisions.thridparty.client", "com.hvisions.wms.client"})
public class HiperBaseApplication extends SpringBootServletInitializer {

    public static void main(String[] args) {
        SpringApplication.run(HiperBaseApplication.class, args);
    }

    /**
     * 可以使得项目用war包部署
     *
     * @param builder builder
     * @return builder
     */
    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        return builder.sources(HiperBaseApplication.class);
    }


}
