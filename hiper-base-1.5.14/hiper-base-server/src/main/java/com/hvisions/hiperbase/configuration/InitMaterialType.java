package com.hvisions.hiperbase.configuration;

import com.hvisions.common.runner.SafetyCommandLineRunner;
import com.hvisions.hiperbase.entity.material.HvBmMaterialType;
import com.hvisions.hiperbase.repository.material.MaterialTypeRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>Title: InitialingzingBeanTest</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-09-09</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Component
@Slf4j
public class InitMaterialType extends SafetyCommandLineRunner {

    private final MaterialTypeRepository materialTypeRepository;

    @Autowired
    public InitMaterialType(MaterialTypeRepository materialTypeRepository) {
        this.materialTypeRepository = materialTypeRepository;
    }

    @Override
    public void callRunner(String... args) throws Exception {
        log.info("初始化物料类型开始");
        log.info("检查物料类型是否经过初始化");
        List<HvBmMaterialType> types = materialTypeRepository.findAll();
        if (types.size() > 0) {
            log.info("物料类型已经创建过，不需要重复创建，直接返回");
            return;
        }
        log.info("物料类型数据为空，进行初始化。。。。");
        List<HvBmMaterialType> list = new ArrayList<>();
        HvBmMaterialType productDto = new HvBmMaterialType();
        productDto.setMaterialTypeName("成品");
        productDto.setMaterialTypeCode("Product");
        productDto.setSortNum(1);
        list.add(productDto);

        HvBmMaterialType semiDto = new HvBmMaterialType();
        semiDto.setMaterialTypeName("半成品");
        semiDto.setMaterialTypeCode("Semi_Product");
        semiDto.setSortNum(2);
        list.add(semiDto);


        HvBmMaterialType rawDto = new HvBmMaterialType();
        rawDto.setMaterialTypeName("原材料");
        rawDto.setMaterialTypeCode("Raw_Material");
        rawDto.setSortNum(3);
        list.add(rawDto);


        HvBmMaterialType subMaterialDto = new HvBmMaterialType();
        subMaterialDto.setMaterialTypeName("辅材");
        subMaterialDto.setMaterialTypeCode("Sub_Material");
        subMaterialDto.setSortNum(4);
        list.add(subMaterialDto);


        HvBmMaterialType packMaterialDto = new HvBmMaterialType();
        packMaterialDto.setMaterialTypeName("包材");
        packMaterialDto.setMaterialTypeCode("Packing_Material");
        packMaterialDto.setSortNum(5);
        list.add(packMaterialDto);


        HvBmMaterialType byProductDto = new HvBmMaterialType();
        byProductDto.setMaterialTypeName("副产品");
        byProductDto.setMaterialTypeCode("By_Product");
        byProductDto.setSortNum(6);
        list.add(byProductDto);
        for (HvBmMaterialType type : list) {
            type.setParentId(0);
            materialTypeRepository.save(type);
        }
        log.info("初始化物料类型结束");
    }
}