package com.hvisions.hiperbase.controller;

import com.hvisions.hiperbase.equipment.EquipmentPurchaseDto;
import com.hvisions.hiperbase.service.equipment.EquipmentPurchaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>Title: EquipmentPurchaseController</p >
 * <p>Description: 设备采购信息控制器</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/5</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */

@RestController
@RequestMapping("/equipmentPurchase")
@Api(tags = "设备采购控制器")
public class EquipmentPurchaseController {

    private final EquipmentPurchaseService equipmentPurchaseService;

    @Autowired
    public EquipmentPurchaseController(EquipmentPurchaseService equipmentPurchaseService) {
        this.equipmentPurchaseService = equipmentPurchaseService;
    }


    @ApiOperation("保存设备采购信息")
    @PostMapping("/save")
    public void equipmentPurchase(@Valid @RequestBody EquipmentPurchaseDto equipmentPurchaseDto) {
        equipmentPurchaseService.save(equipmentPurchaseDto);
    }

    @ApiOperation("根据设备id查询设备采购信息")
    @GetMapping("/getByEquipmentId/{equipmentId}")
    public EquipmentPurchaseDto getByEquipmentId(@PathVariable int equipmentId) {
        return equipmentPurchaseService.findByEquipmentId(equipmentId);
    }
}
