package com.hvisions.hiperbase.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.EasyExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.bom.dto.*;
import com.hvisions.hiperbase.entity.material.HvBmFrame;
import com.hvisions.hiperbase.excel.HvBmFrameDownloadTemplate;
import com.hvisions.hiperbase.materials.dto.FrameTypeDTO;
import com.hvisions.hiperbase.path.HvBmPathDTO;
import com.hvisions.hiperbase.service.material.FrameTypeService;
import com.hvisions.hiperbase.service.material.HvBmFrameService;
import com.hvisions.hiperbase.service.transportTool.TransportToolTypeService;
import com.hvisions.pms.dto.materialRequirement.HvPmMaterialBomItemDTO;
import com.hvisions.pms.dto.materialRequirement.MaterialRequirementQueryDTO;
import com.hvisions.thirdparty.common.dto.IWMSFrameDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 料框主数据Controller
 *
 * <AUTHOR>
 * @date 2024-04-02 10:56
 */

@RestController
@RequestMapping(value = "/hvBmFrame")
@Api(description = "料框主数据")
public class HvBmFrameController {
    @Autowired
    private HvBmFrameService hvBmFrameService;
    @Autowired
    private FrameTypeService frameTypeService;
    @Autowired
    private TransportToolTypeService transportToolTypeService;

    /**
     * 分页查询
     */
    @ApiOperation("分页查询")
    @PostMapping("/list")
    public ResultVO list(@RequestBody HvBmFrame hvBmFrame,
                         @RequestParam(name = "pageNum", defaultValue = "1") Integer pageNo,
                         @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {

        Page<HvBmFrame> page = new Page<>(pageNo, pageSize);
        return ResultVO.success(hvBmFrameService.findPage(page, hvBmFrame));
    }

    /**
     * 列表查询
     * @param hvBmFrame
     * @param pageNo
     * @param pageSize
     * @return
     */
    @ApiOperation("列表查询")
    @PostMapping("/getList")
    public ResultVO getList(@RequestBody HvBmFrame hvBmFrame,
                         @RequestParam(name = "pageNum", defaultValue = "1") Integer pageNo,
                         @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {

        Page<HvBmFrame> page = new Page<>(pageNo, pageSize);
        return ResultVO.success(hvBmFrameService.findPage(page, hvBmFrame));
    }

    /**
     * 导入
     *
     * @param file 文件
     */
    @PostMapping("/import")
    @ApiOperation("导入")
    public ResultVO importData(MultipartFile file, @UserInfo @ApiIgnore UserInfoDTO userInfo) {

        return hvBmFrameService.importData(file, userInfo);

    }


    /**
     * 导出
     *
     * @param hvBmFrame
     */
    @ApiOperation(value = "导出")
    @PostMapping(value = "/exportData")
    @ApiResultIgnore
    public ResultVO<ExcelExportDto> exportData(@RequestBody HvBmFrame hvBmFrame) {

        List<HvBmFrame> list = hvBmFrameService.findListByCondition(hvBmFrame);
        return ResultVO.success(EasyExcelUtil.getExcel(list, HvBmFrame.class, System.currentTimeMillis() + "料框主数据.xlsx"));

    }

    /**
     * 下载模版
     *
     */
    @ApiOperation(value = "下载模版")
    @PostMapping(value = "/downloadTemplate")
    @ApiResultIgnore
    public ResultVO<ExcelExportDto> downloadTemplate() {
        List<HvBmFrameDownloadTemplate> list = new ArrayList<>();
        return ResultVO.success(EasyExcelUtil.getExcel(list, HvBmFrameDownloadTemplate.class, System.currentTimeMillis() + "料框主数据导入模版.xlsx"));

    }


    /**
     * 添加
     *
     * @param hvBmFrame
     */
    @ApiOperation(value = "添加")
    @PostMapping(value = "/add")
    public ResultVO addHvPmAgvTaskRecord(@RequestBody HvBmFrame hvBmFrame, @UserInfo @ApiIgnore UserInfoDTO userInfo) {
        boolean isExist = hvBmFrameService.checkHvBmFrameCodesIsUnique(hvBmFrame.getFrameCode());
        if (isExist) {
            return ResultVO.error(500, "料框编号" + hvBmFrame.getFrameCode() + "已存在");
        }
        if (hvBmFrame.getFrameTypeId() == null ) {
            return ResultVO.error(500, "料框类型不能为空");
        }
        if (hvBmFrame.getTransportToolTypeCode() != null && !hvBmFrame.getTransportToolTypeCode().isEmpty()){
            boolean transportToolTypeCodeIsExist = transportToolTypeService.checkHvBmTransportToolTypeCodesIsUnique(hvBmFrame.getTransportToolTypeCode());
            if (!transportToolTypeCodeIsExist) {
                return ResultVO.error(500, "运输工具类型编号" + hvBmFrame.getTransportToolTypeCode() + "不存在");
            }
        }
        hvBmFrame.setCreatorId(userInfo.getUserName());
        hvBmFrame.setCreateTime(LocalDateTime.now());
        hvBmFrameService.save(hvBmFrame);
        return ResultVO.success("新增成功");

    }

    /**
     * 删除
     *
     * @param id 主键
     */
    @ApiOperation(value = "删除")
    @DeleteMapping(value = "/delete/{id}")
    public ResultVO deleteHvPmAgvTaskRecord(@PathVariable Integer id) {
        return ResultVO.success(hvBmFrameService.removeById(id));
    }

    /**
     * 修改
     *
     * @param hvBmFrame
     */
    @ApiOperation(value = "修改")
    @PutMapping(value = "/update")
    public ResultVO updateHvPmAgvTaskRecord(@RequestBody HvBmFrame hvBmFrame, @UserInfo @ApiIgnore UserInfoDTO userInfo) {
        boolean isExist = hvBmFrameService.checkHvBmFrameCodesIsUnique(hvBmFrame.getFrameCode());
        if (!isExist) {
            return ResultVO.error(500, "料框编号" + hvBmFrame.getFrameCode() + "不存在");
        }
        if (hvBmFrame.getTransportToolTypeCode() != null && !hvBmFrame.getTransportToolTypeCode().isEmpty()) {
            boolean transportToolTypeCodeIsExist = transportToolTypeService.checkHvBmTransportToolTypeCodesIsUnique(hvBmFrame.getTransportToolTypeCode());
            if (!transportToolTypeCodeIsExist) {
                return ResultVO.error(500, "运输工具类型编号" + hvBmFrame.getTransportToolTypeCode() + "不存在");
            }
        }
        hvBmFrame.setUpdaterId(userInfo.getUserName());
        hvBmFrame.setUpdateTime(LocalDateTime.now());
        return ResultVO.success(hvBmFrameService.updateById(hvBmFrame));
    }

    /**
     * 根据id获取
     *
     * @param id 主键
     * @return HvPmAgvTaskRecord hvPmAgvTaskRecordDTO
     */
    @ApiOperation(value = "根据id获取")
    @GetMapping(value = "/get/{id}")
    public ResultVO getList(@PathVariable Integer id) {
        return ResultVO.success(hvBmFrameService.getById(id));
    }

    /**
     * 查询全部
     *
     * @return 列表
     */
    @ApiOperation(value = "查询全部")
    @GetMapping(value = "/getAll")
    public List<HvBmFrame> getAll() {
        return hvBmFrameService.list();
    }

    /**
     * 根据料框编号查询
     *
     * @return 单个对象
     */
    @ApiOperation(value = "根据料框编号查询数据")
    @GetMapping(value = "/findByFrameCode")
    public HvBmFrameDTO findByFrameCode(@RequestParam String frameCode) {
        QueryWrapper<HvBmFrame> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("frame_code", frameCode);
        HvBmFrame frame = hvBmFrameService.getOne(queryWrapper);
        HvBmFrameDTO dto = DtoMapper.convert(frame, HvBmFrameDTO.class);
        if (frame != null) {
            FrameTypeDTO frameTypeDTO = frameTypeService.getFrameTypeById(frame.getFrameTypeId());
            dto.setFrameTypeCode(frameTypeDTO.getFrameTypeCode());
            dto.setFrameTypeName(frameTypeDTO.getFrameTypeName());
        }
        return dto;
    }

    /**
     * 根据料框类型查询
     *
     * @return 列表
     */
    @ApiOperation(value = "根据料框类型查询")
    @GetMapping(value = "/findByFrameType/{frameTypeCode}")
    public List<HvBmFrame> findByFrameType(@PathVariable("frameTypeCode") String frameTypeCode) {
        return hvBmFrameService.findByFrameType(frameTypeCode);
    }

    @ApiOperation(value = "将接收到的料框和料框类型的主数据入库")
    @PostMapping(value = "/saveFrameByReceiving")
    public void saveFrameByReceiving(@RequestBody List<IWMSFrameDTO> iwmsFrameDTOS) {
         hvBmFrameService.saveFrameByReceiving(iwmsFrameDTOS);
    }
    @PostMapping("/getHvBmFrame")
    @ApiOperation(value = "料框条件查询")
    public List<HvBmFrameDTO> getHvBmFrame(@RequestBody HvBmFrameDTO hvBmFrameDTO) {
        List<HvBmFrameDTO> list =  hvBmFrameService.getHvBmFrame(hvBmFrameDTO);
        return list;
    }

    @PostMapping("/getPageFrameStatus")
    @ApiOperation(value = "料框状态查询")
    public Page<FrameStatusDTO> getPageFrameStatus(@RequestBody FrameStatusQueryDTO queryDTO,
                            @RequestParam(name = "pageNum", defaultValue = "1") Integer pageNo,
                            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {

        Page<FrameStatusDTO> page = new Page<>(pageNo, pageSize);
        return hvBmFrameService.getStatusPage(page, queryDTO);
    }
    @ApiOperation(value = "根据料框编号查找料框主数据")
    @GetMapping(value = "/getByFrameCode")
    public HvBmFrameDTO getByFrameCode(@RequestParam String frameCode) {
        HvBmFrameDTO hvBmFrameDTO = hvBmFrameService.getByFrameCode(frameCode);
        return hvBmFrameDTO;
    }
    @PostMapping("/getPageMaterial")
    @ApiOperation(value = "料框子项物料分页")
    public Page<FrameMaterialItemDTO> getPageMaterial(@RequestBody FrameStatusMaterialItemQueryDTO queryDTO,
                                                      @RequestParam(name = "pageNum", defaultValue = "1") Integer pageNo,
                                                      @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        Page<FrameMaterialItemDTO> page = new Page<>(pageNo, pageSize);
        return hvBmFrameService.getPageMaterial(page,queryDTO);
    }


}
