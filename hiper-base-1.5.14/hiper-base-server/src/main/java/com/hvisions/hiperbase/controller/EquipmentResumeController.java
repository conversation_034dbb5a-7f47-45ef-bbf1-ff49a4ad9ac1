package com.hvisions.hiperbase.controller;

import com.hvisions.hiperbase.equipment.EquipmentResumeDto;
import com.hvisions.hiperbase.service.equipment.EquipmentResumeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>Title: EquipmentResumeController</p >
 * <p>Description: 设备履历记录</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/5</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */

@RestController
@RequestMapping("/equipmentResume")
@Api(tags = "设备履历记录控制器")
public class EquipmentResumeController {

    private final EquipmentResumeService equipmentResumeService;

    @Autowired
    public EquipmentResumeController(EquipmentResumeService equipmentResumeService) {
        this.equipmentResumeService = equipmentResumeService;
    }

    @ApiOperation(value = "根据设备id查询设备履历记录")
    @GetMapping("/findByEquipmentId/{equipmentId}")
    public EquipmentResumeDto findByEquipmentId(@PathVariable Integer equipmentId) {
        return equipmentResumeService.findByEquipmentId(equipmentId);
    }

}
