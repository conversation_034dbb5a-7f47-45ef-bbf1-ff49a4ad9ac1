package com.hvisions.hiperbase.repository.schedule;

import com.hvisions.hiperbase.entity.schedule.HvBmHoliday;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>Title: HvBmHolidayRepository</p>
 * <p>Description: 假期仓储层</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/12/3</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface HolidayRepository extends JpaRepository<HvBmHoliday, Integer> {

    /**
     * 根据时间查询假期
     *
     * @param cellId 产线id
     * @param areaId 车间id
     * @param begin  开始时间
     * @param end    结束时间
     * @return 假期列表
     */
    List<HvBmHoliday> findDistinctByCellIdAndAreaIdAndHolidayBetween(int cellId, int areaId, LocalDate begin, LocalDate end);


    /**
     * 根据开始结束时间删除假期信息
     *
     * @param areaId  车间id
     * @param cellId  产线id
     * @param holiday 假期日期
     */
    List<HvBmHoliday> findAllByAreaIdAndCellIdAndHoliday(int areaId, int cellId, LocalDate holiday);
}
