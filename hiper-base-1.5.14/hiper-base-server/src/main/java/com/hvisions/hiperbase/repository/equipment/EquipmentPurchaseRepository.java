package com.hvisions.hiperbase.repository.equipment;

import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentPurchase;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <p>Title: EquipmentPurchaseRepository</p >
 * <p>Description: 设备采购信息</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/5</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Repository
public interface EquipmentPurchaseRepository extends JpaRepository<HvBmEquipmentPurchase, Integer> {
    /**
     * 根据设备id查询设备采购信息
     *
     * @param equipmentId 设备id
     * @return 设备采购信息
     */
    HvBmEquipmentPurchase findOneByEquipmentId(Integer equipmentId);

    /**
     * 根据设备id删除设备采购信息
     *
     * @param equipmentId 设备id
     */
    void deleteAllByEquipmentId(Integer equipmentId);
}
