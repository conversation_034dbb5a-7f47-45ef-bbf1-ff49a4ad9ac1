package com.hvisions.hiperbase.controller;

import com.alibaba.excel.EasyExcel;
import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.utils.EasyExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.entity.material.HvBmFrameType;
import com.hvisions.hiperbase.materials.dto.FrameTypeDTO;
import com.hvisions.hiperbase.service.material.FrameTypeService;
import io.swagger.annotations.Api;
import com.hvisions.common.dto.ExcelExportDto;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title: HvBmFrameTypeController</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2024年3月29日</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/frameType")
@Api(description = "")
public class FrameTypeController {
    private final FrameTypeService frameTypeService;

    @Autowired
    public FrameTypeController(FrameTypeService frameTypeService) {
        this.frameTypeService = frameTypeService;
    }

    @ApiOperation(value = "分页查询HvBmFrameType信息")
    @PostMapping(value = "/query")
    public Page<FrameTypeDTO> queryHvBmFrameType(@RequestBody FrameTypeDTO frameTypeDTO) {
        return frameTypeService.queryFrameType(frameTypeDTO);
    }


    /**
     * 添加
     *
     * @param frameTypeDTO HvBmFrameType
     */
    @ApiOperation(value = "添加HvBmFrameType信息")
    @PostMapping(value = "/create")
    public void addHvBmFrameType(@Valid @RequestBody FrameTypeDTO frameTypeDTO) {
        frameTypeService.addFrameType(frameTypeDTO);
    }

    /**
     * 导入
     */
    @ApiOperation(value = "导入")
    @PostMapping(value = "/import")
    public ResultVO importData(MultipartFile file, @UserInfo @ApiIgnore UserInfo userInfo) {
        return frameTypeService.importData(file,userInfo);
    }

    @ApiOperation(value = "导出")
    @PostMapping(value = "/exportData")
    @ApiResultIgnore
    public ResultVO<ExcelExportDto> exportData(@RequestBody FrameTypeDTO frameTypeDTO) {
        List<HvBmFrameType> list = frameTypeService.findLIstByCondition(frameTypeDTO);
        return ResultVO.success(EasyExcelUtil.getExcel(list, HvBmFrameType.class,System.currentTimeMillis() + "料框类型.xlsx"));
    }

    /**
     * 删除
     *
     * @param id 主键
     */
    @ApiOperation(value = "删除HvBmFrameType信息")
    @DeleteMapping(value = "/delete/{id}")
    public void deleteHvBmFrameType(@PathVariable Integer id) {
        frameTypeService.deleteFrameType(id);
    }

    /**
     * 修改
     *
     * @param frameTypeDTO HvBmFrameType
     */
    @ApiOperation(value = "修改HvBmFrameType")
    @PutMapping(value = "/update")
    public void updateHvBmFrameType(@Valid @RequestBody FrameTypeDTO frameTypeDTO) {
        frameTypeService.updateFrameType(frameTypeDTO);
    }

    /**
     * 根据id获取
     *
     * @param id 主键
     * @return HvBmFrameType hvBmFrameTypeDTO
     */
    @ApiOperation(value = "根据id获取HvBmFrameType")
    @GetMapping(value = "/get/{id}")
    public FrameTypeDTO getList(@PathVariable Integer id) {
        return frameTypeService.getFrameTypeById(id);
    }

    /**
     * 查询全部
     * @return 列表
     */
    @ApiOperation(value = "获取HvBmFrameType列表")
    @GetMapping(value = "/getAll")
    public List<FrameTypeDTO> getAll(){
        return frameTypeService.getAll();
    }

}
