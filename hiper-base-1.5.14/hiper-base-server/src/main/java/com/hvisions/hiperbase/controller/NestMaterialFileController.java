package com.hvisions.hiperbase.controller;

import com.hvisions.hiperbase.entity.material.NestMaterialFile;
import com.hvisions.hiperbase.service.material.NestMaterialFileService;
import com.hvisions.thirdparty.common.dto.NestingMaterialFileDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/nestMaterialFile")
@Slf4j
@Api(tags = "套料系统的物料零件图")
public class NestMaterialFileController {

    @Resource
    private NestMaterialFileService nestMaterialFileService;


    @PostMapping(value = "/getByMaterialCode")
    @ApiOperation(value = "查询零件图地址")
    public NestMaterialFile getByMaterialCode(@RequestParam("materialCode")String materialCode){
        return nestMaterialFileService.getByMaterialCode(materialCode);
    }

    @PostMapping(value = "/getByMaterialCodeAndShipNo")
    @ApiOperation(value = "查询零件图地址")
    public NestMaterialFile getByMaterialCodeAndShipNo(@RequestParam("materialCode") String materialCode, @RequestParam("ShipNo") String ShipNo) {
        return nestMaterialFileService.getByMaterialCodeAndShipNo(materialCode, ShipNo);
    }

    @PostMapping(value = "/saveBatch")
    @ApiOperation(value = "保存")
    public void saveBatch(@RequestBody List<NestingMaterialFileDTO> nestMaterialFileDTOList){
         nestMaterialFileService.saveBatch(nestMaterialFileDTOList);
    }
}
