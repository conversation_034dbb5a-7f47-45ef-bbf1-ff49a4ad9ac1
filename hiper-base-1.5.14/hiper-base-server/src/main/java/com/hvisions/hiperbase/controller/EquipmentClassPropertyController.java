package com.hvisions.hiperbase.controller;

import com.hvisions.hiperbase.equipment.equipmentclass.EquipmentClassPropertyDTO;
import com.hvisions.hiperbase.service.equipment.EquipmentClassPropertyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title: EquipmentClassPropertyController</p>
 * <p>Description: 设备属性类型属性控制器</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/3/25</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping("/equipmentClassProperty")
@Api(description = "属性控制器")
public class EquipmentClassPropertyController {
    private EquipmentClassPropertyService equipmentClassPropertyService;

    @Autowired
    public EquipmentClassPropertyController(EquipmentClassPropertyService equipmentClassPropertyService) {
        this.equipmentClassPropertyService = equipmentClassPropertyService;
    }

    /**
     * 创建属性
     *
     * @param propertyDTO 属性
     * @return 主键
     */
    @PostMapping("/create")
    @ApiOperation("创建属性")
    public Integer create(@Valid @RequestBody EquipmentClassPropertyDTO propertyDTO) {
        return equipmentClassPropertyService.create(propertyDTO);
    }

    /**
     * 更新属性
     *
     * @param propertyDTO 属性
     */
    @PutMapping("/update")
    @ApiOperation("更新属性")
    public void update(@Valid @RequestBody EquipmentClassPropertyDTO propertyDTO) {
        equipmentClassPropertyService.update(propertyDTO);
    }

    /**
     * 删除属性
     *
     * @param id 主键
     */
    @DeleteMapping("/deleteById/{id}")
    @ApiOperation("删除属性")
    public void deleteById(@PathVariable Integer id) {
        equipmentClassPropertyService.deleteById(id);
    }


    /**
     * 获取属性
     *
     * @param id 设备类型id
     */
    @GetMapping("/findByEquipmentClassId/{id}")
    @ApiOperation("获取属性")
    public List<EquipmentClassPropertyDTO> findByEquipmentClassId(@PathVariable Integer id) {
        return equipmentClassPropertyService.findByEquipmentClassId(id);
    }
}









