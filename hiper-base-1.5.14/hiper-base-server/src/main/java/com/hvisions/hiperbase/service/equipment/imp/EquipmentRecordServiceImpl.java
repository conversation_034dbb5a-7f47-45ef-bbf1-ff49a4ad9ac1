package com.hvisions.hiperbase.service.equipment.imp;

import cn.hutool.core.collection.CollectionUtil;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.hiperbase.dao.equipment.EquipmentMapper;
import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentOffLineRecord;
import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentOnlineRecord;
import com.hvisions.hiperbase.equipment.DelEquipmentEvent;
import com.hvisions.hiperbase.equipment.EquipmentOffLineRecordDto;
import com.hvisions.hiperbase.equipment.EquipmentOfflineDto;
import com.hvisions.hiperbase.equipment.EquipmentOnlineDto;
import com.hvisions.hiperbase.repository.equipment.EquipmentOfflineRepository;
import com.hvisions.hiperbase.repository.equipment.EquipmentOnlineRepository;
import com.hvisions.hiperbase.repository.equipment.EquipmentRepository;
import com.hvisions.hiperbase.service.equipment.EquipmentRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <p>Title: EquipmentRecordServiceImpl</p >
 * <p>Description: </p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/7</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Service
@Slf4j
public class EquipmentRecordServiceImpl implements EquipmentRecordService {

    private final EquipmentOnlineRepository equipmentOnlineRepository;

    private final EquipmentOfflineRepository equipmentOfflineRepository;

    private final EquipmentRepository equipmentRepository;

    private final EquipmentMapper equipmentMapper;

    @Autowired
    public EquipmentRecordServiceImpl(EquipmentOnlineRepository equipmentOnlineRepository,
                                      EquipmentOfflineRepository equipmentOfflineRepository,
                                      EquipmentRepository equipmentRepository,
                                      EquipmentMapper equipmentMapper) {
        this.equipmentOnlineRepository = equipmentOnlineRepository;
        this.equipmentOfflineRepository = equipmentOfflineRepository;
        this.equipmentRepository = equipmentRepository;
        this.equipmentMapper = equipmentMapper;
    }

    /**
     * 上线设备
     *
     * @param equipmentOnlineDto 设备上线信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void onlineEquipment(EquipmentOnlineDto equipmentOnlineDto) {
        equipmentRepository.findById(equipmentOnlineDto.getEquipmentId()).ifPresent(equipment -> {
            HvBmEquipmentOnlineRecord onlineRecord = DtoMapper.convert(equipmentOnlineDto, HvBmEquipmentOnlineRecord.class);
            //设置设备状态
            equipment.setEquipmentStatusId(equipmentOnlineDto.getEquipmentStatusId());
            equipmentRepository.saveAndFlush(equipment);
            equipmentOnlineRepository.save(onlineRecord);
        });
    }

    /**
     * 下线设备
     *
     * @param equipmentOfflineDto 设备下线信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void offlineEquipment(EquipmentOfflineDto equipmentOfflineDto) {
        equipmentRepository.findById(equipmentOfflineDto.getEquipmentId()).ifPresent(equipment -> {
            HvBmEquipmentOffLineRecord offLineRecord = DtoMapper.convert(equipmentOfflineDto, HvBmEquipmentOffLineRecord.class);
            //设置设备状态
            equipment.setEquipmentStatusId(equipmentOfflineDto.getEquipmentStatusId());
            equipmentRepository.saveAndFlush(equipment);
            equipmentOfflineRepository.save(offLineRecord);
        });
    }


    /**
     * 根据设备编号查询设备上下线记录
     *
     * @param equipmentId 设备编号
     * @return 设备上下线记录
     */
    @Override
    public List<EquipmentOffLineRecordDto> findEquipmentRecordByEquipmentId(Integer equipmentId) {
        List<EquipmentOffLineRecordDto> equipmentOfflineRecordDtos = new ArrayList<>();
        //上线记录
        List<HvBmEquipmentOffLineRecord> offLineRecords = equipmentOfflineRepository.findAllByEquipmentId(equipmentId);
        //下线记录
        List<HvBmEquipmentOnlineRecord> onlineRecords = equipmentOnlineRepository.findAllByEquipmentId(equipmentId);
        if (CollectionUtil.isNotEmpty(offLineRecords)) {
            //下线记录按时间升序排序
            offLineRecords = offLineRecords.stream().sorted(Comparator.comparing(HvBmEquipmentOffLineRecord::getOfflineTime))
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(onlineRecords)) {
                //上线时间升序排序
                onlineRecords = onlineRecords.stream().sorted(Comparator.comparing(HvBmEquipmentOnlineRecord::getOnlineTime))
                        .collect(Collectors.toList());
            }
            for (int i = 0; i < offLineRecords.size(); i++) {
                EquipmentOffLineRecordDto equipmentOffLineRecordDto = new EquipmentOffLineRecordDto();
                equipmentOffLineRecordDto.setEquipmentId(equipmentId);
                equipmentOffLineRecordDto.setOffLineTime(offLineRecords.get(i).getOfflineTime());
                equipmentOffLineRecordDto.setReason(offLineRecords.get(i).getDescription());
                AtomicReference<String> onlineOperator = new AtomicReference<>("");
                try {
                    LocalDateTime onlineTime = Optional.ofNullable(onlineRecords.get(i))
                            .map(HvBmEquipmentOnlineRecord::getOnlineTime).orElse(null);
                    //设置影响产线时间
                    Optional.ofNullable(onlineRecords.get(i)).map(HvBmEquipmentOnlineRecord::getEffectCellTime)
                            .ifPresent(equipmentOffLineRecordDto::setEffectCellTime);
                    Optional.ofNullable(onlineRecords.get(i)).ifPresent(o ->
                            onlineOperator.set(equipmentMapper.findUserNameByUserId(o.getCreatorId())));
                    if (onlineTime != null) {
                        //设置上线时间
                        equipmentOffLineRecordDto.setOnLineTime(onlineTime);
                        //停机时长(分钟)
                        equipmentOffLineRecordDto.setStopInternal(Duration.between(offLineRecords.get(i).getOfflineTime(), onlineTime).toMinutes());
                    }
                } catch (Exception ignored) {
                }
                //设置上下线操作人
                String offOperator = equipmentMapper.findUserNameByUserId(offLineRecords.get(i).getCreatorId());
                String operator = String.format("下线: %s, 上线: %s", offOperator, onlineOperator.get());
                equipmentOffLineRecordDto.setOperator(operator);
                equipmentOfflineRecordDtos.add(equipmentOffLineRecordDto);
            }
        }
        //下线时间降序排序
        return equipmentOfflineRecordDtos.stream().sorted(Comparator.comparing(EquipmentOffLineRecordDto::getOffLineTime).reversed())
                .collect(Collectors.toList());
    }

    /**
     * 删除设备事件处理
     *
     * @param event 删除设备事件
     */
    @Transactional(rollbackFor = Exception.class)
    @EventListener(DelEquipmentEvent.class)
    public void handleDelEvent(DelEquipmentEvent event) {
        //删除离线记录
        equipmentOfflineRepository.deleteAllByEquipmentId(event.getEquipmentId());
        //删除上线记录
        equipmentOnlineRepository.deleteAllByEquipmentId(event.getEquipmentId());
    }
}
