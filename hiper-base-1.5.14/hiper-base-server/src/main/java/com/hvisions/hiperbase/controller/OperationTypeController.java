package com.hvisions.hiperbase.controller;

import com.hvisions.common.dto.ExtendColumnInfo;
import com.hvisions.common.dto.ExtendInfo;
import com.hvisions.common.service.BaseExtendService;
import com.hvisions.hiperbase.route.dto.OperationTypeDTO;
import com.hvisions.hiperbase.route.dto.OperationTypeQueryDTO;
import com.hvisions.hiperbase.route.dto.OperationTypeWithListDTO;
import com.hvisions.hiperbase.service.route.OperationTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>Title: OperationController</p>
 * <p>Description: 节点类型控制器</p>
 * <p>Company: www.h-visions.com</p>
 * <p>add date: 2018/12/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@Api(description = "节点类型控制器")
@RequestMapping("/operationType")
public class OperationTypeController {
    @SuppressWarnings("SpringJavaAutowiredFieldsWarningInspection")
    @Autowired
    OperationTypeService operationTypeService;


    @Resource(name = "operation_type")
    BaseExtendService baseExtendService;

    //-----------------节点类型----------------------

    /**
     * 新增节点类型
     *
     * @param operationTypeDTO 节点类型
     * @return 新增的节点类型id
     */
    @PostMapping("/addOperationType")
    @ApiOperation(value = "新增节点类型")
    public OperationTypeDTO addOperationType(@RequestBody OperationTypeDTO operationTypeDTO) {
        return operationTypeService.addOperationType(operationTypeDTO);

    }

    /**
     * 删除节点类型
     *
     * @param id 节点类型id
     */
    @DeleteMapping("/deleteOperationTypeById/{id}")
    @ApiOperation(value = "根据节点类型id删除节点类型")
    public void deleteOperationTypeById(@PathVariable int id) {
        operationTypeService.deleteOperationTypeById(id);
    }

    /**
     * 更新节点类型
     *
     * @param operationTypeDTO 节点类型dto
     * @return 节点类型id
     */
    @PutMapping("/updateOperationType")
    @ApiOperation(value = "更新节点类型")
    public OperationTypeDTO updateOperationType(@RequestBody OperationTypeDTO operationTypeDTO) {
        return operationTypeService.updateOperationType(operationTypeDTO);
    }

    /**
     * 根据id查询节点类型
     *
     * @param id 节点类型id
     * @return 节点类型DTO
     */
    @GetMapping("/getOperationTypeById/{id}")
    @ApiOperation(value = "根据id查询节点类型")
    public OperationTypeWithListDTO getOperationTypeById(@PathVariable int id) {
        return operationTypeService.getOperationTypeById(id);
    }


    /**
     * 查询所有节点类型
     *
     * @return list节点类型DTO
     */
    @GetMapping("/getAllOperationType")
    @ApiOperation(value = "查询所有节点类型")
    public List<OperationTypeDTO> getAllOperationType() {
        return operationTypeService.getAllOperationType();
    }

    /**
     * 根据节点类型名称和编码查询分页信息
     *
     * @param operationTypeQueryDTO 查询DTO
     * @return 节点类型分页数据
     */
    @PostMapping("/getOperationTypePageByCodeAndName")
    @ApiOperation(value = "根据节点类型名称和编码查询分页信息")
    public Page<OperationTypeDTO> getOperationTypePageByCodeAndName(@RequestBody OperationTypeQueryDTO operationTypeQueryDTO) {
        return operationTypeService.getOperationTypePageByCodeAndName(operationTypeQueryDTO);
    }


    //-----------------关联关系----------------------



    //-----------------关联关系----------------------

    /**
     * 添加工艺操作扩展属性
     *
     * @param extendColumnInfo 扩展属性信息
     */
    @PostMapping("/addOperationTypeColumn")
    @ApiOperation(value = "添加工艺操作类型扩展属性")
    public void addOperationTypeColumn(@RequestBody ExtendColumnInfo extendColumnInfo) {
        baseExtendService.addExtend(extendColumnInfo);
    }

    /**
     * 删除工艺操作扩展属性
     *
     * @param columnName 扩展属性名称
     */
    @DeleteMapping("/deleteOperationTypeColumnByColumnName/{columnName}")
    @ApiOperation(value = "删除工艺操作类型扩展属性")
    public void deleteOperationTypeColumnByColumnName(@PathVariable String columnName) {
        baseExtendService.dropExtend(columnName);
    }

    /**
     * 更新工艺操作扩展属性
     *
     * @param extendInfo 扩展属性名称
     */
    @PutMapping("/updateOperationTypeExtend")
    @ApiOperation(value = "更新工艺操作类型扩展属性")
    public void updateOperationTypeExtend(@RequestBody ExtendInfo extendInfo) {
        baseExtendService.updateExtendInfo(extendInfo);
    }


    /**
     * 获取工艺操作所有扩展字段信息
     *
     * @return 扩展字段信息
     */
    @GetMapping("/getOperationTypeExtendColumnInfo")
    @ApiOperation(value = "获取工艺操作类型所有扩展字段信息")
    public List<ExtendColumnInfo> getOperationTypeExtendColumnInfo() {
        return baseExtendService.getExtendColumnInfo();
    }
}









