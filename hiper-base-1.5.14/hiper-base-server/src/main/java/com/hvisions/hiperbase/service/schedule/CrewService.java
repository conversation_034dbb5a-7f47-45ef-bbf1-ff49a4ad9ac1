package com.hvisions.hiperbase.service.schedule;

import com.hvisions.hiperbase.schedule.dto.*;

import java.util.List;

/**
 * <p>Title: CrewService</p>
 * <p>Description: 班组service</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */

public interface CrewService {
    /**
     * 添加班组
     *
     * @param crewDTO 班组
     * @return 班组id
     */
    int createOrUpdate(CrewDTO crewDTO);

    /**
     * 添加班组，附带人员信息
     *
     * @param crewWithMemberIdsDTO 人员id列表
     * @return 班组id
     */
    int createOrUpdateCrewWithIds(CrewWithMemberIdsDTO crewWithMemberIdsDTO);

    /**
     * 删除班组
     *
     * @param id 班组id
     */
    void delete(int id);


    /**
     * 根据车间id查询所有班组信息
     *
     * @param areaId 车间id
     * @param cellId 产线id
     * @return 班组信息
     */
    List<CrewWithMemberDTO> getByAreaIdAndCellId(int areaId, int cellId);

    /**
     * 根据班组id查询班组信息
     *
     * @param id 班组信息
     * @return 班组信息
     */
    CrewWithMemberDTO getById(int id);

    /**
     * 根据班组编码查询班组信息
     *
     * @param crewCode 班组编码
     * @param areaId 车间id
     * @param cellId 产线id
     * @return 班组信息
     */
    CrewWithMemberDTO getByCode(String crewCode, Integer areaId, Integer cellId);

    /**
     * 根据班组编码查询班组信息
     *
     * @param crewCodeList 班组编码集合
     * @param areaId 车间id
     * @param cellId 产线id
     * @return 班组信息
     */
    List<CrewWithMemberDTO> getByCodeList(List<String> crewCodeList, Integer areaId, Integer cellId);

    /**
     * 更新班组成员信息
     *
     * @param crewMember 班组成员对应关系
     */
    void update(CrewMember crewMember);

    /**
     * 根据人员id查询班组列表
     * @param userId 人员id
     * @return 班组列表
     */
    List<CrewDTO> getCrewByUserId(int userId);
    /**
     * 根据车间产线id查询班组班次信息
     *
     * @param areaId 车间id
     * @param cellId 产线id
     * @return 班组班次信息
     */
    ShiftCrewQueryResult getCrewShiftByAreaIdAndCellId(int areaId, int cellId);

    /**
     * 通编码删除班组
     * @param crewCode 班组编码
     * @param areaId 车间id
     * @param cellId 产线id
     */
    void deleteByCode(String crewCode, Integer areaId, Integer cellId);

    CrewWithMemberDTO getCrewByCrewCode(String crewCode);
}

    
    
    
    
    
    
    
    
    
    
    