package com.hvisions.hiperbase.repository.equipment;

import com.hvisions.hiperbase.entity.equipment.HvBmRepairArea;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface RepairAreaRepository extends JpaRepository<HvBmRepairArea,Integer> {

    /**
     * 根据areaCode判断返修区是否存在
     * @param areaCode
     * @return
     */
    HvBmRepairArea getByAreaCodeEquals(String areaCode);

    /**
     * 根据lineId判断返修区是否存在
     * @param lineId
     * @return
     */
    HvBmRepairArea getByLineIdEquals(int lineId);
}
