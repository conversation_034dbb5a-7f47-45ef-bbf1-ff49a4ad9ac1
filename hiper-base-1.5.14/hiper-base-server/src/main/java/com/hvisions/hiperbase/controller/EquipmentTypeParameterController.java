package com.hvisions.hiperbase.controller;

import com.hvisions.common.utils.EnumUtil;
import com.hvisions.hiperbase.equipment.EquipmentTypeParameterDTO;
import com.hvisions.hiperbase.equipment.TypeParameterQueryDTO;
import com.hvisions.hiperbase.enums.TypeParameterDataTypeEnum;
import com.hvisions.hiperbase.service.equipment.EquipmentTypeParameterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>Title: HvBmEquipmentParameterController</p >
 * <p>Description: 设备类型参数控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/4/22</p >
 *
 * @deprecated
 * <AUTHOR> bzy
 * @version :1.0.0
 *
 */
@RestController
@Api(description = "设备类型参数控制层")
@Slf4j
@RequestMapping(value = "/parameter")
@Deprecated
public class EquipmentTypeParameterController {

    private final EquipmentTypeParameterService parameterService;

    @Autowired
    public EquipmentTypeParameterController(EquipmentTypeParameterService parameterService) {
        this.parameterService = parameterService;
    }


    /**
     * 新增设备类型参数
     *
     * @param parameterDTO 设备类型参数DTO
     * @return 设备类型参数ID
     */
    @PostMapping(value = "/createEquipmentParameter")
    @ApiOperation(value = "/新增设备类型参数")
    public int createEquipmentParameter(@RequestBody EquipmentTypeParameterDTO parameterDTO) {
        return parameterService.createEquipmentParameter(parameterDTO);
    }


    /**
     * 更新设备类型参数
     *
     * @param parameterDTO 设备类型参数DTO
     * @return 设备类型参数ID
     */
    @PutMapping(value = "/updateEquipmentParameter")
    @ApiOperation(value = "更新设备类型参数")
    public int updateEquipmentParameter(@RequestBody EquipmentTypeParameterDTO parameterDTO) {
        return parameterService.updateEquipmentParameter(parameterDTO);
    }


    /**
     * 删除设备类型参数根据ID列表
     *
     * @param idList 设备类型参数ID列表
     */
    @DeleteMapping(value = "/deleteEquipmentParameter")
    @ApiOperation(value = "/删除设备类型参数根据ID列表")
    public void deleteEquipmentParameter(@RequestBody List<Integer> idList) {
        parameterService.deleteEquipmentParameter(idList);
    }

    /**
     * 根据设备参数类型ID列表查询设备参数类型
     *
     * @param idList id列表
     * @return 设备参数类型列表
     */
    @PostMapping(value = "/getEquipmentParameterByIdList")
    @ApiOperation(value = "/根据设备参数类型ID列表查询设备参数类型")
    public List<EquipmentTypeParameterDTO> getEquipmentParameterByIdList(@RequestBody List<Integer> idList) {
        return parameterService.getEquipmentParameterByIdList(idList);
    }

    /**
     * 根据设备类型ID查询设备类型参数列表
     *
     * @param typeParameterQueryDTO 设备类型查询对象
     * @return 设备类型参数list
     */
    @PostMapping(value = "/getEquipmentParameterByTypeId")
    @ApiOperation(value = "根据设备类型ID查询设备类型参数列表")
    public Page<EquipmentTypeParameterDTO> getEquipmentParameterByTypeId(@RequestBody TypeParameterQueryDTO typeParameterQueryDTO) {
        return parameterService.getEquipmentParameterByTypeId(typeParameterQueryDTO);
    }

    /**
     * 获取数据类型枚举值
     *
     * @return 数据类型枚举值
     */
    @GetMapping(value = "/getAllDataType")
    @ApiOperation(value = "获取数据类型枚举值")
    public Map<Integer, String> getAllDataType() {
        return EnumUtil.enumToMap(TypeParameterDataTypeEnum.class);
    }

}
