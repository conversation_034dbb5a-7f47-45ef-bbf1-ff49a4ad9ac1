package com.hvisions.hiperbase.service.schedule;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.hiperbase.schedule.dto.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: ScheduleService</p>
 * <p>Description: 排产信息service</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface ScheduleService {

    /**
     * 根据id获取排班计划
     *
     * @param id 排班计划id
     * @return 排班计划信息
     */
    ScheduleWithInfoDTO getScheduleById(int id);

    /***
     * 根据开始时间结束时间查询车间产线排班计划
     *
     * @param areaId 车间id
     * @param cellId 产线
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return 当月排班信息
     */
    List<ScheduleDailyInfoDTO> getScheduleListByAreaIdAndCellIdAndTime(int areaId, int cellId, LocalDate beginTime, LocalDate endTime);


    /**
     * 新增或者创建一天的排班计划
     *
     * @param scheduleDailyInfoDTO 一天的排班计划
     * @return 一天的排班信息，包括车间和产线信息
     */
    ScheduleDailyDTO createOrUpdateScheduleDay(ScheduleDailyDTO scheduleDailyInfoDTO);


    /**
     * 根据时间查询班次信息，以及上五次和下五次的班次信息
     *
     * @param time   时间
     * @param areaId 车间id
     * @param cellId 产线id
     * @return 班次信息
     */
    ShiftInfoRelatedDTO getShiftReaTime(LocalDateTime time, int areaId, int cellId);

    /**
     * 根据开始结束时间自动排班
     *
     * @param automaticSchedulingDTO 排班信息
     */
    void createAutomaticScheduling(AutomaticSchedulingDTO automaticSchedulingDTO);

    /**
     * 用来判断是否有时区问题的方法
     *
     * @return 展示数据
     */
    Map<String, Object> showDate();

    /**
     * 获取当天开始时间，收工时间"
     *
     * @param date 日期
     * @return 开始结束时间
     */
    WorkTime getWorkTime(WorkTimeQuery date);

    /**
     * 根据时间查询班次信息，以及上五次和下五次的班次信息
     *
     * @param time   时间
     * @param areaId 车间
     * @param cellId 产线
     * @return 班次信息
     */
    ShiftInfoWithBreakTimeDTO getCurrentSchedule(LocalDateTime time, Integer areaId, Integer cellId);

    /**
     * 导出排班信息
     *
     * @param areaId    车间
     * @param cellId    产线
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 排班计划信息
     */
    ExcelExportDto exportScheduleForFront(Integer areaId, Integer cellId, LocalDate startTime, LocalDate endTime);

    /**
     * 导入模板下载
     *
     * @return 导入模板信息
     */
    ExcelExportDto scheduleExportTemplate();

    /**
     * 导入排班信息
     *
     * @param file 文件
     */
    void importSchedule(MultipartFile file);
}

    
    
    
    
    
    
    
    
    
    
    