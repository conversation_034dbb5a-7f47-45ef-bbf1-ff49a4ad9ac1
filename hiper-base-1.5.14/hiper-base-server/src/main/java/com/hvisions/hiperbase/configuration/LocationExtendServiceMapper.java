package com.hvisions.hiperbase.configuration;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.service.BaseExtendService;
import com.hvisions.hiperbase.enums.EquipmentExceptionEnum;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * <p>Title: LocationExtendServiceMapper</p >
 * <p>Description: location类型转特定服务</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/11/17</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Configuration
public class LocationExtendServiceMapper {


    @Resource(name = "enterprise_extend")
    BaseExtendService enterpriseExtend;
    @Resource(name = "site_extend")
    BaseExtendService siteExtend;
    @Resource(name = "area_extend")
    BaseExtendService areaExtend;
    @Resource(name = "cell_extend")
    BaseExtendService cellExtend;
    @Resource(name = "work_center_extend")
    BaseExtendService workCenterExtend;
    @Resource(name = "post_extend")
    BaseExtendService postExtend;


    @Bean
    public LocationExtendServiceMapper get() {
        return new LocationExtendServiceMapper();
    }


    public BaseExtendService getService(int type) {

        switch (type) {

            case 10:
                return enterpriseExtend;

            case 20:
                return siteExtend;

            case 30:
                return areaExtend;

            case 40:
                return cellExtend;

            case 50:
                return workCenterExtend;

            case 60:
                return postExtend;

            default:
                throw new BaseKnownException(EquipmentExceptionEnum.LOCATION_TYPE_NOT_SUPPORT);

        }


    }
}
