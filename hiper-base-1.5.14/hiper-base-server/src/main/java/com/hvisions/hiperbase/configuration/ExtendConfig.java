package com.hvisions.hiperbase.configuration;

import com.hvisions.common.dto.ExtendInfoParam;
import com.hvisions.common.service.BaseExtendService;
import com.hvisions.common.utils.SqlFactoryUtil;
import com.hvisions.hiperbase.consts.EquipmentConsts;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <p>Title: ExtendConfig</p>
 * <p>Description: 扩展服务根据配置注入</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/12/24</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Configuration
public class ExtendConfig {

    /**
     * @return 获取扩展服务工厂对象
     */
    @Bean
    SqlFactoryUtil getSqlFactory() {
        return new SqlFactoryUtil();
    }

    /**
     * 设备扩展服务
     *
     * @return 设备服务
     */
    @Bean(value = "equipment_extend" )
    BaseExtendService getOperationExtendService(SqlFactoryUtil getSqlFactory) {
        ExtendInfoParam extendInfoParam = new ExtendInfoParam();
        extendInfoParam.setOriginTableName(EquipmentConsts.EQUIPMENT_TABLE_NAME);
        extendInfoParam.setOriginTableIdName(EquipmentConsts.EQUIPMENT_ID);
        extendInfoParam.setExtendTableName(EquipmentConsts.EQUIPMENT_EXTEND_TABLE);
        return getSqlFactory.getSqlBridge(extendInfoParam);
    }

    /**
     * location_企业扩展服务
     *
     * @return location_企业扩展服务
     */
    @Bean(value = "enterprise_extend")
    BaseExtendService getEnterpriseExtendService(SqlFactoryUtil getSqlFactory) {
        ExtendInfoParam extendInfoParam = new ExtendInfoParam();
        extendInfoParam.setOriginTableName(EquipmentConsts.LOCATION_TABLE_NAME);
        extendInfoParam.setOriginTableIdName(EquipmentConsts.LOCATION_EXTEND_ID);
        extendInfoParam.setExtendTableName(EquipmentConsts.LOCATION_EXTEND_ENTERPRISE);
        return getSqlFactory.getSqlBridge(extendInfoParam);
    }

    /**
     * location_工厂扩展服务
     *
     * @return location_工厂扩展服务
     */
    @Bean(value = "site_extend")
    BaseExtendService getSiteExtendService(SqlFactoryUtil getSqlFactory) {
        ExtendInfoParam extendInfoParam = new ExtendInfoParam();
        extendInfoParam.setOriginTableName(EquipmentConsts.LOCATION_TABLE_NAME);
        extendInfoParam.setOriginTableIdName(EquipmentConsts.LOCATION_EXTEND_ID);
        extendInfoParam.setExtendTableName(EquipmentConsts.LOCATION_EXTEND_SITE);
        return getSqlFactory.getSqlBridge(extendInfoParam);
    }

    /**
     * location_车间扩展服务
     *
     * @return location_车间扩展服务
     */
    @Bean(value = "area_extend")
    BaseExtendService getAreaExtendService(SqlFactoryUtil getSqlFactory) {
        ExtendInfoParam extendInfoParam = new ExtendInfoParam();
        extendInfoParam.setOriginTableName(EquipmentConsts.LOCATION_TABLE_NAME);
        extendInfoParam.setOriginTableIdName(EquipmentConsts.LOCATION_EXTEND_ID);
        extendInfoParam.setExtendTableName(EquipmentConsts.LOCATION_EXTEND_AREA);
        return getSqlFactory.getSqlBridge(extendInfoParam);
    }

    /**
     * location_产线扩展服务
     *
     * @return location_产线扩展服务
     */
    @Bean(value = "cell_extend")
    BaseExtendService getCellExtendService(SqlFactoryUtil getSqlFactory) {
        ExtendInfoParam extendInfoParam = new ExtendInfoParam();
        extendInfoParam.setOriginTableName(EquipmentConsts.LOCATION_TABLE_NAME);
        extendInfoParam.setOriginTableIdName(EquipmentConsts.LOCATION_EXTEND_ID);
        extendInfoParam.setExtendTableName(EquipmentConsts.LOCATION_EXTEND_CELL);
        return getSqlFactory.getSqlBridge(extendInfoParam);
    }

    /**
     * location_工位扩展
     *
     * @return location_工位扩展
     */
    @Bean(value = "work_center_extend")
    BaseExtendService getWorkCenterExtendService(SqlFactoryUtil getSqlFactory) {
        ExtendInfoParam extendInfoParam = new ExtendInfoParam();
        extendInfoParam.setOriginTableName(EquipmentConsts.LOCATION_TABLE_NAME);
        extendInfoParam.setOriginTableIdName(EquipmentConsts.LOCATION_EXTEND_ID);
        extendInfoParam.setExtendTableName(EquipmentConsts.LOCATION_EXTEND_WORK_CENTER);
        return getSqlFactory.getSqlBridge(extendInfoParam);
    }
    /**
     * location_岗位扩展
     *
     * @return location_岗位扩展
     */
    @Bean(value = "post_extend")
    BaseExtendService getPostExtendService(SqlFactoryUtil getSqlFactory) {
        ExtendInfoParam extendInfoParam = new ExtendInfoParam();
        extendInfoParam.setOriginTableName(EquipmentConsts.LOCATION_TABLE_NAME);
        extendInfoParam.setOriginTableIdName(EquipmentConsts.LOCATION_EXTEND_ID);
        extendInfoParam.setExtendTableName(EquipmentConsts.LOCATION_EXTEND_POST);
        return getSqlFactory.getSqlBridge(extendInfoParam);
    }
}









