package com.hvisions.hiperbase.service.equipment;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.hvisions.hiperbase.equipment.map.EquipmentHeaderQuery;
import com.hvisions.hiperbase.equipment.map.EquipmentLocationDto;
import com.hvisions.hiperbase.equipment.map.EquipmentMapDto;
import com.hvisions.hiperbase.equipment.map.EquipmentMapHeaderDto;

import java.util.List;

/**
 * <p>Title: EquipmentMapService</p >
 * <p>Description: </p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/6/23</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
public interface EquipmentMapService {

    /**
     * 根据id删除对应的头表数据
     *
     * @param id 头表id
     */
    void deleteHeader(Integer id);

    /**
     * 根据关键字查询符合的头表数据
     *
     * @param query 查询对象
     * @return 头表数据列表
     */
    List<EquipmentMapHeaderDto> findAllHeaderByQuery(EquipmentHeaderQuery query);

    /**
     * 根据地图头表id查询地图详情数据
     *
     * @param headerId 头表id
     * @return 详情数据
     */
    EquipmentMapDto findDetailByHeaderId(Integer headerId);

    /**
     * 保存头表数据
     *
     * @param equipmentMapDto 地图详情数据
     * @param forceUpdate     是否强制更新
     * @throws JsonProcessingException 数据序列化异常
     */
    void save(EquipmentMapDto equipmentMapDto, Boolean forceUpdate) throws JsonProcessingException;

    /**
     * 查询所有地图数据
     *
     * @return 地图数据列表
     */
    List<EquipmentMapDto> findAll();

    /**
     * 根据id查询地图数据
     *
     * @param equipmentId 设备id
     * @return 地图数据
     */
    EquipmentLocationDto findByEquipmentId(Integer equipmentId);
}
