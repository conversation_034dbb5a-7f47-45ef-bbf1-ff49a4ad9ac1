package com.hvisions.hiperbase.repository.equipment;

import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentOffLineRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: EquipmentOfflineRepository</p >
 * <p>Description: </p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/7</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Repository
public interface EquipmentOfflineRepository extends JpaRepository<HvBmEquipmentOffLineRecord, Integer> {
    /**
     * 根据设备id查询下线记录
     *
     * @param equipmentId 设备id
     * @return 设备下线记录
     */
    List<HvBmEquipmentOffLineRecord> findAllByEquipmentId(Integer equipmentId);

    /**
     * 根据设备id删除下线记录
     *
     * @param equipmentId 设备id
     */
    void deleteAllByEquipmentId(int equipmentId);
}
