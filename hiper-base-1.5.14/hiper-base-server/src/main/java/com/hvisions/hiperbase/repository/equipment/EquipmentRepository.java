package com.hvisions.hiperbase.repository.equipment;

import com.hvisions.hiperbase.entity.equipment.HvBmEquipment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: HvEquipmentRepository</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/13</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface EquipmentRepository extends JpaRepository<HvBmEquipment, Integer> {

    /**
     * 根据名称查找对象
     *
     * @param code 名称
     * @return 实体名
     */
    HvBmEquipment findByEquipmentCode(String code);

    /**
     * 根据父级id查询所有
     *
     * @param parentIds 父级别id
     * @return 设备信息列表
     */
    List<HvBmEquipment> findAllByParentIdInAndParentType(List<Integer> parentIds,Integer parentType);
    /**
     * 根据父级id查询所有
     *
     * @param parentId 父级别id
     * @return 设备信息列表
     */
    List<HvBmEquipment> findAllByParentIdAndParentType(int parentId,Integer parentType);



    /**
     * 查看是否存在某个设备类型数据
     *
     * @param typeId 设备类型数据
     * @return 是否存在
     */
    boolean existsByEquipmentTypeId(int typeId);

    /**
     * 根据设备类型id查询设备信息列表
     *
     * @param id 设备类型id
     * @return 设备信息列表
     */
    List<HvBmEquipment> findAllByEquipmentTypeId(int id);


    /**
     * 根据设备位置查询设备信息列表
     *
     * @param parentId 位置id
     * @return 设备信息列表
     */
    List<HvBmEquipment> findAllByParentId(int parentId);
}
















