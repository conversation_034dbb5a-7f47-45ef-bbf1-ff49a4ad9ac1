package com.hvisions.hiperbase.service.equipment.imp;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.hiperbase.equipment.EquipmentStatusDto;
import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentStatus;
import com.hvisions.hiperbase.repository.equipment.EquipmentStatusRepository;
import com.hvisions.hiperbase.service.equipment.EquipmentStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>Title: EquipmentStatusServiceImpl</p >
 * <p>Description: 设备状态service</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/10</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Service
public class EquipmentStatusServiceImpl implements EquipmentStatusService {

    private final EquipmentStatusRepository equipmentStatusRepository;

    @Autowired
    public EquipmentStatusServiceImpl(EquipmentStatusRepository equipmentStatusRepository) {
        this.equipmentStatusRepository = equipmentStatusRepository;
    }

    /**
     * 根据状态类型查询设备状态
     *
     * @param type 状态类型
     * @return 设备状态列表
     */
    @Override
    public List<EquipmentStatusDto> getByType(Integer type) {
        List<HvBmEquipmentStatus> equipmentStatusDtos = equipmentStatusRepository.findAllByType(type);
        return DtoMapper.convertList(equipmentStatusDtos, EquipmentStatusDto.class);
    }

    /**
     * 新增设备状态
     *
     * @param equipmentStatusDto 设备状态dto
     */
    @Override
    public void addEquipmentStatus(EquipmentStatusDto equipmentStatusDto) {
        HvBmEquipmentStatus hvBmEquipmentStatus = DtoMapper.convert(equipmentStatusDto, HvBmEquipmentStatus.class);
        equipmentStatusRepository.save(hvBmEquipmentStatus);
    }

    /**
     * 修改设备状态
     *
     * @param equipmentStatusDto 设备状态dto
     */
    @Override
    public void updateEquipmentStatus(EquipmentStatusDto equipmentStatusDto) {
        HvBmEquipmentStatus hvBmEquipmentStatus = DtoMapper.convert(equipmentStatusDto, HvBmEquipmentStatus.class);
        equipmentStatusRepository.save(hvBmEquipmentStatus);
    }

    /**
     * 删除设备状态
     *
     * @param id 设备状态id
     */
    @Override
    public void deleteEquipmentStatus(Integer id) {
        equipmentStatusRepository.deleteById(id);
    }

    /**
     * 查询所有设备状态
     *
     * @return 设备状态列表
     */
    @Override
    public List<EquipmentStatusDto> getAll() {
        return DtoMapper.convertList(equipmentStatusRepository.findAll(), EquipmentStatusDto.class);
    }
}
