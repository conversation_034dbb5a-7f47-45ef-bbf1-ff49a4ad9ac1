package com.hvisions.hiperbase.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.equipment.RepairAreaDTO;
import com.hvisions.hiperbase.equipment.RepairAreaQueryDTO;
import com.hvisions.hiperbase.equipment.location.LocationDTO;
import com.hvisions.hiperbase.service.equipment.RepairAreaService;
import com.hvisions.thirdparty.common.dto.WeldLineRepairDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

@Slf4j
@Api(tags = "返修区控制器")
@RestController
@RequestMapping("/repairArea")
public class RepairAreaController {

    @Autowired
    private RepairAreaService repairAreaService;

    @PostMapping("/getPage")
    @ApiOperation("分页模糊查询")
    public Page<RepairAreaDTO> getPage(@RequestBody RepairAreaQueryDTO repairAreaQueryDTO){
        return repairAreaService.getPage(repairAreaQueryDTO);
    }

    /**
     * 添加返修区
     * @param repairAreaDTO
     * @return
     */
    @PostMapping("/createRepairArea")
    @ApiOperation("新增返修区")
    public int createRepairArea(@RequestBody RepairAreaDTO repairAreaDTO){
        return repairAreaService.createRepairArea(repairAreaDTO);
    }

    /**
     * 修改返修区
     * @param repairAreaDTO
     * @return
     */
    @PutMapping("/updateRepairArea")
    @ApiOperation("修改返修区")
    public int updateRepairArea(@RequestBody RepairAreaDTO repairAreaDTO){
        return repairAreaService.updateRepairArea(repairAreaDTO);
    }

    /**
     * 根据id删除返修区
     * @param id
     */
    @DeleteMapping("/deleteRepairAreaById/{id}")
    @ApiOperation("删除返修区")
    public void deleteRepairAreaById(@PathVariable Integer id){
        repairAreaService.deleteRepairAreaById(id);
    }

    /**
     * 根据areaCode判断返修区是否存在
     * @param areaCode
     * @return
     */
    @GetMapping("/isExistsRepairAreaByAreaCode/{areaCode}")
    @ApiOperation("根据areaCode判断返修区是否存在")
    public boolean isExistsRepairAreaByAreaCode(@PathVariable String areaCode){
        return repairAreaService.isExistsRepairAreaByAreaCode(areaCode);
    }

    /**
     * 根据lineId获取返修区域
     * @param lineId
     * @return
     */
    @GetMapping("/getRepairAreaByLineId/{lineId}")
    @ApiOperation("根据lineId获取返修区域")
    public RepairAreaDTO getRepairAreaByLineId(@PathVariable int lineId){
        return repairAreaService.getRepairAreaByLineId(lineId);
    }

    /**
     * 获取未被使用的产线列表
     * @return
     */
    @GetMapping("/getNotUsedLine")
    @ApiOperation("获取未被使用的产线列表")
    public List<LocationDTO> getNotUsedLine(){
        return repairAreaService.getNotUsedLine();
    }

    /**
     * 导出所有返修区域信息
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    @ApiResultIgnore
    @GetMapping("/exportRepairArea")
    @ApiOperation("导出所有返修区域信息")
    public ResultVO<ExcelExportDto> exportRepairArea() throws IOException, IllegalAccessException {
        return repairAreaService.exportRepairArea();
    }

    /**
     * 获取导入模板
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/getRepairAreaImportTemplate")
    @ApiOperation(value = "获取物料导入模板 ")
    public ResultVO<ExcelExportDto> getRepairAreaImportTemplate() throws IOException, IllegalAccessException {
        return repairAreaService.getRepairAreaImportTemplate();
    }

    /**
     * 导入返修区域信息
     * @param file
     * @return
     * @throws IllegalAccessException
     * @throws ParseException
     * @throws IOException
     */
    @PostMapping("/importRepairAreas")
    @ApiOperation("导入返修区域信息")
    public ImportResult importRepairAreas(@RequestParam("file")MultipartFile file) throws IllegalAccessException, ParseException, IOException {
        return repairAreaService.importRepairAreas(file);
    }

    /**
     * 返修调度
     * @param weldLineRepairDTO
     * @return
     */
    @PostMapping("/repairDispatch")
    @ApiOperation("返修调度")
    public RepairAreaDTO repairDispatch(@RequestBody WeldLineRepairDTO weldLineRepairDTO){
        return repairAreaService.repairDispatch(weldLineRepairDTO);
    }
}
