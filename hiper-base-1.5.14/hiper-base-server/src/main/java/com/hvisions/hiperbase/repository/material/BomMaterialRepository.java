package com.hvisions.hiperbase.repository.material;

import com.hvisions.hiperbase.entity.material.HvBmBomMaterial;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: BomRoleRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/12/5</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface BomMaterialRepository extends JpaRepository<HvBmBomMaterial, Integer> {
    /**
     * 判断material Id是否存在
     *
     * @param materialId materialType id
     * @return true or false
     */
    boolean existsByMaterialId(int materialId);

    /**
     * 判断bomId是否存在
     *
     * @param bomId BomID
     * @return true or false
     */
    boolean existsByBomId(int bomId);

    /**
     * 通过ID获取
     *
     * @param bomId bomID
     * @return 中间表信息
     */
    HvBmBomMaterial getHvBomMaterialByBomId(int bomId);

    /**
     * 通过ID获取信息
     *
     * @param materialId materials
     * @return 中间表信息
     */

    HvBmBomMaterial getHvBomMaterialByMaterialId(int materialId);


    /**
     * 通过 bomID 删除信息
     *
     * @param bomId bomId
     */
    void deleteAllByBomId(int bomId);

    /**
     * 根据Material ID 删除关联关系
     *
     * @param materialId 物料ID
     */
    void deleteByMaterialId(int materialId);

    /**
     * 根据bomId列表查询
     *
     * @param bomIdList bomId列表
     * @return bom物料关联关系信息列表
     */
    List<HvBmBomMaterial> getAllByBomIdIn(List<Integer> bomIdList);


}
