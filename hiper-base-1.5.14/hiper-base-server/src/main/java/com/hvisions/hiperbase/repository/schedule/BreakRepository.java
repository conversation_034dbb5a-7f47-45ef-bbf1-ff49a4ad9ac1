package com.hvisions.hiperbase.repository.schedule;

import com.hvisions.hiperbase.entity.schedule.HvBmBreakTime;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: BreakRepository</p>
 * <p>Description: 休息间隔的仓储</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/3/31</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface BreakRepository extends JpaRepository<HvBmBreakTime, Integer> {
    /**
     * 根据班次id找到休息时间
     *
     * @param shiftId 班次id
     * @return 休息时间
     */
    List<HvBmBreakTime> findAllByShiftId(Integer shiftId);

    /**
     * 根据班次id列表找到休息时间
     *
     * @param shiftIds 班次id列表
     * @return 休息时间
     */
    List<HvBmBreakTime> findAllByShiftIdIn(List<Integer> shiftIds);
}









