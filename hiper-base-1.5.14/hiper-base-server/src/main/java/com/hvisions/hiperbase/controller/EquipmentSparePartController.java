package com.hvisions.hiperbase.controller;

import com.hvisions.hiperbase.equipment.EquipmentSparePartDto;
import com.hvisions.hiperbase.equipment.EquipmentSparePartQuery;
import com.hvisions.hiperbase.service.equipment.EquipmentSparePartService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title: EquipmentSparePartController</p >
 * <p>Description: 设备备件控制器</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/5</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@RestController
@RequestMapping("/equipmentSparePart")
@Api(tags = "设备备件控制器")
public class EquipmentSparePartController {

    private final EquipmentSparePartService equipmentSparePartService;

    @Autowired
    public EquipmentSparePartController(EquipmentSparePartService equipmentSparePartService) {
        this.equipmentSparePartService = equipmentSparePartService;
    }

    @ApiOperation("新增设备备件数据")
    @PostMapping("/add")
    public void addSparePart(@RequestBody @Valid EquipmentSparePartDto equipmentSparePartDto) {
        equipmentSparePartService.addSparePart(equipmentSparePartDto);
    }

    @ApiOperation("批量新增设备备件数据")
    @PostMapping("/batchAdd/{equipmentId}")
    public void batchAddSparePart(@PathVariable(value = "equipmentId") Integer equipmentId,
                                  @RequestBody(required = false) @Valid List<EquipmentSparePartDto> equipmentSparePartDtos) {
        equipmentSparePartService.batchAddSparePart(equipmentId, equipmentSparePartDtos);
    }


    @ApiOperation("根据设备id查询备件信息")
    @PostMapping("/findAllByEquipmentId")
    public List<EquipmentSparePartDto> findAllByEquipmentId(@RequestBody EquipmentSparePartQuery query) {
        return equipmentSparePartService.findAllByEquipmentId(query);
    }

    @ApiOperation("修改备件信息")
    @PutMapping("/update")
    public void updateSparPart(@RequestBody @Valid EquipmentSparePartDto equipmentSparePartDto) {
        equipmentSparePartService.updateSparePart(equipmentSparePartDto);
    }

    @ApiOperation("删除设备备件信息")
    @DeleteMapping("/delete/{id}")
    public void deleteSparePart(@PathVariable Integer id) {
        equipmentSparePartService.deleteSparePart(id);
    }
}
