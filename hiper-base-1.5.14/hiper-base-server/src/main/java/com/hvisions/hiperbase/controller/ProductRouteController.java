package com.hvisions.hiperbase.controller;

import com.hvisions.common.annotation.EnableFilter;
import com.hvisions.hiperbase.route.dto.*;
import com.hvisions.hiperbase.route.enums.RouteTypeEnum;
import com.hvisions.hiperbase.service.route.RouteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>Title: ProductRouteController</p>
 * <p>Description: 产品工艺路线</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/3/23</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@Slf4j
@Api(description = "产品工艺路线控制器")
@RequestMapping("/productRoute/v2")
public class ProductRouteController {
    private final RouteService routeService;

    @Autowired
    public ProductRouteController(RouteService routeService) {
        this.routeService = routeService;
    }


    /**
     * 创建产品工艺路线
     *
     * @param routeDTO 产品工艺路线
     * @return 主键
     */
    @PostMapping("/create")
    @ApiOperation(value = "创造产品工艺路线")
    public Integer create(@RequestBody RouteDTO routeDTO) {
        return routeService.createProductRoute(routeDTO);
    }

    /**
     * 更新产品工艺路线
     *
     * @param routeDTO 产品工艺路线
     */
    @PutMapping("/update")
    @ApiOperation(value = "更新产品工艺路线")
    public void update(@RequestBody RouteDTO routeDTO) {
        routeService.update(routeDTO);
    }

    /**
     * 更新产品工艺路线
     *
     * @param routeDTO 产品工艺路线(生效也能更新)
     */
    @PutMapping("/updateByRoute")
    @ApiOperation(value = "更新产品工艺路线")
    public void updateByRoute(@RequestBody RouteDTO routeDTO) {
        routeService.updateByRoute(routeDTO);
    }

    /**
     * 产品工艺路线分页查询
     *
     * @param queryDTO 查询条件
     * @return 分页数据
     */
    @PostMapping("/getPage")
    @ApiOperation(value = "产品工艺路线分页查询")
    public Page<RouteDTO> getPage(@RequestBody RouteQueryDTO queryDTO) {
        queryDTO.setRouteType(RouteTypeEnum.PROCTIONROUTE.getCode());
        return routeService.getRoute(queryDTO);
    }


    /**
     * 根据id删除工艺路线
     *
     * @param productRouteId 工艺路线id
     */
    @DeleteMapping("/delete/{productRouteId}")
    @ApiOperation(value = "删除工艺路线")
    public void delete(@PathVariable Integer productRouteId) {
        routeService.deleteProductRoute(productRouteId);
    }

    /**
     * 生效产品工艺路线
     *
     * @param routeId 工艺路线id
     */
    @EnableFilter
    @PutMapping("/activeRoute/{routeId}")
    @ApiOperation(value = "生效产品工艺路线")
    public void activeRoute(@PathVariable Integer routeId) {
        routeService.activeRoute(routeId);
    }

    /**
     * 归档产品工艺路线
     *
     * @param routeId 工艺路线id
     */
    @EnableFilter
    @PutMapping("/archiveRoute/{routeId}")
    @ApiOperation(value = "归档产品工艺路线")
    public void archiveRoute(@PathVariable Integer routeId) {
        routeService.archiveRoute(routeId);
    }

    /**
     * 复制产品工艺路线
     *
     * @param copyDTO 产品工艺路线id
     * @return 复制成功后的产品工艺路线id
     */
    @EnableFilter
    @PostMapping("/copyRoute")
    @ApiOperation(value = "复制产品工艺路线")
    public Integer copyRoute(@RequestBody RouteCopyDTO copyDTO) {
        return routeService.copyProductRoute(copyDTO);
    }

    /**
     * 根据工艺路线模板创建产品工艺路线
     *
     * @param productRouteDTO 产品 工艺路线关系DTO
     * @return 新增关系的id
     */
    @PostMapping("/addProductRoute")
    @ApiOperation(value = "根据工艺路线模板创建产品工艺路线")
    public Integer addProductRoute(@RequestBody ProductRouteDTO productRouteDTO) {
        return routeService.createProductRouteByRoute(productRouteDTO);
    }

    /**
     * 获取产品工艺路线详细信息
     *
     * @param routeId 工艺路线id
     * @return 产品工艺路线详细信息
     */
    @GetMapping("/getRoute/{routeId}")
    @ApiOperation(value = "获取产品工艺路线详细信息")
    public RouteDTO getRoute(@PathVariable Integer routeId) {
        return routeService.getRoute(routeId);
    }

    /**
     * 获取生效的产品工艺id
     *
     * @param materialId 物料id
     * @param routeCode  工艺路线id
     * @return 产品工艺
     */
    @GetMapping("/getActiveVersion")
    @ApiOperation(value = "获取生效版本")
    public RouteDTO getActiveProductRoute(@RequestParam Integer materialId, @RequestParam String routeCode) {
        return routeService.getActiveProductRoute(materialId, routeCode);
    }


    /**
     * 获取工艺路线详细信息
     *
     * @param productRouteId 工艺路线id
     * @param code           拼接后节点code
     * @return 工艺路线详细信息
     */
    @GetMapping("/getStepParam/{productRouteId}/{code}")
    @ApiOperation(value = "获取工艺路线详细信息")
    public List<NodeParameterData> getStepParam(@PathVariable Integer productRouteId, @PathVariable String code) {
        return routeService.getStepParam(productRouteId, code);
    }

    /**
     * 获取工艺路线详细信息
     *
     * @return 工艺路线详细信息
     */
    @PostMapping("/getStepParamPage")
    @ApiOperation(value = "获取工艺路线详细信息")
    public Page<NodeParameterData> getStepParamPage(@RequestBody StepParamQueryDTO stepParamQueryDTO) {
        return routeService.getStepParamPage(stepParamQueryDTO);
    }

    /**
     * 根据产品id和工艺路线id查询产品工艺以及参数信息
     *
     * @param productId 产品id
     * @param routeId   产品工艺路线id
     * @return 产品工艺信息
     */
    @GetMapping("/getEffectRouteByProductIdAndRouteId/{productId}/{routeId}")
    @ApiOperation("根据产品id和工艺路线id查询产品工艺以及参数信息")
    public RouteDTO getEffectRouteByProductIdAndRouteId(@PathVariable Integer productId, @PathVariable Integer routeId) {
        return routeService.getRoute(routeId);
    }

    /**
     * 获取所有配置了产品工艺的物料列表
     *
     * @return 配置了产品工艺的物料列表
     */
    @PostMapping("/getProductRouteMaterial")
    @ApiOperation(value = "获取所有配置了产品工艺的物料列表")
    public Page<MaterialInfo> getProductRouteMaterial(@RequestBody RouteMaterialQuery pageInfo) {
        return routeService.getProductRouteMaterial(pageInfo);
    }

    /**
     * 产品工艺路线分页查询
     *
     * @param queryDTO 查询条件
     * @return 分页数据
     */
    @PostMapping("/getProductRoute")
    @ApiOperation(value = "产品工艺路线分页查询")
    public Page<RouteDTO> getProductRoute(@RequestBody RouteQueryDTO queryDTO) {
        return routeService.getProductRouteByDto(queryDTO);
    }


    /**
     * 升级版本
     *
     * @param id 旧版本id
     * @return 新版本id
     */
    @PostMapping("/levelUp/{id}")
    @ApiOperation(value = "升级参数版本")
    public Integer levelUp(@PathVariable Integer id) {
        return routeService.levelUp(id);
    }


    /**
     * 获取所有生效和归档工艺路线参数详情
     *
     * @return 参数列表
     */
    @GetMapping("getEffectRouteParameter")
    @ApiOperation(value = "获取所有生效和归档工艺路线参数详情")
    public Map<String, List<NodeParameterData>> getEffectRouteParameter() {
        return routeService.getEffectRouteParameter();
    }


    /**
     * 根据工艺流向查询工艺路线
     *
     * @return 参数列表
     */
    @PostMapping("getRouteByDestination")
    @ApiOperation(value = "根据工艺流向查询工艺路线")
    public RouteDTO getRouteByDestination(@RequestParam("destination") String destination) {
        return routeService.getRouteByDestination(destination);
    }



}









