package com.hvisions.hiperbase.service.equipment.imp;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.interfaces.EntitySaver;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.equipment.EquipmentTypeBaseDTO;
import com.hvisions.hiperbase.equipment.EquipmentTypeDTO;
import com.hvisions.hiperbase.equipment.EquipmentTypeImportDTO;
import com.hvisions.hiperbase.equipment.EquipmentTypeQueryDTO;
import com.hvisions.hiperbase.consts.EquipmentConsts;
import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentType;
import com.hvisions.hiperbase.enums.EquipmentExceptionEnum;
import com.hvisions.hiperbase.repository.equipment.EquipmentRepository;
import com.hvisions.hiperbase.repository.equipment.EquipmentTypeRepository;
import com.hvisions.hiperbase.service.equipment.EquipmentTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <p>Title: HvEquipmentTypeServiceImp</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/13</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
@Slf4j
public class EquipmentTypeServiceImpl implements EquipmentTypeService, EntitySaver<EquipmentTypeImportDTO> {

    private final EquipmentTypeRepository hvEquipmentTypeRepository;
    private final EquipmentRepository equipmentRepository;

    @Autowired
    public EquipmentTypeServiceImpl(EquipmentTypeRepository hvEquipmentTypeRepository,
                                    EquipmentRepository equipmentRepository) {
        this.hvEquipmentTypeRepository = hvEquipmentTypeRepository;
        this.equipmentRepository = equipmentRepository;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer deleteEquipmentTypeById(Integer id) {
        //如果设备类型已经使用
        if (equipmentRepository.existsByEquipmentTypeId(id)) {
            throw new BaseKnownException(EquipmentExceptionEnum.EQUIPMENT_TYPE_IN_USE);
        }
        //判断要删除的类型是否有子级设备类型
        if (findAllByParentId(id).size() > 0) {
            throw new BaseKnownException(EquipmentExceptionEnum.EQUIPMENT_TYPE_SUB_HAVE_DATA);
        }
        hvEquipmentTypeRepository.deleteById(id);
        return id;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public EquipmentTypeDTO createEquipmentType(EquipmentTypeDTO hvEquipmentTypeDTO) {
        HvBmEquipmentType save = hvEquipmentTypeRepository.save(DtoMapper.convert(hvEquipmentTypeDTO,
                HvBmEquipmentType.class));

        return DtoMapper.convert(save, EquipmentTypeDTO.class);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public EquipmentTypeDTO updateEquipmentType(EquipmentTypeBaseDTO hvEquipmentTypeDTO) {

        HvBmEquipmentType save = hvEquipmentTypeRepository.save(DtoMapper.convert(hvEquipmentTypeDTO,
                HvBmEquipmentType.class));

        return DtoMapper.convert(save, EquipmentTypeDTO.class);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<EquipmentTypeDTO> getAllEquipmentType() {
        return DtoMapper.convertList(hvEquipmentTypeRepository.findAll(), EquipmentTypeDTO.class);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public EquipmentTypeDTO getEquipmentTypeById(Integer id) {
        return DtoMapper.convert(hvEquipmentTypeRepository.getOne(id), EquipmentTypeDTO.class);
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public EquipmentTypeDTO getEquipmentTypeByCode(String code) {
        return DtoMapper.convert(hvEquipmentTypeRepository.findByEquipmentTypeCode(code), EquipmentTypeDTO.class);
    }

    /**
     * 根据设备类型编码查询设备类型信息列表
     *
     * @param codeIn 设备类型编码列表
     * @return 设备类型信息列表
     */
    @Override
    public List<EquipmentTypeDTO> getAllByEquipmentTypeCodeIn(List<String> codeIn) {
        return DtoMapper.convertList(hvEquipmentTypeRepository.getAllByEquipmentTypeCodeIn(codeIn), EquipmentTypeDTO.class);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<EquipmentTypeDTO> findAllByParentId(Integer parentId) {
        return DtoMapper.convertList(hvEquipmentTypeRepository.findAllByParentId(parentId), EquipmentTypeDTO.class);
    }

    /**
     * 根据设备类型id查询父级设备类型列表
     *
     * @param id 设备类型id
     * @return 父级设备类型列表
     */
    @Override
    public List<EquipmentTypeDTO> findParentTypeById(Integer id) {
        List<HvBmEquipmentType> typeDTOList = new ArrayList<>();
        getParentTypeDto(id, typeDTOList);
        return DtoMapper.convertList(typeDTOList, EquipmentTypeDTO.class);
    }


    private void getParentTypeDto(Integer id, List<HvBmEquipmentType> typeDTOList) {
        Optional<HvBmEquipmentType> hvBmEquipmentType = hvEquipmentTypeRepository.findById(id);
        if (hvBmEquipmentType.isPresent()) {
            typeDTOList.add(hvBmEquipmentType.get());
            if (hvBmEquipmentType.get().getParentId() != 0) {
                getParentTypeDto(hvBmEquipmentType.get().getParentId(), typeDTOList);
            }
        }
    }

    /**
     * 根据ID列表查询设备类型
     *
     * @param idList id列表
     * @return 设备类型信息
     */
    @Override
    public List<EquipmentTypeDTO> getAllByIdIn(List<Integer> idList) {
        return DtoMapper.convertList(hvEquipmentTypeRepository.getAllByIdIn(idList), EquipmentTypeDTO.class);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Page<EquipmentTypeDTO> getEquipmentTypeQuery(EquipmentTypeQueryDTO equipmentTypeQueryDto) {
        Page<HvBmEquipmentType> hvBmEquipmentTypes;
        HvBmEquipmentType hvBmEquipmentType = DtoMapper.convert(equipmentTypeQueryDto, HvBmEquipmentType.class);
        ExampleMatcher exampleMatcher = ExampleMatcher.matching()
                .withMatcher("equipmentTypeCode", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("equipmentTypeName", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase());
        if (equipmentTypeQueryDto.getParentId() == null) {
            equipmentTypeQueryDto.setParentId(0);
            exampleMatcher
                    .withMatcher("parentId", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase());
        }
        Example<HvBmEquipmentType> example = Example.of(hvBmEquipmentType, exampleMatcher);
        hvBmEquipmentTypes = hvEquipmentTypeRepository.findAll(example, equipmentTypeQueryDto.getRequest());
        return DtoMapper.convertPage(hvBmEquipmentTypes,
                EquipmentTypeDTO.class);

    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ImportResult importEquipmentType(MultipartFile file) throws IllegalAccessException, ParseException, IOException {
        return ExcelUtil.importEntity(file,
                EquipmentTypeImportDTO.class,
                this);
    }

    /**
     * 导出设备信息
     *
     * @return 设备信息excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException 访问异常
     */
    @Override
    public ResponseEntity<byte[]> exportEquipmentTypeLink() throws IOException, IllegalAccessException {
        List<EquipmentTypeDTO> list = getAllEquipmentType();
        setEquipmentTypeParentCode(list);
        return ExcelUtil.generateImportFile(list,
                EquipmentConsts.EQUIPMENT_TYPE_FILE_NAME,
                EquipmentTypeDTO.class);

    }

    /**
     * 导出设备信息
     *
     * @return 设备信息excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException 访问异常
     */
    @Override
    public ResultVO<ExcelExportDto> exportEquipmentType() throws IOException, IllegalAccessException {
        List<EquipmentTypeDTO> list = getAllEquipmentType();
        //设置父级节点code
        setEquipmentTypeParentCode(list);
        ResponseEntity<byte[]> result =
                exportEquipmentTypeLink();
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(result.getBody());
        excelExportDto.setFileName(EquipmentConsts.EQUIPMENT_TYPE_FILE_NAME);
        return ResultVO.success(excelExportDto);
    }


    private void setEquipmentTypeParentCode(List<EquipmentTypeDTO> list) {
        for (EquipmentTypeDTO typeDTO : list) {
            if (0 != Optional.ofNullable(typeDTO.getParentId()).orElse(0)) {
                boolean existsByParentId = hvEquipmentTypeRepository.existsById(typeDTO.getParentId());
                if (existsByParentId) {
                    HvBmEquipmentType equipmentType = hvEquipmentTypeRepository.getOne(typeDTO.getParentId());
                    typeDTO.setParentTypeCode(equipmentType.getEquipmentTypeCode());
                } else {
                    typeDTO.setParentTypeCode("父级ID不存在");
                }
            } else {
                Optional<EquipmentTypeDTO> parentEquipment = list.stream()
                        .filter(t -> t.getId().equals(typeDTO.getParentId()))
                        .findFirst();
                if (parentEquipment.isPresent()) {
                    typeDTO.setParentTypeCode(parentEquipment.get().getEquipmentTypeCode());
                } else {
                    typeDTO.setParentTypeCode("父级ID不存在");
                }
            }
        }


    }


    /**
     * 导入设备类型信息
     *
     * @param equipmentTypeImportDTO 设备类型信息
     */
    @Override
    public void saveOrUpdate(EquipmentTypeImportDTO equipmentTypeImportDTO) {
        HvBmEquipmentType finalEquipmentType;
        if (!StringUtils.isEmpty(equipmentTypeImportDTO.getEquipmentTypeCode())) {
            HvBmEquipmentType equipmentType = hvEquipmentTypeRepository.findByEquipmentTypeCode(
                    equipmentTypeImportDTO.getEquipmentTypeCode()
            );
            if (equipmentType != null) {
                finalEquipmentType = equipmentType;
            } else {
                finalEquipmentType = new HvBmEquipmentType();
                finalEquipmentType.setEquipmentTypeCode(equipmentTypeImportDTO.getEquipmentTypeCode());
            }
            if (!StringUtils.isEmpty(equipmentTypeImportDTO.getEquipmentTypeName())) {
                finalEquipmentType.setEquipmentTypeName(equipmentTypeImportDTO.getEquipmentTypeName());
            } else {
                finalEquipmentType.setEquipmentTypeName("");
            }
            if (!StringUtils.isEmpty(equipmentTypeImportDTO.getRemark())) {
                finalEquipmentType.setRemark(equipmentTypeImportDTO.getRemark());
            }
            if (!StringUtils.isEmpty(equipmentTypeImportDTO.getParentTypeCode())) {
                HvBmEquipmentType parentEquipmentType = hvEquipmentTypeRepository.findByEquipmentTypeCode(
                        equipmentTypeImportDTO.getParentTypeCode()
                );
                if (parentEquipmentType != null) {
                    finalEquipmentType.setParentId(parentEquipmentType.getId());
                }
            }
            hvEquipmentTypeRepository.save(finalEquipmentType);
        }
    }

    /**
     * 根据指定节点id遍历其子节点
     *
     * @param parentId 父节点id
     * @return 所有子节点及子节点的子节点
     */
    @Override
    public List<EquipmentTypeDTO> findAllChildTypeByParentId(Integer parentId) {
        try {
            Optional<HvBmEquipmentType> hvBmEquipmentType = hvEquipmentTypeRepository.findById(parentId);
            if (hvBmEquipmentType.isPresent()) {
                List<EquipmentTypeDTO> equipmentTypes = new ArrayList<>();
                equipmentTypes.add(DtoMapper.convert(hvBmEquipmentType.get(), EquipmentTypeDTO.class));
                //获取子类型列表
                List<HvBmEquipmentType> childEquipmentTypes = findAllByParentId(parentId, new ArrayList<>());
                List<EquipmentTypeDTO> childEquipmentTypeList = DtoMapper.convertList(childEquipmentTypes, EquipmentTypeDTO.class);
                equipmentTypes.addAll(childEquipmentTypeList);
                return equipmentTypes;
            }
        } catch (StackOverflowError e) {
            log.error("类型绑定错误,出现循环绑定");
            return null;
        }
        return null;
    }

    @Override
    public List<EquipmentTypeDTO> findAllByChildId(Integer childId) {
        try {
            Optional<HvBmEquipmentType> hvBmEquipmentType = hvEquipmentTypeRepository.findById(childId);
            if (hvBmEquipmentType.isPresent()) {
                List<EquipmentTypeDTO> equipmentTypes = new ArrayList<>();
                equipmentTypes.add(DtoMapper.convert(hvBmEquipmentType.get(), EquipmentTypeDTO.class));
                //获取父类型列表
                List<HvBmEquipmentType> parentEquipmentTypes = findAllByChildId(hvBmEquipmentType.get().getParentId(), new ArrayList<>());
                List<EquipmentTypeDTO> parentEquipmentTypeList = DtoMapper.convertList(parentEquipmentTypes, EquipmentTypeDTO.class);
                equipmentTypes.addAll(parentEquipmentTypeList);
                return equipmentTypes;
            }
        } catch (StackOverflowError e) {
            log.error("类型绑定错误,出现循环绑定");
            return null;
        }
        return null;
    }

    private List<HvBmEquipmentType> findAllByParentId(Integer parentId, List<HvBmEquipmentType> equipmentTypes) {
        List<HvBmEquipmentType> hvBmEquipmentTypes = hvEquipmentTypeRepository.findAllByParentId(parentId);
        if (!CollectionUtils.isEmpty(hvBmEquipmentTypes)) {
            for (HvBmEquipmentType hvBmEquipmentType : hvBmEquipmentTypes) {
                equipmentTypes.add(hvBmEquipmentType);
                findAllByParentId(hvBmEquipmentType.getId(), equipmentTypes);
            }
        }
        return equipmentTypes;
    }

    private List<HvBmEquipmentType> findAllByChildId(Integer parentId, List<HvBmEquipmentType> equipmentTypes) {
        Optional<HvBmEquipmentType> hvBmEquipmentType = hvEquipmentTypeRepository.findById(parentId);
        if (hvBmEquipmentType.isPresent()) {
            equipmentTypes.add(hvBmEquipmentType.get());
            findAllByChildId(hvBmEquipmentType.get().getParentId(), equipmentTypes);
        }
        return equipmentTypes;
    }
}
















