package com.hvisions.hiperbase.controller;

import com.hvisions.hiperbase.equipment.EquipmentWarrantyDto;
import com.hvisions.hiperbase.service.equipment.EquipmentWarrantyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title: EquipmentWarrantyController</p >
 * <p>Description: 设备保修信息控制器</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/5</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@RestController
@RequestMapping("/equipmentWarrant")
@Api(tags = "设备保修控制器")
public class EquipmentWarrantyController {

    private final EquipmentWarrantyService equipmentWarrantyService;

    @Autowired
    public EquipmentWarrantyController(EquipmentWarrantyService equipmentWarrantyService) {
        this.equipmentWarrantyService = equipmentWarrantyService;
    }

    @ApiOperation("新增设备保修信息")
    @PostMapping("/add")
    public void addWarranty(@RequestBody @Valid EquipmentWarrantyDto equipmentWarrantyDto) {
        equipmentWarrantyService.addWarranty(equipmentWarrantyDto);
    }

    @ApiOperation("查询设备保修信息")
    @GetMapping("/findAllByEquipmentId/{equipmentId}")
    public List<EquipmentWarrantyDto> findAllByEquipmentId(@PathVariable Integer equipmentId) {
        return equipmentWarrantyService.findAllByEquipmentId(equipmentId);
    }

    @ApiOperation("修改设备保修信息")
    @PutMapping("/update")
    public void updateWarranty(@RequestBody @Valid EquipmentWarrantyDto equipmentWarrantyDto) {
        equipmentWarrantyService.updateWarranty(equipmentWarrantyDto);
    }

    @ApiOperation("删除设备保修信息")
    @DeleteMapping("/delete/{id}")
    public void deleteWarranty(@PathVariable Integer id) {
        equipmentWarrantyService.deleteWarranty(id);
    }
}
