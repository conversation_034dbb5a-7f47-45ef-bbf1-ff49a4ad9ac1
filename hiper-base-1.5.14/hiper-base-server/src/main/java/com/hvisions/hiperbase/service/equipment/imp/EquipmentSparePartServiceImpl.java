package com.hvisions.hiperbase.service.equipment.imp;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.hiperbase.dao.equipment.EquipmentSparePartMapper;
import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentSparePart;
import com.hvisions.hiperbase.equipment.CopyEquipmentEvent;
import com.hvisions.hiperbase.equipment.DelEquipmentEvent;
import com.hvisions.hiperbase.equipment.EquipmentSparePartDto;
import com.hvisions.hiperbase.equipment.EquipmentSparePartQuery;
import com.hvisions.hiperbase.repository.equipment.EquipmentSparePartRepository;
import com.hvisions.hiperbase.service.equipment.EquipmentSparePartService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>Title: EquipmentSparePartServiceImpl</p >
 * <p>Description: </p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/9</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Service
public class EquipmentSparePartServiceImpl implements EquipmentSparePartService {

    private final EquipmentSparePartRepository equipmentSparePartRepository;

    private final EquipmentSparePartMapper equipmentSparePartMapper;

    @Autowired
    public EquipmentSparePartServiceImpl(EquipmentSparePartRepository equipmentSparePartRepository,
                                         EquipmentSparePartMapper equipmentSparePartMapper) {
        this.equipmentSparePartRepository = equipmentSparePartRepository;
        this.equipmentSparePartMapper = equipmentSparePartMapper;
    }


    /**
     * 新增设备备件
     *
     * @param equipmentSparePartDto 设备备件信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addSparePart(EquipmentSparePartDto equipmentSparePartDto) {
        HvBmEquipmentSparePart hvBmEquipmentSparePart = DtoMapper.convert(equipmentSparePartDto, HvBmEquipmentSparePart.class);
        equipmentSparePartRepository.save(hvBmEquipmentSparePart);
    }

    /**
     * 根据设备id查询设备备件
     *
     * @param query 分页查询条件
     * @return 设备备件列表
     */
    @Override
    public List<EquipmentSparePartDto> findAllByEquipmentId(EquipmentSparePartQuery query) {
        return equipmentSparePartMapper.findAllByQuery(query);
    }

    /**
     * 修改设备备件
     *
     * @param equipmentSparePartDto 设备备件信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSparePart(EquipmentSparePartDto equipmentSparePartDto) {
        HvBmEquipmentSparePart hvBmEquipmentSparePart = DtoMapper.convert(equipmentSparePartDto, HvBmEquipmentSparePart.class);
        equipmentSparePartRepository.save(hvBmEquipmentSparePart);
    }

    /**
     * 删除设备备件
     *
     * @param id 设备备件id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSparePart(Integer id) {
        equipmentSparePartRepository.deleteById(id);
    }

    /**
     * 批量新增备件
     *
     * @param equipmentSparePartDtos 备件信息
     */
    @Override
    public void batchAddSparePart(Integer equipmentId, List<EquipmentSparePartDto> equipmentSparePartDtos) {
        List<HvBmEquipmentSparePart> hvBmEquipmentSpareParts = DtoMapper.convertList(equipmentSparePartDtos, HvBmEquipmentSparePart.class);
        equipmentSparePartRepository.deleteAllByEquipmentId(equipmentId);
        if (!CollectionUtils.isEmpty(hvBmEquipmentSpareParts)) {
            equipmentSparePartRepository.saveAll(hvBmEquipmentSpareParts);
        }
    }

    /**
     * 复制设备事件处理
     *
     * @param event 复制设备事件
     */
    @EventListener(CopyEquipmentEvent.class)
    public void handleCopyEvent(CopyEquipmentEvent event) {
        List<HvBmEquipmentSparePart> equipmentSpareParts = equipmentSparePartRepository.findByEquipmentId(event.getCopyEquipmentId());
        if (CollectionUtils.isEmpty(equipmentSpareParts)) {
            return;
        }
        List<HvBmEquipmentSparePart> copySpareParts = new ArrayList<>();
        for (HvBmEquipmentSparePart equipmentSparePart : equipmentSpareParts) {
            HvBmEquipmentSparePart copySparePart = SerializationUtils.clone(equipmentSparePart);
            copySparePart.setId(null);
            copySparePart.setEquipmentId(event.getEquipmentId());
            copySpareParts.add(copySparePart);
        }
        equipmentSparePartRepository.saveAll(copySpareParts);
    }

    /**
     * 删除设备事件处理
     *
     * @param event 删除设备事件
     */
    @Transactional(rollbackFor = Exception.class)
    @EventListener(DelEquipmentEvent.class)
    public void handleDelEvent(DelEquipmentEvent event) {
        equipmentSparePartRepository.deleteAllByEquipmentId(event.getEquipmentId());
    }
}
