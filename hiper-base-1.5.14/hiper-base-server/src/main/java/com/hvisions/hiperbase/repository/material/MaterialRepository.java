package com.hvisions.hiperbase.repository.material;

import com.hvisions.hiperbase.entity.material.HvBmMaterial;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <p>Title: DemoEntityRepository</p>
 * <p>Description: 系统行政部门仓储层</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/10/23</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface MaterialRepository extends JpaRepository<HvBmMaterial, Integer> {

    /**
     * 根据material编码获取数据
     *
     * @param materialCode 物料编码
     * @param eigenvalue   特征值
     * @return 返回物料数据
     */
    HvBmMaterial getByMaterialCodeAndEigenvalue(String materialCode, String eigenvalue);

    /**
     * 根据ID列表查询物料
     *
     * @param id id
     * @return 物料列表信息
     */
    List<HvBmMaterial> getHvBmMaterialByIdIn(List<Integer> id);

    /**
     * 模糊查询
     *
     * @param materialCode 物料编码
     * @return 物料信息列表
     */
    List<HvBmMaterial> getHvBmMaterialByMaterialCode(String materialCode);

    /**
     * 判断物料分组在物料中是否存在
     *
     * @param materialGroup 物料分组ID
     * @return boolean
     */
    boolean existsByMaterialGroup(int materialGroup);

    /**
     * 判断 单位是否已被使用
     *
     * @param uomId 单位ID
     * @return boolean
     */
    boolean existsByUom(int uomId);


    /**
     * 判断 物料类型是否已被使用
     *
     * @param typeId 单位ID
     * @return boolean
     */
    boolean existsByMaterialType(int typeId);

    /**
     * 查询物料
     *
     * @param code     物料名称
     * @param name     物料名称
     * @param pageable 分页条件
     * @return 分页信息
     */
    Page<HvBmMaterial> getAllByMaterialCodeContainsOrMaterialNameContains(String code, String name, Pageable pageable);

    /**
     * 根据物料类型查询物料信息
     *
     * @param materialTypeIds 物料类型id列表
     * @return 物料数据
     */
    List<HvBmMaterial> findAllByMaterialTypeIn(List<Integer> materialTypeIds);

    List<HvBmMaterial> findByMaterialCodeIn(Collection<String> materialCodes);
}
