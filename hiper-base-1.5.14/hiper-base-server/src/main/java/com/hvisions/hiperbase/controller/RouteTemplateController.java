package com.hvisions.hiperbase.controller;

import com.hvisions.hiperbase.route.dto.RouteCopyDTO;
import com.hvisions.hiperbase.route.dto.RouteDTO;
import com.hvisions.hiperbase.route.dto.RouteQueryDTO;
import com.hvisions.hiperbase.route.enums.RouteTypeEnum;
import com.hvisions.hiperbase.service.route.RouteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

/**
 * <p>Title: RouteTemplateController</p>
 * <p>Description: 工艺路线模板</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/3/23</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RequestMapping("/routeTemplate/v2")
@RestController
@Slf4j
@Api(description = "工艺路线模板控制器")
public class RouteTemplateController {
    private final RouteService routeService;

    @Autowired
    public RouteTemplateController(RouteService routeService) {
        this.routeService = routeService;
    }

    /**
     * 创建工艺路线模板
     *
     * @param routeDTO 工艺路线模板
     * @return 主键
     */
    @PostMapping("/create")
    @ApiOperation(value = "创造工艺路线模板")
    public Integer createRoute(@RequestBody RouteDTO routeDTO) {
        routeDTO.setRouteType(RouteTypeEnum.ROUTE.getCode());
        return routeService.createTemplate(routeDTO);
    }

    /**
     * 更新工艺路线模板
     *
     * @param routeDTO 工艺路线模板
     */
    @PutMapping("/update")
    @ApiOperation(value = "更新工艺路线模板")
    public void updateRoute(@RequestBody RouteDTO routeDTO) {
        routeDTO.setCode();
        routeService.updateTemplate(routeDTO);
    }

    /**
     * 工艺路线模板分页查询
     *
     * @param queryDTO 查询条件
     * @return 分页数据
     */
    @PostMapping("/getPage")
    @ApiOperation(value = "工艺路线模板分页查询")
    public Page<RouteDTO> getRoutePage(@RequestBody RouteQueryDTO queryDTO) {
        queryDTO.setRouteType(RouteTypeEnum.ROUTE.getCode());
        return routeService.getRoute(queryDTO);
    }


    /**
     * 根据id删除工艺路模板线
     *
     * @param routeTemplateId 工艺路线模板id
     */
    @DeleteMapping("/delete/{routeTemplateId}")
    @ApiOperation(value = "删除工艺路线模板")
    public void deleteRoute(@PathVariable Integer routeTemplateId) {
        routeService.deleteRouteTemplate(routeTemplateId);
    }

    /**
     * 复制工艺路模板线模板
     *
     * @param copyDTO 工艺路线模板id
     * @return 复制成功后的工艺路线模板id
     */
    @PostMapping("/copy")
    @ApiOperation(value = "复制工艺路线模板")
    public Integer copy(@RequestBody RouteCopyDTO copyDTO) {
        return routeService.copyRouteTemplate(copyDTO);
    }
}









