package com.hvisions.hiperbase.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.dao.material.MaterialPointMapper;
import com.hvisions.hiperbase.entity.material.HvBmMaterialPoint;
import com.hvisions.hiperbase.materials.dto.MaterialPointDTO;
import com.hvisions.hiperbase.materials.dto.MaterialPointQueryDTO;
import com.hvisions.hiperbase.service.material.MaterialPointAreaService;
import com.hvisions.hiperbase.service.material.MaterialPointService;
import com.hvisions.wms.client.WaresLocationMaterialPointClient;
import com.hvisions.wms.dto.location.WaresLocationMaterialPointSynchronizationDTO;
import com.hvisions.wms.enums.PageControllerTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/materialPoint")
@Api(description = "料点配置")
public class MaterialPointController {

    private MaterialPointService materialPointService;

    private MaterialPointMapper materialPointMapper;

    @Autowired
    private WaresLocationMaterialPointClient waresLocationMaterialPointClient;
    @Autowired
    private MaterialPointAreaService materialPointAreaService;

    @Autowired
    public MaterialPointController(MaterialPointService materialPointService,
                                   MaterialPointMapper materialPointMapper) {
        this.materialPointService = materialPointService;
        this.materialPointMapper = materialPointMapper;
    }

    /**
     * 查询料点配置
     *
     * @return 结果
     */
    @ApiOperation(value = "获取所有料点配置")
    @GetMapping("/getAllMaterialPoint")
    public List<MaterialPointDTO> getAllMaterialPoint() {
        return materialPointService.list().stream()
                .map(t -> DtoMapper.convert(t, MaterialPointDTO.class))
                .collect(Collectors.toList());
    }

    /**
     * 分页查询料点配置
     *
     * @param dto 查询信息
     * @return 分页结果
     */
    @ApiOperation(value = "分页条件查询料点配置")
    @PostMapping("/queryAllMaterialPoint")
    public Page<MaterialPointDTO> queryMaterialPoint(@RequestBody MaterialPointQueryDTO dto) {
        return PageHelperUtil.getPage(this.materialPointMapper::queryMaterialPoint, dto);
    }



    /**
     * 通过ID查询料点配置信息
     *
     * @param materialPointId 料点配置id
     * @return 料点配置信息
     */
    @GetMapping(value = "/getMaterialPoint/{id}")
    @ApiOperation(value = "通过id查询料点配置信息")
    public MaterialPointDTO getMaterialPointById(@PathVariable("id") int materialPointId) {
        return materialPointService.getMaterialPointById(materialPointId);
    }

    /**
     * 通过料点区域编码查询料点配置信息
     *
     * @param pointAreaCode 料点区域编码
     * @return 料点配置信息
     */
    @GetMapping(value = "/getMaterialPointByPointAreaCode")
    @ApiOperation(value = "通过料点区域编码查询料点配置信息")
    public MaterialPointDTO getMaterialPointByPointAreaCode(@RequestParam String pointAreaCode) {
        return materialPointService.getMaterialPointByPointAreaCode(pointAreaCode);
    }

    /**
     * 新增料点配置
     *
     * @param materialPointDTO 传入的对象
     * @return 添加后的实体Id
     */
    @ApiOperation(value = "新增料点配置")
    @PostMapping(value = "/createMaterialPoint")
    @Transactional(rollbackFor = Exception.class)
    public boolean createMaterialPoint(
            @Validated @RequestBody final MaterialPointDTO materialPointDTO,
            @UserInfo @ApiIgnore UserInfoDTO userInfo) {

        return materialPointService.createMaterialPoint(materialPointDTO,userInfo);

    }

    /**
     * 修改料点配置信息
     *
     * @param materialPointDTO 传入的对象
     * @return 添加后的实体Id
     */
    @ApiOperation(value = "更新料点配置信息")
    @PutMapping(value = "/updateMaterialPoint")
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMaterialPoint(
            @Validated @RequestBody final MaterialPointDTO materialPointDTO,
            @UserInfo @ApiIgnore UserInfoDTO userInfo) {

        return materialPointService.updateMaterialPoint(materialPointDTO,userInfo);
    }

    /**
     * 删除料点配置
     *
     * @param materialPointId 料点配置id
     */
    @ApiOperation(value = "删除料点配置")
    @DeleteMapping(value = "/deleteMaterialPointById/{id}")
    @Transactional(rollbackFor = Exception.class)
    public void deleteMaterialPointById(@PathVariable("id") int materialPointId) {
        MaterialPointDTO materialPoint = materialPointService.getMaterialPointById(materialPointId);
        WaresLocationMaterialPointSynchronizationDTO waresLocationMaterialPointSynchronizationDTO = new WaresLocationMaterialPointSynchronizationDTO();
        waresLocationMaterialPointSynchronizationDTO.setMaterialPointCode(materialPoint.getPointCode());
        List<WaresLocationMaterialPointSynchronizationDTO> dtos = new ArrayList<>();
        dtos.add(waresLocationMaterialPointSynchronizationDTO);
        ResultVO<Boolean> resultVO = waresLocationMaterialPointClient.synchronizationBaseModule(dtos, PageControllerTypeEnum.DELETE.getCode());
        if(!resultVO.isSuccess()){
            throw new BaseKnownException("同步base模块异常,请联系管理员");
        }
        materialPointService.removeById(materialPointId);
    }

    /**
     * 获取导入模板
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    @ApiResultIgnore
    @GetMapping("/getImportTemplate")
    @ApiOperation("获取导入模板")
    public ResultVO<ExcelExportDto> getImportTemplate() throws IOException, IllegalAccessException {
        return materialPointService.getImportTemplate();
    }

    /**
     * 导入料点配置
     * @param file
     * @return
     * @throws IllegalAccessException
     * @throws ParseException
     * @throws IOException
     */
    @PostMapping("/importMaterialPoint")
    @ApiOperation("导入料点配置")
    public ImportResult importMaterialPoint(@RequestParam("file") MultipartFile file,@UserInfo @ApiIgnore UserInfoDTO userInfo) throws IllegalAccessException, ParseException, IOException {
        return materialPointService.importMaterialPoint(file,userInfo);
    }

    /**
     *  导出料点配置
     * @param dto
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    @ApiResultIgnore
    @PostMapping(value = "/exportMaterialPoint")
    @ApiOperation(value = "导出料点配置")
    public ResultVO<ExcelExportDto> exportMaterialPoint(@RequestBody MaterialPointQueryDTO dto) throws IOException, IllegalAccessException {
        return materialPointService.exportMaterialPoint(dto);
    }

    /**
     * 通过料点查询料点配置信息
     *
     * @param pointCode 料点区域编码
     * @return 料点配置信息
     */
    @GetMapping(value = "/getMaterialPointByPointCode")
    @ApiOperation(value = "通过料点编码查询料点配置信息(包含库区)")
    MaterialPointDTO getMaterialPointByPointCode(@RequestParam String pointCode) {
        return materialPointService.getMaterialPointByPointCode(pointCode);
    }


    @PutMapping(value = "/updateHaveFrame")
    @ApiOperation(value = "通过料点区域编码查询料点配置信息")
    void updateHaveFrame(@RequestBody MaterialPointDTO materialPointDTO) {
        materialPointService.updateById(DtoMapper.convert(materialPointDTO, HvBmMaterialPoint.class));
    }


    /**
     * 同步wms模块库区主数据
     *
     * @param synchronizationData 传入的对象
     * @return 添加后的实体Id
     */
    @ApiOperation(value = "同步wms模块库区主数据")
    @PutMapping(value = "/synchronizationWmsModule")
    public boolean synchronizationWmsModule(@RequestBody List<WaresLocationMaterialPointSynchronizationDTO> synchronizationData,@RequestParam("typeCode") Integer typeCode,
            @UserInfo @ApiIgnore UserInfoDTO userInfo) {
        return materialPointService.synchronizationWmsModule(synchronizationData,typeCode);
    }

}
