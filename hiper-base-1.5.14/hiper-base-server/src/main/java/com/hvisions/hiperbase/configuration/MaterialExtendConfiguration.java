package com.hvisions.hiperbase.configuration;

import com.hvisions.common.dto.ExtendInfoParam;
import com.hvisions.common.service.BaseExtendService;
import com.hvisions.common.utils.SqlFactoryUtil;
import com.hvisions.hiperbase.consts.MaterialConst;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <p>Title: ExtendConfiguration</p >
 * <p>Description: 扩展字段</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/12/24</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Configuration
public class MaterialExtendConfiguration {
    /**
     * @return 获取扩展服务工厂对象
     */
    @Bean
    SqlFactoryUtil getSqlFactory() {
        return new SqlFactoryUtil();
    }


    @Bean(value = "material_extend")
    BaseExtendService getMaterialExtendService(SqlFactoryUtil getSqlFactory) {
        ExtendInfoParam extendInfoParam = new ExtendInfoParam();
        extendInfoParam.setOriginTableName(MaterialConst.MATERIAL_TABLE_NAME);
        extendInfoParam.setOriginTableIdName(MaterialConst.MATERIAL_EXTEND_ID);
        extendInfoParam.setExtendTableName(MaterialConst.MATERIAL_EXTEND_TABLE_NAME);
        return getSqlFactory.getSqlBridge(extendInfoParam);
    }


}
