package com.hvisions.hiperbase.service.equipment.imp;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.hiperbase.equipment.EquipmentTypeParameterDTO;
import com.hvisions.hiperbase.equipment.TypeParameterQueryDTO;
import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentTypeParameter;
import com.hvisions.hiperbase.repository.equipment.EquipmentTypeParameterRepository;
import com.hvisions.hiperbase.service.equipment.EquipmentTypeParameterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>Title: HvEquipmentParameterServiceImpl</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/4/22</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Service
public class EquipmentTypeParameterServiceImpl implements EquipmentTypeParameterService {


    private final EquipmentTypeParameterRepository parameterRepository;

    @Autowired
    public EquipmentTypeParameterServiceImpl(EquipmentTypeParameterRepository parameterRepository) {
        this.parameterRepository = parameterRepository;
    }


    /**
     * 新增设备类型参数
     *
     * @param parameterDTO 设备类型参数DTO
     * @return 设备类型参数ID
     */
    @Override
    public int createEquipmentParameter(EquipmentTypeParameterDTO parameterDTO) {
        return parameterRepository.save(DtoMapper.convert(parameterDTO, HvBmEquipmentTypeParameter.class)).getId();
    }

    /**
     * 更新设备类型参数
     *
     * @param parameterDTO 设备类型参数DTO
     * @return 设备类型参数ID
     */
    @Override
    public int updateEquipmentParameter(EquipmentTypeParameterDTO parameterDTO) {
        return parameterRepository.save(DtoMapper.convert(parameterDTO, HvBmEquipmentTypeParameter.class)).getId();
    }

    /**
     * 删除设备类型参数根据ID列表
     *
     * @param idList 设备类型参数ID列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteEquipmentParameter(List<Integer> idList) {
        parameterRepository.deleteByIdIn(idList);
    }


    /**
     * 根据设备参数类型ID列表查询设备参数类型
     *
     * @param idList id列表
     * @return 设备参数类型列表
     */
    @Override
    public List<EquipmentTypeParameterDTO> getEquipmentParameterByIdList(List<Integer> idList) {
        return DtoMapper.convertList(parameterRepository.findAllById(idList), EquipmentTypeParameterDTO.class);
    }

    /**
     * 根据设备类型ID查询设备类型参数列表
     *
     * @param typeParameterQueryDTO 设备类型参数查询条件
     * @return 设备类型参数list
     */
    @Override
    public Page<EquipmentTypeParameterDTO> getEquipmentParameterByTypeId(TypeParameterQueryDTO typeParameterQueryDTO) {
        Page<HvBmEquipmentTypeParameter> typeParameters;
        HvBmEquipmentTypeParameter t = DtoMapper.convert(typeParameterQueryDTO, HvBmEquipmentTypeParameter.class);
        ExampleMatcher exampleMatcher = ExampleMatcher.matching()
                .withMatcher("typeId", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase());
        Example<HvBmEquipmentTypeParameter> example = Example.of(t, exampleMatcher);
        typeParameters = parameterRepository.findAll(example, typeParameterQueryDTO.getRequest());
        return DtoMapper.convertPage(typeParameters, EquipmentTypeParameterDTO.class);
    }


}
