package com.hvisions.hiperbase.repository.route;

import com.hvisions.hiperbase.entity.route.HvBmOperationSkill;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: OperationSkillRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/4/16</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Repository
public interface OperationSkillRepository extends JpaRepository<HvBmOperationSkill, Integer> {


    /**
     * 根据工艺操作Id查询工序所需技能
     *
     * @param operationId 工序Id
     * @return 工序所需技能
     */
    List<HvBmOperationSkill> getAllByOperationId(int operationId);
}