package com.hvisions.hiperbase.service.equipment.imp;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.hiperbase.SysBaseDTO;
import com.hvisions.hiperbase.entity.equipment.HvBmEquipment;
import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentClass;
import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentProperty;
import com.hvisions.hiperbase.equipment.CopyEquipmentEvent;
import com.hvisions.hiperbase.equipment.DelEquipmentEvent;
import com.hvisions.hiperbase.equipment.EquipmentDTO;
import com.hvisions.hiperbase.equipment.EquipmentPropertyDataType;
import com.hvisions.hiperbase.equipment.equipmentclass.AddEquipmentPropertyDTO;
import com.hvisions.hiperbase.equipment.equipmentclass.EquipmentPropertyDTO;
import com.hvisions.hiperbase.repository.equipment.EquipmentClassRepository;
import com.hvisions.hiperbase.repository.equipment.EquipmentPropertyRepository;
import com.hvisions.hiperbase.repository.equipment.EquipmentRepository;
import com.hvisions.hiperbase.service.equipment.EquipmentPropertyService;
import com.hvisions.hiperbase.service.equipment.EquipmentService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>Title: EquipmentPropertyServiceImpl</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/3/25</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
public class EquipmentPropertyServiceImpl implements EquipmentPropertyService {
    private final EquipmentPropertyRepository equipmentPropertyRepository;
    private final EquipmentRepository equipmentRepository;
    private final EquipmentService equipmentService;

    private final EquipmentClassRepository equipmentClassRepository;

    @Autowired
    public EquipmentPropertyServiceImpl(EquipmentPropertyRepository equipmentPropertyRepository, EquipmentRepository equipmentRepository, EquipmentService equipmentService, EquipmentClassRepository equipmentClassRepository) {
        this.equipmentPropertyRepository = equipmentPropertyRepository;
        this.equipmentRepository = equipmentRepository;
        this.equipmentService = equipmentService;
        this.equipmentClassRepository = equipmentClassRepository;
    }

    /**
     * 修改设备属性
     *
     * @param propertyDTOs 属性
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(List<EquipmentPropertyDTO> propertyDTOs) {
        for (EquipmentPropertyDTO propertyDTO : propertyDTOs) {
            Assert.notNull(propertyDTO.getId(), "修改时需要传递主键");
            Optional<HvBmEquipmentProperty> property = equipmentPropertyRepository.findById(propertyDTO.getId());
            property.ifPresent(t -> {
                if (!t.getIsConst()) {
                    t.setValue(propertyDTO.getValue());
                    t.setName(propertyDTO.getName());
                    setValue(t);
                    equipmentPropertyRepository.save(t);
                }
            });
        }
    }

    /**
     * 设置属性值
     *
     * @param propertyEntity 实体对象
     */
    private void setValue(HvBmEquipmentProperty propertyEntity) {
        switch (EquipmentPropertyDataType.getByCode(propertyEntity.getDateType())) {
            case STRING:
                propertyEntity.setStringValue(propertyEntity.getValue());
                break;
            case LONG:
                try {
                    propertyEntity.setLongValue(Long.parseLong(propertyEntity.getValue()));
                    propertyEntity.setValue(propertyEntity.getLongValue().toString());
                } catch (Exception ex) {
                    throw new BaseKnownException(10000, "数据格式错误");
                }
                break;
            case FLOAT:
                try {
                    propertyEntity.setFloatValue(Float.parseFloat(propertyEntity.getValue()));
                    propertyEntity.setValue(propertyEntity.getFloatValue().toString());
                } catch (Exception ex) {
                    throw new BaseKnownException(10000, "数据格式错误");
                }
                break;
            default:
                break;
        }
    }

    /**
     * 获取设备属性
     *
     * @param id 设备id
     * @return 设备属性列表
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<EquipmentPropertyDTO> findByEquipmentId(Integer id) {
        Optional<HvBmEquipment> equipment = equipmentRepository.findById(id);
        if (equipment.isPresent()) {
            List<EquipmentPropertyDTO> equipmentPropertyDTOS = DtoMapper.convertList(equipment.get().getProperties(), EquipmentPropertyDTO.class);
            equipmentPropertyDTOS =
                    equipmentPropertyDTOS.stream().sorted(Comparator.comparing(EquipmentPropertyDTO::getClassId,
                            Comparator.nullsFirst(Integer::compareTo))).collect(Collectors.toList());
            equipmentPropertyDTOS.forEach(t -> {
                HvBmEquipmentClass byCode = equipmentClassRepository.findByCode(t.getClassCode());
                if (byCode != null) {
                    t.setClassName(byCode.getName());
                }
            });
            return equipmentPropertyDTOS.stream().sorted(Comparator.comparing(SysBaseDTO::getId).reversed()).collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }
    }

    /**
     * 添加设备属性
     *
     * @param propertyDTO 设备属性
     */
    @Override
    public void addPropertyToEquipment(AddEquipmentPropertyDTO propertyDTO) {
        Optional<HvBmEquipment> equipment = equipmentRepository.findById(propertyDTO.getEquipmentId());
        Assert.isTrue(equipment.isPresent(), "设备信息不存在");
        propertyDTO.setIsConst(false);
        HvBmEquipmentProperty convert = DtoMapper.convert(propertyDTO, HvBmEquipmentProperty.class);
        convert.setEquipment(equipment.get());
        setValue(convert);
        equipmentPropertyRepository.save(convert);
        if (propertyDTO.getSyncToSameEquipment()) {
            syncToSameEquipment(propertyDTO, equipment.get());
        }
        if (propertyDTO.getSyncToSubEquipment()) {
            //同步到子设备
            syncToSubEquipment(propertyDTO, equipment.get());
        }
    }

    /**
     * 同步到子类型设备
     *
     * @param propertyDTO   属性值
     * @param hvBmEquipment 设备
     */
    private void syncToSubEquipment(AddEquipmentPropertyDTO propertyDTO, HvBmEquipment hvBmEquipment) {
        List<EquipmentDTO> equipmentList = equipmentService.findAllChildEquipmentByParentId(hvBmEquipment.getId());
        if (CollectionUtils.isNotEmpty(equipmentList)) {
            equipmentList.forEach(e -> {
                HvBmEquipmentProperty property = equipmentPropertyRepository.findByEquipmentIdAndCode(e.getId(), propertyDTO.getCode());
                if (property == null) {
                    //不存在添加
                    propertyDTO.setIsConst(false);
                    property = DtoMapper.convert(propertyDTO, HvBmEquipmentProperty.class);
                    property.setEquipment(DtoMapper.convert(e, HvBmEquipment.class));
                    setValue(property);
                    equipmentPropertyRepository.saveAndFlush(property);
                }
            });
        }

    }

    private void syncToSameEquipment(AddEquipmentPropertyDTO propertyDTO, HvBmEquipment equipment) {
        if(equipment.getEquipmentTypeId()==null){
            return ;
        }
        List<HvBmEquipment> hvBmEquipments = equipmentRepository.findAllByEquipmentTypeId(equipment.getEquipmentTypeId());
        //同步到相同类型设备
        if (CollectionUtils.isNotEmpty(hvBmEquipments)) {
            hvBmEquipments.stream()
                    .filter(e -> !e.getId().equals(propertyDTO.getEquipmentId()))
                    .forEach(e -> {
                        HvBmEquipmentProperty property = equipmentPropertyRepository.findByEquipmentIdAndCode(e.getId(), propertyDTO.getCode());
                        if (property == null) {
                            //不存在添加
                            propertyDTO.setIsConst(false);
                            property = DtoMapper.convert(propertyDTO, HvBmEquipmentProperty.class);
                            property.setEquipment(e);
                            setValue(property);
                            equipmentPropertyRepository.saveAndFlush(property);
                        }
                    });
        }
    }

    /**
     * 根据Id删除设备属性
     *
     * @param id
     */
    @Override
    public void deletePropertyById(int id) {
        Optional<HvBmEquipmentProperty> property = equipmentPropertyRepository.findById(id);
        Assert.isTrue(property.isPresent(), "设备属性不存在");
        Assert.isTrue(property.get().getClassCode() == null, "设备属性类型下属性无法删除");
        Optional<HvBmEquipment> equipment = equipmentRepository.findById(property.get().getEquipment().getId());
        Assert.isTrue(equipment.isPresent(), "设备信息不存在");
        equipment.get().getProperties().remove(property.get());
        equipmentPropertyRepository.deleteById(id);
    }

    /**
     * 复制设备事件处理
     *
     * @param event 复制设备事件
     */
    @EventListener(CopyEquipmentEvent.class)
    public void handleCopyEvent(CopyEquipmentEvent event) {
        List<HvBmEquipmentProperty> equipmentProperties = equipmentPropertyRepository.findByEquipmentId(event.getCopyEquipmentId());
        if (CollectionUtils.isEmpty(equipmentProperties)) {
            return;
        }
        List<HvBmEquipmentProperty> copyProperties = new ArrayList<>();
        for (HvBmEquipmentProperty equipmentProperty : equipmentProperties) {
            HvBmEquipmentProperty copyProperty = SerializationUtils.clone(equipmentProperty);
            copyProperty.setId(null);
            copyProperty.setEquipment(equipmentRepository.findById(event.getEquipmentId()).orElse(null));
            copyProperties.add(copyProperty);
        }
        equipmentPropertyRepository.saveAll(copyProperties);
    }

    /**
     * 删除设备事件处理
     *
     * @param event 删除设备事件
     */
    @Transactional(rollbackFor = Exception.class)
    @EventListener(DelEquipmentEvent.class)
    public void handleDelEvent(DelEquipmentEvent event) {
        equipmentPropertyRepository.deleteAllByEquipmentId(event.getEquipmentId());
    }
}









