package com.hvisions.hiperbase.service.equipment;

import com.hvisions.common.interfaces.EntitySaver;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.hiperbase.equipment.EquipmentExcelDto;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;

/**
 * <p>Title: EquipmentExcelService</p >
 * <p>Description: 设备excel导入</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/7</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
public interface EquipmentExcelService extends EntitySaver<EquipmentExcelDto> {
    ImportResult importEquipment(MultipartFile file) throws IOException, ParseException, IllegalAccessException;
}
