package com.hvisions.hiperbase.repository.equipment;

import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentSparePart;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>Title: EquipmentSparePartRepository</p >
 * <p>Description: </p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/9</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Repository
public interface EquipmentSparePartRepository extends JpaRepository<HvBmEquipmentSparePart, Integer> {
    /**
     * 根据设备id查询备件
     *
     * @param equipmentId 设备id
     * @return 备件列表
     */
    List<HvBmEquipmentSparePart> findByEquipmentId(Integer equipmentId);

    /**
     * 根据设备id删除备件
     * @param equipmentId 设备id
     */
    @Modifying
    @Transactional(rollbackFor = Exception.class)
    void deleteAllByEquipmentId(Integer equipmentId);
}
