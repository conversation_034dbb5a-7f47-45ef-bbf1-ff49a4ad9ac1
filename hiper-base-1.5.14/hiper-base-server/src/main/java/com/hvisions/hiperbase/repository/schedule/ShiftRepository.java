package com.hvisions.hiperbase.repository.schedule;

import com.hvisions.hiperbase.entity.schedule.HvBmShift;
import com.hvisions.hiperbase.schedule.dto.ShiftDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: HvBmShiftRepository</p>
 * <p>Description: 班次Repo</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface ShiftRepository extends JpaRepository<HvBmShift, Integer> {

    /**
     * 根据车间id查询班次列表
     *
     * @param areaId 车间id
     * @param cellId 产线id
     * @return 班次列表
     */
    List<HvBmShift> findAllByAreaIdAndCellId(int areaId, int cellId);

    /**
     * 通过班次编码 车间产线id 获取班次信息
     *
     * @param shiftCode 班次编码
     * @param areaId    车间id
     * @param CellId    产线id
     * @return 班次信息
     */
    HvBmShift findByShiftCodeAndAreaIdAndCellId(String shiftCode, Integer areaId, Integer CellId);

    /**
     * 通过班次编码 获取班次信息
     * @param shiftCode
     * @return
     */
    HvBmShift findByShiftCode(String shiftCode);
}
