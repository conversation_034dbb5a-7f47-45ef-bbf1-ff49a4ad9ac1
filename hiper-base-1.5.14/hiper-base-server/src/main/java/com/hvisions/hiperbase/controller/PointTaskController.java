package com.hvisions.hiperbase.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.hiperbase.entity.material.HvBmPointTask;
import com.hvisions.hiperbase.service.material.HvBmPointTaskService;
import com.hvisions.thirdparty.common.dto.ThirdpartyInterfaceDTO;
import com.hvisions.thridparty.client.ThirdpartyInterfaceClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 料点任务
 * <AUTHOR>
 * @date 2024-05-14 8:53
 */

@RestController
@RequestMapping(value = "/hvBmPointTask")
@Api(description = "料点任务")
public class PointTaskController {
    @Autowired
    private HvBmPointTaskService hvBmPointTaskService;
    @Autowired
    private ThirdpartyInterfaceClient thirdpartyInterfaceClient;

    /**
     * 分页查询
     */
    @ApiOperation("分页查询")
    @PostMapping("/list")
    public Page<HvBmPointTask> list(@RequestBody HvBmPointTask condition,
                         @RequestParam(name="pageNum", defaultValue="1") Integer pageNo,
                         @RequestParam(name="pageSize", defaultValue="10") Integer pageSize)
    {

        Page<HvBmPointTask> page = new Page<>(pageNo,pageSize);
        return hvBmPointTaskService.findPage(page,condition);
    }


    /**
     * 添加
     *
     * @param hvBmPointTask
     */
    @ApiOperation(value = "添加")
    @PostMapping(value = "/add")
    public boolean add(@RequestBody HvBmPointTask hvBmPointTask,@UserInfo @ApiIgnore UserInfoDTO userInfo) {
        //ThirdpartyInterfaceDTO interfaceDTO = thirdpartyInterfaceClient.getTpInterfaceByInterfaceCode(hvBmPointTask.getInterfaceCode()).getData();
        hvBmPointTask.setCreatorId(userInfo.getId());
        hvBmPointTask.setCreateTime(LocalDateTime.now());
        //hvBmPointTask.setInterfaceId(interfaceDTO.getId().intValue());
        return hvBmPointTaskService.save(hvBmPointTask);

    }

    /**
     * 删除
     *
     * @param id 主键
     */
    @ApiOperation(value = "删除")
    @DeleteMapping(value = "/delete/{id}")
    public boolean delete(@PathVariable Integer id) {
        return hvBmPointTaskService.removeById(id);
    }

    /**
     * 修改
     *
     * @param hvBmPointTask
     */
    @ApiOperation(value = "修改")
    @PutMapping(value = "/update")
    public boolean update(@RequestBody HvBmPointTask hvBmPointTask, @UserInfo @ApiIgnore UserInfoDTO userInfo) {
        //ThirdpartyInterfaceDTO interfaceDTO = thirdpartyInterfaceClient.getTpInterfaceByInterfaceCode(hvBmPointTask.getInterfaceCode()).getData();
        //hvBmPointTask.setInterfaceId(interfaceDTO.getId().intValue());
        hvBmPointTask.setUpdaterId(userInfo.getId());
        hvBmPointTask.setUpdateTime(LocalDateTime.now());
        return hvBmPointTaskService.updateById(hvBmPointTask);
    }

    /**
     * 根据id获取
     *
     * @param id 主键
     * @return HvPmAgvTaskRecord hvPmAgvTaskRecordDTO
     */
    @ApiOperation(value = "根据id获取")
    @GetMapping(value = "/get/{id}")
    public HvBmPointTask getList(@PathVariable Integer id) {
        return hvBmPointTaskService.getById(id);
    }

    /**
     * 查询全部
     * @return 列表
     */
    @ApiOperation(value = "查询全部")
    @GetMapping(value = "/getAll")
    public List<HvBmPointTask> getAll(){
        return hvBmPointTaskService.list();
    }

    /**
     * 根据料点编号获取第三方接口ID 按照执行顺序排序
     * @return
     */
    @ApiOperation(value = "根据料点编号获取第三方接口ID 按照执行顺序排序")
    @GetMapping(value = "/getInterfaceIdsByPoint")
    public List<Integer> getInterfaceIdsByPoint(@RequestParam("pointCode") String pointCode){
        return hvBmPointTaskService.getInterfaceIdsByPoint(pointCode);
    }

    /**
     * 获取所有第三方接口
     */
    @ApiOperation(value = "获取所有第三方接口")
    @GetMapping(value = "/getInterfaceList")
    public List<ThirdpartyInterfaceDTO> getInterfaceList(){
        return hvBmPointTaskService.getInterfaceList();
    }
}
