package com.hvisions.hiperbase.service.equipment.imp;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.dao.equipment.RepairAreaMapper;
import com.hvisions.hiperbase.entity.equipment.HvBmRepairArea;
import com.hvisions.hiperbase.equipment.RepairAreaDTO;
import com.hvisions.hiperbase.equipment.RepairAreaQueryDTO;
import com.hvisions.hiperbase.equipment.location.LocationDTO;
import com.hvisions.hiperbase.repository.equipment.RepairAreaRepository;
import com.hvisions.hiperbase.service.equipment.LocationService;
import com.hvisions.hiperbase.service.equipment.RepairAreaService;
import com.hvisions.thirdparty.common.dto.WeldLineRepairDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

@Service
@Slf4j
public class RepairAreaServiceImpl implements RepairAreaService {

    @Autowired
    private RepairAreaMapper repairAreaMapper;

    @Autowired
    private RepairAreaRepository repairAreaRepository;

    @Autowired
    private LocationService locationService;

    @Override
    public Page<RepairAreaDTO> getPage(RepairAreaQueryDTO repairAreaQueryDTO) {
        return PageHelperUtil.getPage(repairAreaMapper::getPage, repairAreaQueryDTO);
    }

    @Override
    public int createRepairArea(RepairAreaDTO repairAreaDTO) {
        HvBmRepairArea hvBmRepairArea = DtoMapper.convert(repairAreaDTO, HvBmRepairArea.class);
        HvBmRepairArea repairArea = repairAreaRepository.saveAndFlush(hvBmRepairArea);
        return repairArea.getId();
    }

    @Override
    public int updateRepairArea(RepairAreaDTO repairAreaDTO) {
        HvBmRepairArea hvBmRepairArea = DtoMapper.convert(repairAreaDTO, HvBmRepairArea.class);
        HvBmRepairArea repairArea = repairAreaRepository.save(hvBmRepairArea);
        return repairArea.getId();
    }

    @Override
    public void deleteRepairAreaById(int id) {
        repairAreaRepository.deleteById(id);
    }

    @Override
    public boolean isExistsRepairAreaByAreaCode(String areaCode) {
        HvBmRepairArea repairArea = repairAreaRepository.getByAreaCodeEquals(areaCode);
        return repairArea != null;
    }

    @Override
    public RepairAreaDTO getRepairAreaByLineId(int lineId) {
        HvBmRepairArea hvBmRepairArea = repairAreaRepository.getByLineIdEquals(lineId);
        return DtoMapper.convert(hvBmRepairArea, RepairAreaDTO.class);
    }

    @Override
    public List<LocationDTO> getNotUsedLine() {
        return repairAreaMapper.getNotUsedLine();
    }

    @Override
    public ResultVO<ExcelExportDto> exportRepairArea() throws IOException, IllegalAccessException {
        ResponseEntity<byte[]> result = export();
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(result.getBody());
        excelExportDto.setFileName("RepairArea.xls");
        return ResultVO.success(excelExportDto);
    }

    @Override
    public ResponseEntity<byte[]> export() throws IOException, IllegalAccessException {
        List<RepairAreaDTO> repairAreas = repairAreaMapper.getPage(new RepairAreaQueryDTO());
        List<RepairAreaDTO> exportDTO = DtoMapper.convertList(repairAreas, RepairAreaDTO.class);
        return ExcelUtil.generateImportFile(exportDTO, "RepairArea", RepairAreaDTO.class);
    }

    @Override
    public ResultVO<ExcelExportDto> getRepairAreaImportTemplate() throws IOException, IllegalAccessException {
        ResponseEntity<byte[]> result = ExcelUtil.generateImportFile(RepairAreaDTO.class, "RepairArea");
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(result.getBody());
        excelExportDto.setFileName("RepairAreaTemplate.xls");
        return ResultVO.success(excelExportDto);
    }

    @Override
    public ImportResult importRepairAreas(MultipartFile file) throws IllegalAccessException, ParseException, IOException {
        List<RepairAreaDTO> repairAreaDTOS = ExcelUtil.getEntityList(file, 0, RepairAreaDTO.class);
        for (RepairAreaDTO dto : repairAreaDTOS) {
            createRepairArea(dto);
        }
        return new ImportResult();
    }

    @Override
    public RepairAreaDTO repairDispatch(WeldLineRepairDTO weldLineRepairDTO) {
        LocationDTO locationDTO = locationService.getHvBmLocationByCode(weldLineRepairDTO.getLineCode());
        LocationDTO repair = null;
        if (locationDTO == null) {
            throw new BaseKnownException("产线编号未找到,请输入正确的产线编号");
        }
        if (locationDTO.getType() < 40) {
            throw new BaseKnownException("该定位不是返修区域");
        }
        if (locationDTO.getType() == 40) {
            repair = locationDTO;
        }
        if (locationDTO.getType() > 40) {
            repair = locationService.findLocationExtendById(locationDTO.getParentId());
        }
        RepairAreaDTO repairAreaDTO = getRepairAreaByLineId(repair.getId());
        if (repairAreaDTO == null) {
            throw new BaseKnownException("该返修区域不存在");
        }
        return repairAreaDTO;
    }
}
