package com.hvisions.hiperbase.service.equipment.imp;

import cn.hutool.core.lang.Assert;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.hiperbase.equipment.EquipmentDTO;
import com.hvisions.hiperbase.equipment.equipmentclass.EquipmentClassDTO;
import com.hvisions.hiperbase.equipment.equipmentclass.EquipmentClassProperty;
import com.hvisions.hiperbase.equipment.equipmentclass.EquipmentClassQuery;
import com.hvisions.hiperbase.entity.equipment.HvBmEquipment;
import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentClass;
import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentProperty;
import com.hvisions.hiperbase.repository.equipment.EquipmentClassRepository;
import com.hvisions.hiperbase.repository.equipment.EquipmentPropertyRepository;
import com.hvisions.hiperbase.repository.equipment.EquipmentRepository;
import com.hvisions.hiperbase.service.equipment.EquipmentClassService;
import com.hvisions.hiperbase.service.equipment.EquipmentService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>Title: EquipmentClassServiceImpl</p>
 * <p>Description: 设备属性类型实现类</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/3/25</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
public class EquipmentClassServiceImpl implements EquipmentClassService {
    private EquipmentClassRepository repository;
    private EquipmentPropertyRepository propertyRepository;
    private EquipmentRepository equipmentRepository;

    private final EquipmentService equipmentService;

    @Autowired
    public EquipmentClassServiceImpl(EquipmentClassRepository repository, EquipmentPropertyRepository propertyRepository,
                                     EquipmentRepository equipmentRepository, EquipmentService equipmentService) {
        this.repository = repository;
        this.propertyRepository = propertyRepository;
        this.equipmentRepository = equipmentRepository;
        this.equipmentService = equipmentService;
    }

    /**
     * 创建设备属性类型
     *
     * @param classDTO 属性类型
     * @return 主键
     */
    @Override
    public Integer create(EquipmentClassDTO classDTO) {
        Assert.isNull(classDTO.getId(), "创建时不能传递主键");
        HvBmEquipmentClass clazz = repository.save(DtoMapper.convert(classDTO, HvBmEquipmentClass.class));
        return clazz.getId();
    }

    /**
     * 修改设备属性类型
     *
     * @param classDTO 属性类型
     */
    @Override
    public void update(EquipmentClassDTO classDTO) {
        Assert.notNull(classDTO.getId(), "更新时主键不能为空");
        Optional<HvBmEquipmentClass> clazz = repository.findById(classDTO.getId());
        clazz.ifPresent(c -> {
            c.setName(classDTO.getName());
            repository.save(c);
        });
    }

    /**
     * 获取设备属性类型
     *
     * @param id 属性类型主键
     * @return 属性类型
     */
    @Override
    public EquipmentClassDTO findById(Integer id) {
        return repository.findById(id)
                .map(t -> DtoMapper.convert(t, EquipmentClassDTO.class))
                .orElse(null);
    }

    /**
     * 获取设备属性类型
     *
     * @param code 属性类型编码
     * @return 属性类型
     */
    @Override
    public EquipmentClassDTO findByCode(String code) {
        return DtoMapper.convert(repository.findByCode(code), EquipmentClassDTO.class);
    }

    /**
     * 获取设备属性类型分页数据
     *
     * @param query 属性类型编码
     * @return 属性类型分页数据
     */
    @Override
    public Page<EquipmentClassDTO> findPage(EquipmentClassQuery query) {
        return DtoMapper.convertPage(
                repository.findAllByCodeContainsOrNameContaining(
                        query.getCodeOrName(), query.getCodeOrName(), query.getRequest())
                , EquipmentClassDTO.class);
    }

    /**
     * 删除设备属性类型
     *
     * @param id 属性类型
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Integer id) {
        Optional<HvBmEquipmentClass> clazz = repository.findById(id);
        //需要把配置了类型的设备的属性全部删除
        clazz.ifPresent(t -> {
            List<HvBmEquipment> equipments = t.getEquipments();
            for (HvBmEquipment equipment : equipments) {
                equipment.getClasses().remove(t);
                List<HvBmEquipmentProperty> collect = equipment.getProperties().stream()
                        .filter(e -> t.getCode().equals(e.getClassCode()))
                        .collect(Collectors.toList());
                equipment.getProperties().removeAll(collect);
                equipmentRepository.save(equipment);
                propertyRepository.deleteAll(collect);
            }
        });
        repository.deleteById(id);
    }

    /**
     * 根据设备id查询设备属性列表
     *
     * @param id 设备di
     * @return 设备属性类型列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<EquipmentClassDTO> findByEquipmentId(Integer id) {
        Optional<HvBmEquipment> equipment = equipmentRepository.findById(id);
        List<EquipmentClassDTO> classDTOS = new ArrayList<>();
        equipment.ifPresent(t -> {
            List<EquipmentClassDTO> classes = DtoMapper.convertList(t.getClasses(), EquipmentClassDTO.class);
            classDTOS.addAll(classes);
        });
        return classDTOS;
    }

    /**
     * 向设备添加设备属性类型
     *
     * @param equipmentId 设备di
     * @param classId     属性类型id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addClassToEquipment(Integer equipmentId, Integer classId) {
        HvBmEquipment equipment = equipmentRepository.findById(equipmentId)
                .orElseThrow(()->new BaseKnownException("设备信息不存在"));
        HvBmEquipmentClass equipmentClass = repository.findById(classId)
                        .orElseThrow(()->new BaseKnownException("设备属性类型不存在"));
        Assert.isFalse(equipment.getClasses().contains(equipmentClass), "设备已经存在此类型");
        List<HvBmEquipmentProperty> hvBmEquipmentProperties = DtoMapper.convertList(equipmentClass.getProperties(), HvBmEquipmentProperty.class);
        hvBmEquipmentProperties.forEach(t -> {
            t.setClassCode(equipmentClass.getCode());
            t.setId(null);
            t.setClassId(equipmentClass.getId());
            t.setClassName(equipmentClass.getName());
            t.setEquipment(equipment);
        });
        equipment.getClasses().add(equipmentClass);
        equipment.getProperties().addAll(hvBmEquipmentProperties);
        equipmentRepository.save(equipment);
    }

    /**
     * 删除设备的设备属性类型
     *
     * @param equipmentId 设备di
     * @param classId     属性类型id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeClassToEquipment(Integer equipmentId, Integer classId) {
        HvBmEquipment equipment = equipmentRepository.findById(equipmentId)
                .orElseThrow(()->new BaseKnownException("设备信息不存在"));
        HvBmEquipmentClass equipmentClass = repository.findById(classId)
                        .orElseThrow(()->new BaseKnownException("设备属性类型不存在"));
        if (equipment.getClasses().contains(equipmentClass)) {
            equipment.getClasses().remove(equipmentClass);
            List<HvBmEquipmentProperty> propertyList =
                    equipment.getProperties().stream().filter(t -> t.getClassCode() != null).collect(Collectors.toList());
            propertyList = propertyList.stream()
                    .filter(t -> t.getClassCode().equals(equipmentClass.getCode()))
                    .collect(Collectors.toList());
            propertyRepository.deleteAll(propertyList);
            equipment.getProperties().removeAll(propertyList);
            equipmentRepository.save(equipment);
        }
    }

    /**
     * 新增设备属性
     *
     * @param property 设备属性
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addEquipmentClass(EquipmentClassProperty property) {
        Optional<HvBmEquipment> equipment = equipmentRepository.findById(property.getEquipmentId());
        if (equipment.isPresent()) {
            addClassToEquipment(equipment.get().getId(), property.getClassId());
            if (property.getSyncToSameEquipment()) {
                //同步相同设备
                List<HvBmEquipment> hvBmEquipments = equipmentRepository.findAllByEquipmentTypeId(equipment.get().getEquipmentTypeId());
                hvBmEquipments.forEach(t -> {
                    if (!t.getId().equals(equipment.get().getId())) {
                        addClassToEquipment(t.getId(), property.getClassId());
                    }
                });
            }
            if (property.getSyncToSubEquipment()) {
                //同步子设备
                List<EquipmentDTO> subEquipments = equipmentService.findAllChildEquipmentByParentId(equipment.get().getId());
                if (CollectionUtils.isNotEmpty(subEquipments)) {
                    subEquipments.forEach(e -> {
                        addClassToEquipment(e.getId(), property.getClassId());
                    });
                }

            }
        }
    }
}









