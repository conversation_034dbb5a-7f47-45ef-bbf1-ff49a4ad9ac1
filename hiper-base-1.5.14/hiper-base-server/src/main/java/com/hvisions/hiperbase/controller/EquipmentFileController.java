package com.hvisions.hiperbase.controller;

import com.hvisions.hiperbase.equipment.EquipmentFileDTO;
import com.hvisions.hiperbase.service.equipment.EquipmentFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title: HvBmEquipmentFileController</p >
 * <p>Description: 设备文件关联关系控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/2</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@Slf4j
@RequestMapping(value = "/equipmentFile")
@Api(description = "设备文件关联关系控制器")
public class EquipmentFileController {


    private final EquipmentFileService equipmentFileService;

    @Autowired
    public EquipmentFileController(EquipmentFileService equipmentFileService) {
        this.equipmentFileService = equipmentFileService;
    }


    /**
     * 新增设备文件关联
     *
     * @param hvEquipmentFileDTO 设备文件关联关系
     */
    @ApiOperation(value = "新增设备文件关联")
    @PostMapping(value = "/createEquipmentFile")
    public void createEquipmentFile(@Valid @RequestBody EquipmentFileDTO hvEquipmentFileDTO) {
        equipmentFileService.createEquipmentFile(hvEquipmentFileDTO);
    }


    /**
     * 根据ID删除设备文件关联关系
     *
     * @param id 主键ID
     */
    @ApiOperation(value = "根据ID删除设备文件关联关系")
    @DeleteMapping(value = "/deleteEquipmentFileById/{id}")
    public void deleteEquipmentFileById(@PathVariable int id) {
        equipmentFileService.deleteEquipmentFileById(id);
    }

    @ApiOperation(value = "修改设备文件关联关系")
    @PutMapping(value = "/updateEquipmentFile")
    public void updateEquipmentFile(@Valid @RequestBody EquipmentFileDTO hvEquipmentFileDTO) {
        equipmentFileService.updateEquipmentFile(hvEquipmentFileDTO);
    }

    @ApiOperation(value = "根据设备id查询设备文件关联关系")
    @GetMapping("/findAllByEquipmentId/{equipmentId}")
    public List<EquipmentFileDTO> findAllByEquipmentId(@PathVariable Integer equipmentId) {
        return equipmentFileService.findAllByEquipmentId(equipmentId);
    }
}
