package com.hvisions.hiperbase.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.utils.EnumUtil;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.excel.NodeParameterExcel;
import com.hvisions.hiperbase.route.UnitFunction;
import com.hvisions.hiperbase.route.dto.NodeParameterData;
import com.hvisions.hiperbase.route.dto.consts.RouteConsts;
import com.hvisions.hiperbase.route.enums.ParameterDataTypeEnum;
import com.hvisions.hiperbase.route.enums.ParameterDataUsage;
import com.hvisions.hiperbase.route.enums.ParameterTypeEnum;
import com.hvisions.hiperbase.service.route.ParameterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: ParameterController</p>
 * <p>Description: 工艺参数控制器</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/12/24</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@SuppressWarnings("SpringJavaAutowiredFieldsWarningInspection")
@RestController
@RequestMapping("/parameter")
@Api(description = "工艺参数控制器")
public class ParameterController {
    @Autowired
    ParameterService parameterService;

    //-----------------基础接口----------------------
    //-----------------导入导出----------------------

    /**
     * 获取导入模板
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/getParameterImportTemplateLink")
    @ApiOperation(value = "获取导入模板 支持超链接")
    public ResponseEntity<byte[]> getParameterImportTemplateLink() throws IOException, IllegalAccessException {
        return ExcelUtil.generateImportFile(NodeParameterExcel.class, RouteConsts.PARAMETER_FILE_NAME);
    }

    /**
     * 获取导入模板
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/getParameterImportTemplate")
    @ApiOperation(value = "获取导入模板 ")
    public ResultVO<ExcelExportDto> getParameterImportTemplate() throws IOException, IllegalAccessException {
        ResponseEntity<byte[]> result = ExcelUtil.generateImportFile(NodeParameterExcel.class, RouteConsts.PARAMETER_FILE_NAME);
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(result.getBody());
        excelExportDto.setFileName(RouteConsts.PARAMETER_FILE_NAME);
        return ResultVO.success(excelExportDto);
    }


    /**
     * 导出所有参数信息
     *
     * @return 参数信息excel
     */
    @ApiResultIgnore
    @GetMapping(value = "/exportParameter")
    @ApiOperation(value = "导出所有参数信息 ")
    public ResultVO<ExcelExportDto> exportParameter(@RequestParam Integer routeId, @RequestParam(defaultValue = "") String nodeId) {
        return parameterService.exportParameter(routeId,nodeId);
    }

    //-----------------导入导出----------------------

    /**
     * 导入参数信息
     *
     * @param file 物料信息文档
     * @return 导入信息详情
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    @PostMapping(value = "/importParameter")
    @ApiOperation(value = "导入参数信息(code存在则更新，不存在则新增)")
    public List<NodeParameterData> importParameter(@RequestParam("file") MultipartFile file) throws IllegalAccessException,
        ParseException, IOException {
        return parameterService.importParameter(file);
    }

    //-----------------类型接口----------------------

    /**
     * 获取工艺参数类型
     *
     * @return 类型列表
     */
    @GetMapping("/getParameterType")
    @ApiOperation(value = "获取工艺参数类型")
    public Map<Integer, String> getParameterType() {
        return EnumUtil.enumToMap(ParameterTypeEnum.class);
    }

    /**
     * 获取工艺参数数据类型
     *
     * @return 工艺参数数据类型
     */
    @GetMapping("/getParameterDataTypeEnum")
    @ApiOperation(value = "获取工艺参数数据类型")
    public Map<Integer, String> getParameterDataTypeEnum() {
        return EnumUtil.enumToMap(ParameterDataTypeEnum.class);
    }

    /**
     * 获取工艺参数用途
     *
     * @return 工艺参数用途列表
     */
    @GetMapping("/getParameterUsage")
    @ApiOperation(value = "获取工艺参数用途")
    public Map<Integer, String> getParameterUsage() {
        return EnumUtil.enumToMap(ParameterDataUsage.class);
    }
    //-----------------类型接口----------------------

    /**
     * 查询所有单位
     *
     * @return 参数单位
     */
    @GetMapping("/queryAllUnit")
    @ApiOperation(value = "查询所有单位")
    public Map<Integer, String> queryAllUnit() {
        return UnitFunction.queryAllUnit();
    }
}










