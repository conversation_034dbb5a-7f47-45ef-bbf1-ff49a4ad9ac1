package com.hvisions.hiperbase.service.schedule;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.schedule.dto.ShiftDTO;

import java.util.List;

/**
 * <p>Title: ShiftService</p>
 * <p>Description: 班次service</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface ShiftService {
    /**
     * 创建班次
     *
     * @param shiftDTO 班次信息
     * @return 创建后的班次id
     */
    int createOrUpdateShift(ShiftDTO shiftDTO);

    /**
     * 根据id删除班次
     *
     * @param id 班次id
     */
    void deleteShiftById(int id);


    /**
     * 根据车间id查询班次信息
     *
     * @param areaId 车间id
     * @param cellId 产线id
     * @return 班次信息列表
     */
    List<ShiftDTO> getShiftListByAreaIdAndCellId(int areaId, int cellId);

    /**
     * 根据id查询班次信息
     *
     * @param shiftId 班次id
     * @return 班次信息
     */
    ShiftDTO getShiftById(int shiftId);

    /**
     * 根据班次编码查询班次信息
     *
     * @param shiftCode 班次编码
     * @param areaId    车间id
     * @param cellId    产线id
     * @return 班次信息
     */
    ShiftDTO getShiftByShiftCode(String shiftCode, Integer areaId, Integer cellId);

    /**
     * 根据班次编码查询班次信息
     *
     * @param shiftCodeList 班次编码列表
     * @param areaId        车间id
     * @param cellId        产线id
     * @return 班次信息集合
     */
    List<ShiftDTO> getShiftByShiftCodeList(List<String> shiftCodeList, Integer areaId, Integer cellId);

    /**
     * 通过编码删除班次信息
     *
     * @param shiftCode 班次信息
     * @param areaId    车间id
     * @param cellId    产线id
     */
    void deleteShiftByShiftCode(String shiftCode, Integer areaId, Integer cellId);

    ShiftDTO getShiftInfoByShiftCode(String shiftCode);
}
















