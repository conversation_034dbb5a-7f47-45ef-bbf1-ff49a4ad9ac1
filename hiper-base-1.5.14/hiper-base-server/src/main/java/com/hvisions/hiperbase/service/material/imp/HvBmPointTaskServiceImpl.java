package com.hvisions.hiperbase.service.material.imp;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.framework.client.DictionaryItemClient;
import com.hvisions.framework.dto.dictionary.DictionaryItemDTO;
import com.hvisions.hiperbase.dao.material.HvBmPointTaskMapper;
import com.hvisions.hiperbase.entity.material.HvBmPointTask;
import com.hvisions.hiperbase.service.material.HvBmPointTaskService;
import com.hvisions.thirdparty.common.dto.*;
import com.hvisions.thridparty.client.ThirdpartyInterfaceClient;
import com.hvisions.thridparty.client.ThirdpartySystemClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-05-14 8:52
 */
@Service
public class HvBmPointTaskServiceImpl extends ServiceImpl<HvBmPointTaskMapper, HvBmPointTask> implements HvBmPointTaskService {
    @Resource
    private HvBmPointTaskMapper hvBmPointTaskMapper;
    @Autowired
    private DictionaryItemClient dictionaryItemClient;
    @Autowired
    private ThirdpartyInterfaceClient thirdpartyInterfaceClient;
    @Autowired
    private ThirdpartySystemClient thirdpartySystemClient;

    @Override
    public Page<HvBmPointTask> findPage(Page<HvBmPointTask> page, HvBmPointTask condition) {

        Page<HvBmPointTask> pageList = hvBmPointTaskMapper.findPage(page, condition);
        List<ThirdpartyInterfaceDTO> thirdpartyInterfaceDTOS = thirdpartyInterfaceClient.getTpInterfaceList().getData();
        //获取第三接口字典信息
        ResultVO<List<DictionaryItemDTO>> thirdPartyInterfaceDictionaryItemClientAll = dictionaryItemClient.findAll("thirdpartyInterface");
        List<DictionaryItemDTO> thirdPartyInterfacedata = thirdPartyInterfaceDictionaryItemClientAll.getData();
        //获取第三方接口系统信息
        List<ThirdpartySystemDTO> thirdpartySystemDTOS = thirdpartySystemClient.getThirdpartySystemList().getData();
        Map<String, String> interfaceCodeMap = new HashMap<>();
        Map<Integer, String> thirdpartySystemMap = new HashMap<>();
        Map<Long, ThirdpartyInterfaceDTO> thirdpartyInterfaceDTOMap = new HashMap<>();
        for (DictionaryItemDTO thirdPartyInterfacedatum : thirdPartyInterfacedata) {
            interfaceCodeMap.put(thirdPartyInterfacedatum.getItemKey(), thirdPartyInterfacedatum.getItemValue());
        }
        if (thirdpartySystemDTOS != null)
            for (ThirdpartySystemDTO thirdpartySystemDTO : thirdpartySystemDTOS) {
                thirdpartySystemMap.put(thirdpartySystemDTO.getId(), thirdpartySystemDTO.getSystemName());
            }
        if (thirdpartyInterfaceDTOS != null)
            for (ThirdpartyInterfaceDTO thirdpartyInterfaceDTO : thirdpartyInterfaceDTOS) {
                thirdpartyInterfaceDTOMap.put(thirdpartyInterfaceDTO.getId(), thirdpartyInterfaceDTO);
            }

        List<HvBmPointTask> list = pageList.getRecords().stream().peek(hvBmPointTask -> {
            ThirdpartyInterfaceDTO thirdpartyInterfaceDTO = thirdpartyInterfaceDTOMap.get(hvBmPointTask.getInterfaceId().longValue());
            if (thirdpartyInterfaceDTO != null) {
                String systemName = thirdpartySystemMap.get(thirdpartyInterfaceDTO.getSystemId());
                String interfaceName = interfaceCodeMap.get(thirdpartyInterfaceDTO.getInterfaceCode());
                hvBmPointTask.setInterfaceName(systemName + "-" + interfaceName);
            }
        }).collect(Collectors.toList());
        pageList.setRecords(list);
        return pageList;

    }


    @Override
    public List<Integer> getInterfaceIdsByPoint(String pointCode) {
        return hvBmPointTaskMapper.getInterfaceIdsByPoint(pointCode);
    }

    @Override
    public List<ThirdpartyInterfaceDTO> getInterfaceList() {
        try {
            ResultVO<List<ThirdpartyInterfaceDTO>> resultVO = thirdpartyInterfaceClient.getTpInterfaceList();
            if (resultVO == null) {
                throw new BaseKnownException("第三方接口获取失败，请检查第三方接口微服务系统运行情况");
            }
            //获取第三方接口系统信息
            List<ThirdpartySystemDTO> thirdpartySystemDTOS = thirdpartySystemClient.getThirdpartySystemList().getData();
            //获取第三接口字典信息
            ResultVO<List<DictionaryItemDTO>> thirdPartyInterfaceDictionaryItemClientAll = dictionaryItemClient.findAll("thirdpartyInterface");
            List<DictionaryItemDTO> thirdPartyInterfacedata = thirdPartyInterfaceDictionaryItemClientAll.getData();
            List<ThirdpartyInterfaceDTO> interfaceDTOS = resultVO.getData();
            Map<String, String> interfaceCodeMap = new HashMap<>();
            Map<Integer, String> thirdpartySystemMap = new HashMap<>();
            if (thirdPartyInterfacedata != null)
                for (DictionaryItemDTO thirdPartyInterfacedatum : thirdPartyInterfacedata) {
                    interfaceCodeMap.put(thirdPartyInterfacedatum.getItemKey(), thirdPartyInterfacedatum.getItemValue());
                }
            if (thirdpartySystemDTOS != null)
                for (ThirdpartySystemDTO thirdpartySystemDTO : thirdpartySystemDTOS) {
                    thirdpartySystemMap.put(thirdpartySystemDTO.getId(), thirdpartySystemDTO.getSystemName());
                }
            return interfaceDTOS.stream().peek(thirdpartyInterfaceDTO -> {
                if (interfaceCodeMap.get(thirdpartyInterfaceDTO.getInterfaceCode()) != null) {
                    thirdpartyInterfaceDTO.setInterfaceCode(interfaceCodeMap.get(thirdpartyInterfaceDTO.getInterfaceCode()));
                }
                String systemName = thirdpartySystemMap.get(thirdpartyInterfaceDTO.getSystemId());

                thirdpartyInterfaceDTO.setInterfaceName(systemName + ":" + thirdpartyInterfaceDTO.getInterfaceCode());
            }).collect(Collectors.toList());
        } catch (Exception e) {
            throw new BaseKnownException("第三方接口获取失败，请检查第三方接口微服务系统运行情况");
        }
    }
}
