package com.hvisions.hiperbase.controller;

import com.hvisions.hiperbase.equipment.equipmentclass.AddEquipmentPropertyDTO;
import com.hvisions.hiperbase.equipment.equipmentclass.EquipmentPropertyDTO;
import com.hvisions.hiperbase.service.equipment.EquipmentPropertyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * <p>Title: EquipmentPropertyController</p>
 * <p>Description: 设备属性控制器</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/3/25</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Api(description = "设备属性控制器")
@RestController
@RequestMapping(value = "/equipmentProperty")
public class EquipmentPropertyController {
    private EquipmentPropertyService equipmentPropertyService;

    @Autowired
    public EquipmentPropertyController(EquipmentPropertyService equipmentPropertyService) {
        this.equipmentPropertyService = equipmentPropertyService;
    }

    /**
     * 修改设备属性
     *
     * @param propertyDTOs 属性
     */
    @ApiOperation("修改设备属性")
    @PutMapping("/update")
    public void update(@Valid @RequestBody List<EquipmentPropertyDTO> propertyDTOs) {
        equipmentPropertyService.update(propertyDTOs);
    }

    /**
     * 获取设备属性
     *
     * @param id 设备id
     * @return 设备属性列表
     */
    @ApiOperation("获取设备属性")
    @GetMapping("/findByEquipmentId/{id}")
    public List<EquipmentPropertyDTO> findByEquipmentId(@PathVariable Integer id) {
        return equipmentPropertyService.findByEquipmentId(id);
    }

    /**
     * 获取设备属性,如果只传递code。会返回第一条数据
     *
     * @param id        设备id
     * @param code      属性编码
     * @param clazzCode 属性类型
     * @return 设备属性列表
     */
    @ApiOperation("获取设备属性,如果设备只有唯一编码的属性可以直接获取，如果存在多个同名的属性，需要传递属性类型编码再查询，否则可能会出现错误")
    @GetMapping("/findByCode")
    public EquipmentPropertyDTO findByCode(@RequestParam Integer id,
                                           @RequestParam String code,
                                           @RequestParam(required = false) String clazzCode) {
        List<EquipmentPropertyDTO> equipmentPropertyDTOList = equipmentPropertyService.findByEquipmentId(id);
        Stream<EquipmentPropertyDTO> stream = equipmentPropertyDTOList.stream();
        stream = stream.filter(t -> code.equals(t.getCode()));
        if (clazzCode != null) {
            stream = stream.filter(t -> clazzCode.equals(t.getClassCode()));
        }
        Optional<EquipmentPropertyDTO> first = stream.findFirst();
        return first.orElse(null);

    }

    /**
     * 添加设备属性
     *
     * @param propertyDTO 设备属性
     */
    @PostMapping(value = "/addPropertyToEquipment")
    @ApiOperation(value = "添加设备属性")
    public void addPropertyToEquipment(@Valid @RequestBody AddEquipmentPropertyDTO propertyDTO) {
        equipmentPropertyService.addPropertyToEquipment(propertyDTO);
    }

    /**
     * 根据Id删除设备属性
     */
    @DeleteMapping(value = "/deletePropertyById/{id}")
    @ApiOperation(value = "根据Id删除设备属性")
    public void deletePropertyById(@PathVariable int id) {
        equipmentPropertyService.deletePropertyById(id);
    }
}









