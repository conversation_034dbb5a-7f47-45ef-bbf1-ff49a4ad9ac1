package com.hvisions.hiperbase.service.equipment;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.entity.equipment.HvBmEquipment;
import com.hvisions.hiperbase.equipment.*;
import com.hvisions.hiperbase.equipment.location.LocationMsgDTO;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;

import java.io.IOException;
import java.util.List;

/**
 * <p>Title: EquipmentService</p>
 * <p>Description: 设备属性扩展服务</p>
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p>
 * <p>create date: 2018/11/15</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface EquipmentService {


    /**
     * 获取所有设备信息
     *
     * @return 设备信息
     */
    List<EquipmentDTO> getAllEquipment();


    /**
     * 根据名称。code获取设备信息
     *
     * @param equipmentQueryDTO 查询条件
     * @return 设备分页信息
     */
    Page<EquipmentDTO> getEquipmentPage(EquipmentQueryDTO equipmentQueryDTO);


    /**
     * 根据设备id获取设备信息
     *
     * @param id 设备id
     * @return 设备信息
     */
    EquipmentDTO findByEquipmentId(int id);

    /**
     * 根据cellId查询所在区域所有的设备信息
     *
     * @param id cellId
     * @return 设备信息列表
     */
    List<EquipmentDTO> getByCellId(int id);


    /**
     * 添加设备信息
     *
     * @param hvEquipmentDTO 设备
     * @return 设备id
     */
    EquipmentDTO create(EquipmentDTO hvEquipmentDTO);

    /**
     * 更新设备信息
     *
     * @param hvEquipmentDTO 设备信息
     * @return 设备id
     */
    EquipmentDTO updateEquipment(EquipmentDTO hvEquipmentDTO);

    /**
     * 根据设备类型id查询设备信息列表
     *
     * @param id 设备类型id
     * @return 设备信息列表
     */
    List<EquipmentDTO> getEquipmentListByEquipmentTypeId(int id);

    /**
     * 根据设备Code查询设备信息
     *
     * @param equipmentCode 设备Code
     * @return 设备信息
     */
    EquipmentDTO getEquipmentCodeByCode(String equipmentCode);


    /**
     * 添加设备与cell关系
     *
     * @param cellEquipmentDTO 设备与Cell关系对象
     */
    void updateCellEquipmentRelation(CellWithEquipmentDTO cellEquipmentDTO);

    /**
     * 添加设备与cell关系
     *
     * @param cellEquipmentDTO 设备与Cell关系对象
     */
    void updateCellEquipment(CellEquipmentDTO cellEquipmentDTO);

    /**
     * 删除设备与Cell关系
     *
     * @param equipmentId 设备Id
     * @param cellId      产线Id
     */
    void deleteCellEquipmentRelation(int equipmentId, int cellId);

    /**
     * 导出设备信息
     *
     * @return 设备信息excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException 访问异常
     */
    ResponseEntity<byte[]> exportEquipmentLink() throws IOException, IllegalAccessException;

    /**
     * 导出设备信息
     *
     * @param equipmentQueryDTO 导出条件
     * @return 设备信息excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException 访问异常
     */
    ResponseEntity<byte[]> exportEquipmentLink(EquipmentQueryDTO equipmentQueryDTO) throws IOException, IllegalAccessException;


    /**
     * 导出设备信息
     *
     * @return 设备信息excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException 访问异常
     */
    ResultVO<ExcelExportDto> exportEquipment() throws IOException, IllegalAccessException;

    /**
     * 导出设备信息
     *
     * @param equipmentQueryDTO 导出条件
     * @return 设备信息excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException 访问异常
     */
    ResultVO<ExcelExportDto> exportEquipment(EquipmentQueryDTO equipmentQueryDTO) throws IOException, IllegalAccessException;

    /**
     * 根据id列表查询设备信息列表
     *
     * @param idList 设备id列表
     * @return 设备信息列表
     */
    List<EquipmentDTO> findByEquipmentIdList(List<Integer> idList);

    /**
     * 根据设备ID查询所在位置信息（集团，工厂，车间，产线）
     *
     * @param id 设备ID
     * @return 设备所在位置信息
     */
    LocationMsgDTO getLocationMsgDtoByEquipmentId(int id);

    /**
     * 根据ID删除设备信息
     *
     * @param id 设备ID
     */
    void deleteEquipmentById(int id);


    /**
     * 根据产线ID查询产线下所有设备和子设备
     *
     * @param cellId 产线ID
     * @return 设备列表
     */
    List<EquipmentDTO> getAllEquipmentByCellId(int cellId);

    /**
     * 根据产线Id列表查询设备
     *
     * @param cellIdList 产线ID列表
     * @return 设备列表
     */
    List<EquipmentDTO> getAllEquipmentByCellIdList(List<Integer> cellIdList);


    /**
     * 根据查询条件查询列表
     *
     * @param equipmentQueryDTO 设备查询对象
     * @return 查询列表
     */
    List<EquipmentDTO> getEquipmentList(EquipmentQueryDTO equipmentQueryDTO);


    /**
     * 根据父设备id查询子设备列表
     *
     * @param parentId 父设备ID
     * @return 设备列表
     */
    List<EquipmentDTO> findAllChildEquipmentByParentId(Integer parentId);

    /**
     * 复制设备
     *
     * @param equipmentDto 复制源设备id,和新设备信息
     */
    void copy(CopyEquipmentDto equipmentDto);

    /**
     * 根据设备编码查询设备列表
     *
     * @param equipmentCodes 设备编码
     * @return 设备列表
     */
    List<EquipmentDTO> getAllEquipmentsByCodeIn(List<String> equipmentCodes);
    /**
     * 根据CellId查询Cell下所属的设备信息
     *
     * @param ids CellID
     * @return 设备信息列表
     */
    List<EquipmentDTO> getEquipmentListByCellIdList(List<Integer> ids);


    List<HvBmEquipment> getAllEquipmentByLocationId(Integer locationId);
}
















