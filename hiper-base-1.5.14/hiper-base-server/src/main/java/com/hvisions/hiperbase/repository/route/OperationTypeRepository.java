package com.hvisions.hiperbase.repository.route;

import com.hvisions.hiperbase.entity.route.HvBmOperationType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <p>Title: OperationRepository</p>
 * <p>Description: 节点步骤仓储</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/12/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface OperationTypeRepository extends JpaRepository<HvBmOperationType, Integer> {

    /**
     * 查询分页数据
     *
     * @param code     编码
     * @param name     名称
     * @param pageable 分页信息
     * @return 分页数据
     */
    Page<HvBmOperationType> findAllByOperationTypeCodeContainingAndOperationTypeNameContainingAndParentId(String code,
                                                                                                          String name,
                                                                                                          Integer parentId,
                                                                                                          Pageable pageable);


    /**
     * 节点类型编码查询
     *
     * @param operationCode 节点类型编码
     * @return 节点类型
     */
    HvBmOperationType findByOperationTypeCodeEquals(String operationCode);

    /**
     * 父级节点是否存在
     *
     * @param parentId 父级Id
     * @return true false
     */
    boolean existsByParentId(Integer parentId);
}









