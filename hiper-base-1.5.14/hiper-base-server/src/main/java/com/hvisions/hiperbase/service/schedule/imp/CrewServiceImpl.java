package com.hvisions.hiperbase.service.schedule.imp;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.framework.client.AuthClient;
import com.hvisions.framework.dto.user.UserInfoDto;
import com.hvisions.hiperbase.entity.schedule.HvBmCrew;
import com.hvisions.hiperbase.entity.schedule.HvBmCrewMember;
import com.hvisions.hiperbase.entity.schedule.HvBmSchedule;
import com.hvisions.hiperbase.repository.schedule.CrewMemberRepository;
import com.hvisions.hiperbase.repository.schedule.CrewRepository;
import com.hvisions.hiperbase.repository.schedule.ScheduleRepository;
import com.hvisions.hiperbase.schedule.dto.*;
import com.hvisions.hiperbase.schedule.enums.ScheduleExceptionEnum;
import com.hvisions.hiperbase.service.schedule.CrewService;
import com.hvisions.hiperbase.service.schedule.ShiftService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>Title: CrewServiceImp</p>
 * <p>Description: 班组服务实现</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
public class CrewServiceImpl implements CrewService {

    private final CrewRepository crewRepository;
    /**
     * 班组成员
     */
    private final CrewMemberRepository crewMemberRepository;
    private final ScheduleRepository scheduleRepository;
    private final AuthClient authClient;
    private final ShiftService shiftService;


    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
    @Autowired
    public CrewServiceImpl(AuthClient authClient, CrewRepository crewRepository,
                           CrewMemberRepository crewMemberRepository,
                           ScheduleRepository scheduleRepository,
                           ShiftService shiftService) {
        this.crewRepository = crewRepository;
        this.crewMemberRepository = crewMemberRepository;
        this.authClient = authClient;
        this.scheduleRepository = scheduleRepository;
        this.shiftService = shiftService;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int createOrUpdate(CrewDTO crewDTO) {
        if (crewDTO.getAreaId() == 0) {
            crewDTO.setCellId(0);
        }
        //如果传的值为NULL 则修改默认为O 生产
        if (crewDTO.getCrewType() == null) {
            crewDTO.setCrewType(0);
        }
        if (crewDTO.getIsEnable() == null) {
            crewDTO.setIsEnable(true);
        }
        return crewRepository.save(DtoMapper.convert(crewDTO, HvBmCrew.class)).getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int createOrUpdateCrewWithIds(CrewWithMemberIdsDTO crewWithMemberIdsDTO) {
        int id = createOrUpdate(crewWithMemberIdsDTO);
        CrewMember crewMember = new CrewMember();
        crewMember.setCrewId(id);
        crewMember.setUserId(crewWithMemberIdsDTO.getMemberIds());
        update(crewMember);
        return id;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(int crewId) {
        //查询相关排班计划 通过 班组id
        List<HvBmSchedule> schedules = scheduleRepository.findAllByCrewId(crewId);
        if (schedules != null && schedules.size() == 0) {
            //删除班组
            crewRepository.deleteById(crewId);
            //删除班组成员信息
            crewMemberRepository.deleteByCrewId(crewId);
        } else {
            //当前班组已经使用删除失败
            throw new BaseKnownException(ScheduleExceptionEnum.CURRENT_TEAM_HAS_FAILED_TO_USE_DELETE);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<CrewWithMemberDTO> getByAreaIdAndCellId(int areaId, int cellId) {
        //获取所有班组
        List<CrewWithMemberDTO> crewDTOS = DtoMapper.convertList(crewRepository.findAllByAreaIdAndCellId(areaId, cellId), CrewWithMemberDTO.class);
        //循环调用auth人员接口，添加人员信息到dto中
        for (CrewWithMemberDTO crewDTO : crewDTOS) {
            //获取班组人员id列表
            List<HvBmCrewMember> hvBmCrewMembers = crewMemberRepository.findAllByCrewId(crewDTO.getId());
            setMemberInfo(crewDTO, hvBmCrewMembers);
        }
        //根据id进行倒序输出
        return crewDTOS.stream().sorted(Comparator.comparing(CrewWithMemberDTO::getId).reversed()).collect(Collectors.toList());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CrewWithMemberDTO getById(int id) {
        HvBmCrew hvBmCrew = crewRepository.getOne(id);
        CrewWithMemberDTO crewWithMemberDTO = DtoMapper.convert(hvBmCrew, CrewWithMemberDTO.class);
        List<HvBmCrewMember> hvBmCrewMembers = crewMemberRepository.findAllByCrewId(hvBmCrew.getId());
        setMemberInfo(crewWithMemberDTO, hvBmCrewMembers);
        return crewWithMemberDTO;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CrewWithMemberDTO getByCode(String crewCode, Integer areaId, Integer cellId) {
        HvBmCrew crew = crewRepository.findByCrewCodeAndAreaIdAndAndCellId(crewCode, areaId, cellId);
        if (crew != null) {
            return this.getById(crew.getId());
        }
        return null;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<CrewWithMemberDTO> getByCodeList(List<String> crewCodeList, Integer areaId, Integer cellId) {
        List<CrewWithMemberDTO> crewWithMemberDTOS = new ArrayList<>();
        if (crewCodeList != null && crewCodeList.size() > 0) {
            for (String code : crewCodeList) {
                CrewWithMemberDTO byCode = this.getByCode(code, areaId, cellId);
                if (byCode != null) {
                    crewWithMemberDTOS.add(byCode);
                }
            }
        }
        return crewWithMemberDTOS;
    }

    /**
     * 更新班组信息，
     *
     * @param crewWithMemberDTO 需要添加用户信息的班组信息
     * @param hvBmCrewMembers   班组和人员id的对应关系
     */
    private void setMemberInfo(CrewWithMemberDTO crewWithMemberDTO, List<HvBmCrewMember> hvBmCrewMembers) {
        if (hvBmCrewMembers.size() > 0) {
            //调用接口返回人员信息
            ResultVO<List<UserInfoDto>> userInfoDtos = authClient.getUserInfoByIds(hvBmCrewMembers.stream()
                    .map(HvBmCrewMember::getUserId).collect(Collectors.toList()));
            //如果失败报错
            if (userInfoDtos.isSuccess()) {
                crewWithMemberDTO.setUserInfoDtos(userInfoDtos.getData());
            } else {
                throw new BaseKnownException(userInfoDtos);
            }
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(CrewMember crewMember) {
        //获取之前所有的人员信息
        List<HvBmCrewMember> hvBmCrewMemberOld = crewMemberRepository.findAllByCrewId(crewMember.getCrewId());
        //获取之前有的。现在没有的用户ID
        List<HvBmCrewMember> memberInfo = hvBmCrewMemberOld.stream()
                .filter(t -> !crewMember.getUserId().contains(t.getUserId()))
                .collect(Collectors.toList());
        //删除
        if (memberInfo.size() > 0) {
            crewMemberRepository.deleteAll(memberInfo);
        }
        //获取现在有。之前没有的用户id
        List<Integer> userIdList = crewMember.getUserId()
                .stream()
                .filter(t -> hvBmCrewMemberOld.stream()
                        .noneMatch(j -> j.getUserId().equals(t)))
                .collect(Collectors.toList());
        //重新添加人员信息
        List<HvBmCrewMember> hvBmCrewMembersNew = new ArrayList<>();
        for (Integer userId : userIdList) {
            hvBmCrewMembersNew.add(new HvBmCrewMember(crewMember.getCrewId(), userId));
        }
        if (hvBmCrewMembersNew.size() > 0) {
            crewMemberRepository.saveAll(hvBmCrewMembersNew);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<CrewDTO> getCrewByUserId(int userId) {
        List<Integer> crewIds = crewMemberRepository.findAllByUserId(userId).stream()
                .map(HvBmCrewMember::getCrewId)
                .distinct()
                .collect(Collectors.toList());
        return DtoMapper.convertList(crewRepository.findAllById(crewIds), CrewDTO.class);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ShiftCrewQueryResult getCrewShiftByAreaIdAndCellId(int areaId, int cellId) {
        ShiftCrewQueryResult result = new ShiftCrewQueryResult();
        List<CrewWithMemberDTO> crews = getByAreaIdAndCellId(areaId, cellId);
        List<ShiftDTO> shifts = shiftService.getShiftListByAreaIdAndCellId(areaId, cellId);
        //标识为可以查到
        result.setDefaultCell(0);
        //如果查询结果有空,查询产线为空的时候，
        if (crews.size() == 0 || shifts.size() == 0) {
            cellId = 0;
            crews = getByAreaIdAndCellId(areaId, cellId);
            shifts = shiftService.getShiftListByAreaIdAndCellId(areaId, cellId);
            //标识为车间通用排班
            result.setDefaultCell(1);
            //如果还是为空，查询通用班组班次信息
            if (crews.size() == 0 || shifts.size() == 0) {
                areaId = 0;
                crews = getByAreaIdAndCellId(areaId, cellId);
                shifts = shiftService.getShiftListByAreaIdAndCellId(areaId, cellId);
                //标识为总通用排班
                result.setDefaultCell(2);
            }
        }
        result.setAreaId(areaId);
        result.setCellId(cellId);
        result.setCrewDTOS(crews);
        result.setShiftDTOS(shifts);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByCode(String crewCode, Integer areaId, Integer cellId) {
        CrewWithMemberDTO byCode = this.getByCode(crewCode, areaId, cellId);
        if (byCode != null) {
            this.delete(byCode.getId());
        }
    }

    @Override
    public CrewWithMemberDTO getCrewByCrewCode(String crewCode) {
        return DtoMapper.convert(crewRepository.findByCrewCode(crewCode),CrewWithMemberDTO.class);
    }
}
