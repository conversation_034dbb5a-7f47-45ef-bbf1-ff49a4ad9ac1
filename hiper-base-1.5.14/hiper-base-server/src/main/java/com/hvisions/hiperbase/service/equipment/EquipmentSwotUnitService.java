package com.hvisions.hiperbase.service.equipment;

import com.hvisions.hiperbase.equipment.EquipmentSwotUnitDto;

import java.util.List;

/**
 * <p>Title: EquipmentSwotUnit</p >
 * <p>Description: 设备读数单位service</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/9</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
public interface EquipmentSwotUnitService {
    /**
     * 新增设备读数单位
     *
     * @param equipmentSwotUnitDto 设备读数单位dto
     */
    void save(EquipmentSwotUnitDto equipmentSwotUnitDto);

    /**
     * 修改设备读数单位
     *
     * @param equipmentSwotUnitDto 设备读数单位dto
     */
    void update(EquipmentSwotUnitDto equipmentSwotUnitDto);

    /**
     * 删除设备读数单位
     *
     * @param id 设备读数单位id
     */
    void deleteById(Integer id);

    /**
     * 查询设备读数单位
     *
     * @return 设备读数单位dto
     */
    List<EquipmentSwotUnitDto> findAll();
}
