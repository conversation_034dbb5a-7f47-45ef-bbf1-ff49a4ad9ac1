package com.hvisions.hiperbase.configuration;

import com.hvisions.common.dto.ExtendInfoParam;
import com.hvisions.common.service.BaseExtendService;
import com.hvisions.common.utils.SqlFactoryUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <p>Title: ExtendConfig</p>
 * <p>Description: 扩展服务根据配置注入</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/12/24</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Configuration
public class RouteExtendConfig {
    /**
     * @return 获取扩展服务工厂对象
     */
    @Bean
    SqlFactoryUtil getSqlFactory() {
        return new SqlFactoryUtil();
    }
    /**
     * 工艺操作扩展服务
     *
     * @return 服务
     */
    @Bean(value = "operation_extend")
    BaseExtendService getOperationExtendService(SqlFactoryUtil getSqlFactory) {
        ExtendInfoParam extendInfoParam = new ExtendInfoParam();
        extendInfoParam.setOriginTableName("hv_bm_operation");
        extendInfoParam.setOriginTableIdName("operation_id");
        extendInfoParam.setExtendTableName("hv_bm_operation_extend");
        return getSqlFactory.getSqlBridge(extendInfoParam);
    }


    @Bean(value = "operation_type")
    BaseExtendService getOperationTypeExtendService(SqlFactoryUtil getSqlFactory) {
        ExtendInfoParam extendInfoParam = new ExtendInfoParam();
        extendInfoParam.setOriginTableName("hv_bm_operation_type");
        extendInfoParam.setOriginTableIdName("operation_type_id");
        extendInfoParam.setExtendTableName("hv_bm_operation_type_extend");
        return getSqlFactory.getSqlBridge(extendInfoParam);
    }
}









