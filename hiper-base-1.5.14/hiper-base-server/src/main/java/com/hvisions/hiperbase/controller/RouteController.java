package com.hvisions.hiperbase.controller;

import com.hvisions.hiperbase.route.dto.Node;
import com.hvisions.hiperbase.route.dto.NodeParameterData;
import com.hvisions.hiperbase.route.dto.RouteDTO;
import com.hvisions.hiperbase.service.route.RouteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: RouteController</p>
 * <p>Description: 工艺路线控制器</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/12/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 * @see ProductRouteController
 * @deprecated 已经废弃，使用新的控制器
 */
@RestController
@Api(description = "工艺路线控制器")
@RequestMapping("/route")
@Deprecated
public class RouteController {
    private final RouteService routeService;

    @Autowired
    public RouteController(RouteService routeService) {
        this.routeService = routeService;
    }


    /**
     * 获取工艺路线详细信息
     *
     * @param routeId 工艺路线id
     * @return 工艺路线详细信息
     */
    @GetMapping("/getRoute/{routeId}")
    @ApiOperation(value = "获取工艺路线详细信息")
    public RouteDTO getRoute(@PathVariable Integer routeId) {
        return routeService.getRoute(routeId);
    }

    /**
     * 获取工艺路线，包含子工艺路线信息
     *
     * @param id 工艺路线id
     * @return 工艺路线信息
     */

    @GetMapping("/getRouteWithSubRoute/{id}")
    @ApiOperation(value = "获取工艺路线，其中有子工艺路线所有信息")
    public RouteDTO getRouteWithSubRoute(@PathVariable Integer id) {
        return routeService.getRouteWithSubRoute(id);
    }

    /**
     * 根据产品id 列表查询产品工艺信息
     *
     * @param productId 产品id
     * @return ProductionDTO 产品工艺路线关系
     */
    @GetMapping("/getProductRouteDTOsByProductId/{productId}")
    @ApiOperation("根据产品id 查询产品工艺路线关系")
    public List<RouteDTO> getProductRouteDTOsByProductIds(@PathVariable Integer productId) {
        return routeService.getProductRouteDTOsByProductId(productId);

    }

    /**
     * 根据产品id 列表查询工艺路线信息
     *
     * @param productId 产品id
     * @return ProductionDTO 工艺路线
     */
    @GetMapping("/getRouteDTOsByProductId/{productId}")
    @ApiOperation("根据产品id 查询产品工艺路线关系")
    public List<RouteDTO> getRouteDTOsByProductId(@PathVariable Integer productId) {
        return routeService.getRouteDTOsByProductId(productId);

    }


    /**
     * 获取工艺路线详细信息
     *
     * @param productRouteId 工艺路线id
     * @param code           拼接后节点code
     * @return 工艺路线详细信息
     */
    @GetMapping("/getStepParam/{productRouteId}/{code}")
    @ApiOperation(value = "获取工艺路线详细信息")
    public List<NodeParameterData> getStepParam(@PathVariable Integer productRouteId, @PathVariable String code) {
        return routeService.getStepParam(productRouteId, code);
    }


    /**
     * 根据id查询工艺流程key
     *
     * @param id 工艺操作id
     * @return key值列表
     */
    @GetMapping("/getNodeListByRouteIdAndOperationType/{id}/{operationTypeId}")
    @ApiOperation(value = "根据工艺路线id和节点类型查询工艺节点信息")
    public List<Node> getNodeListByRouteIdAndOperationType(@PathVariable int id, @PathVariable int operationTypeId) {
        return routeService.getNodeListByRouteIdAndOperationType(id, operationTypeId);
    }
    /**
     * 查询所有生效工艺路线,不含详细信息
     *
     * @return List数据
     */
    @GetMapping("/getEffectRoute")
    @ApiOperation(value = "查询所有生效和归档工艺路线,不含详细信息")
    public List<RouteDTO> getEffectRoute() {
        return routeService.getEffectRoute();
    }
    /**
     * 升级版本
     *
     * @param id 旧版本id
     * @return 新版本id
     */
    @PostMapping("/levelUp/{id}")
    @ApiOperation(value = "升级参数版本")
    public Integer levelUp(@PathVariable Integer id) {
        return routeService.levelUp(id);
    }

    /**
     * 根据产品id和工艺路线id查询产品工艺以及参数信息
     *
     * @param productId 产品id
     * @param routeId   产品工艺路线id
     * @return 产品工艺信息
     */
    @GetMapping("/getEffectRouteByProductIdAndRouteId/{productId}/{routeId}")
    @ApiOperation("根据产品id和工艺路线id查询产品工艺以及参数信息")
    public RouteDTO getEffectRouteByProductIdAndRouteId(@PathVariable Integer productId, @PathVariable Integer routeId) {
        return routeService.getRoute(routeId);
    }

    /**
     * 根据路线编码，路线版本查询产品工艺信息
     *
     * @param routeCode    工艺编码
     * @param routeVersion 工艺版本
     * @return ProductionDTO 产品工艺路线关系
     */
    @GetMapping("/getProductRouteDTOByRouteCodeAndRouteVersion/{routeCode}/{routeVersion}")
    @ApiOperation("根据路线编码，路线版本查询产品工艺信息")
    public RouteDTO getProductRouteDTOByRouteCodeAndRouteVersion(
            @PathVariable String routeCode, @PathVariable String routeVersion){
        return routeService.getProductRouteDTOByRouteCodeAndRouteVersion(routeCode,routeVersion);
    }

    /**
     * 根据产品id，路线编码，路线版本查询产品工艺信息
     *
     * @param productId    产品id
     * @param routeCode    工艺编码
     * @param routeVersion 工艺版本
     * @return ProductionDTO 产品工艺路线关系
     */
    @GetMapping("/getProductRouteDTOByProductId/{productId}/{routeCode}/{routeVersion}")
    @ApiOperation("根据产品id 查询产品工艺路线关系")
    public RouteDTO getProductRouteDTOByProductId(
            @PathVariable("productId") Integer productId, @PathVariable("routeCode") String routeCode, @PathVariable("routeVersion") String routeVersion){
        return routeService.getProductRouteDTOByProductId(productId,routeCode,routeVersion);
    }

}











