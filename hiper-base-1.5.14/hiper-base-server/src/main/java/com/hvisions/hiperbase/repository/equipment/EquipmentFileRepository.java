package com.hvisions.hiperbase.repository.equipment;

import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentFile;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: HvEquipmentFileRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/2</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface EquipmentFileRepository extends JpaRepository<HvBmEquipmentFile, Integer> {

    /**
     * 根据设备ID查询设备文件
     *
     * @param equipmentId 设备ID
     * @return 设备文件ID列表
     */
    List<HvBmEquipmentFile> findAllByEquipmentId(int equipmentId);

    /**
     * 根据设备ID删除设备文件
     *
     * @param equipmentId 设备ID
     */
    void deleteAllByEquipmentId(Integer equipmentId);
}
