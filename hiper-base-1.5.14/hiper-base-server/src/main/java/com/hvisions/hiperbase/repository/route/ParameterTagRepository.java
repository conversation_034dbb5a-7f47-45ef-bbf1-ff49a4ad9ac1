package com.hvisions.hiperbase.repository.route;

import com.hvisions.hiperbase.entity.route.HvBmParameterTag;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <p>Title: ParameterGroupRepository</p>
 * <p>Description: 标签仓储</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/11/17</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface ParameterTagRepository extends JpaRepository<HvBmParameterTag, Integer> {
    /**
     * 分页
     *
     * @param code     编码
     * @param pageable 分页信息
     * @return 分页数据
     */
    Page<HvBmParameterTag> findAllByCodeContains(String code, Pageable pageable);


    /**
     * 根据编码定位分组
     *
     * @param code 分组编码
     * @return 分组
     */
    HvBmParameterTag findByCode(String code);
}

    
    
    
    