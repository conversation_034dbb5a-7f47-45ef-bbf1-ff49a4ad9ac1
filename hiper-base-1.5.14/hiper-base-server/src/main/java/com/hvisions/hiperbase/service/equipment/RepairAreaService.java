package com.hvisions.hiperbase.service.equipment;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.equipment.RepairAreaDTO;
import com.hvisions.hiperbase.equipment.RepairAreaQueryDTO;
import com.hvisions.hiperbase.equipment.location.LocationDTO;
import com.hvisions.thirdparty.common.dto.WeldLineRepairDTO;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

public interface RepairAreaService {

    /**
     * 分页查询
     * @param repairAreaQueryDTO 查询条件
     * @return
     */
    Page<RepairAreaDTO> getPage(RepairAreaQueryDTO repairAreaQueryDTO);

    /**
     * 添加返修区
     * @param repairAreaDTO
     * @return
     */
    int createRepairArea(RepairAreaDTO repairAreaDTO);

    /**
     * 修改返修区
     * @param repairAreaDTO
     * @return
     */
    int updateRepairArea(RepairAreaDTO repairAreaDTO);

    /**
     * 删除返修区
     * @param id
     */
    void deleteRepairAreaById(int id);

    /**
     * 根据areaCode判断返修区是否存在
     * @param areaCode
     * @return
     */
    boolean isExistsRepairAreaByAreaCode(String areaCode);

    /**
     * 根据lineId获取返修区域
     * @param lineId
     * @return
     */
    RepairAreaDTO getRepairAreaByLineId(int lineId);

    /**
     * 获取未被使用的产线列表
     * @return
     */
    List<LocationDTO> getNotUsedLine();

    /**
     * 导出所有返修区域信息
     * @return 返修区域信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    ResultVO<ExcelExportDto> exportRepairArea() throws IOException, IllegalAccessException;


    /**
     * 导出所有返修区域信息
     * @return 返修区域信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    ResponseEntity<byte[]> export() throws IOException, IllegalAccessException;

    /**
     * 获取导入模板
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    ResultVO<ExcelExportDto> getRepairAreaImportTemplate() throws IOException, IllegalAccessException;

    /**
     * 导入返修区域信息
     *
     * @param file bom信息文档
     * @return 返回信息
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    ImportResult importRepairAreas(MultipartFile file) throws IllegalAccessException, ParseException, IOException;

    /**
     * 返修调度
     * @param weldLineRepairDTO
     * @return
     */
    RepairAreaDTO repairDispatch(WeldLineRepairDTO weldLineRepairDTO);
}
