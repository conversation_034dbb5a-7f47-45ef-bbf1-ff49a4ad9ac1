package com.hvisions.hiperbase.repository.material;

import com.hvisions.hiperbase.entity.material.HvBmBomItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: BomItemRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/11/27</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface BomItemRepository extends JpaRepository<HvBmBomItem, Integer> {

    /**
     * 根据bomId查询信息
     *
     * @param bomId bomId
     * @return 信息列表
     */
    List<HvBmBomItem> findAllByBomId(int bomId);

    /**
     * 通过ID获取数据
     *
     * @param id id
     * @return bomItem信息
     */
    HvBmBomItem getById(int id);

    /***
     * 判断materialId是否存在
     * @param materialsId materialId
     * @return true or false
     */
    boolean existsByMaterialsId(Integer materialsId);

    /***
     * 判断bomItemCode是否存在
     * @param bomItemCode bomItemCode
     * @return true or false
     */
    boolean existsByBomItemCode(String bomItemCode);

    /**
     * 根据bOMId删除BomItem
     *
     * @param bomId bomId
     */
    void deleteAllByBomId(Integer bomId);

    /**
     * 根据bomId查询信息
     *
     * @param bomId bomId
     * @return 信息列表
     */
    List<HvBmBomItem> getByBomId(Integer bomId);

    /**
     * 根据BOMid列表查询信息
     *
     * @param bomIdIn bomId列表
     * @return bomItem信息
     */
    List<HvBmBomItem> getAllByBomIdIn(List<Integer> bomIdIn);

    /**
     * 根据bomItemCode查询信息
     *
     * @param bomItemCode bomItemCode
     * @return 信息列表
     */
    HvBmBomItem getByBomItemCode(String bomItemCode);

    /**
     * 根据bomItemCode查询信息
     *
     * @param bomItemCode bomItemCode
     * @return 信息列表
     */
    HvBmBomItem getAllByBomItemCodeAndBomId(String bomItemCode, Integer bomId);

    /**
     * 根据bomId和MaterialId查询bomItem
     *
     * @param bomId      bomId
     * @param materialId 物料ID
     * @return bomItem信息
     */
    HvBmBomItem getByBomIdAndMaterialsId(Integer bomId, Integer materialId);


    /**
     * 根据bomId是否可以找到bomItem信息
     * @param bomId bomId
     */
    Boolean existsByBomId(int bomId);
}
