<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.hiperbase.dao.equipment.EquipmentSparePartMapper">
    <select id="findAllByQuery" resultType="com.hvisions.hiperbase.equipment.EquipmentSparePartDto" databaseId="mysql">
        SELECT es.id,
               es.equipment_id,
               es.number,
               es.spare_part_id,
               s.spare_code as spare_part_code,
               s.spare_name as spare_part_name
        FROM hv_bm_equipment_spare_part AS es
                     LEFT JOIN eam.hv_eam_spare AS s ON es.spare_part_id = s.id
        WHERE es.equipment_id = #{query.equipmentId}
    </select>

    <select id="findAllByQuery" resultType="com.hvisions.hiperbase.equipment.EquipmentSparePartDto" databaseId="sqlserver">
        SELECT es.id,
               es.equipment_id,
               es.number,
               es.spare_part_id,
               s.spare_code as spare_part_code,
               s.spare_name as spare_part_name
        FROM hv_bm_equipment_spare_part AS es
                     LEFT JOIN eam.dbo.hv_eam_spare AS s ON es.spare_part_id = s.id
        WHERE es.equipment_id = #{query.equipmentId}
    </select>
</mapper>