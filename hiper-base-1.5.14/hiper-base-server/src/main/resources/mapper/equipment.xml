<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.hiperbase.dao.equipment.EquipmentMapper">
    <!--mysql-->
    <select id="findEquipmentById" resultType="com.hvisions.hiperbase.equipment.EquipmentDTO" databaseId="mysql">
        SELECT e.id,
               e.equipment_code,
               e.equipment_name,
               e.equipment_type_id,
               t.equipment_type_code,
               t.equipment_type_name,
               p.purchase_price,
               e.photo_id,
               e.responser_id,
               u.user_name       as responser_name,
               e.response_part_id,
               d.department_name as response_part_name,
               e.equipment_status_id,
               s.type            as equipment_status_type,
               s.status_name     as equipment_status_name,
               e.parent_id,
               e.parent_type,
               e.create_time,
               e.update_time,
               e.remark
        FROM hv_bm_equipment as e
                 LEFT JOIN hv_bm_equipment_type as t on e.equipment_type_id = t.id
                 LEFT JOIN hv_bm_equipment_purchase as p on e.id = p.equipment_id
                 LEFT JOIN framework.sys_user as u on e.responser_id = u.id
                 LEFT JOIN framework.sys_department as d on e.response_part_id = d.id
                 LEFT JOIN hv_bm_equipment_status as s on e.equipment_status_id = s.id
        where e.id = #{id}
    </select>
    <select id="findEquipmentByQuery" resultType="com.hvisions.hiperbase.equipment.EquipmentDTO"
            databaseId="mysql">
        SELECT e.id,
        e.equipment_code,
        e.equipment_name,
        e.equipment_type_id,
        t.equipment_type_name,
        t.equipment_type_code as equipment_type_code,
        p.purchase_price,
        e.photo_id,
        e.responser_id,
        u.user_name as responser_name,
        e.response_part_id,
        d.department_name as response_part_name,
        e.equipment_status_id,
        s.type as equipment_status_type,
        s.status_name as equipment_status_name,
        ec.order_num ,
        e.parent_id,
        e.parent_type,
        e.create_time,
        e.update_time,
        e.remark
        FROM hv_bm_equipment as e
        LEFT JOIN hv_bm_equipment_type as t on e.equipment_type_id = t.id
        LEFT JOIN hv_bm_equipment_purchase as p on e.id = p.equipment_id
        LEFT JOIN framework.sys_user as u on e.responser_id = u.id
        LEFT JOIN framework.sys_department as d on e.response_part_id = d.id
        LEFT JOIN hv_bm_equipment_status as s on e.equipment_status_id = s.id
        LEFT JOIN hv_bm_equipment_cell as ec on e.id = ec.equipment_id
        <where>
            <if test="dto.equipmentId != null">
                and e.id = #{dto.equipmentId}
            </if>
            <if test="dto.equipmentCode != null and dto.equipmentCode != ''">
                and e.equipment_code like concat('%', #{dto.equipmentCode}, '%')
            </if>
            <if test="dto.equipmentIdList !=null and dto.equipmentIdList.size() > 0">
                and e.id in
                <foreach collection="dto.equipmentIdList" item="item" separator="," index="index" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.equipmentName != null and dto.equipmentName != ''">
                and e.equipment_name like concat('%', #{dto.equipmentName}, '%')
            </if>
            <if test="dto.equipmentCodeList != null and dto.equipmentCodeList.size() > 0">
                and e.equipment_code in
                <foreach collection="dto.equipmentCodeList" item="item" separator="," index="index" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.keyword != null and dto.keyword != &quot;&quot;">
                (e.equipment_name like concat('%', #{dto.keyword}
                , '%')
                or
                e.equipment_code like concat('%'
                , #{dto.keyword}
                , '%'))
            </if>
            <if test="dto.equipmentTypeIds != null and dto.equipmentTypeIds.size() > 0">
                and e.equipment_type_id in
                <foreach collection="dto.equipmentTypeIds" item="equipmentTypeId" separator="," index="index" open="("
                         close=")">
                    #{equipmentTypeId}
                </foreach>
            </if>
<!--            <if test="dto.directLocationId != null">-->
<!--                and exists(-->
<!--                select 1 from hv_bm_equipment_cell t3 where t3.equipment_id = e.id-->
<!--                and t3.cell_id = #{dto.directLocationId}-->
<!--                )-->
<!--            </if>-->
            <if test="dto.directLocationId != null">
                and e.parent_id = #{dto.directLocationId}
            </if>
            <if test="dto.extend != null and dto.extend.size() > 0">
                <foreach collection="dto.extend.entrySet()" item="item" index="index">
                    and ${index} == #{item}
                </foreach>
            </if>
            <if test="dto.filterIds != null and dto.filterIds.size() > 0">
                and e.id not in
                <foreach collection="dto.filterIds" open="(" close=")" separator="," index="index" item="equipmentId">
                    #{equipmentId}
                </foreach>
            </if>
        </where>
    </select>
    <select id="findEquipmentExcelDto" resultType="com.hvisions.hiperbase.equipment.EquipmentExcelDto"
            databaseId="mysql">
        SELECT e.id,
        e.equipment_code,
        e.equipment_name,
        e.parent_type,
        e.parent_id,
        t.equipment_type_code,
        u.user_account AS responser_account,
        d.department_code AS response_part_code
        FROM hv_bm_equipment as e
        LEFT JOIN hv_bm_equipment_type as t on e.equipment_type_id = t.id
        LEFT JOIN framework.sys_user_login as u on e.responser_id = u.user_id
        LEFT JOIN framework.sys_department as d on e.response_part_id = d.id
        <where>
            <if test="dto.keyword != null and dto.keyword != &quot;&quot;">
                (e.equipment_name like concat('%', #{dto.keyword}, '%') or
                e.equipment_code like concat('%', #{dto.keyword}, '%'))
            </if>
            <if test="dto.equipmentName != null and dto.equipmentName != &quot;&quot;">
                and e.equipment_name like concat('%', #{dto.equipmentName}, '%')
            </if>
            <if test="dto.equipmentCode != null and dto.equipmentCode != &quot;&quot;">
                and e.equipment_code like concat('%', #{dto.equipmentCode}, '%')
            </if>
            <if test="dto.equipmentTypeId != null">
                and e.equipment_type_id = #{dto.equipmentTypeId}
            </if>
            <if test="dto.locationIds != null and dto.locationIds.size() > 0">
                and exists ( select 1
                from hv_bm_equipment_cell t1 where t1.equipment_id = e.id
                and t1.cell_id in
                <foreach collection="dto.locationIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="dto.extend != null and dto.extend.size() > 0">
                <foreach collection="dto.extend.entrySet()" item="item" index="index">
                    and ${index} == #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findUserIdByAccount" resultType="java.lang.Integer" databaseId="mysql">
        select user_id
        from framework.sys_user_login
        where user_account = #{account}
    </select>

    <select id="findUserNameByUserId" resultType="java.lang.String" databaseId="mysql">
        select user_name
        from framework.sys_user
        where id = #{userId}
    </select>
    <select id="findDepartmentIdByCode" resultType="java.lang.Integer" databaseId="mysql">
        select id
        from framework.sys_department
        where department_code = #{code}
    </select>

    <!--sqlserver-->
    <select id="findEquipmentById" resultType="com.hvisions.hiperbase.equipment.EquipmentDTO"
            databaseId="sqlserver">
        SELECT e.id,
               e.equipment_code,
               e.equipment_name,
               e.equipment_type_id,
               t.equipment_type_code,
               t.equipment_type_name,
               p.purchase_price,
               e.photo_id,
               e.responser_id,
               u.user_name       as responser_name,
               e.response_part_id,
               d.department_name as response_part_name,
               e.equipment_status_id,
               s.type            as equipment_status_type,
               s.status_name     as equipment_status_name,
               e.parent_id,
               e.parent_type,
               e.create_time,
               e.update_time,
               e.remark
        FROM hv_bm_equipment as e
                 LEFT JOIN hv_bm_equipment_type as t on e.equipment_type_id = t.id
                 LEFT JOIN hv_bm_equipment_purchase as p on e.id = p.equipment_id
                 LEFT JOIN framework.dbo.sys_user as u on e.responser_id = u.id
                 LEFT JOIN framework.dbo.sys_department as d on e.response_part_id = d.id
                 LEFT JOIN hv_bm_equipment_status as s on e.equipment_status_id = s.id
        where e.id = #{id}
    </select>
    <select id="findEquipmentByQuery" resultType="com.hvisions.hiperbase.equipment.EquipmentDTO"
            databaseId="sqlserver">
        SELECT e.id,
        e.equipment_code,
        e.equipment_name,
        e.equipment_type_id,
        t.equipment_type_name,
        t.equipment_type_code as equipment_type_code,
        p.purchase_price,
        e.photo_id,
        e.responser_id,
        u.user_name as responser_name,
        e.response_part_id,
        d.department_name as response_part_name,
        e.equipment_status_id,
        s.type as equipment_status_type,
        s.status_name as equipment_status_name,
        e.parent_id,
        e.parent_type,
        e.create_time,
        e.update_time,
        e.remark
        FROM hv_bm_equipment as e
        LEFT JOIN hv_bm_equipment_type as t on e.equipment_type_id = t.id
        LEFT JOIN hv_bm_equipment_purchase as p on e.id = p.equipment_id
        LEFT JOIN framework.dbo.sys_user as u on e.responser_id = u.id
        LEFT JOIN framework.dbo.sys_department as d on e.response_part_id = d.id
        LEFT JOIN hv_bm_equipment_status as s on e.equipment_status_id = s.id
        <where>
            <if test="dto.equipmentCode != null and dto.equipmentCode != ''">
                e.equipment_code like concat('%', #{dto.equipmentCode}, '%')
            </if>
            <if test="dto.equipmentName != null and dto.equipmentName != ''">
                and e.equipment_name like concat('%', #{dto.equipmentName}, '%')
            </if>
            <if test="dto.keyword != null and dto.keyword != &quot;&quot;">
                (e.equipment_name like concat('%', #{dto.keyword}
                , '%')
                or
                e.equipment_code like concat('%'
                , #{dto.keyword}
                , '%'))
            </if>
            <if test="dto.equipmentTypeIds != null and dto.equipmentTypeIds.size() > 0">
                and e.equipment_type_id in
                <foreach collection="dto.equipmentTypeIds" item="equipmentTypeId" separator="," index="index" open="("
                         close=")">
                    #{equipmentTypeId}
                </foreach>
            </if>
            <if test="dto.locationIds != null and dto.locationIds.size() > 0">
                and exists (
                select 1
                from hv_bm_equipment_cell t1 where t1.equipment_id = e.id
                and t1.cell_id in
                <foreach collection="dto.locationIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="dto.extend != null and dto.extend.size() > 0">
                <foreach collection="dto.extend.entrySet()" item="item" index="index">
                    and ${index} == #{item}
                </foreach>
            </if>
            <if test="dto.filterIds != null and dto.filterIds.size() > 0">
                and e.id not in
                <foreach collection="dto.filterIds" open="(" close=")" separator="," index="index" item="equipmentId">
                    #{equipmentId}
                </foreach>
            </if>
        </where>
    </select>
    <select id="findEquipmentExcelDto" resultType="com.hvisions.hiperbase.equipment.EquipmentExcelDto"
            databaseId="sqlserver">
        SELECT e.id,
        e.equipment_code,
        e.equipment_name,
        e.parent_type,
        e.parent_id,
        t.equipment_type_code,
        u.user_account AS responser_account,
        d.department_code AS response_part_code
        FROM hv_bm_equipment as e
        LEFT JOIN hv_bm_equipment_type as t on e.equipment_type_id = t.id
        LEFT JOIN framework.dbo.sys_user_login as u on e.responser_id = u.user_id
        LEFT JOIN framework.dbo.sys_department as d on e.response_part_id = d.id
        <where>
            <if test="dto.keyword != null and dto.keyword != &quot;&quot;">
                (e.equipment_name like concat('%', #{dto.keyword}, '%') or
                e.equipment_code like concat('%', #{dto.keyword}, '%'))
            </if>
            <if test="dto.equipmentName != null and dto.equipmentName != &quot;&quot;">
                and e.equipment_name like concat('%', #{dto.equipmentName}, '%')
            </if>
            <if test="dto.equipmentCode != null and dto.equipmentCode != &quot;&quot;">
                and e.equipment_code like concat('%', #{dto.equipmentCode}, '%')
            </if>
            <if test="dto.equipmentTypeId != null">
                and e.equipment_type_id = #{dto.equipmentTypeId}
            </if>
            <if test="dto.locationIds != null and dto.locationIds.size() > 0">
                and exists ( select 1
                from hv_bm_equipment_cell t1 where t1.equipment_id = e.id
                and t1.cell_id in
                <foreach collection="dto.locationIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="dto.extend != null and dto.extend.size() > 0">
                <foreach collection="dto.extend.entrySet()" item="item" index="index">
                    and ${index} == #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findUserIdByAccount" resultType="java.lang.Integer" databaseId="sqlserver">
        select user_id
        from framework.dbo.sys_user_login
        where user_account = #{account}
    </select>

    <select id="findUserNameByUserId" resultType="java.lang.String" databaseId="sqlserver">
        select user_name
        from framework.dbo.sys_user
        where id = #{userId}
    </select>
    <select id="findDepartmentIdByCode" resultType="java.lang.Integer" databaseId="sqlserver">
        select id
        from framework.dbo.sys_department
        where department_code = #{code}
    </select>
</mapper>