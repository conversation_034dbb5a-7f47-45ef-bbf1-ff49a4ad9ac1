<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.hiperbase.dao.material.MaterialMapper">
    <resultMap id="material" type="com.hvisions.hiperbase.materials.dto.MaterialDTO">
        <result property="uomName" column="description"/>
        <result property="materialGroupCode" column="group_code"/>
        <result property="materialGroupName" column="group_name"/>
        <result property="bomVersion" column="bom_versions"/>
    </resultMap>

    <select id="getMaterialByQuery" resultMap="material"
            parameterType="com.hvisions.hiperbase.materials.dto.MaterialQueryDTO">
        select m.*,
        t.material_type_code,
        t.material_type_name,
        g.group_code,
        g.group_name,
        b.id as bomId,
        b.bom_code,
        b.bom_name,
        b.bom_versions,
        u.symbol,
        u.description
        from hv_bm_material m
        left join hv_bm_material_extend e on m.id = e.material_id
        left join hv_bm_material_type t on m.material_type = t.id
        left join hv_bm_material_group g on m.material_group = g.id
        left join hv_bm_bom_material bm on m.id = bm.material_id
        left join hv_bm_bom b on bm.bom_id = b.id
        left join hv_bm_unit u on m.uom = u.id
        <where>
            <if test="dto.materialCode != null">
                and m.material_code like concat('%', #{dto.materialCode}, '%')
            </if>
            <if test="dto.materialName != null">
                and m.material_name like concat('%', #{dto.materialName}, '%')
            </if>
            <if test="dto.materialTypeIds != null and dto.materialTypeIds.size() > 0">
                and m.material_type in
                <foreach collection="dto.materialTypeIds" open="(" close=")" separator="," item="materialTypeId"
                         index="index">
                    #{materialTypeId}
                </foreach>
            </if>
            <if test="dto.materialGroup != null">
                and m.material_group = #{dto.materialGroup}
            </if>

            <if test="dto.keyWord != null">
                and m.material_code like concat('%', #{dto.keyWord}, '%')
                or m.material_name like
                concat('%', #{dto.keyWord}, '%')
                or m.eigenvalue like concat('%', #{dto.keyWord}, '%')
            </if>
            <if test="dto.extend != null and dto.extend.size() > 0">
                <foreach collection="dto.extend.entrySet()" item="item" index="index">
                    and ${index} = #{item}
                </foreach>
            </if>
        </where>
    </select>

    <resultMap id="rm" type="com.hvisions.hiperbase.materials.dto.MaterialDTO">
    </resultMap>

    <select resultMap="rm" id="getMaterials" parameterType="com.hvisions.hiperbase.materials.dto.QueryDTO">
        SELECT t1.id,
        t1.special_purchase_type_code,
        t1.create_time,
        t1.update_time,
        t1.creator_id,
        t1.updater_id,
        t1.material_code,
        t1.material_desc,
        t1.material_name,
        t1.eigenvalue,
        t1.material_group,
        t1.material_type,
        t1.photo_id,
        t1.uom,
        t1.serial_number_profile,
        t2.description uomName,
        t3.material_type_code,
        t3.material_type_name,
        t4.group_code materialGroupCode,
        t4.group_name materialGroupName,
        t6.id bomId,
        t6.bom_code,
        t6.bom_desc,
        t6.bom_count,
        t6.bom_name,
        t6.bom_versions bomVersion,
        t1.specs,
        t1.weight,weight,
        t1.quality,
        t1.out_going,
        t1.m_width,
        t1.m_length,
        he.height
        FROM hv_bm_material t1
        LEFT JOIN hv_bm_unit t2 ON t1.uom = t2.id
        LEFT JOIN hv_bm_material_type t3 ON t1.material_type = t3.id
        LEFT JOIN hv_bm_material_group t4 ON t1.material_group = t4.id
        LEFT JOIN hv_bm_bom_material t5 ON t1.id = t5.material_id
        LEFT JOIN hv_bm_bom t6 ON t6.id = t5.bom_id
        LEFT JOIN hv_bm_material_extend he ON he.material_id = t1.id
        <where>
            <if test="query.id != null">
                AND t1.id = #{query.id}
            </if>
            <if test="query.keyWord != null and query.keyWord != ''">
                AND (t1.material_code like concat('%', #{query.keyWord}, '%')
                OR t1.material_name like concat('%', #{query.keyWord}, '%')
                OR t1.eigenvalue like concat('%', #{query.keyWord}, '%'))
            </if>
            <if test="query.materialCode != null and query.materialCode != ''">
                AND t1.material_code like concat('%', #{query.materialCode}, '%')
            </if>
            <if test="query.materialName != null and query.materialName != ''">
                AND t1.material_name like concat('%', #{query.materialName}, '%')
            </if>
            <if test="query.materialGroup != null">
                AND t4.id = #{query.materialGroup}
            </if>
            <if test="query.hasBom != null and query.hasBom == true">
                AND t6.id IS NOT NULL
            </if>
            <if test="query.hasBom != null and query.hasBom == false">
                AND t6.id IS NULL
            </if>
            <if test="query.materialTypeIds != null and query.materialTypeIds.size > 0">
                <foreach collection="query.materialTypeIds" index="index" item="item" open="and t3.id in("
                         separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.materialTypes != null and query.materialTypes.size > 0">
                <foreach collection="query.materialTypes" index="index" item="item" open="and t3.material_type_code in("
                         separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.materialIdList != null and query.materialIdList.size > 0">
                <foreach collection="query.materialIdList" index="index" item="item" open="and t1.id in("
                         separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.materialSegmentation != null and query.materialSegmentation != ''">
                and t1.material_code like concat(#{query.materialSegmentation},'%')
            </if>
        </where>
    </select>


    <select id="getByMaterialCode" resultType="com.hvisions.hiperbase.materials.dto.MaterialDTO">
        select
        id,
        material_name materialName,special_purchase_type_code
        from hv_bm_material
        where material_code = #{materialCode} and eigenvalue = 1
    </select>

    <select id="getMaterialIdByMaterialGroupId" resultType="java.lang.Integer">
        select
        id
        from hv_bm_material
        where material_group = #{materialGroupId}
    </select>



    <select id="getMaterialByCode" resultType="com.hvisions.hiperbase.materials.dto.MaterialDTO">
        SELECT id,
               create_time,
               update_time,
               creator_id,
               updater_id,
               material_code,
               material_desc,
               material_name,
               eigenvalue,
               material_group,
               material_type,
               photo_id,
               uom,
               serial_number_profile,
               special_purchase_type_code
        FROM hv_bm_material material_group
        where material_code = #{materialCode}
    </select>

    <select id="getMaterialListByMaterialCodes" resultType="com.hvisions.hiperbase.materials.dto.MaterialDTO">
        SELECT id,
               material_code as materialCode,
               material_name as materialName,
               special_purchase_type_code as specialPurchaseTypeCode,
               weight
        FROM hv_bm_material
        where material_code in
        <foreach collection="materialCodes" open="(" close=")" separator="," item="materialCode">
            #{materialCode}
        </foreach>
    </select>
    <select id="getOneByMaterialCode" resultType="com.hvisions.hiperbase.materials.dto.MaterialInfoDTO">
        SELECT
               material_code as materialCode,
               material_name as materialName,
               eigenvalue,
               specs,
               weight
        from hv_bm_material
        where material_code = #{materialCode}
    </select>
    <select id="getAllMaterialAndExtendInfo" resultType="com.hvisions.thirdparty.common.dto.MesMaterialDTO">
        SELECT
            e.ship_type AS shipType,-- 船型
            e.stage,-- 阶段
            e.block,-- 分段
            m.material_code AS materialsCode,-- 物料编码
            m.material_name AS materialsName,-- 物料名称
            t.material_type_code AS materialsType,-- 物料类型
            g.group_code AS pt,-- 零件类型
            m.out_going AS outFlag,-- 是否外发
            e.quantity,-- 数量
            m.special_purchase_type_code AS specialPurchaseTypeCode,-- 自制/外协(0:自制,1:外协)
            e.gps2,-- 加工代码
            e.gps3,-- 其他信息
            e.gps4,-- 部材号
            e.nested_on AS nestedOn,-- 套料图号
            e.ship,-- 船舶编号
            e.Area AS area,-- 面积
            m.m_length AS length,-- 长度
            m.m_width AS width,-- 宽度
            e.height,-- 高度
            e.dm,-- 尺寸(型板)
            m.weight,-- 重量
            m.quality AS textureOfMaterial,-- 材质
            m.specs AS specifications,-- 规格
            e.parent_code AS parentCode,-- 父级
            e.block_code AS blockCode,-- 组立代码/流向代码
            m.section_code AS sectionCode -- 型材截面代码
        FROM
            hv_bm_material m
                LEFT JOIN hv_bm_material_extend e ON m.id = e.material_id
                LEFT JOIN hv_bm_material_type t ON m.material_type = t.id
                LEFT JOIN hv_bm_material_group g ON m.material_group = g.id
        WHERE
            e.deleteFlag != 1 OR e.deleteFlag IS NULL
    </select>

</mapper>