<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.hiperbase.dao.equipment.EquipmentWarrantyMapper">
    <select id="findWarentysByEquipmentId" resultType="com.hvisions.hiperbase.equipment.EquipmentWarrantyDto" databaseId="mysql">
        SELECT w.id,
               w.equipment_id,
               w.warranty_type,
               w.type,
               w.expire_date,
               w.cert_no,
               w.description,
               w.equipment_swot,
               w.swot_unit_id,
               u.unit_name as swot_unit_name,
               w.supplier_id,
               s.supplier_name,
               w.create_time
        FROM hv_bm_equipment_warranty AS w
                     LEFT JOIN hv_bm_equipment_swot_unit AS u ON w.swot_unit_id = u.id
                     LEFT JOIN framework.sys_supplier as s on w.supplier_id = s.id
        where w.equipment_id = #{equipmentId}
    </select>
    <select id="findWarentysByEquipmentId" resultType="com.hvisions.hiperbase.equipment.EquipmentWarrantyDto" databaseId="sqlserver">
        SELECT w.id,
               w.equipment_id,
               w.warranty_type,
               w.type,
               w.expire_date,
               w.cert_no,
               w.description,
               w.equipment_swot,
               w.swot_unit_id,
               u.unit_name as swot_unit_name,
               w.supplier_id,
               s.supplier_name,
               w.create_time
        FROM hv_bm_equipment_warranty AS w
                     LEFT JOIN hv_bm_equipment_swot_unit AS u ON w.swot_unit_id = u.id
                     LEFT JOIN framework.dbo.sys_supplier as s on w.supplier_id = s.id
        where w.equipment_id = #{equipmentId}
    </select>
</mapper>