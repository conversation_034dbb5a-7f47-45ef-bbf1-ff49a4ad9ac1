<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hvisions.hiperbase.dao.transportTool.TransportToolMapper">

    <!-- 1. 公共查询字段 -->
    <sql id="queryTransportTool">
        t.id,
    t.transport_tool_code      transportToolCode,
    t.transport_tool_desc      transportToolDesc,
    t.transport_tool_type_code transportToolTypeCode,
    t.transport_tool_specs     transportToolSpecs,
    t.is_auto_device           isAutoDevice,
    t.create_time              createTime,
    t.update_time              updateTime,
    t.creator_id               creatorId,
    t.updater_id                updaterId,
    t.site_num                 siteNum
    </sql>

    <sql id="queryTransportToolStatus">
        t.id,
    t.transport_tool_code      transportToolCode,
    t.transport_tool_type_code transportToolTypeCode,
    t.work_status            workStatus,
    t.current_location      currentLocation,
    t.ip_address            ipAddress,
    t.battery               battery,
    t.create_time              createTime,
    t.update_time              updateTime,
    t.creator_id               creatorId,
    t.updater_id                updaterId
    </sql>

    <!-- 2. 查询列表 -->
    <select id="findListByCondition"
            resultType="com.hvisions.hiperbase.entity.transportTool.HvBmTransportTool">
        SELECT
        <include refid="queryTransportTool"/>
        FROM
        `hv_bm_transport_tool` t
        <where>
            <if test="hvBmTransportTool.transportToolCode != null and hvBmTransportTool.transportToolCode != ''">
                AND t.transport_tool_code = #{hvBmTransportTool.transportToolCode}
            </if>
            <if test="hvBmTransportTool.transportToolDesc != null and hvBmTransportTool.transportToolDesc != ''">
                AND t.transport_tool_desc LIKE CONCAT('%', #{hvBmTransportTool.transportToolDesc}, '%')
            </if>
            <if test="hvBmTransportTool.transportToolTypeCode != null and hvBmTransportTool.transportToolTypeCode != ''">
                AND t.transport_tool_type_code = #{hvBmTransportTool.transportToolTypeCode}
            </if>
            <if test="hvBmTransportTool.transportToolSpecs != null and hvBmTransportTool.transportToolSpecs != ''">
                AND t.transport_tool_specs LIKE CONCAT('%', #{hvBmTransportTool.transportToolSpecs}, '%')
            </if>
            <if test="hvBmTransportTool.isAutoDevice != null">
                AND t.is_auto_device = #{hvBmTransportTool.isAutoDevice}
            </if>
        </where>
    </select>



    <select id="getPage" resultType="com.hvisions.hiperbase.transportTool.TransportToolDTO">
        select
            <include refid="queryTransportTool"/>
        from
            `hv_bm_transport_tool` t
        <where>
            <if test="query.transportToolCode != null and query.transportToolCode != ''">
                AND t.transport_tool_code LIKE CONCAT('%',#{query.transportToolCode} , '%')
            </if>
            <if test="query.transportToolDesc != null and query.transportToolDesc != ''">
                AND t.transport_tool_desc LIKE CONCAT('%', #{query.transportToolDesc}, '%')
            </if>
            <if test="query.transportToolTypeCode != null and query.transportToolTypeCode != ''">
                AND t.transport_tool_type_code LIKE CONCAT('%', #{query.transportToolTypeCode}, '%')
            </if>
            <if test="query.transportToolSpecs != null and query.transportToolSpecs != ''">
                AND t.transport_tool_specs LIKE CONCAT('%', #{query.transportToolSpecs}, '%')
            </if>
            <if test="query.isAutoDevice != null">
                AND t.is_auto_device = #{query.isAutoDevice}
            </if>
        </where>
    </select>
    <select id="getStatusPage" resultType="com.hvisions.hiperbase.transportTool.TransportToolStatusDTO">
        SELECT
            id,
            transport_tool_code,
            transport_tool_type_code,
            work_status,
            current_location,
            ip_address,
            battery
        FROM
            hv_bm_transport_tool
        <where>
            <if test="query.transportToolCode != null and query.transportToolCode != ''">
                AND transport_tool_code like concat('%',#{query.transportToolCode},'%')
            </if>
            <if test="query.transportToolTypeCode != null and query.transportToolTypeCode != ''">
                AND transport_tool_type_code like concat('%',#{query.transportToolTypeCode},'%')
            </if>
            <if test="query.workStatus != null">
                AND work_status = #{query.workStatus}
            </if>
            <if test="query.currentLocation != null and query.currentLocation != ''">
                AND current_location like concat('%',#{query.currentLocation},'%')
            </if>
            <if test="query.ipAddress != null and query.ipAddress != ''">
                AND ip_address like concat('%',#{query.ipAddress},'%')
            </if>
            <if test="query.battery != null and query.battery != ''">
                AND battery like concat('%',#{query.battery},'%')
            </if>

        </where>
    </select>

    <select id="findStatusListByCondition"
            resultType="com.hvisions.hiperbase.transportTool.TransportToolStatusDTO">
        SELECT
        <include refid="queryTransportToolStatus"/>
        FROM
        `hv_bm_transport_tool` t
        <where>
            <if test="query.transportToolCode != null and query.transportToolCode != ''">
                AND transport_tool_code like concat('%',#{query.transportToolCode},'%')
            </if>
            <if test="query.transportToolTypeCode != null and query.transportToolTypeCode != ''">
                AND transport_tool_type_code like concat('%',#{query.transportToolTypeCode},'%')
            </if>
            <if test="query.workStatus != null">
                AND work_status = #{query.workStatus}
            </if>
            <if test="query.currentLocation != null and query.currentLocation != ''">
                AND current_location like concat('%',#{query.currentLocation},'%')
            </if>
            <if test="query.ipAddress != null and query.ipAddress != ''">
                AND ip_address like concat('%',#{query.ipAddress},'%')
            </if>
            <if test="query.battery != null and query.battery != ''">
                AND battery like concat('%',#{query.battery},'%')
            </if>
        </where>
    </select>


</mapper>