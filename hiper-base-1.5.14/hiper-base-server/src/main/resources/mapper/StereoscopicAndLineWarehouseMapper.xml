<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.hiperbase.dao.equipment.StereoscopicAndLineWarehouseMapper">

    <resultMap id="StereoscopicAndLineWarehouseMap" type="com.hvisions.hiperbase.equipment.StereoscopicAndLineWarehouseDTO">
        <id column="id" property="id"></id>
        <result column="stereoscopic_warehouse_id" property="stereoscopicWarehouseId"></result>
        <result column="line_warehouse_id" property="lineWarehouseId"></result>
        <result column="line_id" property="lineId"></result>
        <result column="create_time" property="createTime"></result>
        <result column="update_time" property="updateTime"></result>
        <result column="creator_id" property="creatorId"></result>
        <result column="updater_id" property="updaterId"></result>
        <result column="lineName" property="lineName"></result>
        <result column="stereoscopicWarehouseName" property="stereoscopicWarehouseName"></result>
        <result column="lineWarehouseName" property="lineWarehouseName"></result>
        <result column="stereoscopicWarehouseAncestors" property="stereoscopicWarehouseAncestors"></result>
        <result column="lineWarehouseAncestors" property="lineWarehouseAncestors"></result>
    </resultMap>

    <select id="getPage" resultMap="StereoscopicAndLineWarehouseMap">
        select
            slw.id,
            slw.stereoscopic_warehouse_id,
            slw.line_warehouse_id,
            slw.line_id,
            slw.create_time,
            slw.update_time,
            slw.creator_id,
            slw.updater_id,
            l.name lineName,
            concat(sw.parent_id,',',sw.id) stereoscopicWarehouseAncestors,
            concat(lw.parent_id,',',lw.id) lineWarehouseAncestors,
            concat((select name from wms.hv_wms_wares_location where id = sw.parent_id),' / ',sw.name) stereoscopicWarehouseName,
            concat((select name from wms.hv_wms_wares_location where id = lw.parent_id),' / ',lw.name) lineWarehouseName
        from
            hv_bm_stereoscopic_and_line_warehouse slw
            left join hv_bm_location l on slw.line_id = l.id
            left join wms.hv_wms_wares_location sw on slw.stereoscopic_warehouse_id = sw.id
            left join wms.hv_wms_wares_location lw on slw.line_warehouse_id = lw.id
        <where>
            <if test="query.stereoscopicWarehouseId != null">
                and slw.stereoscopic_warehouse_id = #{query.stereoscopicWarehouseId}
                or slw.stereoscopic_warehouse_id in (select id from wms.hv_wms_wares_location where FIND_IN_SET(#{query.stereoscopicWarehouseId},concat(sw.parent_id,',',sw.id)))
            </if>
            <if test="query.lineWarehouseId != null">
                and slw.line_warehouse_id = #{query.lineWarehouseId}
                or slw.line_warehouse_id in (select id from wms.hv_wms_wares_location where FIND_IN_SET(#{query.lineWarehouseId},concat(lw.parent_id,',',lw.id)))
            </if>
            <if test="query.lineId != null">
                and slw.line_id = #{query.lineId}
            </if>
        </where>
    </select>

</mapper>