<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.hiperbase.dao.equipment.EquipmentMapMapper">
    <resultMap id="equipmentDetailMap" type="com.hvisions.hiperbase.equipment.map.EquipmentMapDto">
        <collection property="equipmentMapDetailDtos"
                    ofType="com.hvisions.hiperbase.equipment.map.EquipmentMapDetailDto"
                    select="findDetailByHeaderId" column="{headerId=id}"/>
    </resultMap>

    <select id="findAllHeaderByQuery" resultType="com.hvisions.hiperbase.equipment.map.EquipmentMapHeaderDto">
        select id, map_name, map_desc
        from hv_bm_equipment_map_header
        <where>
            <if test="query.keyWord != null and query.keyWord != ''">
                map_name like concat('%', #{query.keyWord}, '%')
            </if>
        </where>
    </select>

    <select id="findByHeaderId" resultMap="equipmentDetailMap">
        select id, map_name, map_desc, map_file_id
        from hv_bm_equipment_map_header
        where id = #{headerId}
    </select>

    <select id="findDetailByHeaderId" resultType="com.hvisions.hiperbase.equipment.map.EquipmentMapDetailDto">
        SELECT d.id, d.equipment_id, d.x_axis, d.y_axis, e.equipment_code, e.equipment_name
        FROM hv_bm_equipment_map_detail AS d
                 JOIN hv_bm_equipment AS e ON d.equipment_id = e.id
        WHERE d.header_id = #{headerId}
    </select>


</mapper>