<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.hiperbase.dao.transportTool.TransportToolTypeMapper">

    <resultMap id="TransportToolTypeMap" type="com.hvisions.hiperbase.transportTool.TransportToolTypeDTO">
        <result column="id" property="id"></result>
        <result column="transport_tool_type_code" property="transportToolTypeCode"></result>
        <result column="transport_tool_type_desc" property="transportToolTypeDesc"></result>
    </resultMap>

    <select id="getPage" resultMap="TransportToolTypeMap">
        select
        id,transport_tool_type_code, transport_tool_type_desc
        from
        hv_bm_transport_tool_type
        <where>
            <!-- 检查运输工具类型编码 -->
            <if test="query.transportToolTypeCode != null and query.transportToolTypeCode != ''">
                and transport_tool_type_code = #{query.transportToolTypeCode}
            </if>
            <!-- 检查运输工具类型描述 -->
            <if test="query.transportToolTypeDesc != null and query.transportToolTypeDesc != ''">
                and transport_tool_type_desc like concat('%', #{query.transportToolTypeDesc}, '%')
            </if>
        </where>
    </select>

    <sql id="queryTransportToolType">
        ttt.id,
        ttt.transport_tool_type_code transportToolTypeCode,
        ttt.transport_tool_type_desc transportToolTypeDesc,
        ttt.create_time createTime,
        ttt.update_time updateTime,
        ttt.creator_id creatorId,
        ttt.updater_id updaterId,
        ttt.site_num siteNum
    </sql>

    <select id="findListByCondition" resultType="com.hvisions.hiperbase.entity.transportTool.HvBmTransportToolType">
        SELECT
        <include refid="queryTransportToolType"/>
        FROM
        `hv_bm_transport_tool_type` ttt
        <where>
            <if test="hvBmTransportToolType.transportToolTypeCode != null and hvBmTransportToolType.transportToolTypeCode != ''">
                and ttt.transport_tool_type_code = #{hvBmTransportToolType.transportToolTypeCode}
            </if>
            <if test="hvBmTransportToolType.transportToolTypeDesc != null and hvBmTransportToolType.transportToolTypeDesc != ''">
                and ttt.transport_tool_type_desc like concat("%",#{hvBmTransportToolType.transportToolTypeDesc},"%")
            </if>
            <if test="hvBmTransportToolType.creatorId != null">
                and ttt.creator_id like concat("%",#{hvBmTransportToolType.creatorId},"%")
            </if>
            <if test="hvBmTransportToolType.updaterId != null">
                and ttt.updater_id like concat("%",#{hvBmTransportToolType.updaterId},"%")
            </if>
        </where>
    </select>

</mapper>