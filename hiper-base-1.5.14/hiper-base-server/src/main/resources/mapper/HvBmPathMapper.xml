<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.hiperbase.dao.path.HvBmPathMapper">

    <!-- 分页查询 -->
    <select id="getPage" resultType="com.hvisions.hiperbase.path.HvBmPathDTO">
        SELECT
        id,
        path_code,
        start_point,
        end_point,
        path_task_type_code
        FROM
        hv_bm_path
        <where>
            <if test="query.pathCode != null and query.pathCode != ''">
                AND path_code = #{query.pathCode}
            </if>
            <if test="query.startPoint != null">
                AND start_point = #{query.startPoint}
            </if>
            <if test="query.pathTaskTypeCode != null">
                AND path_task_type_code = #{query.pathTaskTypeCode}
            </if>
        </where>
    </select>

    <sql id="queryPath">
        p.path_code      pathCode,
        p.start_point      startPoint,
        p.end_point      endPoint,
        p.path_task_type_code pathTaskTypeCode,
        p.create_time              createTime,
        p.update_time              updateTime,
        p.creator_id               creatorId,
        p.updater_id               updaterId
    </sql>

    <select id="findListByCondition" resultType="com.hvisions.hiperbase.entity.path.HvBmPath">
        SELECT
        <include refid="queryPath"/>
        FROM
        `hv_bm_path` p
        <where>
            <if test="hvBmPathDTO.pathCode != null and hvBmPathDTO.pathCode != ''">
                AND p.path_code = #{hvBmPathDTO.pathCode}
            </if>
            <if test="hvBmPathDTO.startPoint != null and hvBmPathDTO.startPoint != ''">
                AND p.start_point LIKE CONCAT('%', #{hvBmPathDTO.startPoint}, '%')
            </if>
            <if test="hvBmPathDTO.endPoint != null and hvBmPathDTO.endPoint != ''">
                AND p.end_point LIKE CONCAT('%', #{hvBmPathDTO.endPoint}, '%')
            </if>
            <if test="hvBmPathDTO.pathTaskTypeCode != null">
                AND p.path_task_type_code = #{hvBmPathDTO.pathTaskTypeCode}
            </if>
            <if test="hvBmPathDTO.creatorId != null">
                AND p.creator_id = #{hvBmPathDTO.creatorId}
            </if>
            <if test="hvBmPathDTO.updaterId != null">
                AND p.updater_id = #{hvBmPathDTO.updaterId}
            </if>
        </where>
    </select>
    <select id="getHvBmPath" resultType="com.hvisions.hiperbase.path.HvBmPathDTO">
        SELECT
        <include refid="queryPath"/>
        FROM
        `hv_bm_path` p
        <where>
            <if test="hvBmPathDTO.pathCode != null and hvBmPathDTO.pathCode != ''">
                AND p.path_code = #{hvBmPathDTO.pathCode}
            </if>
            <if test="hvBmPathDTO.startPoint != null and hvBmPathDTO.startPoint != ''">
                AND p.start_point LIKE CONCAT('%', #{hvBmPathDTO.startPoint}, '%')
            </if>
            <if test="hvBmPathDTO.endPoint != null and hvBmPathDTO.endPoint != ''">
                AND p.end_point LIKE CONCAT('%', #{hvBmPathDTO.endPoint}, '%')
            </if>
            <if test="hvBmPathDTO.pathTaskTypeCode != null">
                AND p.path_task_type_code = #{hvBmPathDTO.pathTaskTypeCode}
            </if>
            <if test="hvBmPathDTO.creatorId != null">
                AND p.creator_id = #{hvBmPathDTO.creatorId}
            </if>
            <if test="hvBmPathDTO.updaterId != null">
                AND p.updater_id = #{hvBmPathDTO.updaterId}
            </if>
        </where>
    </select>
</mapper>
