<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.hiperbase.dao.material.HvBmSpecialMaterialMapper">

    <sql id="findByCondition">
        select
        sm.id,
        sm.line_id lineId,
        l.parent_id areaId,
        l.name lineName,
        sm.point_area_id pointAreaId,
        pa.point_area_code pointAreaName,
        sm.material_id materialId,
        m.material_name materialName,
        sm.min_width minWidth,
        sm.max_width maxWidth,
        sm.min_length minLength,
        sm.max_length maxLength
        from hv_bm_special_material sm
        left join hv_bm_material_point_area pa
        on pa.id = sm.point_area_id
        left join hv_bm_location l
        on l.id = sm.line_id
        left join hv_bm_material m
        on m.id = sm.material_id
        <where>
            <if test="condition.materialId != null and condition.materialId != 0">
                and sm.material_id = #{condition.materialId}
            </if>

            <if test="condition.pointAreaId != null and condition.pointAreaId != 0">
                and sm.point_area_id = #{condition.pointAreaId}
            </if>

            <if test="condition.lineId != null and condition.lineId != 0">
                and sm.line_id = #{condition.lineId}
            </if>
        </where>
    </sql>
    <select id="pageList" resultType="com.hvisions.hiperbase.entity.material.HvBmSpecialMaterial">
        <include refid="findByCondition"/>
    </select>

    <select id="exportData" resultType="com.hvisions.hiperbase.entity.material.HvBmSpecialMaterial">
        <include refid="findByCondition"/>
    </select>

    <select id="findMaterialPointDTOByMaterialCodeAndLineId" resultType="com.hvisions.hiperbase.materials.dto.MaterialPointAreaDTO">
        select
        mpa.id id,
        mpa.point_area_code pointAreaCode,
        mpa.point_area_name pointAreaName,
        mpa.location_id locationId
        from hv_bm_material_point_area mpa
        left join hv_bm_special_material sm
        on sm.point_area_id = mpa.id
        left join hv_bm_material m
        on m.id = sm.material_id
        where
        sm.line_id = #{lineId}
        and material_code in
        <foreach item="item" collection="materialCodes" separator="," open="(" close=")" >
            #{item}
        </foreach>
        limit 1

    </select>
</mapper>