<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.hiperbase.dao.material.MaterialPointAreaMapper">
    <resultMap id="MaterialPointAreaResult" type="com.hvisions.hiperbase.materials.dto.MaterialPointAreaDTO">
        <id property="id" column="id" />
        <result property="pointAreaCode" column="point_area_code"/>
        <result property="pointAreaName" column="point_area_name"/>
        <result property="locationId" column="location_id"/>
    </resultMap>
    
    <select id="queryMaterialPointArea" resultMap="MaterialPointAreaResult">
        select mpa.id, mpa.point_area_code, mpa.point_area_name, mpa.location_id, l.name as location_name
        from hv_bm_material_point_area mpa
        left join hv_bm_location l on mpa.location_id = l.id
        <where>
            <if test="pointAreaCode != null and pointAreaCode != ''">
                and point_area_code like concat('%',#{pointAreaCode},'%')
            </if>
            <if test="pointAreaName != null and pointAreaName != ''">
                and point_area_name like concat('%',#{pointAreaName},'%')
            </if>
            <if test="locationId != null">
                and location_id = #{locationId}
            </if>
        </where>
    </select>


    <select id="getMaterialPointAreaByStationCode" resultMap="MaterialPointAreaResult">
        select mpa.id, mpa.point_area_code, mpa.point_area_name, mpa.location_id
        from hv_bm_material_point_area mpa
        left join hv_bm_location l on mpa.location_id = l.id
        where l.code = #{stationCode}
    </select>

    <select id="findLineCodeByMaterialPointCode" resultType="java.lang.String">
        select
        `code`
        from hv_bm_location
        where id = (
            select
            parent_id
            from hv_bm_location
            where id = (
                select
                pa.location_id
                from hv_bm_material_point_area pa
                left join hv_bm_material_point mp
                on pa.id = mp.point_area_id
                where mp.point_code = #{materialPointCode}
            )
        )
    </select>

</mapper>