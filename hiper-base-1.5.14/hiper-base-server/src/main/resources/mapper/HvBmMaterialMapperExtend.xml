<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.hiperbase.dao.material.HvBmMaterialExtendMapper">
    <!--mysql-->
    <insert id="insertBatchMaterialExtend" parameterType="java.util.List">
        INSERT INTO hv_bm_material_extend (
        material_id,
        ship_type,
        stage,
        block,
        gps2,
        gps3,
        gps4,
        nested_On,
        area,
        ship,
        height,
        dm,
        parent_code,
        block_code,
        effective_period,
        thickness,
        deleteFlag
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.materialId},
            #{item.shipType},
            #{item.stage},
            #{item.block},
            #{item.gps2},
            #{item.gps3},
            #{item.gps4},
            #{item.nestedOn},
            #{item.area},
            #{item.ship},
            #{item.height},
            #{item.dm},
            #{item.parentCode},
            #{item.blockCode},
            #{item.effective_period},
            #{item.thickness},
            #{item.deleteFlag}
            )
        </foreach>
    </insert>
</mapper>