<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.hiperbase.dao.material.HvBmPointTaskMapper">
    <select id="findPage" resultType="com.hvisions.hiperbase.entity.material.HvBmPointTask">
        SELECT
            pt.id,
            pt.point_id pointId,
            mp.point_name pointName,
            pt.sequence,
            pt.interface_id interfaceId
        FROM
            hv_bm_point_task pt
            left join hv_bm_material_point mp
            on pt.point_id = mp.id
        <where>
            <if test="condition.pointId != null">
                and pt.point_id = #{condition.pointId}
            </if>
            <if test="condition.interfaceId != null">
                and pt.interface_id = #{condition.interfaceId}
            </if>
        </where>
    </select>

    <select id="getInterfaceIdsByPoint" resultType="java.lang.Integer">
        select
        pt.interface_id interfaceId
        from hv_bm_point_task pt
        left join hv_bm_material_point mp
        on pt.point_id = mp.id
        where mp.point_code = #{endPoint} order by pt.sequence asc
    </select>
</mapper>