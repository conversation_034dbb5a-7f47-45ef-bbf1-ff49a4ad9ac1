<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.hiperbase.dao.material.MaterialPointMapper">
    <resultMap id="MaterialPointResult" type="com.hvisions.hiperbase.materials.dto.MaterialPointDTO">
        <id property="id" column="id" />
        <result property="pointCode" column="point_code"/>
        <result property="pointName" column="point_name"/>
        <result property="pointAreaId" column="point_area_id"/>
        <association property="pointArea"
                     javaType="com.hvisions.hiperbase.materials.dto.MaterialPointDTO"
                     resultMap="com.hvisions.hiperbase.dao.material.MaterialPointAreaMapper.MaterialPointAreaResult" />
    </resultMap>
    
    <select id="queryMaterialPoint" resultMap="MaterialPointResult">
        select mp.id, mp.point_code, mp.point_name, mp.point_area_id,
            mpa.point_area_code, mpa.point_area_name
        from hv_bm_material_point mp
        left join hv_bm_material_point_area mpa on mp.point_area_id = mpa.id
        <where>
            <if test="pointCode != null and pointCode != ''">
                and point_code like concat('%',#{pointCode},'%')
            </if>
            <if test="pointName != null and pointName != ''">
                and point_name like concat('%',#{pointName},'%')
            </if>
            <if test="pointAreaId != null">
                and point_area_id = #{pointAreaId}
            </if>
        </where>
    </select>
    <select id="queryMaterialPointExport" resultType="com.hvisions.hiperbase.export.MaterialPointExportDTO">
        select mp.id, mp.point_code, mp.point_name, mp.point_area_id,
        mpa.point_area_code, mpa.point_area_name
        from hv_bm_material_point mp
        left join hv_bm_material_point_area mpa on mp.point_area_id = mpa.id
        <where>
            <if test="pointCode != null and pointCode != ''">
                and point_code like concat('%',#{pointCode},'%')
            </if>
            <if test="pointName != null and pointName != ''">
                and point_name like concat('%',#{pointName},'%')
            </if>
            <if test="pointAreaId != null">
                and point_area_id = #{pointAreaId}
            </if>
        </where>
    </select>


</mapper>