<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.hiperbase.dao.material.HvBmFrameMapper">

    <sql id="query">
        bf.ID,
        bf.frame_code frameCode,
        bf.frame_name frameName,
        bf.creator_id creatorId,
        bf.frame_type_id frameTypeId,
        concat(bfr.frame_type_code,'-',bfr.frame_type_name) frameTypeDescribe,
        bfr.frame_type_name frameTypeName,
        bfr.frame_type_code frameTypeCode,
        bf.create_time createTime,
        bf.updater_id updaterId,
        bf.update_time updateTime,
        bf.frame_length frameLength,
        bf.frame_width frameWidth,
        bf.frame_height frameHeight,
        bf.frame_load frameLoad,
        bf.load_height loadHeight,
        bf.current_location currentLocation,
        bf.transport_tool_type_code transportToolTypeCode
    </sql>

    <select id="findListByCondition" resultType="com.hvisions.hiperbase.entity.material.HvBmFrame">
        SELECT
          <include refid="query"/>
        FROM
        `hv_bm_frame` bf
        left join hv_bm_frame_type bfr
        on bf.frame_type_id = bfr.id
        <where>
            <if test="hvBmFrame.frameCode != null and hvBmFrame.frameCode != ''">
                and bf.frame_code = #{hvBmFrame.frameCode}
            </if>
            <if test="hvBmFrame.frameName != null and hvBmFrame.frameName != ''">
                and bf.frame_name like concat("%",#{hvBmFrame.frameName},"%")
            </if>
            <if test="hvBmFrame.frameTypeId != null and hvBmFrame.frameTypeId != 0">
                and bf.frame_type_id = #{hvBmFrame.frameTypeId}
            </if>
            <if test="hvBmFrame.transportToolTypeCode != null and hvBmFrame.transportToolTypeCode != ''">
                and bf.transport_tool_type_code = #{hvBmFrame.transportToolTypeCode}
            </if>
        </where>
    </select>

    <select id="findCurrentHvBmFrameCodeList" resultType="java.lang.String">
        SELECT
        frame_code frameCode
        FROM
        `hv_bm_frame`
    </select>

    <select id="findPageList" resultType="com.hvisions.hiperbase.entity.material.HvBmFrame">
        SELECT
        <include refid="query"/>
        FROM
        `hv_bm_frame` bf
        left join hv_bm_frame_type bfr
        on bf.frame_type_id = bfr.id
        <where>
            <if test="hvBmFrame.frameCode != null and hvBmFrame.frameCode != ''">
                and bf.frame_code = #{hvBmFrame.frameCode}
            </if>
            <if test="hvBmFrame.frameName != null and hvBmFrame.frameName != ''">
                and bf.frame_name like concat("%",#{hvBmFrame.frameName},"%")
            </if>
            <if test="hvBmFrame.frameTypeId != null and hvBmFrame.frameTypeId != 0">
                and bf.frame_type_id = #{hvBmFrame.frameTypeId}
            </if>
            <if test="hvBmFrame.transportToolTypeCode != null and hvBmFrame.transportToolTypeCode != ''">
                and bf.transport_tool_type_code = #{hvBmFrame.transportToolTypeCode}
            </if>
            <if test="hvBmFrame.currentLocation != null and hvBmFrame.currentLocation != ''">
                and bf.current_location like concat("%",#{hvBmFrame.currentLocation},"%")
            </if>
        </where>
    </select>

    <select id="findByFrameType" resultType="com.hvisions.hiperbase.entity.material.HvBmFrame">
        SELECT
        <include refid="query"/>
        FROM
        `hv_bm_frame` bf
        left join hv_bm_frame_type bfr
        on bf.frame_type_id = bfr.id
        where bfr.frame_type_code = #{frameTypeCode}
    </select>
    <select id="getHvBmFrame" resultType="com.hvisions.hiperbase.bom.dto.HvBmFrameDTO">
        SELECT
        bf.ID,
        bf.frame_code frameCode,
        bf.frame_name frameName,
        bf.creator_id creatorId,
        bf.frame_type_id frameTypeId,
        bfr.frame_type_name frameTypeName,
        bfr.frame_type_code frameTypeCode,
        bf.create_time createTime,
        bf.updater_id updaterId,
        bf.update_time updateTime,
        bf.frame_length frameLength,
        bf.frame_width frameWidth,
        bf.frame_height frameHeight,
        bf.frame_load frameLoad,
        bf.load_height loadHeight,
        bf.transport_tool_type_code transportToolTypeCode,
        bfr.frame_type_name transportToolTypeName,
        bf.current_location currentLocation,
        bf.frame_status frameStatus,
        bf.locked,
        bf.product_work_order_code productWorkOrderCode,
        bf.segmentation_code segmentationCode
        FROM
        `hv_bm_frame` bf
        left join hv_bm_frame_type bfr
        on bf.frame_type_id = bfr.id
        <where>
            <if test="hvBmFrameDTO.frameCode != null and hvBmFrameDTO.frameCode != ''">
                and bf.frame_code = #{hvBmFrameDTO.frameCode}
            </if>
            <if test="hvBmFrameDTO.frameName != null and hvBmFrameDTO.frameName != ''">
                and bf.frame_name like concat("%",#{hvBmFrameDTO.frameName},"%")
            </if>
            <if test="hvBmFrameDTO.currentLocation != null and hvBmFrameDTO.currentLocation != ''">
                and bf.current_location like concat("%",#{hvBmFrameDTO.currentLocation},"%")
            </if>
            <if test="hvBmFrameDTO.frameTypeId != null and hvBmFrameDTO.frameTypeId != 0">
                and bf.frame_type_id = #{hvBmFrameDTO.frameTypeId}
            </if>
            <if test="hvBmFrameDTO.transportToolTypeCode != null and hvBmFrameDTO.transportToolTypeCode != ''">
                and bf.transport_tool_type_code = #{hvBmFrameDTO.transportToolTypeCode}
            </if>
            <if test="hvBmFrameDTO.frameTypeCode != null and hvBmFrameDTO.frameTypeCode != ''">
                and bfr.frame_type_code = #{hvBmFrameDTO.frameTypeCode}
            </if>
            <if test="hvBmFrameDTO.frameStatus != null ">
                and bf.frame_status = #{hvBmFrameDTO.frameStatus}
            </if>
            <if test="hvBmFrameDTO.locked != null ">
                and bf.locked = #{hvBmFrameDTO.locked}
            </if>
        </where>
    </select>
    <select id="getByFrameCode" resultType="com.hvisions.hiperbase.bom.dto.HvBmFrameDTO">
        SELECT
            bf.ID,
            bf.frame_code frameCode,
            bf.frame_name frameName,
            bf.creator_id creatorId,
            bf.frame_type_id frameTypeId,
            bfr.frame_type_name frameTypeName,
            bfr.frame_type_code frameTypeCode,
            bf.create_time createTime,
            bf.updater_id updaterId,
            bf.update_time updateTime,
            bf.frame_length frameLength,
            bf.frame_width frameWidth,
            bf.frame_height frameHeight,
            bf.frame_load frameLoad,
            bf.load_height loadHeight,
            bf.transport_tool_type_code transportToolTypeCode,
            bfr.frame_type_name transportToolTypeName,
            bf.current_location currentLocation,
            bf.frame_status frameStatus,
            bf.locked,
            bf.product_work_order_code productWorkOrderCode,
            bf.segmentation_code segmentationCode
            FROM
            `hv_bm_frame` bf
            left join hv_bm_frame_type bfr
            on bf.frame_type_id = bfr.id
        where bf.frame_code =#{frameCode} LIMIT 1
    </select>
    <select id="getStatusPage" resultType="com.hvisions.hiperbase.bom.dto.FrameStatusDTO">
        SELECT
        bf.ID,
        bf.frame_code frameCode,
        bf.frame_name frameName,
        bf.creator_id creatorId,
        bf.frame_type_id frameTypeId,
        bfr.frame_type_name frameTypeName,
        bfr.frame_type_code frameTypeCode,
        bf.current_location currentLocation,
        bf.frame_status frameStatus,
        bf.locked,
        bf.product_work_order_code productWorkOrderCode,
        bf.segmentation_code segmentationCode
        FROM
        `hv_bm_frame` bf
        left join hv_bm_frame_type bfr
        on bf.frame_type_id = bfr.id
        <where>
            <if test="query.frameCode != null and query.frameCode != ''">
                and bf.frame_code = #{query.frameCode}
            </if>
            <if test="query.frameName != null and query.frameName != ''">
                and bf.frame_name like concat("%",#{query.frameName},"%")
            </if>
            <if test="query.currentLocation != null and query.currentLocation != ''">
                and bf.current_location like concat("%",#{query.currentLocation},"%")
            </if>
            <if test="query.frameTypeCode != null and query.frameTypeCode != ''">
                and bfr.frame_type_code like concat("%",#{query.frameTypeCode},"%")
            </if>
            <if test="query.frameStatus != null">
                and bf.frame_status = #{query.frameStatus}
            </if>
            <if test="query.locked != null">
                and bf.locked = #{query.locked}
            </if>
            <if test="query.productWorkOrderCode != null and query.productWorkOrderCode != ''">
                and bf.product_work_order_code like concat("%",#{query.productWorkOrderCode},"%")
            </if>
        </where>
    </select>
    <select id="getPageMaterial" resultType="com.hvisions.hiperbase.bom.dto.FrameMaterialItemDTO">
        SELECT
            bm.ID,
            bm.material_code AS materialCode,
            bm.material_name AS materialName,
            hws.quantity
        FROM
            hiper_base.hv_bm_material bm
                INNER JOIN wms.hv_wms_stocks hws
                           ON bm.id = hws.material_id
        WHERE
            hws.frame_code = #{query.frameCode}
    </select>


</mapper>
