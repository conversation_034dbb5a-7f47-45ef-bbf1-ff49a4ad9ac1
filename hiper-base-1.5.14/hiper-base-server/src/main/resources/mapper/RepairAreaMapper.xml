<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.hiperbase.dao.equipment.RepairAreaMapper">
    
    <resultMap id="RepairAreaLocationMap" type="com.hvisions.hiperbase.equipment.RepairAreaDTO">
        <id column="r_id" property="id"></id>
        <result column="area_code" property="areaCode"></result>
        <result column="area_name" property="areaName"></result>
        <result column="line_id" property="lineId"></result>
        <result column="create_time" property="createTime"></result>
        <result column="update_time" property="updateTime"></result>
        <result column="creator_id" property="creatorId"></result>
        <result column="updater_id" property="updaterId"></result>
        <result column="name" property="lineName"></result>
    </resultMap>
    
    <select id="getPage" resultMap="RepairAreaLocationMap">
        select
            r.id r_id,r.area_code,r.area_name,r.line_id,r.create_time,r.update_time,r.creator_id,r.updater_id,l.name
        from hv_bm_repair_area r,hv_bm_location l
        <where>
            r.line_id=l.id
            <if test="query.areaCode != null">
                and r.area_code like concat('%',#{query.areaCode},'%')
            </if>
            <if test="query.areaName != null and query.areaName != ''">
                and r.area_name like concat('%',#{query.areaName},'%')
            </if>
            <if test="query.lineId != null">
                and r.line_id = #{query.lineId}
            </if>
        </where>
--         order by r.create_time desc
    </select>

    <select id="getNotUsedLine" resultType="com.hvisions.hiperbase.equipment.location.LocationDTO">
        SELECT l.id,l.name
            FROM hv_bm_repair_area r
            right join hv_bm_location l
            on r.line_id=l.id
            where l.type=40
            and r.id is null
    </select>

</mapper>