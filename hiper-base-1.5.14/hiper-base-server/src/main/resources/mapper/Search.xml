<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.hiperbase.dao.route.RouteDao">

    <resultMap id="pd" type="com.hvisions.hiperbase.route.dto.ParameterDTO">
        <result property="parameterCode" column="parameter_code"/>
        <result property="parameterName" column="parameter_name"/>
        <result property="parameterType" column="parameter_type"/>
        <result property="parameterUsage" column="parameter_usage"/>
        <result property="ifOnline" column="if_online"/>
        <result property="ifOffline" column="if_offline"/>
        <result property="dataType" column="data_type"/>
        <result property="isSpot" column="is_spot"/>
        <result property="operationId" column="operation_id"/>
        <result property="operationName" column="operation_name"/>
        <result property="operationCode" column="operation_code"/>
        <result property="unitCode" column="unit_code"/>
    </resultMap>
    <select id="getParameterByQuery" resultMap="pd" parameterType="com.hvisions.hiperbase.route.dto.ParameterQueryDTO">
        SELECT t1.*,
        o1.operation_name,
        o1.operation_code,
        t3.code groupCode,
        t3.name groupName,
        t3.description groupDescription
        FROM
        hv_bm_parameter t1
        left join hv_bm_operation o1 on t1.operation_id = o1.id
        left join hv_bm_parameter_group t3 on t1.group_id = t3.id
        <where>
            <if test="dto.parameterUsage !=null">
                and ((t1.parameter_usage+1) &amp; (#{dto.parameterUsage}+1)) &gt; 0
            </if>
            <if test="dto.parameterCode !=null">
                and t1.parameter_code like concat('%',#{dto.parameterCode},'%')
            </if>
            <if test="dto.parameterName !=null">
                and t1.parameter_name like concat('%',#{dto.parameterName},'%')
            </if>
            <if test="dto.parameterType !=null">
                and t1.parameter_type = #{dto.parameterType}
            </if>
            <if test="dto.keyword !=null and dto.keyword != &quot;&quot;">
                and t1.parameter_name like concat('%',#{dto.keyword},'%') or t1.parameter_code like
                concat('%',#{dto.keyword},'%')
            </if>
            <if test="dto.groupId !=null">
                and t1.group_id = #{dto.groupId}
            </if>
        </where>
    </select>

    <select id="getProductRouteMaterial" resultType="com.hvisions.hiperbase.route.dto.MaterialInfo" databaseId="mysql">
        select t1.id,
        t1.material_name,
        t1.material_code,
        t3.bom_code,
        t3.bom_name,
        t3.bom_versions as bomVersion,
        t3.id as bomId
        from hv_bm_material t1
        left join hv_bm_bom_material t2 on t1.id = t2.material_id
        left join hv_bm_bom t3 on t2.bom_id = t3.id
        <where>
            <if test="query.keyword != null and query.keyword != ''">
                and (t1.material_code like concat('%',#{query.keyword},'%')
                or t1.material_name like concat('%',#{query.keyword},'%'))
            </if>
            and exists (
            select 1 from
            hv_bm_route t2 where t2.product_id = t1.id
            )
        </where>

    </select>
    <select id="getProductRouteMaterial" resultType="com.hvisions.hiperbase.route.dto.MaterialInfo"
            databaseId="sqlserver">
        select t1.id,
        t1.material_name,
        t1.material_code,
        t3.bom_code,
        t3.bom_name,
        t3.bom_versions as bomVersion,
        t3.id as bomId
        from hv_bm_material t1
        left join hv_bm_bom_material t2 on t1.id = t2.material_id
        left join hv_bm_bom t3 on t2.bom_id = t3.id
        <where>
            <if test="query.keyword != null and query.keyword != ''">
                and (t1.material_code like concat('%',#{query.keyword},'%')
                or t1.material_name like concat('%',#{query.keyword},'%'))
            </if>
            and exists (
            select 1 from
            hv_bm_route t2 where t2.product_id = t1.id
            )
        </where>
    </select>


    <select id="getProductRouteDTOByRouteCodeAndRouteVersion" resultType="com.hvisions.hiperbase.route.dto.RouteDTO">
        select
        id
        from hv_bm_route
        where route_code = #{routeCode} and route_version = #{routeVersion}
    </select>

    <select id="getProductRouteDTOByProductId" resultType="com.hvisions.hiperbase.route.dto.RouteDTO">
        select
        id
        from hv_bm_route
        where route_code = #{routeCode} and route_version = #{routeVersion} and product_id = #{productId}
    </select>
</mapper>