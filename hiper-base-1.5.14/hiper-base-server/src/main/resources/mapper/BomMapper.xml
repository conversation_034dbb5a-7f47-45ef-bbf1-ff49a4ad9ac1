<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.hiperbase.dao.material.BomMapper">
    <update id="updateBomStatusByBomCode">
        update
        hv_bm_bom
        set bom_status = #{bomStatus} where bom_code = #{bomCode} and bom_versions != #{bomVersion}
    </update>

    <select id="findBomIdsByBomCode" resultType="java.lang.Integer">
        select
        id
        from hv_bm_bom
        where bom_code = #{bomCode}
    </select>
</mapper>