<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.hiperbase.dao.equipment.LocationMapper">
    <resultMap id="getLocation" type="com.hvisions.hiperbase.equipment.location.LocationDTO"/>
    <select id="getLocationByQuery" resultMap="getLocation"
            parameterType="com.hvisions.hiperbase.equipment.location.LocationQueryDTO">
        select t1.* from
        hv_bm_location t1
        left join
        hv_bm_equipment_cell t2
        on t1.id = t2.cell_id
        <where>
            <if test="dto.codeLike != null and dto.codeLike != &apos;&apos;">
                and t1.code like concat('%',#{dto.codeLike},'%')
            </if>
            <if test="dto.codeEqual != null and dto.codeEqual != &apos;&apos;">
                and t1.code = #{dto.codeEqual}
            </if>
            <if test="dto.nameLike != null and dto.nameLike != &apos;&apos;">
                and t1.name like concat('%',#{dto.nameLike},'%')
            </if>
            <if test="dto.nameEqual != null and dto.nameEqual != &apos;&apos;">
                and t1.name = #{dto.nameEqual}
            </if>
            <if test="dto.type !=null">
                and t1.type = #{dto.type}
            </if>
            <if test="dto.types !=null and dto.types.size > 0">
                <foreach collection="dto.types" index="index" item="item"
                         open="and t1.type in("
                         separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.parentId !=null">
                and t1.parent_id = #{dto.parentId}
            </if>
            <if test="dto.parentIds !=null and dto.parentIds.size > 0">
                <foreach collection="dto.parentIds" index="index" item="item"
                         open="and t1.parent_id in("
                         separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.equipmentIds !=null and dto.equipmentIds.size > 0">
                <foreach collection="dto.equipmentIds" index="index" item="item"
                         open="and t2.equipment_id in("
                         separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findIdsByType" resultType="java.lang.Integer">
        select
        id
        from hv_bm_location
        where `type` = #{type}
    </select>

    <select id="getLineByStationCode" resultType="string">
        SELECT `code`
        FROM hv_bm_location
        WHERE id = (SELECT parent_id
                    FROM hv_bm_location
                    WHERE code = #{stationCode}
            LIMIT 1
            )
    </select>
    <update id="updateByCode" parameterType="com.hvisions.hiperbase.entity.equipment.HvBmLocation">
        UPDATE
            hv_bm_location
        SET
            name = #{name}
        WHERE
            id = #{id}
    </update>
</mapper>