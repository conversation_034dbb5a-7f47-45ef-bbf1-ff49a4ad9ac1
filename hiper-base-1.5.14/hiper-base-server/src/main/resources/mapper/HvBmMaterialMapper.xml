<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.hiperbase.dao.material.HvBmMaterialMapper">
    <!--mysql-->
    <insert id="insertBatchMaterial" parameterType="java.util.List" databaseId="mysql">
        INSERT INTO hv_bm_material (
        material_code,
        material_name,
        material_type,
        material_desc,
        material_group,
        uom,
        serial_number_profile,
        sys_num,
        eigenvalue,
        photo_id,
        specs,
        weight,
        quality,
        out_going,
        m_width,
        m_length,
        section_code
        )
        VALUES
        <foreach collection="list" item="material" index="index" separator=",">
            (
            #{material.materialCode},
            #{material.materialName},
            #{material.materialType},
            #{material.materialDesc},
            #{material.materialGroup},
            #{material.uom},
            #{material.serialNumberProfile, jdbcType=BOOLEAN},
            #{material.sysNum},
            #{material.eigenvalue},
            #{material.photoId},
            #{material.specs},
            #{material.weight},
            #{material.quality},
            #{material.outGoing},
            #{material.mWidth},
            #{material.mLength},
            #{material.sectionCode}
            )
        </foreach>
    </insert>
    <select id="getIdByMaterialCode" resultType="java.lang.Integer">
        SELECT id
        FROM hv_bm_material
        WHERE material_code = #{materialCode} limit 1
    </select>
</mapper>