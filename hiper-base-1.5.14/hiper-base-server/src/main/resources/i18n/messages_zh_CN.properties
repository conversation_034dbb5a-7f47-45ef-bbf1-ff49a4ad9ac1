#éç¨å¼å¸¸------------------------------------------------
SUCCESS=æå
SERVER_ERROR=æå¡å¨å¼å¸¸
JSON_PARSE_ERROR=Jsonè§£æéè¯¯
ILLEGAL_STRING=Jsonè§£æåºéï¼è¯·æ£æ¥jsonç»æ
NULL_RESULT=æ¥è¯¢ä¸ºç©º
VIOLATE_INTEGRITY=è¿åéå®ï¼è¯·æ£æ¥æ¯å¦æéå¤æ°æ®
IMPORT_FILE_NO_SUPPORT=æä»¶ç±»åä¸æ¯æ
IMPORT_SHEET_IS_NULL=æä»¶sheetè¡¨ä¸å­å¨
ENTITY_PROPERTY_NOT_SUPPORT=å®ä½å±æ§ä¸æ¯æï¼è¯·æ£æ¥å¯¼å¥æ°æ®
SAVE_SHOULD_NO_IDENTITY=ä¿å­ä¸åºè¯¥æä¸»é®
UPDATE_SHOULD_HAVE_IDENTITY=æ´æ°åºè¯¥æä¸»é®
NO_SUCH_ELEMENT=æ¥è¯¢ä¸å­å¨
CONST_VIOLATE=è¿åæ°æ®éå¶ï¼è¯·æ£æ¥æ¯å¦æç©ºå­ç¬¦ä¸²ä¼ è¿
DATA_INTEGRITY_VIOLATION=æ°æ®å®æ´æ§éªè¯åºé
CONSTRAINT_VIOLATION=è¿åæ°æ®å®æ´æ§éªè¯
COLUMN_EXISTS_VALUE=è¢«ä½¿ç¨çå­æ®µä¸å¯å é¤
#------------------------------------------------------------------------------------------------
DEMO_EXCEPTION_ENUM=å½éååè½ç¤ºä¾
BOM_NOT_EXISTS=bomä¸å­å¨
MATERIALS_CANNOT_BE_ZERO=materialsIdä¸è½ä¸º0
MATERIALS_NOT_EXISTS=è¯¥ç©æä¸å­å¨
BOM_CANNOT_BE_ZERO=bomIdä¸è½ä¸º0
BOM_ITEM_CANNOT_BE_ZERO=bomItemIdä¸è½ä¸º0
BOM_ITEM_NOT_EXISTS=bomItemIdä¸å­å¨
UNIT_ID_NOT_EXISTS=ç©æè®¡éåä½IDä¸å­å¨
REPETITIVE_CORRELATIVE_MATERIAL=éå¤å³èç©æID
BOM_MATERIAL_NOT_EXISTS=ç©æè¡¨ä¸æ²¡æå³èçBOMè¡¨
SYMBOL_EXISTS=ç©æåä½ç¼ç ä¸å¯éå¤
DESC_EXISTS=è®¡éåä½æè¿°å·²å­å¨
NOW_NEWLY_CANNOT_BE_MODIFIED=éæ°å¢çBOMä¸å¯çæ
MATERIAL_BOM_RELEVANCY=ç©æä¸BOMå­å¨å³èå³ç³»,å é¤è¢«æç»
MATERIAL_BOM_ITEM_RELEVANCY=ç©æä¸BOMITEMå­å¨å³èå³ç³»ï¼å é¤è¢«æç»
MATERIAL_SUBSTITUTE_ITEM_RELEVANCY=ç©æä¸SUBSTITUTEITEMå­å¨å³èå³ç³»ï¼å é¤è¢«æç»
BOM_ITEM_CODE_EXISTS=bomItemç¼ç å·²å­å¨
MATERIAL_CODE_EIGENVALUE_ERROR=ç©æç¼ç æç¹å¾å¼å¡«åéè¯¯
MATERIALS_NOT_NULL=è¯·å¡«å¥ç©æID
SUBSTITUTE_ITEM_COUNT_OUT_OF_RANGE=æ¿ä»£æ¯ä¾è¶åºèå´
MATERIAL_GROUP_IS_USE=ç©æåç»å·²è¢«ä½¿ç¨
UOM_IS_USE=ç©æåä½å·²è¢«ä½¿ç¨
BOM_ID_NOT_NULL=bomID ä¸è½ä¸ºç©º
BOM_ITEM_MATERIAL_CODE_EXISTS=bomItemç©æç¼ç å­å¨
BOM_STATUS_ARCHIVED=bomå·²å½æ¡£
DESCRIPTION_NOT_NULL=åä½æè¿°ä¸å­å¨
MATERIAL_TYPE_ERROR=ç©æç±»åéè¯¯
MATERIAL_TYPE_NAME_ERROR=ç©æç±»ååç§°åºé
REPEATED_ENTRY_INTO_FORCE_BOMCODE=å­å¨ç¸åçææ°æ®
UOM_ERROR=ç©æåä½åºé
UOM_NAME_ERROR=ç©æåä½ä¸ºç©ºæèä¸å­å¨
UNIT_SYMBOL_NOT_NULL=åä½ç¬¦å·ä¸è½ä¸ºç©º
UNIT_SYMBOL_NOT_NULL_PLEASE_FILL_IN_CORRECTLYU=è¯·æ­£ç¡®å¡«ååä½ç¬¦å·
BOM_MATERIAL_IS_EXISTS=bomå·²å­å¨ç»å®å³ç³»
BOM_CODE_OR_VERSION_NOT_NULL=bomç¼ç æèçæ¬ä¸è½ä¸ºç©º
REGEX_ERROR_MATERIAL_NOT_FIND=æ¾ä¸å°å¯¹åºç©æä¿¡æ¯
MATERIAL_HAVE_BOM=ç©æå·²æç»å®BOM
MATERIAL_PARSE_NOT_SET=ç©æè§£æè§åæ²¡æéç½®
PARSE_SETTING_ERROR=è§£æè§ååºéãå¼å§ä½ç½®ä¸è½å¤§äºç»æä½ç½®ï¼æ­£åè¡¨è¾¾å¼å¿é¡»åå«?<code>, å¯éï¼?<id>,å¯éï¼?<eigenvalue>
NOT_BOM=æªæ¾å°å¯¹åºBom
BOM_NOT_LESSTHEN_ZERO=bomæ°éä¸è½å°äºç­äº0
NOT_NEW_BOM_CANNOT_BE_UPDATE=éæ°å¢bomä¸è½ä¿®æ¹
BOM_NOT_FIND=æªæ¾å°bomä¿¡æ¯
FIXED_TYPE_CANNOT_BE_DELETED=åºç¡ç±»åæ æ³å é¤
MATERIAL_TYPE_NOT_NULL=ç©æç±»åä¸åè®¸ä¸ºç©º
MATERIAL_TYPE_IS_USE=ç©æç±»åå·²è¢«ä½¿ç¨
MATERIAL_TYPE_HAVE_DATA=ç©æç±»åæä¸çº§ç±»åï¼æ æ³å é¤
BOM_VERSIONS_EXISTS=åä¸bomçæ¬å·ä¸å¯éå¤
NOT_NEW_BOM_CANNOT_BE_DELETE=åªææ°å¢BOMæè½å é¤
CHOOSE_BOM=è¯·éæ©bom
BOM_STATE_NOT_TAKEEFFECT=åªæçæbomå¯ä»¥å½æ¡£
OPERATION_USED=å·¥èºæä½å·²ç»è¢«ä½¿ç¨
MATERIAL_SERVER_ERROR=ç©ææå¡å¼å¸¸
FILE_SERVER_ERROR=æä»¶æå¡å¼å¸¸
ROUTE_STEP_TYPE_ERROR=å·¥èºæ­¥éª¤ç±»åéè¯¯
EQUIPMENT_SERVER_ERROR=è®¾å¤æå¡å¼å¸¸
PARAMETER_USED=åæ°å·²ç»è¢«ä½¿ç¨
ROUTE_LOOP=å·¥èºè·¯çº¿æ ¡éªéè¯¯ï¼åºç°å¾ªç¯
PARAMETER_TYPE_ERROR=å·¥èºåæ°ç±»åéè¯¯
MIN_BIGGER_THAN_MAX=éè¯¯ï¼æå°å¼å¤§äºæå¤§å¼
PARAMETER_VALUE_ERROR=å·¥èºåæ°æ ¼å¼ä¸åè¦æ±
ROUTE_STEP_ORDER_ERROR=å·¥èºæ­¥éª¤å·¥åºéå¤
EQUIPMENT_NOT_IN_CELL=å·¥èºè·¯çº¿ç»å®äº§çº¿ä¸­ä¸å­å¨æ­¤è®¾å¤
MATERIAL_VERSION_BEEN_BINDING_ERROR=äº§åå·²ç»æè¯¥çæ¬
MATERIAL_ROUTE_NOT_NEW=åæ°çæ¬ä¸æ¯æ°å»ºç¶æ
MATERIAL_NOT_BINDING=äº§åæªç»å®
PARAMETER_HAS_BEEN_BIND_ERROR=è¯¥åæ°å·²ç»ç»å®
OPERATION_CANT_BE_CHANGED=åæ°å·¥èºæä½ä¸è½ä¿®æ¹
OPERATION_CANT_BE_NULL=åæ°å·¥èºæä½ä¸è½ä¸ºç©º
MATERIAL_NOT_HAVE_ACTIVE_PARAMETER_VERSION=ç©ææ²¡æçæçå·¥èºåæ°çæ¬
PARAMETER_NOT_FOUND=åæ°æªæ¾å°
MATERIAL_NOT_FOUND=ç©ææªæ¾å°
ROUTE_NOT_FOUND=å·¥èºæªæ¾å°
OPERATION_NOT_FOUND=å·¥èºæä½æªæ¾å°
ROUTE_ALREADY_EXISTED=å·¥èºè·¯çº¿å·²ç»å­å¨
MATERIAL_HAS_BEEN_BIND=äº§åå·²ç»ç»å®å¶ä»å·¥èºè·¯çº¿
ROUTE_STEP_ORDER_ZERO=å·¥èºæ­¥éª¤æ°åå·ä¸è½ä¸º0
OPERATIONCODE_USED=å·¥èºæä½ç¼ç å·²å­å¨
OPERATIONCODE_TYPE_USED=å·¥èºæä½ç±»åç¼ç å·²å­å¨
OPERATION_TYPE_USED=å·¥èºæä½ç±»åå·²è¢«ä½¿ç¨
PARAMETERTYPE_MUST_BE_CHOSEN=åæ°ç±»åå¿é¡»è¢«éæ©
ROUTE_ELEMENTS_LOST=å·¥èºè·¯çº¿åç´ ç¼ºå¤±æèä½¿ç¨éè¯¯
ROUTE_HAS_BEEN_BIND=å·¥èºè·¯çº¿å·²ç»è¢«ç»å®
ROUTE_CODE_CANT_BE_CHANGED=å·¥èºè·¯çº¿ç¼ç ä¸è½ä¿®æ¹
ONLY_NEWLY_CAN_BE_MODIFIED=åªææ°å»ºç¶æå·¥èºè·¯çº¿å¯ä»¥è¢«ä¿®æ¹
ONLY_EFFECT_CAN_BE_BIND=åªæçæç¶æå·¥èºè·¯çº¿å¯ä»¥ç»å®
ROUTE_CODE_AND_VERSION_CANT_BE_SAME=å·¥èºè·¯çº¿ç¼ç åçæ¬ä¸è½åæ¶éå¤
SERIALIZE_ERROR=åºååå¤±è´¥
RULE_COMPARE_ERROR=è§åè®¾ç½®æèæ ¡éªéè¯¯
GROUP_USED=æ ç­¾å·²è¢«ä½¿ç¨
TYPE_HAVE_SUB=å·¥èºæä½ç±»åæ¥æå­çº§èç¹æ æ³å é¤
NOT_CURRENT_DATABASES_SUPPORTED=ä¸æ¯æè¯¥ç§æ°æ®åºæ¥è¯¢
AUTH_SERVICE_ERROR=åºç¡æ¨¡åè°ç¨åºéï¼è¯·æ£æ¥åºç¡æå¡
SCHEDULE_OUT_DATE=æç­æ¥æå·²è¿ï¼ä¸åè®¸ä¿®æ¹
HOLIDAY_NOT_MATCH_YEAR=åææ¥æä¸å¹´ä»½ä¸å¹é
CREW_NOT_SET=è½¦é´ç­ç»æªè®¾ç½®
SHIFT_NOT_SET=è½¦é´ç­æ¬¡æªè®¾ç½®
BEGIN_TIME_LATER_THAN_END_TIME=å¼å§æ¶é´ä¸è½å¤§äºç»ææ¶é´
CREW_NOT_EXISTS=ç­ç»ä¸å­å¨
SHIFT_NOT_EXISTS=ç­æ¬¡ä¸å­å¨
CREW_IS_NOT_ENABLE=ç­ç»å·²ç»ç¦ç¨
SHIFT_NOT_ENABLE=ç­æ¬¡å·²ç»ç¦ç¨
CREW_SHIFT_INFO_EMPTY=ç­ç»ç­æ¬¡ä¿¡æ¯ä¸ºç©º
# è¾å¥æ¶é´éè¯¯
INPUT_TIME_ERROR=è¾å¥æ¶é´éè¯¯
# å½åç­ç»å·²ç»ä½¿ç¨å é¤å¤±è´¥
CURRENT_TEAM_HAS_FAILED_TO_USE_DELETE=å½åå¯¹è±¡å·²ç»è¢«ä½¿ç¨,å é¤å¤±è´¥
EQUIPMENT_EXISTS_DATA=æ­¤è®¾å¤å­å¨å­çº§è®¾å¤
EQUIPMENT_TYPE_SUB_HAVE_DATA=è®¾å¤ç±»åæå­çº§æ°æ®
EQUIPMENT_TYPE_IN_USE=è®¾å¤ç±»åå·²ç»ä½¿ç¨
EQUIPMENT_TYPE_NOT_EXISTS=è®¾å¤ç±»åä¸å­å¨
PARENT_EQUIPMENT_NOT_EXISTS=ç¶è®¾å¤ä¸å­å¨
LOCATION_TYPE_NOT_SUPPORT=ä½ç½®ç±»åä¸æ¯æ
EQUIPMENT_PARENT_IS_SELF=ç¶èç¹ä¸è½æ¯èªå·±
EQUIPMENT_PARENT_IS_CHILD=ç¶èç¹ä¸è½æ¯å­èç¹ä¸­çä¸ä¸ª
TYPE_MISMATCHING_PARENT_ID=ç±»åäºç¶èç¹ä¸å¹é
PARENT_ID_NOT_EXISTS=ç¶èç¹ä¸å­å¨
PARAMS_ERROR=åæ°å¼å¸¸
CELL_NOT_EXISTS=äº§çº¿ä¸å­å¨
LOCATION_HAVE_DATA=å·¥åå»ºæ¨¡å­å¨å­åç´ 
EQUIPMENT_HAS_BEEN_CELL=è®¾å¤å·²è¢«å¶ä»äº§çº¿ç»å®
CELL_HAS_BEEN_EQUIPMENT=äº§çº¿ä¸æç»å®è®¾å¤
PLEASE_MAKE_SURE_TO_SELECT_CELL=è¯·ç¡®è®¤äº§çº¿IDæ­£ç¡®
LOCATION_ID_IS_NOT_NULL=äº§çº¿IDä¸è½ä¸ºç©º
LOCATION_HAVE_EQUIPMENT=äº§çº¿ä¸ç»å®çæè®¾å¤
EXPORT_ERROR=å¯¼åºéè¯¯