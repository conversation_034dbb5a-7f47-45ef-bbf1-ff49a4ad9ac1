# éç¨å¼å¸¸ä¿¡æ¯
SUCCESS=SUCCESS
SERVER_ERROR=SERVER_ERROR
JSON_PARSE_ERROR=JSON_PARSE_ERROR
ILLEGAL_STRING=ILLEGAL_STRING
NULL_RESULT=NULL_RESULT
VIOLATE_INTEGRITY=VIOLATE_INTEGRITY
IMPORT_FILE_NO_SUPPORT=IMPORT_FILE_NO_SUPPORT
IMPORT_SHEET_IS_NULL=IMPORT_SHEET_IS_NULL
ENTITY_PROPERTY_NOT_SUPPORT=ENTITY_PROPERTY_NOT_SUPPORT
SAVE_SHOULD_NO_IDENTITY=SAVE_SHOULD_NO_IDENTITY
UPDATE_SHOULD_HAVE_IDENTITY=UPDATE_SHOULD_HAVE_IDENTITY
CONST_VIOLATE=const_violate
NO_SUCH_ELEMENT=NO_SUCH_ELEMENT
DATA_INTEGRITY_VIOLATION=DATA_INTEGRITY_VIOLATION
COLUMN_EXISTS_VALUE=COLUMN_EXISTS_VALUE
#------------------------------------------------
DEMO_EXCEPTION_ENUM=DEMO_EXCEPTION_ENUM
MATERIALS_CANNOT_BE_ZERO=MATERIALS_CANNOT_BE_ZERO
MATERIALS_NOT_EXISTS=MATERIALS_NOT_EXISTS
BOM_NOT_EXISTS=BOM_NOT_EXISTS
BOM_CANNOT_BE_ZERO=BOM_CANNOT_BE_ZERO
BOM_ITEM_CANNOT_BE_ZERO=BOM_ITEM_CANNOT_BE_ZERO
BOM_ITEM_NOT_EXISTS=BOM_ITEM_NOT_EXISTS
UNIT_ID_NOT_EXISTS=UNIT_ID_NOT_EXISTS
CONSTRAINT_VIOLATION=CONSTRAINT_VIOLATION
REPETITIVE_CORRELATIVE_MATERIAL=REPETITIVE_CORRELATIVE_MATERIAL
BOM_MATERIAL_NOT_EXISTS=BOM_MATERIAL_NOT_EXISTS
SYMBOL_EXISTS=SYMBOL_EXISTS
DESC_EXISTS=DESC_EXISTS
NOW_NEWLY_CANNOT_BE_MODIFIED=NOW_NEWLY_CANNOT_BE_MODIFIED
MATERIAL_BOM_RELEVANCY=MATERIAL_BOM_RELEVANCY
MATERIAL_BOM_ITEM_RELEVANCY=MATERIAL_BOM_ITEM_RELEVANCY
MATERIAL_SUBSTITUTE_ITEM_RELEVANCY=MATERIAL_SUBSTITUTE_ITEM_RELEVANCY
BOM_ITEM_CODE_EXISTS=BOM_ITEM_CODE_EXISTS
MATERIAL_CODE_EIGENVALUE_ERROR=MATERIAL_CODE_EIGENVALUE_ERROR
MATERIALS_NOT_NULL=MATERIALS_NOT_NULL
SUBSTITUTE_ITEM_COUNT_OUT_OF_RANGE=SUBSTITUTE_ITEM_COUNT_OUT_OF_RANGE
MATERIAL_GROUP_IS_USE=MATERIAL_GROUP_IS_USE
UOM_IS_USE=UOM_IS_USE
BOM_ID_NOT_NULL=BOM_ID_NOT_NULL
BOM_ITEM_MATERIAL_CODE_EXISTS=BOM_ITEM_MATERIAL_CODE_EXISTS
BOM_STATUS_ARCHIVED=BOM_STATUS_ARCHIVED
DESCRIPTION_NOT_NULL=DESCRIPTION_NOT_NULL
MATERIAL_TYPE_ERROR=MATERIAL_TYPE_ERROR
MATERIAL_TYPE_NAME_ERROR=MATERIAL_TYPE_NAME_ERROR
REPEATED_ENTRY_INTO_FORCE_BOMCODE=REPEATED_ENTRY_INTO_FORCE_BOMCODE
UOM_ERROR=UOM_ERROR
UOM_NAME_ERROR=UOM_NAME_ERROR
UNIT_SYMBOL_NOT_NULL=UNIT_SYMBOL_NOT_NULL
UNIT_SYMBOL_NOT_NULL_PLEASE_FILL_IN_CORRECTLYU=UNIT_SYMBOL_NOT_NULL_PLEASE_FILL_IN_CORRECTLYU
BOM_MATERIAL_IS_EXISTS=BOM_MATERIAL_IS_EXISTS
BOM_CODE_OR_VERSION_NOT_NULL=BOM_CODE_OR_VERSION_NOT_NULL
REGEX_ERROR_MATERIAL_NOT_FIND=REGEX_ERROR_MATERIAL_NOT_FIND
MATERIAL_HAVE_BOM=MATERIAL_HAVE_BOM
MATERIAL_PARSE_NOT_SET=MATERIAL_PARSE_NOT_SET
PARSE_SETTING_ERROR=PARSE_SETTING_ERROR
NOT_BOM=NOT_BOM
BOM_NOT_LESSTHEN_ZERO=BOM_NOT_LESSTHEN_ZERO
NOT_NEW_BOM_CANNOT_BE_UPDATE=NOT_NEW_BOM_CANNOT_BE_UPDATE
BOM_NOT_FOUND=bon_not_find
FIXED_TYPE_CANNOT_BE_DELETED=FIXED_TYPE_CANNOT_BE_DELETED
MATERIAL_TYPE_NOT_NULL=MATERIAL_TYPE_NOT_NULL
MATERIAL_TYPE_IS_USE=MATERIAL_TYPE_IS_USE
MATERIAL_TYPE_HAVE_DATA=MATERIAL_TYPE_HAVE_DATA
BOM_VERSIONS_EXISTS=BOM_VERSIONS_EXISTS
NOT_NEW_BOM_CANNOT_BE_DELETE=NOT_NEW_BOM_CANNOT_BE_DELETE
CHOOSE_BOM=CHOOSE_BOM
BOM_STATE_NOT_TAKEEFFECT=BOM_STATE_NOT_TAKEEFFECT
OPERATION_USED=OPERATION_USED
MATERIAL_SERVER_ERROR=MATERIAL_SERVER_ERROR
FILE_SERVER_ERROR=FILE_SERVER_ERROR
ROUTE_STEP_TYPE_ERROR=ROUTE_STEP_TYPE_ERROR
EQUIPMENT_SERVER_ERROR=EQUIPMENT_SERVER_ERROR
PARAMETER_USED=PARAMETER_USED
ROUTE_LOOP=ROUTE_LOOP
PARAMETER_TYPE_ERROR=PARAMETER_TYPE_ERROR
MIN_BIGGER_THAN_MAX=MIN_BIGGER_THAN_MAX
PARAMETER_VALUE_ERROR=PARAMETER_VALUE_ERROR
ROUTE_STEP_ORDER_ERROR=ROUTE_STEP_ORDER_ERROR
EQUIPMENT_NOT_IN_CELL=EQUIPMENT_NOT_IN_CELL
MATERIAL_VERSION_BEEN_BINDING_ERROR=MATERIAL_VERSION_BEEN_BINDING_ERROR
MATERIAL_ROUTE_NOT_NEW=MATERIAL_ROUTE_NOT_NEW
MATERIAL_NOT_BINDING=MATERIAL_NOT_BINDING
PARAMETER_HAS_BEEN_BIND_ERROR=PARAMETER_HAS_BEEN_BIND_ERROR
OPERATION_CANT_BE_CHANGED=OPERATION_CANT_BE_CHANGED
OPERATION_CANT_BE_NULL=OPERATION_CANT_BE_NULL
MATERIAL_NOT_HAVE_ACTIVE_PARAMETER_VERSION=MATERIAL_NOT_HAVE_ACTIVE_PARAMETER_VERSION
PARAMETER_NOT_FOUND=PARAMETER_NOT_FOUND
MATERIAL_NOT_FOUND=MATERIAL_NOT_FOUND
ROUTE_NOT_FOUND=ROUTE_NOT_FOUND
OPERATION_NOT_FOUND=OPERATION_NOT_FOUND
ROUTE_ALREADY_EXISTED=ROUTE_ALREADY_EXISTED
MATERIAL_HAS_BEEN_BIND=MATERIAL_HAS_BEEN_BIND
ROUTE_STEP_ORDER_ZERO=ROUTE_STEP_ORDER_ZERO
OPERATIONCODE_USED=OPERATIONCODE_USED
PARAMETERTYPE_MUST_BE_CHOSEN=PARAMETERTYPE_MUST_BE_CHOSEN
ROUTE_ELEMENTS_LOST=ROUTE_ELEMENTS_LOST
ROUTE_HAS_BEEN_BIND=ROUTE_HAS_BEEN_BIND
ROUTE_CODE_CANT_BE_CHANGED=ROUTE_CODE_CANT_BE_CHANGED
ONLY_NEWLY_CAN_BE_MODIFIED=ONLY_NEWLY_CAN_BE_MODIFIED
ONLY_EFFECT_CAN_BE_BIND=ONLY_EFFECT_CAN_BE_BIND
ROUTE_CODE_AND_VERSION_CANT_BE_SAME=ROUTE_CODE_AND_VERSION_CANT_BE_SAME
SERIALIZE_ERROR=SERIALIZE_ERROR
RULE_COMPARE_ERROR=RULE_COMPARE_ERROR
OPERATION_TYPE_USED=OPERATION_TYPE_USED
GROUP_USED=GROUP_USED
OPERATIONCODE_TYPE_USED=OPERATIONCODE_TYPE_USED
TYPE_HAVE_SUB=TYPE_HAVE_SUB
NOT_CURRENT_DATABASES_SUPPORTED=NOT_CURRENT_DATABASES_SUPPORTED
AUTH_SERVICE_ERROR=AUTH_SERVICE_ERROR
SCHEDULE_OUT_DATE=SCHEDULE_OUT_DATE
HOLIDAY_NOT_MATCH_YEAR=HOLIDAY_NOT_MATCH_YEAR
CREW_NOT_SET=CREW_NOT_SET
SHIFT_NOT_SET=SHIFT_NOT_SET
BEGIN_TIME_LATER_THAN_END_TIME=BEGIN_TIME_LATER_THAN_END_TIME
CREW_NOT_EXISTS=CREW_NOT_EXISTS
SHIFT_NOT_EXISTS=SHIFT_NOT_EXISTS
CREW_IS_NOT_ENABLE=CREW_IS_NOT_ENABLE
SHIFT_NOT_ENABLE=SHIFT_NOT_ENABLE
CREW_SHIFT_INFO_EMPTY=CREW_SHIFT_INFO_EMPTY
INPUT_TIME_ERROR=INPUT_TIME_ERROR
CURRENT_TEAM_HAS_FAILED_TO_USE_DELETE=CURRENT_TEAM_HAS_FAILED_TO_USE_DELETE
EQUIPMENT_EXISTS_DATA=EQUIPMENT_EXISTS_DATA
EQUIPMENT_TYPE_SUB_HAVE_DATA=EQUIPMENT_TYPE_SUB_HAVE_DATA
EQUIPMENT_TYPE_IN_USE=EQUIPMENT_TYPE_IN_USE
EQUIPMENT_TYPE_NOT_EXISTS=EQUIPMENT_TYPE_NOT_EXISTS
PARENT_EQUIPMENT_NOT_EXISTS=PARENT_EQUIPMENT_NOT_EXISTS
LOCATION_TYPE_NOT_SUPPORT=LOCATION_TYPE_NOT_SUPPORT
EQUIPMENT_PARENT_IS_SELF=EQUIPMENT_PARENT_IS_SELF
EQUIPMENT_PARENT_IS_CHILD=EQUIPMENT_PARENT_IS_CHILD
TYPE_MISMATCHING_PARENT_ID=TYPE_MISMATCHING_PARENT_ID
PARENT_ID_NOT_EXISTS=PARENT_ID_NOT_EXISTS
PARAMS_ERROR=PARAMS_ERROR
CELL_NOT_EXISTS=CELL_NOT_EXISTS
LOCATION_HAVE_DATA=LOCATION_HAVE_DATA
EQUIPMENT_HAS_BEEN_CELL=EQUIPMENT_HAS_BEEN_CELL
CELL_HAS_BEEN_EQUIPMENT=CELL_HAS_BEEN_EQUIPMENT
PLEASE_MAKE_SURE_TO_SELECT_CELL=PLEASE_MAKE_SURE_TO_SELECT_CELL
LOCATION_ID_IS_NOT_NULL=LOCATION_ID_IS_NOT_NULL
LOCATION_HAVE_EQUIPMENT=LOCATION_HAVE_EQUIPMENT
EXPORT_ERROR=EXPORT_ERROR