CREATE TABLE `hv_bm_frame` (
  `ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `frame_code` varchar(32) NOT NULL COMMENT '料框编号(唯一)',
  `frame_name` varchar(255) DEFAULT NULL COMMENT '料框名称',
  `frame_type_id` int(11) DEFAULT NULL COMMENT '料框类型',
  `creator_id` varchar(32) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updater_id` varchar(32) DEFAULT NULL COMMENT '修改者',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `frame_code_index` (`frame_code`) COMMENT '唯一'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='料框主数据';

CREATE TABLE `hv_bm_material_point` (
                                        `id` int NOT NULL AUTO_INCREMENT COMMENT '料点id',
                                        `point_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '编号',
                                        `point_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名称',
                                        `point_area_id` int NOT NULL DEFAULT '50' COMMENT '料点区域',
                                        `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
                                        `creator_id` int DEFAULT NULL COMMENT '创建id',
                                        `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
                                        `updater_id` int DEFAULT NULL COMMENT '更新id',
                                        `have_frame` int DEFAULT NULL COMMENT '有料框（0：是，1：否）',
                                        PRIMARY KEY (`id`) USING BTREE,
                                        UNIQUE KEY `point_code` (`point_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=48 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='料点配置';

CREATE TABLE `hv_bm_material_point_area` (
   `id` int NOT NULL AUTO_INCREMENT COMMENT '料点区域id',
   `point_area_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '编号',
   `point_area_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名称',
   `location_id` int NOT NULL COMMENT '工位',
   `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
   `creator_id` int DEFAULT NULL COMMENT '创建id',
   `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
   `updater_id` int DEFAULT NULL COMMENT '更新id',
   PRIMARY KEY (`id`) USING BTREE,
   UNIQUE KEY `point_code` (`point_area_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='料点区域';

CREATE TABLE `hv_bm_special_material` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `material_id` int(11) DEFAULT NULL COMMENT '物料ID',
  `min_width` double DEFAULT NULL COMMENT '最小宽度',
  `max_width` double DEFAULT NULL COMMENT '最大宽度',
  `min_length` double DEFAULT NULL COMMENT '最小长度',
  `max_length` double DEFAULT NULL COMMENT '最大长度',
  `line_id` int(11) DEFAULT NULL COMMENT '产线ID',
  `point_area_id` int(11) DEFAULT NULL COMMENT '料点区域ID',
  `creator_id` int(11) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updater_id` int(11) DEFAULT NULL COMMENT '修改者',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='特殊零件';


CREATE TABLE `hv_bm_tao_material_file` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键iD',
  `model` varchar(32) DEFAULT NULL COMMENT '船型',
  `segmentation_code` varchar(32) DEFAULT NULL COMMENT '分段号',
  `tao_file` varchar(255) DEFAULT NULL COMMENT '套料图信息',
  `specs` varchar(32) DEFAULT NULL COMMENT '零件规格',
  `length` varchar(32) DEFAULT NULL COMMENT '长度',
  `weight` varchar(32) DEFAULT NULL COMMENT '零件重量',
  `quality` int(11) DEFAULT NULL COMMENT '数量',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `creator_id` int(11) DEFAULT NULL COMMENT '创建者',
  `updater_id` int(11) DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8 COMMENT='型材套路配置';