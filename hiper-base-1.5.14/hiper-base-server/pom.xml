<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hiper-base</artifactId>
        <groupId>com.hvisions</groupId>
        <version>1.5.14</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>hiper-base-server</artifactId>
    <packaging>${packing-type}</packaging>
    <!--使得项目可以进行war包部署，mvn package -P war 即可-->
    <profiles>
        <profile>
            <id>jar</id>
            <!--默认配置-->
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <packing-type>jar</packing-type>
            </properties>
        </profile>
        <profile>
            <id>war</id>
            <properties>
                <packing-type>war</packing-type>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-web</artifactId>
                    <!-- 移除嵌入式tomcat插件 -->
                    <exclusions>
                        <exclusion>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-starter-tomcat</artifactId>
                        </exclusion>
                    </exclusions>
                </dependency>
                <dependency>
                    <groupId>javax.servlet</groupId>
                    <artifactId>javax.servlet-api</artifactId>
                    <version>3.1.0</version>
                </dependency>

                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                    <scope>provided</scope>
                </dependency>
            </dependencies>
        </profile>
    </profiles>

    <dependencies>

        <!--springboot-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!--健康检查-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <!--测试-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <!--MyBatisPlus jar可兼容MyBatis 只增强不改变-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.5.1</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>2.2.0</version>
        </dependency>

        <!--swagger-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>2.9.2</version>
        </dependency>
        <dependency>
            <groupId>io.github.swagger2markup</groupId>
            <artifactId>swagger2markup</artifactId>
            <version>1.3.3</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>2.9.2</version>
        </dependency>
        <!--sql-server依赖-->
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
            <scope>runtime</scope>
        </dependency>
        <!--oracle-->
        <dependency>
            <groupId>com.hynnet</groupId>
            <artifactId>oracle-driver-ojdbc6</artifactId>
            <version>12.1.0.1</version>
        </dependency>
        <!--mysql-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.11</version>
        </dependency>
        <!--如果需要连接config服务器。就可以打开此配置-->
        <!--<dependency>-->
        <!--<groupId>org.springframework.cloud</groupId>-->
        <!--<artifactId>spring-cloud-config-client</artifactId>-->
        <!--</dependency>-->
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>hiper-base-common</artifactId>
            <version>1.5.14</version>
        </dependency>
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>framework-log-spring-boot-starter</artifactId>
            <version>1.7.0</version>
        </dependency>

        <!--rabbitmq 消息队列，如果需要可以打开-->
        <!--<dependency>-->
        <!--<groupId>org.springframework.boot</groupId>-->
        <!--<artifactId>spring-boot-starter-amqp</artifactId>-->
        <!--</dependency>-->
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>hvisions-common-springboot-starter</artifactId>
            <version>3.5.1</version>
        </dependency>
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>framework-client</artifactId>
            <version>1.7.0</version>
        </dependency>
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>thirdparty-common</artifactId>
            <version>1.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>pms-client</artifactId>
            <version>1.4.3</version>
        </dependency>
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>thirdparty-client</artifactId>
            <version>1.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>wms-client</artifactId>
            <version>1.2.5</version>
        </dependency>
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>wms-common</artifactId>
            <version>1.2.5</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.0.5</version>
        </dependency>
    </dependencies>
    <distributionManagement>
        <repository>
            <id>release</id>
            <url>http://**************:8022/repository/maven-server-release/</url>
        </repository>
    </distributionManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>
</project>
