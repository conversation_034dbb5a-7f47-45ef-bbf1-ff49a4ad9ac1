package com.hvisions.hiperbase.client;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.client.fallback.FrameTypeFallBack;
import com.hvisions.hiperbase.materials.dto.FrameTypeDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;


@FeignClient(name = "hiper-base", path = "/frameType", fallbackFactory = FrameTypeFallBack.class)
public interface FrameTypeClient {
    /**
     * 创建料框类型
     *
     * @param frameTypeDTO
     * @return
     */
    @PostMapping(value = "/create")
    ResultVO create(@RequestBody FrameTypeDTO frameTypeDTO);

    /**
     * 修改框料属性类型
     *
     * @param frameTypeDTO
     * @return
     */
    @PutMapping(value = "/update")
    ResultVO update(@RequestBody FrameTypeDTO frameTypeDTO);

    /**
     * 删除料框类型
     *
     * @param id
     * @return
     */
    @DeleteMapping(value = "/delete/{id}")
    ResultVO delete(@PathVariable("id") Integer id);

    /**
     * 根据id获取料框类型
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/get/{id}")
    ResultVO<FrameTypeDTO> getById(@PathVariable("id") Integer id);

    /**
     * 获取分页数据
     *
     * @param frameTypeDTO
     * @return
     */
    @PostMapping(value = "/query")
    ResultVO<FrameTypeDTO> query(@RequestBody FrameTypeDTO frameTypeDTO);

    /**
     * 获取全部列表
     *
     * @return
     */
    @GetMapping(value = "/getAll")
    ResultVO<FrameTypeDTO> getAll();
}
