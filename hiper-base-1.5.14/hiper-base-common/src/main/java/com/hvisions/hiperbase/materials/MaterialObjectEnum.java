package com.hvisions.hiperbase.materials;

/**
 * <p>Title: MaterialObjectEnum</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2018/12/7</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public enum MaterialObjectEnum {
    /**
     * DtoCode枚举
     */

    MATERIAL_DTO(501),
    BASE_MATERIAL_DTO(502),
    MATERIAL_TYPE_DTO(504),
    BASE_BOM_DTO(506),
    BASE_BOMITEM_DTO(507),
    BOM_DTO(509),
    BOM_ITEM_DTO(510),
    BOM_MATERIAL_DTO(511),
    BOM_QUERY_DTO(512),
    SUBSTITUTEITEM_DTO(513),
    UNIT_DTO(514),
    MATERIAL_CODE_QUERY_DTO(515),

    ;

    int code;

    MaterialObjectEnum(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

}
