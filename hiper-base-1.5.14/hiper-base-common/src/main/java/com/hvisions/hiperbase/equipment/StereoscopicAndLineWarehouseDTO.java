package com.hvisions.hiperbase.equipment;

import com.hvisions.hiperbase.SysBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/5/22
 */
@Data
public class StereoscopicAndLineWarehouseDTO extends SysBaseDTO {

    /**
     * 立体库区id
     */
    @ApiModelProperty(value = "立体库区id")
    private Integer stereoscopicWarehouseId;

    /**
     * 立体库区名称
     */
    @ApiModelProperty(value = "立体库区名称")
    private String stereoscopicWarehouseName;

    /**
     * 线边库区id
     */
    @ApiModelProperty(value = "线边库区id")
    private Integer lineWarehouseId;

    /**
     * 线边库区名称
     */
    @ApiModelProperty(value = "线边库区名称")
    private String lineWarehouseName;

    /**
     * 产线id
     */
    @ApiModelProperty(value = "产线id")
    private Integer lineId;

    /**
     * 产线名称
     */
    @ApiModelProperty(value = "产线名称")
    private String lineName;

    private String stereoscopicWarehouseAncestors;
    private String lineWarehouseAncestors;
}
