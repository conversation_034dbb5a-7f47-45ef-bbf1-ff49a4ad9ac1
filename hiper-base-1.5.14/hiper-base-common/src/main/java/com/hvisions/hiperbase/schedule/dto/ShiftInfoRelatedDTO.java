package com.hvisions.hiperbase.schedule.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>Title:ShiftInfoRelatedDTO</p>
 * <p>Description:根据时间查询班次信息及前五班后五班的信息</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/6/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Data
@ApiModel(description = "根据时间查询班次信息及前五班后五班的信息")
public class ShiftInfoRelatedDTO {

    /**
     * 当天排班信息
     */
    @ApiModelProperty(value = " 当天排班信息 ")
    private ShiftInfoDTO shiftInfoDTO;

    /**
     * 前五天排班信息
     */
    @ApiModelProperty(value = " 前五天排班信息 ")
    private List<ShiftInfoDTO> before;

    /**
     * 后五天排班信息
     */
    @ApiModelProperty(value = " 后五天排班信息 ")
    private List<ShiftInfoDTO> after;


}
