package com.hvisions.wms.swagger;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>Title: MyTest</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/9/9</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Slf4j
public class MyTest {
    @Test
    public void testLocalTime(){
        LocalDateTime time = LocalDate.now().atStartOfDay();
        log.info(time.toString());
    }
}









