#dev配置
spring:
  datasource:
    #数据库驱动，mysql8.0
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: 123456
    url: **********************************************************************************************
  #redis 地址和端口号
  redis:
    host: ************
    port: 6379
  rabbitmq:
    host: ************
    port: 5672
    username: admin
    password: admin
h-visions:
  #是否添加所有控制器请求记录到log服务
  log:
    enable: false
  #是否开启校验库位
  location:
    valid: true
  quality:
    name: QA
  receipt:
    frozen: true
  supplier:
    name: '医疗科技'
    code: 'MedicalTechnology'
  #服务名称,可以使用中文，日志服务会使用
  service-name: wms
  # 此处配置为audit信息的创建方式。dto 为当请求控制器的时候如果入参为SysDTO可以自动赋值。jpa为使用jpa的audit方式进行实现。
  # 如果是dto的方式，某些接口内部创建的对象就无法自动赋值，但是如果是用jpa就不太适合mybatis的方式。mybatis的audit后续会考虑增加进去。
  #可以使用swagger的接口，使用对应的测试方法，生成api文档，支持markdown和ascii
  swagger:
    # 如果为false或者没有此属性。swagger界面将不会加载
    enable: true
    api-url: http://localhost:${server.port}/v2/api-docs;
    asciidoc-dir: ./build/asciidoc/
    markdown-dir: ./build/markdown/
