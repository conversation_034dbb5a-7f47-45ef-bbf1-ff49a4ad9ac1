# suppress inspection "UnusedProperty" for whole file
# éç¨å¼å¸¸ä¿¡æ¯
SUCCESS=SUCCESS
COLUMN_EXISTS = COLUMN_EXISTS
ERROR_QUANTITY=ERROR_QUANTITY
QUANTITY_NOT_ENOUGH=QUANTITY_NOT_ENOUGH
TRANSFER_BOX_NOT_EMPTY=TRANSFER_BOX_NOT_EMPTY
BOX_NOT_EXIST=BOX_NOT_EXIST
SERVER_ERROR=SERVER_ERROR
JSON_PARSE_ERROR=JSON_PARSE_ERROR
ILLEGAL_STRING=ILLEGAL_STRING
NULL_RESULT=NULL_RESULT
VIOLATE_INTEGRITY=VIOLATE_INTEGRITY
IMPORT_FILE_NO_SUPPORT=IMPORT_FILE_NO_SUPPORT
IMPORT_SHEET_IS_NULL=IMPORT_SHEET_IS_NULL
ENTITY_PROPERTY_NOT_SUPPORT=ENTITY_PROPERTY_NOT_SUPPORT
SAVE_SHOULD_NO_IDENTITY=SAVE_SHOULD_NO_IDENTITY
UPDATE_SHOULD_HAVE_IDENTITY=UPDATE_SHOULD_HAVE_IDENTITY
CONST_VIOLATE=CONST_VIOLATE
NO_SUCH_ELEMENT=NO_SUCH_ELEMENT
ENTITY_NOT_EXISTS=ENTITY_NOT_EXISTS
DATA_INTEGRITY_VIOLATION=DATA_INTEGRITY_VIOLATION
COLUMN_PATTERN_ILLEGAL=COLUMN_PATTERN_ILLEGAL
COLUMN_EXISTS_VALUE=COLUMN_EXISTS_VALUE
#èªå®ä¹å¼å¸¸ä¿¡æ¯
DEMO_EXCEPTION_ENUM=DEMO_EXCEPTION_ENUM
LOCATION_FROZEN=location_frozen
IS_FINISH=IS_FINISH
NOT_FIND_QUALITY=NOT_FIND_QUALITY
NOT_FIND_SHELF=NOT_FIND_SHELF
NOT_OPERATION=NOT_OPERATION
LOCATION_AND_NUM_HAVE_ONE=LOCATION_AND_NUM_HAVE_ONE
QUANTITY_NOT_CREATE=QUANTITY_NOT_CREATE
ALREADY_SHELF_NOT_CREATE=ALREADY_SHELF_NOT_CREATE
MATERIAL_NOT_QUALIFIED=MATERIAL_NOT_QUALIFIED
ALREADY_SHELF=ALREADY_SHELF
DELETE_ERROR=DELETE_ERROR
UPDATE_ERROR=UPDATE_ERROR
SHELF_DELETE_ERROR=SHELF_DELETE_ERROR
LINE_NOT_ALL_FINISH=LINE_NOT_ALL_FINISH
IS_NOT_OPERATION = IS_NOT_OPERATION
IS_NOT_FINISH = IS_NOT_FINISH
IS_NOT_DELETE = IS_NOT_DELETE
IS_NOT_UPDATE = IS_NOT_UPDATE
OPERATION_NOT_CREATE = OPERATION_NOT_CREATE
OPERATION_NOT_UPDATE = OPERATION_NOT_UPDATE
STATE_NOT_UPDATE = STATE_NOT_UPDATE
DELIVER_FAIL= DELIVER_FAIL
COUNT_OVER_LIMIT=COUNT_OVER_LIMIT
NOT_OPERATION_DELETE=NOT_OPERATION_DELETE
ACTUAL_COUNT_NOT_ZERO=ACTUAL_COUNT_NOT_ZERO
FIND_TAKING_ERROR=FIND_TAKING_ERROR
NOT_FINISH_QUANTITY=NOT_FINISH_QUANTITY
CHOOSE_MATERIAL=CHOOSE_MATERIAL
RECEIPT_IS_FINISH=RECEIPT_IS_FINISH
NOT_FINISH_SHELF=NOT_FINISH_SHELF
RECEIPT_IS_FINISH_NOT_IN_STOCK=RECEIPT_IS_FINISH_NOT_IN_STOCK
OPERATION_FAIL =OPERATION_FAIL
PICK_FAIL = PICK_FAIL
APPROVE_NOT_FINISH = APPROVE_NOT_FINISH
APPROVED_NOT_DELETE = APPROVED_NOT_DELETE
IS_NOT_MATERIAL = IS_NOT_MATERIAL
PICK_FINISH = PICK_FINISH
PICKING = PICKING
PICK_NOT_START = PICK_NOT_START
STOCK_FINISH = STOCK_FINISH
FINISH_STOCK_TAKING=FINISH_STOCK_TAKING
ADJUST_ORDER_NOT_EXIST = ADJUST_ORDER_NOT_EXIST
DELIVER_ORDER_NOT_EXIST = DELIVER_ORDER_NOT_EXIST
QUANTITY_IS_FINISH=QUANTITY_IS_FINISH
STOCK_ORDER_NOT_START=STOCK_ORDER_NOT_START
STOCK_NOT_FINISH = STOCK_NOT_FINISH
SHELF_IN=SHELF_IN
SHELF_QUANTITY_NOT=SHELF_QUANTITY_NOT
STOCK_DELETE_ERROR=STOCK_DELETE_ERROR
MATERIAL_IS_EXIST = MATERIAL_IS_EXIST
OUT_ORDER_DELETE_FAIL= PICKING_NOT_DELETE
DELIVER_LINE_NOT_EXIT = DELIVER_LINE_NOT_EXIT
DELIVER_NOT_OPERATION = DELIVER_NOT_OPERATION
ADJUST_LIVE_NOT_EXIST = ADJUST_LIVE_NOT_EXIST
ADJUST_NOT_OPERATION = ADJUST_NOT_OPERATION
QUANTITY_NOT_ZERO=QUANTITY_NOT_ZERO
MATERIAL_SHElF=MATERIAL_SHElF
RECEIPT_FINISH=RECEIPT_FINISH
QUANTITY_FINISH=QUANTITY_FINISH
QUANTITY_NOT_ILLEGAL= QUANTITY_NOT_ILLEGAL
OUT_ODER_NOT_EXIT = OUT_ODER_NOT_EXIT
APPROVE_FAIL= APPROVE_FAIL
CREATE_NOT_APPROVE = CREATE_NOT_APPROVE
ADD_NOT_APPROVE = ADD_NOT_APPROV
STOCK_FAIL=Issue failed. The issue quantity is out of range
NOT_SHELF_MATERIAL=There are no materials to be put on the shelf
DELETE_NOT_APPROVE=Only unapproved issue documents can be operated
RECEIPT_IS_NOT_FIND=The material information of the corresponding receipt is not found in the shelf information\
NOT_HAVE_RULE=Storage rule not configured
SET_MIN_UNIT=The minimum unit for putting on the shelf is not set
DIVISION_NOT_ZERO=The minimum number of units on the shelf cannot be 0
OUT_LINE_NOT_EXIT=Approval failed, no new issue doc details were created
DELIVER_NOT_CREATE=The migration type is not supported
DELETE_ADJUST_FAIL=Deletion failed, inventory adjustment completed
CONFIRM_ADJUST_FAIL=Failed to submit, inventory adjustment completed
UPDATE_ADJUST_FAIL=Inventory adjustment has been completed. Operation is not allowed
FROM_STOCK_NOT_EXIST=Source location has no inventory
DELIVER_FINISHED=The stock transfer order has been completed
OCCUPY_EXISTS=Inventory occupied
STOCK_EMPTY=Inventory is empty
OCCUPY_NOT_FINISH=The library migration has not started and cannot be ended
OUT_ORDER_NOT_EXISTS=Delivery order does not exist, please check
OUT_ORDER_NOT_IN_CREATE_STATE=The work order can only be operated in the new status
NOT_FIND_MATERIAL=Corresponding item not found
DELIVER_IN_PICKING=Picking up materials has started, operation failed
NO_MODIFICATION_OR_DELETION_IS_ALLOWED=Prohibit operation after taking effect
THIS_PURCHASE_ORDER_IS_NOT_AVAILABLE=Purchase order not found
EMPTY_ORDER=The purchase order has no detailed data
OUT_ORDER_IS_APPROVED=The issue document has been approved and cannot be operated
#åºåºå¼å¸¸
STOCK_OUT_ISFINISHED=Issue completed, cannot be deleted
BATCH_NUM_EXISTED=Batch number already exists
STOCK_OUT_DETAIL_IS_EMPTY=The delivery list is empty
STOCK_OUT_REPEATED=The delivery order has been completed and cannot be issued repeatedly
#çç¹
STOCK_PULL_ISEMPTY=The corresponding location data is empty
STOCK_CHOOSE_EMPTY=The material data of the corresponding warehouse location is empty
HAS_PULL_BEFORE=The material of the corresponding location has been processed
HAS_CHOOSE_BEFORE=The material of the corresponding location has been added
STOCK_OUT_MATERIAL_EXISTS=The material has been added to the delivery list
CHECKING=Receipt  operation is not allowed in inventory counting status
NOT_HAVE_RULE=NOT_HAVE_RULE