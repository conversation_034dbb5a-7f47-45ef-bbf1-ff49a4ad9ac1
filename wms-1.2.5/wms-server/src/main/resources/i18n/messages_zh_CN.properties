# suppress inspection "UnusedProperty" for whole file
#éç¨å¼å¸¸
SUCCESS=æå
ERROR_QUANTITY=åºåºæ°éå¼å¸¸å°äºåºå­ï¼å ç¨ä¿¡æ¯åºé/åºå­ä¿¡æ¯å¼å¸¸ï¼è¯·æ£æ¥åºåºæ°æ®ï¼
QUANTITY_NOT_ENOUGH=åºå­æ°éä¸è¶³
TRANSFER_BOX_NOT_EMPTY=ä¸­è½¬ç®±ä¸ä¸ºç©º
BOX_NOT_EXIST=ä¸­è½¬ç®±ä¸å­å¨
SERVER_ERROR=æå¡å¨å¼å¸¸
JSON_PARSE_ERROR=Jsonè§£æéè¯¯
ILLEGAL_STRING=Jsonè§£æåºéï¼è¯·æ£æ¥jsonç»æ
NULL_RESULT=æ¥è¯¢ä¸ºç©º
VIOLATE_INTEGRITY=è¿åéå®ï¼è¯·æ£æ¥æ¯å¦æéå¤æ°æ®
IMPORT_FILE_NO_SUPPORT=æä»¶ç±»åä¸æ¯æ
IMPORT_SHEET_IS_NULL=æä»¶sheetè¡¨ä¸å­å¨
ENTITY_PROPERTY_NOT_SUPPORT=å®ä½å±æ§ä¸æ¯æï¼è¯·æ£æ¥å¯¼å¥æ°æ®
SAVE_SHOULD_NO_IDENTITY=ä¿å­ä¸åºè¯¥æä¸»é®
UPDATE_SHOULD_HAVE_IDENTITY=æ´æ°åºè¯¥æä¸»é®
CONST_VIOLATE=è¿åéå¶ï¼è¯·æ£æ¥æ°æ®åº
NO_SUCH_ELEMENT=æ°æ®æ¥è¯¢ä¸å­å¨
ENTITY_NOT_EXISTS=å¾æ©å±å¯¹è±¡ä¸å­å¨ï¼è¯·æ¥è¯¢åå¯¹è±¡æ¯å¦å­å¨
DATA_INTEGRITY_VIOLATION=æ°æ®å®æ´æ§éªè¯åºé
COLUMN_PATTERN_ILLEGAL=æ©å±åæ ¼å¼éæ³ï¼åªåè®¸æ°å­ï¼ä¸åçº¿ï¼è±æå­ç¬¦
COLUMN_EXISTS_VALUE=è¢«ä½¿ç¨çå­æ®µä¸å¯å é¤
COLUMN_EXISTS = æ©å±å­æ®µå·²ç»å­å¨
#èªå®ä¹å¼å¸¸
DEMO_EXCEPTION_ENUM=ç¤ºä¾å¼å¸¸ç±»å
LOCATION_FROZEN=åºä½è¢«å»ç»
NOT_FIND_QUALITY=æªæ¾å°è¯·éªåä¿¡æ¯
IS_FINISH=è¯·éªåå·²æ£æµå®æ
NOT_FIND_SHELF=æªæ¾å°ä¸æ¶æ¹æ¬¡
NOT_OPERATION=è´¨æ£åæ ¼ç©æä¸åè®¸æä½
LOCATION_AND_NUM_HAVE_ONE=éå»ç»åºä½ä¸æ¹æ¬¡å·ä¸è½åæ¶ä¸ºç©º
QUANTITY_NOT_CREATE=è¯¥è´§ç©å·²å¨è´¨æ£ä¸­
ALREADY_SHELF_NOT_CREATE=è¯¥è´§ç©å·²ä¸æ¶
MATERIAL_NOT_QUALIFIED=è´§ç©æ£éªæªåæ ¼,ä¸è½ä¸æ¶
DELETE_ERROR=å é¤å¤±è´¥,åªææ°å»ºç¶æçæ¶è´§åå¯ä»¥å é¤
UPDATE_ERROR=åªææ°å»ºç¶æçæ¶è´§åå¯ä»¥ä¿®æ¹
SHELF_DELETE_ERROR=å·²ä¸æ¶ä¸åè®¸å é¤
LINE_NOT_ALL_FINISH=çç¹æç»æªå¨é¨å®æ
IS_NOT_OPERATION=å·²ç§»åºä¸åè®¸éç½®
IS_NOT_FINISH=åºåºæªå®æä¸åè®¸ä¿®æ¹
IS_NOT_DELETE=å·²ç§»åºä¸åè®¸å é¤
IS_NOT_UPDATE=å·²ç§»åºä¸åè®¸æä½
OPERATION_NOT_CREATE=ä¸å¡ç±»åä¸æ¯æ
OPERATION_NOT_UPDATE=åºå­åå¹¶æç»ä¸åè®¸ä¿®æ¹
STATE_NOT_UPDATE=åªææ°å»ºç¶æçç§»åºåå¯ä»¥æä½æç»
DELIVER_FAIL=ç§»åºå¤±è´¥
COUNT_OVER_LIMIT=ä¸æ¶æ°éè¶åºæ»é
ACTUAL_COUNT_NOT_ZERO=è´§ç©å®éæ°éåºå¤§äº0
NOT_OPERATION_DELETE=åªææ°å»ºè´§ç©ä¿¡æ¯å¯ä»¥å é¤
FIND_TAKING_ERROR=çç¹åä¸å­å¨
NOT_FINISH_QUANTITY=è´¨æ£å°æªå®æ
QUANTITY_FINISH=è´¨æ£å·²å®æ
CHOOSE_MATERIAL=è¯·éæ©è¦è´¨æ£çè´§ç©
RECEIPT_IS_FINISH=è´§ç©å·²å®æä¸æ¶ï¼æ æ³éç½®ä¸æ¶ä¿¡æ¯
NOT_FINISH_SHELF=æè´§ç©æªå®æä¸æ¶
RECEIPT_FINISH=æ¶è´§åå·²å®æ
MATERIAL_SHElF=è´§ç©å·²ç»å¨é¨å¨ä¸æ¶ä¸­ï¼æ éåæ¬¡ä¸æ¶
RECEIPT_IS_FINISH_NOT_IN_STOCK=æ¶è´§åè´§ç©å·²ä¸æ¶å¥åºã
OPERATION_FAIL=åºåºåç¶æä¸åè®¸æä½
PICK_FAIL=è¯·éæ©ææç©æåºåº
APPROVE_NOT_FINISH=å®¡æ¹æªå®æ
APPROVED_NOT_DELETE=å®¡æ¹å®æä¸åè®¸å é¤
IS_NOT_MATERIAL=è¯·éæ©ç©æ
PICK_FINISH=æ¡æå·²å®æ
PICKING=å·²ç»å¼å§æ¡æ
PICK_NOT_START=æªå¼å§æ¡æ
STOCK_FINISH=åºåºå·²å®æ
FINISH_STOCK_TAKING=çç¹åå·²çç¹
QUANTITY_IS_FINISH=è¯·éªåå·²å®æ
STOCK_ORDER_NOT_START=çç¹åæªå¼å§çç¹
ADJUST_ORDER_NOT_EXIST=åºå­è°æ´åä¸å­å¨
DELIVER_ORDER_NOT_EXIST=ç§»åºåä¸å­å¨
STOCK_NOT_FINISH=æªå®å¨åºåº
SHELF_IN=å·²å¨ä¸æ¶ä¸­ï¼ä¸åè®¸æ°å¢æ¶è´§ç©æ
SHELF_QUANTITY_NOT=è´§ç©æªå®å¨ä¸æ¶
STOCK_DELETE_ERROR=åªææ°å»ºç¶æçç¹åå¯ä»¥å é¤
MATERIAL_IS_EXIST=åºåºåå·²éæ©åºåºç©æï¼è¯·éç½®ååèªå¨æ¥æ¾åºå­
QUANTITY_NOT_ZERO=ä¸æ¶æ°éå¿é¡»å¤§äº0
OUT_ORDER_DELETE_FAIL=å é¤å¤±è´¥ï¼åªææ°å»ºç¶æåºåºåå¯ä»¥å é¤
DELIVER_LINE_NOT_EXIT=è¯·éæ©ç§»åºæç»åç§»åº
DELIVER_NOT_OPERATION=æ°å»ºç¶æçç§»åºåå¯ä»¥æ°å»ºæç»
ADJUST_LIVE_NOT_EXIST=åºå­è°æ´æç»ä¸å­å¨
ADJUST_NOT_OPERATION=æ°å»ºå¤±è´¥ï¼åªææ°å»ºç¶æçåºå­è°æ´åå¯ä»¥æ°å»ºæç»
QUANTITY_NOT_ILLEGAL=ç³è¯·åºåºæ°éå¿é¡»å¤§äº0
OUT_ODER_NOT_EXIT=åºåºåä¸å­å¨
APPROVE_FAIL=å®¡æ¹ä¸éè¿ï¼ä¸åè®¸æ¡æ
CREATE_NOT_APPROVE=åªææªå®¡æ¹çåºåºåå¯ä»¥æä½
ADD_NOT_APPROVE=åªææªå®¡æ¹çåºåºåå¯ä»¥æ·»å ç©æ
STOCK_FAIL=åºåºå¤±è´¥ï¼åºåºæ°éè¶åºèå´
NOT_SHELF_MATERIAL=æ²¡æå¾ä¸æ¶ç©æ
DELETE_NOT_APPROVE=åªææªå®¡æ¹åºåºåçå¯ä»¥æä½
RECEIPT_IS_NOT_FIND=ä¸æ¶ä¿¡æ¯æªæ¾å°å¯¹åºçæ¶è´§åç©æä¿¡æ¯
NOT_HAVE_RULE=æªéç½®å¨ä½è§å
SET_MIN_UNIT=æªè®¾ç½®ä¸æ¶æå°åä½
DIVISION_NOT_ZERO=æå°ä¸æ¶åä½æ°éä¸è½ä¸º0
OUT_LINE_NOT_EXIT=å®¡æ¹å¤±è´¥ï¼æªæ°å»ºåºåºåè¯¦æ
DELIVER_NOT_CREATE=ç§»åºç±»åä¸æ¯æ
DELETE_ADJUST_FAIL=å é¤å¤±è´¥ï¼åºå­è°æ´å·²å®æ
CONFIRM_ADJUST_FAIL=æäº¤å¤±è´¥ï¼åºå­è°æ´å·²å®æ
UPDATE_ADJUST_FAIL=åºå­è°æ´å·²å®æ,ä¸åè®¸æä½
FROM_STOCK_NOT_EXIST=æºåºä½æ²¡æåºå­
DELIVER_FINISHED=ç§»åºåå·²ç»ç»æ
OCCUPY_EXISTS=åºå­å·²ç»å ç¨
ALREADY_SHELF=å·²ç»ä¸æ¶
STOCK_EMPTY=åºå­ä¸ºç©º
OCCUPY_NOT_FINISH=æªå¼å§ç§»åºï¼ä¸è½ç»æ
OUT_ORDER_NOT_EXISTS=åºåºåä¸å­å¨ï¼è¯·æ£æ¥
OUT_ORDER_NOT_IN_CREATE_STATE=å·¥åéè¦å¨æ°å»ºç¶æä¸æè½æä½
NOT_FIND_MATERIAL=æªæ¾å°å¯¹åºç©æ
DELIVER_IN_PICKING=æ¡æå·²ç»å¼å§ï¼æä½å¤±è´¥
NO_MODIFICATION_OR_DELETION_IS_ALLOWED=çæåç¦æ­¢æä½
THIS_PURCHASE_ORDER_IS_NOT_AVAILABLE=æ¥è¯¢ä¸å°éè´­å
EMPTY_ORDER=éè´­åæ æç»æ°æ®
OUT_ORDER_IS_APPROVED=åºåºåå·²ç»å®¡æ¹ä¸åè®¸æä½
#åºåºå¼å¸¸
STOCK_OUT_ISFINISHED=åºåºå®æ,ä¸åè®¸å é¤
BATCH_NUM_EXISTED=æ¹æ¬¡å·å·²ç»å­å¨
STOCK_OUT_DETAIL_IS_EMPTY=åºåºæ¸åä¸ºç©º
STOCK_OUT_REPEATED=åºåºåå·²ç»å®æ,ä¸å¯éå¤åºåº
STOCK_OUT_MATERIAL_EXISTS=åºåºæ¸åå·²æ·»å è¿è¯¥ç©æ
#çç¹
STOCK_PULL_ISEMPTY=å¯¹åºåºä½æ æ°æ®
STOCK_CHOOSE_EMPTY=å¯¹åºåºä½ç©ææ°æ®ä¸ºç©º
HAS_PULL_BEFORE=å¯¹åºåºä½çç©æå·²è¢«å¤ç
HAS_CHOOSE_BEFORE=å¯¹åºåºä½çç©æå·²è¢«æ·»å 
CHECKING=åºå­çç¹ç¶æä¸åè®¸åºå¥åºæä½