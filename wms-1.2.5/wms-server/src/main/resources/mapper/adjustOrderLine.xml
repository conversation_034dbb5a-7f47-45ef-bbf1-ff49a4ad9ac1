<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.wms.dao.AdjustOrderLineMapper">
    <resultMap id="baseLine" type="com.hvisions.wms.dto.adjust.AdjustOrderLineDTO"></resultMap>

    <select id="getLine" resultMap="baseLine" parameterType="int" databaseId="mysql">
        SELECT h.deliver_order_id,
               h.state,
               h.location_id,
               h.material_id,
               h.quantity,
               h.description,
               l.code                                                      AS locationCode,
               l.name                                                      AS locationName,
               hb.description                                              AS unitName,
               m.material_name,
               m.material_code,
               h.material_batch_num,
               h.id,
               h.create_time,
               case when (sk.quantity is null) then 0 else sk.quantity end AS origin
        FROM hv_wms_adjust_order_line h
                 LEFT JOIN hv_wms_stocks sk on h.material_id = sk.material_id and h.location_id = sk.location_id and
                                               h.material_batch_num = sk.material_batch_num
                 LEFT JOIN hiper_base.hv_bm_material m ON m.id = h.material_id
                 LEFT JOIN hv_wms_wares_location l ON l.id = h.location_id
                 LEFT JOIN hiper_base.hv_bm_unit hb ON m.uom = hb.id
        WHERE h.deliver_order_id = #{id}
    </select>
    <select id="getLine" resultMap="baseLine" parameterType="int" databaseId="sqlserver">
        SELECT h.deliver_order_id,
               h.state,
               h.location_id,
               h.material_id,
               h.quantity,
               h.description,
               l.code                                                      AS locationCode,
               l.name                                                      AS locationName,
               hb.description                                              AS unitName,
               m.material_name,
               m.material_code,
               h.material_batch_num,
               h.id,
               h.create_time,
               case when (sk.quantity is null) then 0 else sk.quantity end AS origin
        FROM hv_wms_adjust_order_line h
                 LEFT JOIN hv_wms_stocks sk on h.material_id = sk.material_id and h.location_id = sk.location_id and
                                               h.material_batch_num = sk.material_batch_num
                 LEFT JOIN hiper_base.dbo.hv_bm_material m ON m.id = h.material_id
                 LEFT JOIN hv_wms_wares_location l ON l.id = h.location_id
                 LEFT JOIN hiper_base.dbo.hv_bm_unit hb ON m.uom = hb.id
        WHERE h.deliver_order_id = #{id}
    </select>
</mapper>