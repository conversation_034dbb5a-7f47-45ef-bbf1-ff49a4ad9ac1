<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.wms.dao.ReportMapper">
    <select id="summary" resultType="com.hvisions.wms.dto.report.ReportSummaryDTO">
        select t1.num as purchaseInWarehouseCount,
               t2.num as inWarehouseCount,
               t3.num as outWarehouseCount,
               t4.num as adjustCount,
               t5.num as deliverOrderCount,
               t6.num as frozenLocationCount,
               t7.num as stockCheckCount
        from (select count(*) as num
              from hv_wms_in_warehouse_order_header
              where actual_in_time > #{time}
                and state = 2
                and type = 1) t1
                 join (select count(*) as num
                       from hv_wms_in_warehouse_order_header
                       where actual_in_time > #{time}
                         and type = 2
                         and state = 2) t2
                 join (select count(*) as num from hv_wms_stock_outing_header where out_stock_time > #{time}
                                                                                and status = 1) t3
                 join (select count(*) as num from hv_wms_adjust_order where complete_time > #{time}
                                                                         and state = '完成') t4
                 join (select count(*) as num from hv_wms_deliver_order where complete_time > #{time}
                                                                          and state = '完成') t5
                 join (select count(distinct (location_id)) as num
                       from hv_wms_frozen_material
                       where location_id is not null) t6
                 join (select count(*)as num
                       from hv_wms_stock_check_header
                       where stock_check_time > #{time}
                         and check_status = 1) t7

    </select>
</mapper>