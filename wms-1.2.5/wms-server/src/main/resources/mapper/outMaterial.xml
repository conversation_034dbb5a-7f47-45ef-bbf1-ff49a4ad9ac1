<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.wms.dao.OutOrderLineMapper">

    <resultMap id="outMaterial" type="com.hvisions.wms.dto.outstock.OutLineDTO">
        <result property="id" column="hid"/>
        <result property="quantity" column="hQuantity"/>
        <result property="materialName" column="material_name"/>
        <result property="materialCode" column="material_code"/>
        <result property="materialId" column="material_id"/>
        <result property="orderId" column="order_id"/>
        <result property="unitName" column="unitName"/>
        <result property= "tolerance" column="tolerance"></result>
        <collection property="outMaterialList" column = "outMaterialList" ofType="com.hvisions.wms.dto.outstock.OutMaterialDTO">
            <result property="id" column="mid"/>
            <result property="quantity" column="mQuantity"/>
            <result property="materialName" column="material_name"/>
            <result property="materialCode" column="material_code"/>
            <result property="materialId" column="material_id"/>
            <result property="locationName" column="locationName"/>
            <result property="locationCode" column="locationCode"/>
            <result property="locationId" column="location_id"/>
            <result property="materialBatchNum" column="material_batch_num"/>
            <result property="preMaterialBatchNum" column="pre_material_batch_num"/>
            <result property="orderLineId" column="order_line_id"/>
            <result property="state" column="mState"/>
            <result property="supplierId" column="supplier_id"/>
        </collection>
    </resultMap>
    <select id="getOutMaterial" resultMap="outMaterial" parameterType="Integer" databaseId="mysql">
        select
        h.quantity as hQuantity,
        hm.state as mState,
        h.id as hid,
        h.order_id,
        h.material_id,
        m.material_name,
        m.material_code,
        hm.material_batch_num,
        hm.pre_material_batch_num,
        hm.quantity as mQuantity,
        hm.id as mid,
        hm.location_id,
        l.name as locationName,
        l.code as locationCode,
        hm.order_line_id,
        me.tolerance,
        hb.description as unitName,
        hm.supplier_id
        from hv_wms_order_line h
        left join hiper_base.hv_bm_material m on m.id = h.material_id
        left join hv_wms_out_material hm on hm.order_line_id = h.id
        left join hv_wms_wares_location l on l.id = hm.location_id
        left join hiper_base.hv_bm_unit hb on m.uom = hb.id
        left join hiper_base.hv_bm_material_extend me on me.material_id = h.material_id
        <where>
            h.order_id = #{orderId}
        </where>
    </select>
    <select id="getOutMaterial" resultMap="outMaterial" parameterType="Integer" databaseId="sqlserver">
        select
            h.quantity as hQuantity,
            hm.state as mState,
            h.id as hid,
            h.order_id,
            h.material_id,
            m.material_name,
            m.material_code,
            hm.material_batch_num,
            hm.pre_material_batch_num,
            hm.quantity as mQuantity,
            hm.id as mid,
            hm.location_id,
            l.name as locationName,
            l.code as locationCode,
            hm.order_line_id,
            me.tolerance,
            hb.description as unitName,
            hm.supplier_id
        from hv_wms_order_line h
        left join hiper_base.dbo.hv_bm_material m on m.id = h.material_id
        left join hv_wms_out_material hm on hm.order_line_id = h.id
        left join hv_wms_wares_location l on l.id = hm.location_id
        left join hiper_base.dbo.hv_bm_unit hb on m.uom = hb.id
        left join hiper_base.dbo.hv_bm_material_extend me on me.material_id = h.material_id
        <where>
            h.order_id = #{orderId}
        </where>
    </select>

    <select id="getMaterial" resultType="com.hvisions.wms.dto.outstock.OutMaterialDTO" parameterType="Integer" databaseId="mysql">
        select
        hm.state,
        hm.material_id,
        m.material_name,
        m.material_code,
        hm.material_batch_num,
        hm.pre_material_batch_num,
        hm.quantity,
        hm.id,
        hm.location_id,
        l.name as locationName,
        l.code as locationCode,
        hm.order_line_id
        from hv_wms_order_line h
        left join hiper_base.hv_bm_material m on m.id = h.material_id
        left join hv_wms_out_material hm on hm.order_line_id = h.id
        left join hv_wms_wares_location l on l.id = hm.location_id
        <where>
            h.order_id = #{orderId}
        </where>
    </select>
    <select id="getMaterial" resultType="com.hvisions.wms.dto.outstock.OutMaterialDTO" parameterType="Integer" databaseId="sqlserver">
        select
        hm.state,
        hm.material_id,
        m.material_name,
        m.material_code,
        hm.material_batch_num,
        hm.pre_material_batch_num,
        hm.quantity,
        hm.id,
        hm.location_id,
        l.name as locationName,
        l.code as locationCode,
        hm.order_line_id
        from hv_wms_order_line h
        left join hiper_base.dbo.hv_bm_material m on m.id = h.material_id
        left join hv_wms_out_material hm on hm.order_line_id = h.id
        left join hv_wms_wares_location l on l.id = hm.location_id
        <where>
            h.order_id = #{orderId}
        </where>
    </select>

</mapper>