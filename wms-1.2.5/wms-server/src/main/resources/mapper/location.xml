<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.wms.dao.WaresLocationMapper">

    <resultMap id="location" type="com.hvisions.wms.dto.location.WaresLocationDTO">


    </resultMap>

    <select id="getLocationByQuery" resultMap="location"
            parameterType="com.hvisions.wms.dto.location.WaresLocationQueryDTO">
        select
        h.*
        from hv_wms_wares_location h

        <where>
            <if test="dto.code != null and dto.code != &apos;&apos;">
                and h.code like concat('%',#{dto.code},'%')
            </if>
            <if test="dto.description != null and dto.description != &apos;&apos; ">
                and h.description like concat('%', #{dto.description},'%')
            </if>
            <if test="dto.parentId != null">
                and h.parent_id = #{dto.parentId}
            </if>
            <if test="dto.storeAnything !=null">
                <if test="dto.storeAnything ==true">
                    and h.store_anything = 1
                </if>
                <if test=" dto.storeAnything ==false">
                    and h.store_anything = 0
                </if>
            </if>

        </where>
    </select>

    <resultMap id="ruleMaterial" type="com.hvisions.wms.dto.location.MaterialStockQuantityDTO">
    </resultMap>

    <select id="getRuleAndQuantityByMaterialId" resultMap="ruleMaterial" parameterType="java.lang.Integer">
        SELECT t1.material_id, t1.location_id, IFNULL(sum(t2.quantity), 0)AS quantity, t1.store_max, t1.store_min
        FROM hv_wms_wares_location_rule t1
                 LEFT JOIN hv_wms_stocks t2 ON t1.material_id = t2.material_id
                                                   AND t1.location_id = t2.location_id
        WHERE t1.material_id = #{materialId}
        GROUP BY t1.material_id,
                 t1.location_id,
                 t1.store_max,
                 t1.store_min
    </select>

    <select id="findById" resultType="com.hvisions.wms.dto.location.WaresLocationDTO">
        select
        `code`,
        `name`
        from hv_wms_wares_location
        where id = #{id}
    </select>

    <select id="getWareLocationInfoByLocationCode"
            resultType="com.hvisions.wms.dto.location.WareLocationInfoDTO">
        SELECT
            l.CODE AS code,
            l.NAME AS name,
            l.warehouse_type_code AS warehouseTypeCode,
            mp.id AS materialPointId,
            mp.material_point_code AS materialPointCode,
            mp.material_point_name AS materialPointName
        FROM
            hv_wms_wares_location_material_point mp
                LEFT JOIN hv_wms_wares_location l ON mp.location_id = l.id
        WHERE
            mp.material_point_code = #{materialPointCode}
    </select>


</mapper>