<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.wms.dao.FactoryLesCarMapper">

    <select id="getPage" resultType="com.hvisions.wms.dto.factoryLesCar.FactoryLesCarDTO">
        SELECT
            id,
            task_no AS taskNo,
            apply_start_time AS applyStartTime,
            apply_end_time AS applyEndTime,
            car_type AS carType,
            car_work_type AS carWorkType,
            work_content AS workContent,
            apply_car_dept AS applyCarDept,
            project_no AS projectNo,
            pallet_no AS palletNo,
            start_point AS startPoint,
            end_point AS endPoint,
            deliver_man_no AS deliverManNo,
            receive_man_no AS receiveManNo,
            apply_man_no AS applyManNo,
            segmentation_code AS segmentationCode,
            remark,
            status
        FROM hv_wms_factory_les_car
        <where>
            <if test="query.taskNo != null and query.taskNo != ''">
                AND task_no LIKE CONCAT('%',#{query.taskNo},'%')
            </if>
            <if test="query.carType != null and query.carType != ''">
                AND car_type = #{query.carType}
            </if>
            <if test="query.carWorkType != null and query.carWorkType != ''">
                AND car_work_type = #{query.carWorkType}
            </if>
            <if test="query.workContent != null and query.workContent != ''">
                AND work_content LIKE CONCAT('%',#{query.workContent},'%')
            </if>
            <if test="query.projectNo != null and query.projectNo != ''">
                AND project_no LIKE CONCAT('%',#{query.projectNo},'%')
            </if>
            <if test="query.startPoint != null and query.startPoint != ''">
                AND start_point = #{query.startPoint}
            </if>
            <if test="query.endPoint != null and query.endPoint != ''">
                AND end_point = #{query.endPoint}
            </if>
            <if test="query.applyCarDept != null and query.applyCarDept != ''">
                AND apply_car_dept LIKE CONCAT('%',#{query.applyCarDept},'%')
            </if>
        </where>
    </select>
    <select id="getPageMaterial" resultType="com.hvisions.hiperbase.bom.dto.FrameMaterialItemDTO">
        SELECT
            bm.ID,
            bm.material_code AS materialCode,
            bm.material_name AS materialName,
            hws.quantity
        FROM
            hiper_base.hv_bm_material bm
                INNER JOIN wms.hv_wms_stocks hws
                           ON bm.id = hws.material_id
        WHERE
            hws.frame_code = #{query.frameCode}
    </select>

</mapper>