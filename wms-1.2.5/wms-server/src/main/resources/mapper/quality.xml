<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.wms.dao.QualityMapper">


    <resultMap id="qm" type="com.hvisions.wms.dto.quality.QualityControlDTO">
        <result property="materialName" column="material_name"/>
        <result property="materialCode" column="material_code"/>
        <result property="eigenvalue" column="eigenvalue"/>
        <result property="unitName" column="unit_name"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="materialBatchNum" column="material_batch_num"/>
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator_id" property="creatorId"/>
        <result column="update_id" property="updaterId"/>
        <result column="site_num" property="siteNum"/>
        <result property="receiptMaterialId" column="receipt_material_id"/>
        <result property="isQualified" column="is_qualified"/>
        <result property="qualityId" column="quality_id"/>
        <collection property="materialControlDTOS" ofType="com.hvisions.wms.dto.quality.MaterialControlDTO"
                    javaType="java.util.ArrayList">
            <result property="qualityControlId" column="quality_control_id"/>
            <result property="receiptMaterialId" column="material_receipt_material_id"/>
            <result property="code" column="code"/>
            <result property="name" column="name"/>
            <result column="material_control_id" property="id"/>
            <result property="controlDesc" column="control_desc"/>
            <result property="isQualified" column="material_is_qualified"/>
        </collection>
    </resultMap>

    <select id="getAllByQualityId" resultMap="qm" parameterType="java.lang.Integer" databaseId="mysql">
        select
               c.*,
               bm.material_name,
               bm.material_code,
               bm.eigenvalue,
               u.description as unit_name,
               s.supplier_name,
               m.material_batch_num,
               ms.id as material_control_id,
               ms.quality_control_id,
               ms.receipt_material_id as material_receipt_material_id,
               ms.code,
               ms.name,
               ms.control_desc,
               ms.is_qualified as material_is_qualified
        from
             hv_wms_quality_control c
                 left join hv_wms_receipt_material m
                     on c.receipt_material_id = m.id
                 left join hiper_base.hv_bm_material bm
                     on m.material_id = bm.id
                 left join hiper_base.hv_bm_unit u
                     on bm.uom =u.id
                 left join hv_wms_receipt re
                     on re.id = m.receipt_id
                 left join framework.sys_supplier s
                     on s.id =re.supplier_id
                 left join hv_wms_material_control ms
                     on ms.quality_control_id = c.id
        where    c.quality_id = #{qualityId}
    </select>
    <select id="getAllByQualityId" resultMap="qm" parameterType="java.lang.Integer" databaseId="sqlserver">
        select
        c.*,
        bm.material_name,
        bm.material_code,
        bm.eigenvalue,
        u.description as unit_name,
        s.supplier_name,
        m.material_batch_num,
        ms.id as material_control_id,
        ms.quality_control_id,
        ms.receipt_material_id as material_receipt_material_id,
        ms.code,
        ms.name,
        ms.control_desc,
        ms.is_qualified as material_is_qualified
        from
        hv_wms_quality_control c
        left join hv_wms_receipt_material m
        on c.receipt_material_id = m.id
        left join hiper_base.dbo.hv_bm_material bm
        on m.material_id = bm.id
        left join hiper_base.dbo.hv_bm_unit u
        on bm.uom =u.id
        left join hv_wms_receipt re
        on re.id = m.receipt_id
        left join framework.dbo.sys_supplier s
        on s.id =re.supplier_id
        left join hv_wms_material_control ms
        on ms.quality_control_id = c.id
        where    c.quality_id = #{qualityId}
    </select>

    <resultMap id="qq" type="com.hvisions.wms.dto.quality.QualityDTO">

    </resultMap>
    <select id="getAllByQuery" resultMap="qq" parameterType="com.hvisions.wms.dto.quality.QualityQueryDTO" databaseId="mysql">
        select
        q.*,
        r.code as receipt_code,
        u.user_name as userName,
        u.description as description
        from
        hv_wms_quality q
        left join hv_wms_receipt r
        on q.receipt_id = r.id
        left join framework.sys_user u
        on q.inspector = u.id
        <where>
            <if test="dto.isQualified != null">
                and q.is_qualified = #{dto.isQualified}
            </if>
            <if test="dto.receiptCode != null">
                and r.code like concat('%', #{dto.receiptCode},'%')
            </if>
            <if test="dto.qualityCode != null">
                and q.quality_code like concat('%',#{dto.qualityCode},'%')
            </if>
            <if test="dto.version != null">
                and q.version = #{dto.version}
            </if>
        </where>
    </select>
    <select id="getAllByQuery" resultMap="qq" parameterType="com.hvisions.wms.dto.quality.QualityQueryDTO" databaseId="sqlserver">
        select
        q.*,
        r.code as receipt_code,
        u.user_name as userName,
        u.description as description
        from
        hv_wms_quality q
        left join hv_wms_receipt r
        on q.receipt_id = r.id
        left join framework.dbo.sys_user u
        on q.inspector = u.id
        <where>
            <if test="dto.isQualified != null">
                and q.is_qualified = #{dto.isQualified}
            </if>
            <if test="dto.receiptCode != null">
                and r.code like concat('%', #{dto.receiptCode},'%')
            </if>
            <if test="dto.qualityCode != null">
                and q.quality_code like concat('%',#{dto.qualityCode},'%')
            </if>
            <if test="dto.version != null">
                and q.version = #{dto.version}
            </if>
        </where>
    </select>

</mapper>