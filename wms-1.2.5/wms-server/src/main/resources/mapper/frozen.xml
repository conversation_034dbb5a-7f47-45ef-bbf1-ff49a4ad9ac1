<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.wms.dao.FrozenMaterialMapper">

    <resultMap id="frozenMaterial" type="com.hvisions.wms.dto.stock.FrozenMaterialDTO">
    </resultMap>


    <select id="getAllFrozenMaterialByQuery" resultMap="frozenMaterial"
            parameterType="com.hvisions.wms.dto.stock.FrozenMaterialQueryDTO" databaseId="mysql">
        select
        f.* ,
        l.code as location_code,
        l.name as location_name,
        u.user_name
        from
        hv_wms_frozen_material f
        left join hv_wms_wares_location l on f.location_id = l.id
        left join framework.sys_user u on f.creator_id = u.id
        <where>
            <if test="dto.orderCode !=null and dto.orderCode != &apos;&apos; ">
                and f.order_code like concat('%',#{dto.orderCode},'%')
            </if>
            <if test="dto.operation !=null and dto.operation != &apos;&apos;">
                and f.operation like concat('%',#{dto.operation},'%')
            </if>
            <if test="dto.freezeBatchNumber !=null and dto.freezeBatchNumber != &apos;&apos;">
                and f.freeze_batch_number like concat('%',#{dto.freezeBatchNumber},'%')
            </if>
            <if test=" dto.locationIds != null and dto.locationIds.size > 0 ">
                <foreach collection="dto.locationIds" index="index" item="item" open="and f.location_id in("
                         separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getAllFrozenMaterialByQuery" resultMap="frozenMaterial"
            parameterType="com.hvisions.wms.dto.stock.FrozenMaterialQueryDTO" databaseId="sqlserver">
        select
        f.* ,
        l.code as location_code,
        l.name as location_name,
        u.user_name
        from
        hv_wms_frozen_material f
        left join hv_wms_wares_location l on f.location_id = l.id
        left join framework.dbo.sys_user u on f.creator_id = u.id
        <where>
            <if test="dto.orderCode !=null and dto.orderCode != &apos;&apos; ">
                and f.order_code like concat('%',#{dto.orderCode},'%')
            </if>
            <if test="dto.operation !=null and dto.operation != &apos;&apos;">
                and f.operation like concat('%',#{dto.operation},'%')
            </if>
            <if test="dto.freezeBatchNumber !=null and dto.freezeBatchNumber != &apos;&apos;">
                and f.freeze_batch_number like concat('%',#{dto.freezeBatchNumber},'%')
            </if>
            <if test=" dto.locationIds != null and dto.locationIds.size > 0 ">
                <foreach collection="dto.locationIds" index="index" item="item" open="and f.location_id in("
                         separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>