<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.wms.dao.HistoryMapper">

    <resultMap id="history" type="com.hvisions.wms.dto.history.HistoryDTO">

    </resultMap>

    <select id="getStockHistory" resultMap="history" parameterType="com.hvisions.wms.dto.history.QueryHistory"
            databaseId="mysql">
        SELECT
        h.id as id ,
        h.create_time,
        h.operation,
        h.order_code,
        h.description,
        h.quantity ,
        h.order_code,
        h.location_id,
        h.pre_material_batch_num,
        h.origin_quantity,
        h.material_batch_num,
        m.id as material_id,
        m.material_name ,
        l.name as locationName,
        m.material_code ,
        l.code as locationCode,
        h.id,
        h.creator_id,
        u.user_name,
        hb.description as unitName
        FROM
        hv_wms_history h
        left join hiper_base.hv_bm_material m on m.id = h.material_id
        left join hv_wms_wares_location l on l.id = h.location_id
        left join framework.sys_user u on h.creator_id = u.id
        left join hiper_base.hv_bm_unit hb on m.uom = hb.id
        <where>
            <if test="dto.locationIds.size >0 ">
                <foreach collection="dto.locationIds" index="index" item="item" open="and h.location_id in("
                         separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.materialId != null  ">
                AND m.id = #{dto.materialId}
            </if>
            <if test="dto.materialNumber != null and dto.materialNumber != &apos;&apos; ">
                AND m.material_code like concat('%',#{dto.materialNumber},'%')
            </if>
            <if test="dto.locationCode != null and dto.locationCode != &apos;&apos;">
                AND l.code like concat('%',#{dto.locationCode},'%')
            </if>
            <if test="dto.operation != null and dto.operation != &apos;&apos;">
                AND h.operation like concat('%',#{dto.operation},'%')
            </if>
            <if test="dto.orderCode != null and dto.orderCode != &apos;&apos;">
                AND h.order_code like concat('%',#{dto.orderCode},'%')
            </if>
            <if test="dto.beginDate != null">
                AND h.create_time <![CDATA[ >= ]]> #{dto.beginDate}
            </if>
            <if test="dto.endDate != null ">
                AND h.create_time <![CDATA[ <= ]]> #{dto.endDate}
            </if>
        </where>
    </select>
    <select id="getStockHistory" resultMap="history" parameterType="com.hvisions.wms.dto.history.QueryHistory"
            databaseId="sqlserver">
        SELECT
        h.id as id ,
        h.create_time,
        h.operation,
        h.order_code,
        h.description,
        h.quantity ,
        h.order_code,
        h.location_id,
        h.pre_material_batch_num,
        h.material_batch_num,
        h.origin_quantity,
        m.id as material_id,
        m.material_name ,
        l.name as locationName,
        m.material_code ,
        l.code as locationCode,
        h.id,
        h.creator_id,
        u.user_name,
        hb.description as unitName
        FROM
        hv_wms_history h
        left join hiper_base.dbo.hv_bm_material m on m.id = h.material_id
        left join hv_wms_wares_location l on l.id = h.location_id
        left join framework.dbo.sys_user u on h.creator_id = u.id
        left join hiper_base.dbo.hv_bm_unit hb on m.uom = hb.id
        <where>
            <if test="dto.locationIds.size >0 ">
                <foreach collection="dto.locationIds" index="index" item="item" open="and h.location_id in("
                         separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.materialId != null  ">
                AND m.id = #{dto.materialId}
            </if>
            <if test="dto.materialNumber != null and dto.materialNumber != &apos;&apos; ">
                AND m.material_code like concat('%',#{dto.materialNumber},'%')
            </if>
            <if test="dto.locationCode != null and dto.locationCode != &apos;&apos;">
                AND l.code like concat('%',#{dto.locationCode},'%')
            </if>
            <if test="dto.operation != null and dto.operation != &apos;&apos;">
                AND h.operation like concat('%',#{dto.operation},'%')
            </if>
            <if test="dto.orderCode != null and dto.orderCode != &apos;&apos;">
                AND h.order_code like concat('%',#{dto.orderCode},'%')
            </if>
            <if test="dto.beginDate != null">
                AND h.create_time <![CDATA[ >= ]]> #{dto.beginDate}
            </if>
            <if test="dto.endDate != null ">
                AND h.create_time <![CDATA[ <= ]]> #{dto.endDate}
            </if>
        </where>
    </select>


</mapper>