<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.wms.dao.TransferBoxMapper">

    <resultMap id="rm1" type="com.hvisions.wms.dto.transfer.TransferBoxDetailDTO">
    </resultMap>

    <select id="getDetail" resultMap="rm1" databaseId="mysql">
        SELECT t1.id,
               t1.material_id,
               t2.material_name,
               t2.material_code,
               t1.material_batch,
               t1.quantity,
               t1.box_id
        FROM hv_wms_transfer_box_detail t1
                 LEFT JOIN hiper_base.hv_bm_material t2 on t1.material_id = t2.id
        WHERE t1.box_id = #{boxId}
    </select>
    <select id="getDetail" resultMap="rm1" databaseId="sqlserver">
        SELECT t1.id,
               t1.material_id,
               t2.material_name,
               t2.material_code,
               t1.material_batch,
               t1.quantity,
               t1.box_id
        FROM hv_wms_transfer_box_detail t1
                 LEFT JOIN hiper_base.dbo.hv_bm_material t2 on t1.material_id = t2.id
        WHERE t1.box_id = #{boxId}
    </select>

</mapper>