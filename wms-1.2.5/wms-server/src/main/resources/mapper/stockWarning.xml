<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.wms.dao.StockWarningMapper">
    <resultMap id="stockWarning" type="com.hvisions.wms.dto.stockWarning.BaseStockWarning"></resultMap>

    <select id="getStockWarning" resultMap="stockWarning" databaseId="mysql">
        SELECT
        hs.quantity,
        hl.store_min,
        hl.store_max,
        m.material_name,
        m.material_code,
        l.code as locationCode,
        l.name as locationName
        FROM
        hv_wms_wares_location_rule hl
        left join (SELECT material_id, sum(quantity) AS quantity,location_id FROM hv_wms_stocks GROUP BY location_id, material_id ) hs ON hs.material_id = hl.material_id and hs.location_id = hl.location_id
        left join hv_wms_wares_location l on l.id = hl.location_id
        left join hiper_base.hv_bm_material m on m.id = hl.material_id
        <where>
            <if test=" locationId.size >0 ">
                <foreach collection="locationId" index="index" item="item" open="and hl.location_id in(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getStockWarning" resultMap="stockWarning" databaseId="sqlserver">
        SELECT
        hs.quantity,
        hl.store_min,
        hl.store_max,
        m.material_name,
        m.material_code,
        l.code as locationCode,
        l.name as locationName
        FROM
        hv_wms_wares_location_rule hl
        left join (SELECT material_id, sum(quantity) AS quantity,location_id FROM hv_wms_stocks GROUP BY location_id, material_id ) hs ON hs.material_id = hl.material_id and hs.location_id = hl.location_id
        left join hv_wms_wares_location l on l.id = hl.location_id
        left join hiper_base.dbo.hv_bm_material m on m.id = hl.material_id
        <where>
            <if test=" locationId.size >0 ">
                <foreach collection="locationId" index="index" item="item" open="and hl.location_id in(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

</mapper>