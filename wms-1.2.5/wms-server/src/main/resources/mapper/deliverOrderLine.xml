<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.wms.dao.DeliverOrderLineMapper">

    <resultMap id="orderLine" type="com.hvisions.wms.dto.deliver.DeliverOrderLineDTO"></resultMap>

	<select id="getLine" resultMap="orderLine" parameterType="int" databaseId="mysql">
		SELECT
			   h.id,
			   h.state,
			   h.quantity,
			   m.material_name,
			   m.material_code,
			   h.material_id,
			   h.deliver_order_id,
			   h.material_batch_num,
			   h.to_location_id,
			   l.name as fromLocationName,
			   c.name as toLocationName,
			   h.description,
			   h.stock_id
		FROM
			 hv_wms_deliver_order_line h
				 LEFT JOIN hiper_base.hv_bm_material m ON m.id = h.material_id
				 LEFT JOIN hv_wms_wares_location l ON l.id = h.from_location_id
				 LEFT JOIN hv_wms_wares_location c ON c.id = h.to_location_id
		WHERE
				h.deliver_order_id = #{id}
	</select>

    <select id="getLine" resultMap="orderLine" parameterType="int" databaseId="sqlserver">
        SELECT
	        h.id,
	        h.state,
	        h.quantity,
	        m.material_name,
	        m.material_code,
	        h.material_id,
	        h.deliver_order_id,
	        h.material_batch_num,
	        h.to_location_id,
	        l.name as fromLocationName,
	        c.name as toLocationName,
	        h.description,
	        h.stock_id
        FROM
	        hv_wms_deliver_order_line h
	        LEFT JOIN hiper_base.dbo.hv_bm_material m ON m.id = h.material_id
	        LEFT JOIN hv_wms_wares_location l ON l.id = h.from_location_id
	        LEFT JOIN hv_wms_wares_location c ON c.id = h.to_location_id
        WHERE
	        h.deliver_order_id = #{id}
    </select>

</mapper>