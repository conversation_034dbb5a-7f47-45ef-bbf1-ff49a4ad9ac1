<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.wms.dao.StockOutLineMapper">
    <select id="findOutingStatusByLineId" resultType="java.lang.Integer">
        select t1.status
        from hv_wms_stock_outing_header t1
                     left join hv_wms_stock_outing_line t2 on t1.id = t2.header_id
        where t2.id = #{lineId,jdbcType=INTEGER}
    </select>
</mapper>