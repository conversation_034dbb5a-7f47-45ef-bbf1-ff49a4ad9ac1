<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.wms.dao.ReceiptMapper">

    <resultMap id="receiptPage" type="com.hvisions.wms.dto.receipt.ReceiptDTO">
    </resultMap>

    <select id="getReceiptByQuery" resultMap="receiptPage" databaseId="mysql">
        select
        r1.*,
        u.user_name as create_name,
        s1.supplier_code,
        s1.supplier_name
        from
        hv_wms_receipt r1
        left join framework.sys_supplier s1
        on r1.supplier_id = s1.id
        left join framework.sys_user u
        on r1.creator_id = u.id
        <where>
            <if test="dto.code !=null">
                and r1.code like concat('%',#{dto.code},'%')
            </if>
            <if test="dto.receiptType != null">
                and r1.receipt_type = #{dto.receiptType}
            </if>
            <if test="dto.receiptState != null">
                and r1.receipt_state = #{dto.receiptState}
            </if>
            <if test="dto.supplierId != null">
                and r1.supplier_id = #{dto.supplierId}
            </if>
            <if test="dto.createDateStart !=null and dto.createDateEnd !=null">
                and r1.create_time BETWEEN #{dto.createDateStart} AND #{dto.createDateEnd}
            </if>
            <if test="dto.arrivalDateStart !=null and dto.arrivalDateEnd !=null">
                and r1.arrival_date BETWEEN #{dto.arrivalDateStart} AND #{dto.arrivalDateEnd}
            </if>
            <if test="dto.shelfDateStart !=null and dto.shelfDateEnd !=null">
                and r1.shelf_date BETWEEN #{dto.shelfDateStart} AND #{dto.shelfDateEnd}
            </if>
        </where>
    </select>
    <select id="getReceiptByQuery" resultMap="receiptPage" databaseId="sqlserver">
        select
        r1.*,
        u.user_name as create_name,
        s1.supplier_code,
        s1.supplier_name
        from
        hv_wms_receipt r1
        left join framework.dbo.sys_supplier s1
        on r1.supplier_id = s1.id
        left join framework.dbo.sys_user u
        on r1.creator_id = u.id
        <where>
            <if test="dto.code !=null">
                and r1.code like concat('%',#{dto.code},'%')
            </if>
            <if test="dto.receiptType != null">
                and r1.receipt_type = #{dto.receiptType}
            </if>
            <if test="dto.receiptState != null">
                and r1.receipt_state = #{dto.receiptState}
            </if>
            <if test="dto.supplierId != null">
                and r1.supplier_id = #{dto.supplierId}
            </if>
            <if test="dto.createDateStart !=null and dto.createDateEnd !=null">
                and r1.create_time BETWEEN #{dto.createDateStart} AND #{dto.createDateEnd}
            </if>
            <if test="dto.arrivalDateStart !=null and dto.arrivalDateEnd !=null">
                and r1.arrival_date BETWEEN #{dto.arrivalDateStart} AND #{dto.arrivalDateEnd}
            </if>
            <if test="dto.shelfDateStart !=null and dto.shelfDateEnd !=null">
                and r1.shelf_date BETWEEN #{dto.shelfDateStart} AND #{dto.shelfDateEnd}
            </if>
        </where>
    </select>

    <resultMap id="receiptMaterial" type="com.hvisions.wms.dto.receipt.ReceiptMaterialDTO">


    </resultMap>
    <select id="getAllByReceiptId" resultMap="receiptMaterial" parameterType="java.lang.Integer" databaseId="mysql">
        select
               r.*,
               m.material_name,
               m.material_code,
               m.eigenvalue,
               u.description as unitName
        from
             warehouse.hv_wms_receipt_material r
                 left join hiper_base.hv_bm_material m
                     on r.material_id = m.id
                 left join hiper_base.hv_bm_unit u
                     on m.uom =u.id
        where
                r.receipt_id = #{id}
    </select>
    <select id="getAllByReceiptId" resultMap="receiptMaterial" parameterType="java.lang.Integer" databaseId="sqlserver">
        select
         r.*,
        m.material_name,
        m.material_code,
        m.eigenvalue,
        u.description as unitName
        from
        warehouse.hv_wms_receipt_material r
        left join hiper_base.dbo.hv_bm_material m
        on r.material_id = m.id
        left join hiper_base.dbo.hv_bm_unit u
        on m.uom =u.id
        where
        r.receipt_id = #{id}
    </select>


    <resultMap id="shelf" type="com.hvisions.wms.dto.receipt.ShelfMaterialDTO">

    </resultMap>

    <select id="getShelfByReceiptId" resultMap="shelf" parameterType="java.lang.Integer" databaseId="mysql">
        select
               s.*,
               m.material_name,
               m.material_code,
               m.eigenvalue,
               u.description as unitName
        from
             warehouse.hv_wms_shelf_material s
                 left join hv_wms_receipt_material r
                     on s.receipt_material = r.id
                 left join hiper_base.hv_bm_material m
                     on r.material_id = m.id
                 left join hiper_base.hv_bm_unit u
                     on m.uom =u.id
        where
                s.receipt_id = #{id}
    </select>
    <select id="getShelfByReceiptId" resultMap="shelf" parameterType="java.lang.Integer" databaseId="sqlserver">
        select
        s.*,
        m.material_name,
        m.material_code,
        m.eigenvalue,
        u.description as unitName
        from
        warehouse.hv_wms_shelf_material s
        left join hv_wms_receipt_material r
        on s.receipt_material = r.id
        left join hiper_base.dbo.hv_bm_material m
        on r.material_id = m.id
        left join hiper_base.dbo.hv_bm_unit u
        on m.uom =u.id
        where
        s.receipt_id = #{id}
    </select>

    <resultMap id="shelf_material_count" type="com.hvisions.wms.dto.receipt.ShelfMaterialDTO">


    </resultMap>

    <select id="getShelfCountByReceiptId" resultMap="shelf_material_count" parameterType="java.lang.Integer" databaseId="mysql">
        select
               sum(s.used_count) as used_count,
               all_count,
               m.material_name,
               m.material_code,
               m.eigenvalue,
               u.description as unitName,
               s.old_material_batch_num
        from
             warehouse.hv_wms_shelf_material s
                 left join hv_wms_receipt_material r
                     on s.receipt_material = r.id
                 left join hiper_base.hv_bm_material m
                     on r.material_id = m.id
                 left join hiper_base.hv_bm_unit u
                     on m.uom =u.id
        where
                s.receipt_id = #{id} and s.show_state != 0
        group  by
                 r.material_id,s.all_count,s.old_material_batch_num
    </select>
    <select id="getShelfCountByReceiptId" resultMap="shelf_material_count" parameterType="java.lang.Integer" databaseId="sqlserver">
        select
		sum(s.used_count) as used_count,
		all_count,
        m.material_name,
        m.material_code,
        m.eigenvalue,
        u.description as unitName,
        s.old_material_batch_num
        from
        warehouse.hv_wms_shelf_material s
        left join hv_wms_receipt_material r
        on s.receipt_material = r.id
        left join hiper_base.dbo.hv_bm_material m
        on r.material_id = m.id
        left join hiper_base.dbo.hv_bm_unit u
        on m.uom =u.id
        where
        s.receipt_id = #{id} and s.show_state != 0
        group  by
        	r.material_id,s.all_count,s.old_material_batch_num
    </select>

    <resultMap id="pro" type="com.hvisions.wms.dto.quality.MaterialPropertyDTO"></resultMap>
    <select id="getAllByMaterialId" resultMap="pro" databaseId="mysql">
        select t1.*
        from
        hiper_base.hv_bm_material_property t1
        <where>
            <if test="ids != null and ids.size>0">
                <foreach collection="ids" index="index" item="item" open="and t1.material_id in("
                         separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

        </where>
    </select>
    <select id="getAllByMaterialId" resultMap="pro" databaseId="sqlserver">
        select t1.*
        from
        hiper_base.dbo.hv_bm_material_property t1
        <where>
            <if test="ids != null and ids.size>0">
                <foreach collection="ids" index="index" item="item" open="and t1.material_id in("
                         separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

        </where>
    </select>

    <select id="selectMaterialExtend" parameterType="java.lang.Integer"
            resultType="java.util.HashMap" databaseId="mysql">
        select t1.* from hiper_base.hv_bm_material_extend t1 where t1.material_id = #{id}
    </select>
    <select id="selectMaterialExtend" parameterType="java.lang.Integer"
            resultType="java.util.HashMap" databaseId="sqlserver">
       select t1.* from hiper_base.dbo.hv_bm_material_extend t1 where t1.material_id = #{id}
    </select>

    <resultMap id="productMessage" type="com.hvisions.wms.dto.receipt.ReceiptProductDTO"></resultMap>

    <select id="getMaterialByCodeIn" resultMap="productMessage" databaseId="mysql">
        select t1.material_name,t1.material_code,t1.id as material_id
        from
        hiper_base.hv_bm_material t1
        <where>
            <if test="codes != null and codes.size>0">
                <foreach collection="codes" index="index" item="item" open="and t1.material_code in("
                         separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getMaterialByCodeIn" resultMap="productMessage" databaseId="sqlserver">
        select t1.material_name,t1.material_code,t1.id as material_id
        from
        hiper_base.dbo.hv_bm_material t1
        <where>
            <if test="codes != null and codes.size>0">
                <foreach collection="codes" index="index" item="item" open="and t1.material_code in("
                         separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>


    <resultMap id="materialMessage" type="com.hvisions.wms.dto.receipt.MaterialDTO"></resultMap>

    <select id="getMaterialByIdIn" resultMap="materialMessage" databaseId="mysql">
        select t1.material_name,
        t1.material_code,
        t1.id,
        t1.eigenvalue,
        t2.group_code as materialGroupCode,
        t3.description as unitName,
        t4.material_type_code as materialType
        from
        hiper_base.hv_bm_material t1
        left join hiper_base.hv_bm_material_group t2
        on t1.material_group = t2.id
        left join hiper_base.hv_bm_unit t3
        on t1.uom = t3.id
        left join hiper_base.hv_bm_material_type t4
        on t4.id = t1.material_type
        <where>
            <if test="ids != null and ids.size>0">
                <foreach collection="ids" index="index" item="item" open="and t1.id in("
                         separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getMaterialByIdIn" resultMap="materialMessage" databaseId="sqlserver">
        select t1.material_name,
        t1.material_code,
        t1.id,
        t1.eigenvalue,
        t2.group_code as materialGroupCode,
        t3.description as unitName,
        t4.material_type_code as materialType
        from
        hiper_base.dbo.hv_bm_material t1
        left join hiper_base.dbo.hv_bm_material_group t2
        on t1.material_group = t2.id
        left join hiper_base.dbo.hv_bm_unit t3
        on t1.uom = t3.id
        left join hiper_base.dbo.hv_bm_material_type t4
        on t4.id = t1.material_type
        <where>
            <if test="ids != null and ids.size>0">
                <foreach collection="ids" index="index" item="item" open="and t1.id in("
                         separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>



    <resultMap id="materialByCode" type="com.hvisions.wms.dto.receipt.MaterialDTO"></resultMap>
    <select id="getMaterialByCode" resultMap="materialByCode" databaseId="mysql">
        select t1.material_name,
        t1.material_code,
        t1.id,
        t1.eigenvalue
        from
        hiper_base.hv_bm_material t1
        <where>
            t1.material_code = #{code}
        </where>
    </select>
    <select id="getMaterialByCode" resultMap="materialByCode" databaseId="sqlserver">
        select t1.material_name,
        t1.material_code,
        t1.id,
        t1.eigenvalue
        from
        hiper_base.dbo.hv_bm_material t1
        <where>
            t1.material_code = #{code}
        </where>
    </select>

    <select id="getMaterialById" resultMap="materialByCode" databaseId="mysql">
        select t1.material_name,
        t1.material_code,
        t1.id,
        t1.eigenvalue,
        t2.group_code as materialGroupCode,
        t3.description as unitName,
        t4.material_type_code as materialType
        from
        hiper_base.hv_bm_material t1
        left join hiper_base.hv_bm_material_group t2
        on t1.material_group = t2.id
        left join hiper_base.hv_bm_unit t3
        on t1.uom = t3.id
        left join hiper_base.hv_bm_material_type t4
        on t4.id = t1.material_type
        <where>
            t1.id = #{id}
        </where>
    </select>
    <select id="getMaterialById" resultMap="materialByCode" databaseId="sqlserver">
        select t1.material_name,
        t1.material_code,
        t1.id,
        t1.eigenvalue,
        t2.group_code as materialGroupCode,
        t3.description as unitName,
        t4.material_type_code as materialType
        from
        hiper_base.dbo.hv_bm_material t1
        left join hiper_base.dbo.hv_bm_material_group t2
        on t1.material_group = t2.id
        left join hiper_base.dbo.hv_bm_unit t3
        on t1.uom = t3.id
        left join hiper_base.dbo.hv_bm_material_type t4
        on t4.id = t1.material_type
        <where>
            t1.id = #{id}
        </where>
    </select>

    <update id="updateSemiProductState">
        update hv_wms_product t1
        <set>
            t1.state = 2
        </set>
        <where>
            <if test="codes != null and codes.size>0">
                <foreach collection="codes" index="index" item="item" open="and t1.material_batch_num in("
                         separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </update>
</mapper>