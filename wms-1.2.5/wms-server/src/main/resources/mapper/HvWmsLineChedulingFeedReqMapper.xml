<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hvisions.wms.dao.HvWmsLineChedulingFeedReqMapper">
    <resultMap id="lineRecordResultMap" type="com.hvisions.wms.dto.rcs.HvWmsLineSchedulingFeedReq">
        <id column="id" property="id"/>
        <result column="request_code" property="requestcode"/>
        <result column="req_time" property="reqtime"/>
        <result column="task_type" property="tasktype"/>
        <result column="frame_code" property="framecode"/>
        <result column="frame_type" property="frametype"/>
        <result column="startpoint_code" property="startpointcode"/>
        <result column="point_code" property="pointcode"/>
        <result column="priority" property="priority"/>
        <result column="request_system" property="requestsystem"/>
        <result column="station_code" property="stationcode"/>

    </resultMap>

        <insert id="addLineSchedule" parameterType="com.hvisions.wms.dto.rcs.HvWmsLineSchedulingFeedReq" useGeneratedKeys="true" keyProperty="id">
            INSERT INTO hv_wms_line_scheduling_feed_req(request_code, req_time, task_type,frame_code,frame_type,startpoint_code, point_code,priority,request_system,station_code)
            VALUES(#{requestcode}, now(), #{tasktype}, #{framecode},#{frametype},#{startpointcode}, #{pointcode},#{priority},#{requestsystem},#{stationcode})
        </insert>
    <update id="updateLineSchedule" parameterType="com.hvisions.wms.dto.rcs.HvWmsLineSchedulingFeedReq">
        update hv_wms_line_scheduling_feed_req
        <set>

            <if test="reqtime!=null">req_time=now(),</if>
            <if test="tasktype!=null">task_type=#{tasktype},</if>
            <if test="framecode!=null">frame_code=#{framecode},</if>
            <if test="frametype!=null">frame_type=#{frametype},</if>
            <if test="startpointcode!=null">startpoint_code=#{startpointcode},</if>
            <if test="pointcode!=null">point_code=#{pointcode},</if>
            <if test="priority!=null">priority=#{priority},</if>
            <if test="requestsystem!=null">request_system=#{requestsystem},</if>
            <if test="stationcode!=null">station_code=#{stationcode},</if>
        </set>
        where request_code=#{requestcode}
    </update>

    <delete id="deleteLineSchedule">
            delete from hv_wms_line_scheduling_feed_req where request_code = #{requestcode}
    </delete>

    <select id="findAllLineSchedule" resultType="com.hvisions.wms.dto.rcs.HvWmsLineSchedulingFeedReq">
            select * from hv_wms_line_scheduling_feed_req where request_code = #{requestCode}
    </select>
    <select id="getLineSchedulePage"
            resultType="com.hvisions.wms.dto.rcs.LineSchedulingReceiveReqPageDTO">
        SELECT DISTINCT
            l.id,
            l.request_code,
            a.request_task_no as taskNo,
            l.task_type,
            l.frame_code,
            l.frame_type,
            l.startpoint_code,
            l.point_code,
            l.request_system,
            a.scheduling_state as status,
            a.actual_start_time as actualStartTime,
            a.actual_end_time as actualEndTime
        FROM
            hv_wms_line_scheduling_feed_req as l
            LEFT JOIN
            hv_wms_agv_scheduling as a
        ON
            l.request_code = a.request_task_no
        <where>
            <if test="query.requestCode != null and query.requestCode != ''">
                AND l.request_code Like CONCAT('%', #{query.requestCode}, '%')
            </if>
            <if test="query.taskNo != null and query.taskNo != ''">
                AND a.request_task_no Like CONCAT('%', #{query.taskNo}, '%')
            </if>
            <if test="query.taskType != null">
                AND l.task_type = #{query.taskType}
            </if>
            <if test="query.status != null">
                AND a.scheduling_state = #{query.status}
            </if>
            <if test="query.frameCode != null and query.frameCode != ''">
                AND l.frame_code Like CONCAT('%', #{query.frameCode}, '%')
            </if>
            <if test="query.startpointCode != null and query.startpointCode != ''">
                AND l.startpoint_code Like CONCAT('%', #{query.startpointCode}, '%')
            </if>
            <if test="query.pointCode != null and query.pointCode != ''">
                AND l.point_code Like CONCAT('%', #{query.pointCode}, '%')
            </if>
        </where>
    </select>

</mapper>