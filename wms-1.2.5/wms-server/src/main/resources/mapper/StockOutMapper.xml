<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.wms.dao.StockOutMapper">
    <select id="getPageByQuery" resultType="com.hvisions.wms.dto.stockouting.StockOutingHeaderDto">
        select *
        from hv_wms_stock_outing_header
        <where>
            <if test="query.status != null">
                status = #{query.status,jdbcType=INTEGER}
            </if>
            <if test="query.owNumber != null and query.owNumber != ''">
                and ow_number like concat('%', #{query.owNumber,jdbcType=VARCHAR}, '%')
            </if>
            <if test="query.startTime != null and query.endTime != null">
                and out_stock_time between #{query.startTime,jdbcType=TIMESTAMP} and #{query.endTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>

    <select id="getStockOutDetilById" parameterType="Integer" resultMap="stockHeader">
        select *
        from hv_wms_stock_outing_header
        where id = #{owId}
    </select>

    <resultMap id="stockHeader" type="com.hvisions.wms.dto.stockouting.StockOutingHeaderDto">
        <collection property="stockOutingLineDtos" ofType="com.hvisions.wms.dto.stockouting.StockOutingLineDto"
                    select="getStockLine" column="{header_id=id}"/>
    </resultMap>

    <select id="getStockLine" resultMap="stockLine">
        select *
        from hv_wms_stock_outing_line
        where header_id = #{header_id}
    </select>

    <resultMap id="stockLine" type="com.hvisions.wms.dto.stockouting.StockOutingLineDto">
        <collection property="stockOutingBatchDtos"
                    ofType="com.hvisions.wms.dto.stockouting.StockOutingBatchDto"
                    select="getStockBatch"
                    column="{materialId=material_id,materialCode=material_code,outingLineId=id}"/>
    </resultMap>

    <select id="getStockBatch" resultType="com.hvisions.wms.dto.stockouting.StockOutingBatchDto">
        select *
        from hv_wms_stock_outing_batch
        where material_id = #{materialId}
          and material_code = #{materialCode}
          and outing_line_id = #{outingLineId}
    </select>

    <update id="updateAssociateNumber" parameterType="com.hvisions.wms.entity.stock.HvWmsStockOutingHeader">
        update hv_wms_stock_outing_header
        set associate_number=#{header.associateNumber,jdbcType=VARCHAR}
        where id = #{header.id,jdbcType=INTEGER}
    </update>
</mapper>