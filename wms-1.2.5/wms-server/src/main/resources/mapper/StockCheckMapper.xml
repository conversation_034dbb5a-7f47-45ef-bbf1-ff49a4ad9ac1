<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.wms.dao.StockCheckMapper">
    <select id="getPageByQuery" resultType="com.hvisions.wms.dto.stockcheck.StockCheckHeaderDto">
        select *
        from hv_wms_stock_check_header
        <where>
            <if test="query.startTime != null and query.endTime != null">
                stock_check_time between #{query.startTime,jdbcType=TIMESTAMP} and #{query.endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="query.keyWord != null and query.keyWord != ''">
                and sc_number like concat('%', #{query.keyWord}, '%')
            </if>
        </where>
    </select>

    <select id="getByScId" resultMap="stockCheckHeader">
        select *
        from hv_wms_stock_check_header
        where id = #{scId}
    </select>

    <resultMap id="stockCheckHeader" type="com.hvisions.wms.dto.stockcheck.StockCheckHeaderDto">
        <collection property="stockCheckLineDtos"
                    ofType="com.hvisions.wms.dto.stockcheck.StockCheckLineDto"
                    select="getLineByHeaderId"
                    column="{header_id=id}"/>
    </resultMap>

    <select id="getLineByHeaderId" resultMap="getDetail">
        select *
        from hv_wms_stock_check_line
        where header_id = #{header_id}
    </select>

    <select id="getLineByMaterial"
            parameterType="com.hvisions.wms.dto.stockcheck.StockCheckMaterialDto"
            resultMap="getDetail">
        select t.*
        from (select *
              from hv_wms_stock_check_line
              where header_id = #{query.headerId,jdbcType=INTEGER}
                and location_id = #{query.locationId,jdbcType=INTEGER}) as t
        <where>
            <if test="query.keyWord != null and query.keyWord != ''">
                t.material_code like concat('%', #{query.keyWord}, '%')
                        or t.material_name like concat('%', #{query.keyWord}, '%')
            </if>
        </where>
    </select>

    <select id="getLineById" resultMap="getDetail">
        select *
        from hv_wms_stock_check_line
        where id = #{lineId}
    </select>

    <select id="getDetailByLineId" resultType="com.hvisions.wms.dto.stockcheck.StockCheckDetailDto">
        select *
        from hv_wms_stock_check_detail
        where line_id = #{lineId}
    </select>

    <resultMap id="getDetail" type="com.hvisions.wms.dto.stockcheck.StockCheckLineDto">
        <collection property="stockCheckDetailDtos"
                    ofType="com.hvisions.wms.dto.stockcheck.StockCheckDetailDto"
                    select="getDetailByLineId" column="{lineId=id}"/>
    </resultMap>
</mapper>