<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.wms.dao.HvWmAgvSchedulingMapper">
    <!-- 定义可重用的SQL片段 -->
    <sql id="PageQuery">
        id,
        task_no,
        task_type,
        request_task_no,
        agv_no,
        agv_name,
        agv_type_no,
        agv_type_name,
        start_point,
        end_point,
        priority,
        scheduling_state,
        order_no,
        pallet_type_no,
        pallet_type_name,
        pallet_name,
        pallet_no,
        manual,
        planned_start_time,
        planned_end_time,
        actual_start_time,
        actual_end_time,
        create_time,
        creator_name,
        site_num,
        update_time,
        updater_name
    </sql>

    <sql id="ListQuery">
        task_no,
        task_type,
        request_task_no,
        agv_no,
        agv_name,
        agv_type_no,
        agv_type_name,
        start_point,
        end_point,
        priority,
        scheduling_state,
        order_no,
        pallet_type_no,
        pallet_type_name,
        pallet_name,
        pallet_no,
        manual,
        planned_start_time,
        planned_end_time,
        actual_start_time,
        actual_end_time,
        create_time,
        creator_name,
        update_time,
        updater_name
    </sql>

    <select id="getPage" parameterType="com.hvisions.wms.dto.agvScheduling.HvWmAgvSchedulingQueryDTO" resultType="com.hvisions.wms.dto.agvScheduling.HvWmAgvSchedulingShowDTO">
        SELECT
        <include refid="PageQuery"/>
        FROM
        hv_wms_agv_scheduling
        <where>
            <if test="query.taskNo != null and query.taskNo != ''">
                AND task_no LIKE CONCAT('%', #{query.taskNo}, '%')
            </if>
            <if test="query.requestTaskNo != null and query.requestTaskNo != ''">
                AND request_task_no LIKE CONCAT('%', #{query.requestTaskNo}, '%')
            </if>
            <if test="query.taskType != null">
                AND task_type = #{query.taskType}
            </if>
            <if test="query.agvNo != null and query.agvNo != ''">
                AND agv_no LIKE CONCAT('%', #{query.agvNo}, '%')
            </if>
            <if test="query.agvTypeName != null and query.agvTypeName != ''">
                AND agv_type_name LIKE CONCAT('%', #{query.agvTypeName}, '%')
            </if>
            <if test="query.palletNo != null and query.palletNo != ''">
                AND pallet_no LIKE CONCAT('%', #{query.palletNo}, '%')
            </if>
            <if test="query.palletTypeName != null and query.palletTypeName != ''">
                AND pallet_type_name LIKE CONCAT('%', #{query.palletTypeName}, '%')
            </if>
            <if test="query.schedulingState != null">
                AND scheduling_state = #{query.schedulingState}
            </if>
            <if test="query.plannedStartTime != null and query.plannedEndTime != null">
                AND planned_start_time BETWEEN #{query.plannedStartTime} AND #{query.plannedEndTime}
            </if>
        </where>
    </select>
    <select id="findListByCondition" resultType="com.hvisions.wms.entity.agvScheduling.HvWmAgvScheduling">
        SELECT
        <include refid="PageQuery"/>
        FROM
        hv_wms_agv_scheduling
        <where>
            <if test="query.taskNo != null and query.taskNo != ''">
                AND task_no LIKE CONCAT('%', #{query.taskNo}, '%')
            </if>
            <if test="query.requestTaskNo != null and query.requestTaskNo != ''">
                AND request_task_no LIKE CONCAT('%', #{query.requestTaskNo}, '%')
            </if>
            <if test="query.taskType != null">
                AND task_type = #{query.taskType}
            </if>
            <if test="query.agvNo != null and query.agvNo != ''">
                AND agv_no LIKE CONCAT('%', #{query.agvNo}, '%')
            </if>
            <if test="query.agvTypeName != null and query.agvTypeName != ''">
                AND agv_type_name LIKE CONCAT('%', #{query.agvTypeName}, '%')
            </if>
            <if test="query.palletNo != null and query.palletNo != ''">
                AND pallet_no LIKE CONCAT('%', #{query.palletNo}, '%')
            </if>
            <if test="query.palletTypeName != null and query.palletTypeName != ''">
                AND pallet_type_name LIKE CONCAT('%', #{query.palletTypeName}, '%')
            </if>
            <if test="query.schedulingState != null">
                AND scheduling_state = #{query.schedulingState}
            </if>
            <if test="query.plannedStartTime != null and query.plannedEndTime != null">
                AND planned_start_time BETWEEN #{query.plannedStartTime} AND #{query.plannedEndTime}
            </if>
        </where>
    </select>
    <select id="statMaterialBox" resultType="com.hvisions.wms.dto.agvScheduling.MaterialBoxStatDTO">
        SELECT
                pallet_no,
                pallet_name,
                COUNT(*) AS useCount,
                AVG(
                CASE
                    WHEN actual_start_time IS NOT NULL AND actual_end_time IS NOT NULL
                    AND actual_end_time > actual_start_time
                    THEN TIMESTAMPDIFF(MINUTE, actual_start_time, actual_end_time)
                    ELSE 0
                    END
                    ) AS avgUseDuration
            FROM
                hv_wms_agv_scheduling
            <where>
                <!-- 时间范围筛选 -->
                <if test="query.startTime != null">
                    AND actual_start_time >= #{query.startTime}
                </if>
                <if test="query.endTime != null">
                    AND #{query.endTime} >= actual_end_time
                </if>
                <!-- 运输工具编号筛选 -->
                <if test="query.transportNo != null and query.transportNo != ''">
                    AND agv_no = #{query.transportNo}
                </if>
                <!-- 排除无效状态的数据（根据实际业务调整） -->
                AND scheduling_state = 3 -- 只统计已完成的任务
            </where>
            GROUP BY
                pallet_no, pallet_name
            HAVING
                useCount > 0
            ORDER BY
                useCount DESC
    </select>
</mapper>