<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.wms.dao.StockOutBatchMapper">
    <delete id="deleteByOutingLine" parameterType="com.hvisions.wms.entity.stock.HvWmsStockOutingLine">
        delete
        from hv_wms_stock_outing_batch
        where material_id = #{line.materialId,jdbcType=INTEGER}
          and material_code = #{line.materialCode,jdbcType=VARCHAR}
          and outing_line_id = #{line.id,jdbcType=INTEGER}
    </delete>
    <select id="findOutingStatusByBatchId" resultType="java.lang.Integer">
        select t1.status
        from hv_wms_stock_outing_header t1
                     left join hv_wms_stock_outing_line t2 on t1.id = t2.header_id
                     left join hv_wms_stock_outing_batch t3 on t3.outing_line_id = t2.id
        where t3.id = #{batchId,jdbcType=INTEGER}
    </select>
    <select id="findBatchsByLineId" resultType="com.hvisions.wms.dto.stockouting.StockOutingBatchDto">
        select *
        from hv_wms_stock_outing_batch
        where outing_line_id = #{outingLineId}
    </select>
    <select id="findBatchsByQuery" resultType="com.hvisions.wms.dto.stockouting.StockOutingBatchDto">
        SELECT b.id,
               b.batch_number,
               b.num,
               b.location_id,
               b.material_id,
               b.material_code,
               b.outing_line_id,
               l.`name` as location_name
        FROM `hv_wms_stock_outing_batch` as b
                     LEFT JOIN hv_wms_wares_location as l
                on b.location_id = l.id
        where b.outing_line_id = #{lineId}
    </select>
</mapper>