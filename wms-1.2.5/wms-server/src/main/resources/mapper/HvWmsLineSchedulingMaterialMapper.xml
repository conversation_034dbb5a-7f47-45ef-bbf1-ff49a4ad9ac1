<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hvisions.wms.dao.HvWmsLineSchedulingMaterialMapper">
    <select id="getPage" resultType="com.hvisions.wms.dto.rcs.HvWmsLineSchedulingMatListDTO">
        SELECT
            id,
            material_code,
            material_name,
            order_number,
            quality,
            mat_order
        FROM
            hv_wms_line_scheduling_material_list
        <where>
            <if test="query.requestCode != null and query.requestCode != ''">
                AND request_code LIKE CONCAT('%', #{query.requestCode}, '%')
            </if>
        </where>
    </select>
</mapper>