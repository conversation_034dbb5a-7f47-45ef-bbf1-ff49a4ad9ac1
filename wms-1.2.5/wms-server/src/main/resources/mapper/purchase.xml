<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.wms.dao.PurchaseMapper">

    <resultMap id="pur" type="com.hvisions.wms.dto.purchase.PurchaseHeaderDTO">


    </resultMap>

    <select id="getHeaderByQuery" parameterType="java.util.Map" resultMap="pur" databaseId="mysql">
        SELECT
        t1.*,
        t2.user_name applicantName,
        t3.department_name,
        t4.supplier_name
        FROM
        hv_wms_purchase_header t1
        LEFT JOIN framework.sys_user t2 ON t1.applicant_id = t2.id
        LEFT JOIN framework.sys_department t3 ON t1.department_id = t3.id
        LEFT JOIN framework.sys_supplier t4 ON t1.supplier_id = t4.id
        <where>
            <if test="receiptNumber != null and receiptNumber != ''">
                and t1.receipt_number like concat('%',${receiptNumber},'%')
            </if>
            <if test="supplierId != null">
                and t1.supplier_id = #{supplierId}
            </if>
            <if test="departmentId != null">
                and t1.department_id = #{departmentId}
            </if>
            <if test="applicantId != null">
                and t1.applicant_id = #{applicantId}
            </if>
            <if test="purchaseState != null">
                and t1.purchase_state = #{purchaseState}
            </if>
            <if test="startTime != null and endTime != null">
                and t1.delivery_date between #{startTime} and #{endTime}
            </if>
        </where>
    </select>
    <select id="getHeaderByQuery" parameterType="java.util.Map" resultMap="pur" databaseId="sqlserver">
        SELECT
        t1.*,
        t2.user_name applicantName,
        t3.department_name,
        t4.supplier_name
        FROM
        hv_wms_purchase_header t1
        LEFT JOIN framework.dbo.sys_user t2 ON t1.applicant_id = t2.id
        LEFT JOIN framework.dbo.sys_department t3 ON t1.department_id = t3.id
        LEFT JOIN framework.dbo.sys_supplier t4 ON t1.supplier_id = t4.id
        <where>
            <if test="receiptNumber != null and receiptNumber != ''">
                and t1.receipt_number like concat('%',${receiptNumber},'%')
            </if>
            <if test="supplierId != null">
                and t1.supplier_id = #{supplierId}
            </if>
            <if test="departmentId != null">
                and t1.department_id = #{departmentId}
            </if>
            <if test="applicantId != null">
                and t1.applicant_id = #{applicantId}
            </if>
            <if test="purchaseState != null">
                and t1.purchase_state = #{purchaseState}
            </if>
            <if test="startTime != null and endTime != null">
                and t1.delivery_date between #{startTime} and #{endTime}
            </if>
        </where>
    </select>

</mapper>