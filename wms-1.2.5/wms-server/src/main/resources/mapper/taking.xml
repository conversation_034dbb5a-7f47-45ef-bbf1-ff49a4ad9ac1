<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.wms.dao.StockTaskingOrderMapper">

    <resultMap id="taking" type="com.hvisions.wms.dto.stocktaking.StockTakingOrderDTO">

    </resultMap>

    <select id="getAllByQuery" resultMap="taking"
            parameterType="com.hvisions.wms.dto.stocktaking.StockTakingQuery" databaseId="mysql">
        select t.*,
        l.name as locationName,
        m.material_name
        from
        hv_wms_stock_taking_order t
        left join hv_wms_wares_location l
        on t.location_id = l.id
        left join hiper_base.hv_bm_material m
        on t.material_id = m.id
        <where>
            <if test="dto.orderCode != null and dto.orderCode != &apos;&apos;">
                and t.order_code like concat('%', #{dto.orderCode},'%')
            </if>
            <if test="dto.state != null and dto.state != &apos;&apos;">
                and t.state = #{dto.state}
            </if>
            <if test="dto.locationId != null">
                and t.location_id = #{dto.locationId}
            </if>
            <if test="dto.materialId != null">
                and t.material_id = #{dto.materialId}
            </if>
            <if test="dto.createTimeStart != null and dto.createTimeEnd != null ">
                and t.create_time between #{dto.createTimeStart} and #{dto.createTimeEnd}
            </if>
            <if test="dto.finishTimeStart != null and dto.finishTimeEnd != null ">
                and t.finish_time between #{dto.finishTimeStart} and #{dto.finishTimeEnd}
            </if>
        </where>
    </select>
    <select id="getAllByQuery" resultMap="taking"
            parameterType="com.hvisions.wms.dto.stocktaking.StockTakingQuery" databaseId="sqlserver">
        select t.*,
        l.name as locationName,
        m.material_name
        from
        hv_wms_stock_taking_order t
        left join hv_wms_wares_location l
        on t.location_id = l.id
        left join hiper_base.dbo.hv_bm_material m
        on t.material_id = m.id
        <where>
            <if test="dto.orderCode != null and dto.orderCode != &apos;&apos;">
                and t.order_code like concat('%', #{dto.orderCode},'%')
            </if>
            <if test="dto.state != null and dto.state != &apos;&apos;">
                and t.state = #{dto.state}
            </if>
            <if test="dto.locationId != null">
                and t.location_id = #{dto.locationId}
            </if>
            <if test="dto.materialId != null">
                and t.material_id = #{dto.materialId}
            </if>
            <if test="dto.createTimeStart != null and dto.createTimeEnd != null ">
                and t.create_time between #{dto.createTimeStart} and #{dto.createTimeEnd}
            </if>
            <if test="dto.finishTimeStart != null and dto.finishTimeEnd != null ">
                and t.finish_time between #{dto.finishTimeStart} and #{dto.finishTimeEnd}
            </if>
        </where>
    </select>


    <resultMap id="getStockLine" type="com.hvisions.wms.dto.stocktaking.StockTakingOrderLineDTO">

    </resultMap>
    <select id="getAllByStockTakId" parameterType="java.lang.Integer" resultMap="getStockLine" databaseId="mysql">
        select
               t.*,
               m.material_name,
               m.material_code,
               u.description as unit,
               l.name as locationName
        from
             hv_wms_stock_taking_order_line t
                 left join hiper_base.hv_bm_material m
                     on t.material_id = m.id
                 left join hv_wms_wares_location l
                     on t.location_id = l.id
                 left join  hiper_base.hv_bm_unit u
                     on u.id = m.uom
        where  t.order_id =#{id}

    </select>
    <select id="getAllByStockTakId" parameterType="java.lang.Integer" resultMap="getStockLine" databaseId="sqlserver">
        select
        t.*,
        m.material_name,
        m.material_code,
        u.description as unit,
        l.name as locationName
        from
       hv_wms_stock_taking_order_line t
       left join hiper_base.dbo.hv_bm_material m
       on t.material_id = m.id
       left join hv_wms_wares_location l
       on t.location_id = l.id
       left join  hiper_base.hv_bm_unit u
       on u.id = m.uom
       where  t.order_id =#{id}

    </select>


</mapper>