<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.wms.dao.HvWmRcsReportedTaskRecordMapper">

    <resultMap id="BaseResultMap" type="com.hvisions.wms.dto.rcs.HvWmRcsReportedTaskRecord">
            <id property="id" column="id" />
            <result property="robottaskcode" column="robotTaskCode" />
            <result property="singlerobotcode" column="singleRobotCode" />
            <result property="currentseq" column="currentSeq" />
            <result property="mapcode" column="mapCode" />
            <result property="method" column="method" />
            <result property="carriername" column="carrierName" />
            <result property="carriertype" column="carrierType" />
            <result property="carriercategory" column="carrierCategory" />
            <result property="carrierdir" column="carrierDir" />
            <result property="slotcode" column="slotCode" />
            <result property="slotname" column="slotName" />
            <result property="slotcategory" column="slotCategory" />
            <result property="x" column="x" />
            <result property="y" column="y" />
            <result property="amrcategory" column="amrCategory" />
            <result property="amrtype" column="amrType" />
            <result property="zonecode" column="zoneCode" />
    </resultMap>

    <sql id="Base_Column_List">
        id,robotTaskCode,singleRobotCode,currentSeq,mapCode,method,
        carrierName,carrierType,carrierCategory,carrierDir,slotCode,
        slotName,slotCategory,x,y,amrCategory,
        amrType,zoneCode
    </sql>


</mapper>
