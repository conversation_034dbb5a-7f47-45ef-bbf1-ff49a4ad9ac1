<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.wms.dao.OutOrderMapper">

    <resultMap id="outStock" type="com.hvisions.wms.dto.outstock.OutStockDTO">
    </resultMap>

    <select id="getOutStock" resultMap="outStock" parameterType = "com.hvisions.wms.dto.outstock.OutStockQueryDTO" databaseId="mysql">
        SELECT
        h.*,
        u.user_name,
        au.user_name as approveName
        FROM
        hv_wms_out_order h
        left join framework.sys_user u on h.creator_id = u.id
        left join framework.sys_user au on h.approve_id = au.id
        <where>
            <if test="dto.code != null and dto.code != &apos;&apos;">
                and h.code like concat('%',#{dto.code},'%')
            </if>
            <if test="dto.state != null">
                AND  h.state = #{dto.state}
            </if>
            <if test="dto.orderType != null and dto.orderType != &apos;&apos;">
                AND  h.order_type like concat('%',#{dto.orderType},'%')
            </if>
            <if test="dto.queryStartCreateDate != null and dto.queryStartCreateDate != &apos;&apos;">
                AND  h.create_time <![CDATA[ >= ]]> #{dto.queryStartcreateDate}
            </if>
            <if test="dto.queryEndCreateDate != null and dto.queryEndCreateDate != &apos;&apos;">
                AND  h.create_time <![CDATA[ <= ]]> #{dto.queryEndcreateDate}
            </if>
            <if test="dto.queryStartFinishDate != null">
                AND  h.finish_time <![CDATA[ >=  ]]> #{dto.queryStartFinishDate}
            </if>
            <if test="dto.queryEndFinishDate != null">
                AND  h.finish_time <![CDATA[ <= ]]> #{dto.queryEndFinishDate}
            </if>
            <if test="dto.approveStates!=null and dto.approveStates.size >0 ">
                <foreach collection="dto.approveStates" index="index" item="item" open="and h.approve_state in("
                         separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getOutStock" resultMap="outStock" parameterType = "com.hvisions.wms.dto.outstock.OutStockQueryDTO" databaseId="sqlserver">
        SELECT
        h.*,
        u.user_name,
        au.user_name as approveName
        FROM
        hv_wms_out_order h
        left join framework.dbo.sys_user u on h.creator_id = u.id
        left join framework.dbo.sys_user au on h.approve_id = au.id
        <where>
            <if test="dto.code != null and dto.code != &apos;&apos;">
                and h.code like concat('%',#{dto.code},'%')
            </if>
            <if test="dto.state != null">
                AND  h.state = #{dto.state}
            </if>
            <if test="dto.orderType != null and dto.orderType != &apos;&apos;">
                AND  h.order_type like concat('%',#{dto.orderType},'%')
            </if>
            <if test="dto.queryStartCreateDate != null and dto.queryStartCreateDate != &apos;&apos;">
                AND  h.create_time <![CDATA[ >= ]]> #{dto.queryStartcreateDate}
            </if>
            <if test="dto.queryEndCreateDate != null and dto.queryEndCreateDate != &apos;&apos;">
                AND  h.create_time <![CDATA[ <= ]]> #{dto.queryEndcreateDate}
            </if>
            <if test="dto.queryStartFinishDate != null">
                AND  h.finish_time <![CDATA[ >=  ]]> #{dto.queryStartFinishDate}
            </if>
            <if test="dto.queryEndFinishDate != null">
                AND  h.finish_time <![CDATA[ <= ]]> #{dto.queryEndFinishDate}
            </if>
            <if test="dto.approveStates!=null and dto.approveStates.size >0 ">
                <foreach collection="dto.approveStates" index="index" item="item" open="and h.approve_state in("
                         separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>




</mapper>