<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.wms.dao.AdjustOrderMapper">
    <resultMap id="baseOrder" type="com.hvisions.wms.dto.adjust.AdjustOrderDTO">
    </resultMap>

    <select id="getOrderByQuery" resultMap="baseOrder"
            parameterType="com.hvisions.wms.dto.adjust.AdjustOrderQuery" databaseId="mysql">
        SELECT h.id,
               h.CODE,
               h.order_code,
               h.create_time,
               h.state,
               h.complete_time,
               h.operation,
               h.description,
               u.user_name as createUserName,
               a.user_name as approvedBy
        FROM hv_wms_adjust_order h
                     LEFT JOIN framework.sys_user u ON h.creator_id = u.id
                     LEFT JOIN framework.sys_user a ON h.finisher_id = u.id
        <where>
            <if test="dto.code != null and dto.code != &apos;&apos;">
                and h.code like concat('%', #{dto.code}, '%')
            </if>
            <if test="dto.state != null and dto.state != &apos;&apos;">
                and h.state = #{dto.state}
            </if>
            <if test="dto.orderCode != null and dto.orderCode != &apos;&apos;">
                and h.order_code like concat('%', #{dto.orderCode}, '%')
            </if>
            <if test="dto.operation != null  and dto.operation != &apos;&apos;">
                and h.operation like concat('%', #{dto.operation}, '%')
            </if>
            <if test="dto.createStart != null">
                AND h.create_time <![CDATA[ >= ]]> #{dto.createStart}
            </if>
            <if test="dto.createEnd != null">
                AND h.create_time <![CDATA[ <= ]]> #{dto.createEnd}
            </if>
            <if test="dto.finishStart != null">
                AND h.complete_time <![CDATA[ >= ]]> #{dto.finishStart}
            </if>
            <if test="dto.finishEnd != null">
                AND h.complete_time <![CDATA[ <= ]]> #{dto.finishEnd}
            </if>
        </where>
    </select>
    <select id="getOrderByQuery" resultMap="baseOrder"
            parameterType="com.hvisions.wms.dto.adjust.AdjustOrderQuery" databaseId="sqlserver">
        SELECT h.id,
               h.CODE,
               h.order_code,
               h.create_time,
               h.state,
               h.complete_time,
               h.operation,
               h.description,
               u.user_name as createUserName,
               a.user_name as approvedBy
        FROM hv_wms_adjust_order h
                     LEFT JOIN framework.dbo.sys_user u ON h.creator_id = u.id
                     LEFT JOIN framework.dbo.sys_user a ON h.finisher_id = u.id
        <where>
            <if test="dto.code != null and dto.code != &apos;&apos;">
                and h.code like concat('%', #{dto.code}, '%')
            </if>
            <if test="dto.state != null and dto.state != &apos;&apos;">
                and h.state = #{dto.state}
            </if>
            <if test="dto.orderCode != null and dto.orderCode != &apos;&apos;">
                and h.order_code like concat('%', #{dto.orderCode}, '%')
            </if>
            <if test="dto.operation != null  and dto.operation != &apos;&apos;">
                and h.operation like concat('%', #{dto.operation}, '%')
            </if>
            <if test="dto.createStart != null">
                AND h.create_time <![CDATA[ >= ]]> #{dto.createStart}
            </if>
            <if test="dto.createEnd != null">
                AND h.create_time <![CDATA[ <= ]]> #{dto.createEnd}
            </if>
            <if test="dto.finishStart != null">
                AND h.complete_time <![CDATA[ >= ]]> #{dto.finishStart}
            </if>
            <if test="dto.finishEnd != null">
                AND h.complete_time <![CDATA[ <= ]]> #{dto.finishEnd}
            </if>
        </where>
    </select>
    <select id="getHeaderById" resultType="com.hvisions.wms.dto.adjust.AdjustOrderDTO">
        SELECT h.id,
               h.CODE,
               h.create_time,
               u.user_name as createUserName
        FROM hv_wms_adjust_order h
                     LEFT JOIN framework.sys_user u ON h.creator_id = u.id
        where h.id = #{id,jdbcType=INTEGER}
    </select>
</mapper>