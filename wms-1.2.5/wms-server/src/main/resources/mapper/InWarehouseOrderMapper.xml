<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.wms.dao.InWarehouseOrderMapper">
    <select id="query" resultType="com.hvisions.wms.dto.inwarehouseorder.InWarehouseOrderHeaderDTO" databaseId="mysql">
        select
        t1.*,
        t2.supplier_name ,
        t3.user_name as operator
        from
        hv_wms_in_warehouse_order_header t1
        left join framework.sys_supplier t2 on t1.supplier_id = t2.id
        left join framework.sys_user t3 on t1.creator_id = t3.id
        <where>
            t1.type = #{query.type}
            <if test="query.receiptNumber != null and query.receiptNumber != ''">
                and t1.receipt_number like concat('%',#{query.receiptNumber},'%')
            </if>
            <if test="query.purchaseReceiptNumber != null and query.purchaseReceiptNumber != ''">
                and t1.purchase_receipt_number like concat('%',#{query.purchaseReceiptNumber},'%')
            </if>
            <if test="query.startTime != null and query.endTime != null">
                and t1.actual_in_time between #{query.startTime} and #{query.endTime}
            </if>
            <if test="query.state != null">
                and t1.state = #{query.state}
            </if>
        </where>
    </select>
    <select id="query" resultType="com.hvisions.wms.dto.inwarehouseorder.InWarehouseOrderHeaderDTO" databaseId="sqlserver">
        select
        t1.*,
        t2.supplier_name ,
        t3.user_name as operator
        from
        hv_wms_in_warehouse_order_header t1
        left join framework.dbo.sys_supplier t2 on t1.supplier_id = t2.id
        left join framework.dbo.sys_user t3 on t1.creator_id = t3.id
        <where>
            t1.type = #{query.type}
            <if test="query.receiptNumber != null and query.receiptNumber != ''">
                and t1.receipt_number like concat('%',#{query.receiptNumber},'%')
            </if>
            <if test="query.purchaseReceiptNumber != null and query.purchaseReceiptNumber != ''">
                and t1.purchase_receipt_number like concat('%',#{query.purchaseReceiptNumber},'%')
            </if>
            <if test="query.startTime != null and query.endTime != null">
                and t1.actual_in_time between #{query.startTime} and #{query.endTime}
            </if>
            <if test="query.state != null">
                and t1.state = #{query.state}
            </if>
        </where>
    </select>
</mapper>