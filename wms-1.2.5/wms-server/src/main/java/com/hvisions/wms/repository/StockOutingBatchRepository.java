package com.hvisions.wms.repository;

import com.hvisions.wms.entity.stock.HvWmsStockOutingBatch;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <p>Title: StockOutingBatchRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/9/3</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Repository
public interface StockOutingBatchRepository extends JpaRepository<HvWmsStockOutingBatch, Integer> {

    /**
     * 根据出库清单id删除批次数据
     *
     * @param outLineId 出库清单id
     */
    void deleteByOutingLineId(Integer outLineId);

    /**
     * 根据批次号,库位,详情id查询批次详情
     *
     * @param batchNumber 批次号
     * @param locationId  库位id
     * @param lineId      入库清单id
     * @return 批次详情
     */
    HvWmsStockOutingBatch findByBatchNumberAndLocationIdAndOutingLineId(String batchNumber, Integer locationId, Integer lineId);
}
