package com.hvisions.wms.service;

import com.hvisions.wms.dto.adjust.AdjustOrderDTO;
import com.hvisions.wms.dto.adjust.AdjustOrderLineDTO;
import com.hvisions.wms.dto.adjust.AdjustOrderQuery;
import com.hvisions.wms.dto.adjust.SummaryDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <p>Title: AdjustOrderService</p >
 * <p>Description: 调整单业务层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date:  2020/9/9</p >
 *
 * <AUTHOR> liuwei
 * @version :1.0.0
 */
public interface AdjustOrderService {
    /**
     * 添加修改调整单
     *
     * @param adjustOrderDTO 调整单信息
     * @return 调整单
     */
    AdjustOrderDTO createOrder(AdjustOrderDTO adjustOrderDTO);


    /**
     * 删除调整单
     *
     * @param id 调整单id
     */
    void deleteOrderById(int id);

    /**
     * 查询调整单分页数据信息
     *
     * @param query 查询条件
     * @return 分页数据
     */
    Page<AdjustOrderDTO> getOrderPage(AdjustOrderQuery query);

    /**
     * 查询调整单明细信息
     *
     * @param id 调整单id
     * @return 明细信息
     */
    List<AdjustOrderLineDTO> getLine(int id);

    /**
     * 添加修改调整单明细信息
     *
     * @param lineDTO 明细信息
     * @return 明细id
     */
    Integer createLine(AdjustOrderLineDTO lineDTO);

    /**
     * 删除明细信息
     *
     * @param id 明细id
     */
    void deleteLineById(int id);

    /**
     * 确认调整单明细信息，并提交调整单，调整库存
     *
     * @param id 调整单id
     */
    void confirm(int id);

    /**
     * 查询调整概览
     *
     * @param id 调整单id
     */
    List<SummaryDTO> summary(Integer id);

    /**
     * 根据headerId查询header数据
     * @param id headerId
     * @return header数据
     */
    AdjustOrderDTO getHeaderById(Integer id);
}
