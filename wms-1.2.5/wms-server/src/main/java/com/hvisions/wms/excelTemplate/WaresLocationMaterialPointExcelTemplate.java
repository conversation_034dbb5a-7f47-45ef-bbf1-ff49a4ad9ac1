package com.hvisions.wms.excelTemplate;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 库位/料点 导入模版
 * <AUTHOR>
 * @date 2024-06-24 16:50
 */
@Data
public class WaresLocationMaterialPointExcelTemplate {

    /**
     * 仓库编号
     */
    @ExcelProperty(value = "仓库编号")
    private String warehouseCode;

    /**
     * 仓库名称
     */
    @ExcelProperty(value = "仓库名称")
    private String warehouseName;

    /**
     * 库区类型
     */
    @ExcelProperty(value = "库区类型")
    private String WarehouseAreaType;

    /**
     * 库区编码
     */
    @ExcelProperty(value = "库区编码")
    private String WarehouseAreaCode;

    /**
     * 库区名称
     */
    @ExcelProperty(value = "库区名称")
    private String WarehouseAreaName;

    /**
     * 料点/库位编号
     */
    @ExcelProperty(value = "料点/库位编号")
    private String materialPointCode;
    /**
     * 料点/库位名称
     */
    @ExcelProperty(value = "料点/库位名称")
    private String materialPointName;
    /**
     * 工位编号
     */
    @ExcelProperty(value = "工位编号")
    private String stationCode;
}
