package com.hvisions.wms.configuration;

import com.hvisions.common.dto.ExtendColumnInfo;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.client.MaterialClient;
import com.hvisions.wms.consts.ColumnsConst;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title: RunnerLoadOne</p >
 * <p>Description: 容器初始化加载方法</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date:  2020/10/20</p >
 *
 * <AUTHOR> liuwei
 * @version :1.0.0
 */
@Component
@Slf4j
public class ColumnsAutoConfig implements CommandLineRunner {

    @Autowired
    private MaterialClient materialClient;

    @Override
    public void run(String... args) {
        try {
            ResultVO<List<ExtendColumnInfo>> columnsResult = materialClient.getAllMaterialExtend();
            if (!columnsResult.isSuccess()) {
                log.info("查询物料服务失败");
                return;
            }
            List<ExtendColumnInfo> extendColumnInfos = columnsResult.getData();
            long toleranceCount = extendColumnInfos.stream()
                    .filter(columnInfo -> columnInfo.getColumnName().equals(ColumnsConst.TOLERANCE))
                    .count();
            if (toleranceCount == 0L) {
                ExtendColumnInfo extendColumnInfo = new ExtendColumnInfo();
                extendColumnInfo.setChName("允差");
                extendColumnInfo.setEnName(ColumnsConst.TOLERANCE);
                extendColumnInfo.setColumnType("DECIMAL");
                extendColumnInfo.setColumnName(ColumnsConst.TOLERANCE);
                materialClient.createMaterialExtend(extendColumnInfo);
            }
            long effectivePeriodCount = extendColumnInfos.stream()
                    .filter(columnInfo -> columnInfo.getColumnName().equals(ColumnsConst.EFFECTIVE_PERIOD))
                    .count();
            if (effectivePeriodCount == 0L) {
                ExtendColumnInfo extendColumnInfo = new ExtendColumnInfo();
                extendColumnInfo.setChName("有效期");
                extendColumnInfo.setEnName(ColumnsConst.EFFECTIVE_PERIOD);
                extendColumnInfo.setColumnType("INT");
                extendColumnInfo.setColumnName(ColumnsConst.EFFECTIVE_PERIOD);
                materialClient.createMaterialExtend(extendColumnInfo);
            }
            log.info("增加扩展字段成功");
        } catch (Exception e) {
            log.error("增加扩展字段失败", e);
        }
    }

}
