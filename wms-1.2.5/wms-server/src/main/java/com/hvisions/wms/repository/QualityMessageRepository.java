package com.hvisions.wms.repository;

import com.hvisions.wms.entity.quantity.HvWmsQualityControl;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: QualityMessagRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/9/3</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Repository
public interface QualityMessageRepository extends JpaRepository<HvWmsQualityControl, Integer> {


    /**
     * 根据请验单查询质检项目
     *
     * @param qualityId 请验单Id
     * @return 质检项目列表
     */
    List<HvWmsQualityControl> getAllByQualityId(Integer qualityId);
}