package com.hvisions.wms.repository;

/**
 * <p>Title: HvWmsInWarehouseOrderDetail</p>
 * <p>Description: 入库单详情表</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/9/3</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */

import com.hvisions.wms.entity.HvWmsInWarehouseOrderHeader;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface InWarehouseOrderHeaderRepository extends JpaRepository<HvWmsInWarehouseOrderHeader, Integer> {
    /**
     * 查询采购单对应的入库单
     * @param purchaseReceiptNumber 采购单号
     * @return 入库单列表
     */
    List<HvWmsInWarehouseOrderHeader> findAllByPurchaseReceiptNumber(String purchaseReceiptNumber);
}









