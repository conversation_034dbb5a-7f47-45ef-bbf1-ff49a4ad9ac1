package com.hvisions.wms.service.imp;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.utils.SerialUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.client.MaterialClient;
import com.hvisions.hiperbase.materials.dto.BaseMaterialDTO;
import com.hvisions.hiperbase.materials.dto.MaterialDTO;
import com.hvisions.wms.dao.AdjustOrderLineMapper;
import com.hvisions.wms.dao.AdjustOrderMapper;
import com.hvisions.wms.dto.adjust.AdjustOrderDTO;
import com.hvisions.wms.dto.adjust.AdjustOrderLineDTO;
import com.hvisions.wms.dto.adjust.AdjustOrderQuery;
import com.hvisions.wms.dto.adjust.SummaryDTO;
import com.hvisions.wms.dto.stock.AdjustStorageDTO;
import com.hvisions.wms.dto.stock.MaterialStockCount;
import com.hvisions.wms.entity.HvWmsAdjustOrder;
import com.hvisions.wms.entity.HvWmsAdjustOrderLine;
import com.hvisions.wms.entity.HvWmsStocks;
import com.hvisions.wms.enums.AdjustEnum;
import com.hvisions.wms.enums.WmsExceptionEnum;
import com.hvisions.wms.repository.AdjustOrderLineRepository;
import com.hvisions.wms.repository.AdjustOrderRepository;
import com.hvisions.wms.repository.StocksRepository;
import com.hvisions.wms.service.AdjustOrderService;
import com.hvisions.wms.service.StocksService;
import com.hvisions.wms.utils.ValidLocation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>Title: AdjustOrderServiceImpl</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date:  2020/9/9</p >
 *
 * <AUTHOR> liuwei
 * @version :1.0.0
 */
@Service
@Slf4j
public class AdjustOrderServiceImpl implements AdjustOrderService {

    private final AdjustOrderRepository adjustOrderRepository;
    private final AdjustOrderMapper adjustOrderMapper;
    private final AdjustOrderLineRepository lineRepository;
    private final AdjustOrderLineMapper adjustOrderLineMapper;
    private final StocksService stocksService;
    private final SerialUtil serialUtil;
    private final MaterialClient materialClient;
    private final StocksRepository stocksRepository;
    private final ValidLocation validLocation;

    @Autowired
    public AdjustOrderServiceImpl(AdjustOrderRepository adjustOrderRepository,
                                  AdjustOrderMapper adjustOrderMapper,
                                  AdjustOrderLineRepository lineRepository,
                                  AdjustOrderLineMapper adjustOrderLineMapper,
                                  StocksService stocksService,
                                  SerialUtil serialUtil,
                                  MaterialClient materialClient,
                                  StocksRepository stocksRepository,
                                  ValidLocation validLocation) {
        this.adjustOrderRepository = adjustOrderRepository;
        this.adjustOrderMapper = adjustOrderMapper;
        this.lineRepository = lineRepository;
        this.adjustOrderLineMapper = adjustOrderLineMapper;
        this.stocksService = stocksService;
        this.serialUtil = serialUtil;
        this.materialClient = materialClient;
        this.stocksRepository = stocksRepository;
        this.validLocation = validLocation;
    }

    @Override
    public AdjustOrderDTO createOrder(AdjustOrderDTO adjustOrderDTO) {
        HvWmsAdjustOrder orderEntity;
        if (adjustOrderDTO.getId() == null) {
            orderEntity = DtoMapper.convert(adjustOrderDTO, HvWmsAdjustOrder.class);
            orderEntity.setState(AdjustEnum.CREATE.getName());
            orderEntity.setCode("AW" + serialUtil.getSerialNumber("hvisions.serialNumber:wms.adjustOrder"));
            return DtoMapper.convert(orderEntity, AdjustOrderDTO.class);
        } else {
            orderEntity = adjustOrderRepository.getOne(adjustOrderDTO.getId());
            if (!StringUtils.contains(AdjustEnum.CREATE.getName(), orderEntity.getState())) {
                throw new BaseKnownException(WmsExceptionEnum.UPDATE_ADJUST_FAIL);
            }
            orderEntity = DtoMapper.convert(adjustOrderDTO, HvWmsAdjustOrder.class);
            orderEntity.setState(AdjustEnum.CREATE.getName());
        }
        HvWmsAdjustOrder entity = adjustOrderRepository.save(orderEntity);
        return DtoMapper.convert(entity, AdjustOrderDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteOrderById(int id) {
        HvWmsAdjustOrder hvWmsAdjustOrder = adjustOrderRepository.getOne(id);
        if (!hvWmsAdjustOrder.getState().contains(AdjustEnum.CREATE.getName())) {
            throw new BaseKnownException(WmsExceptionEnum.DELETE_ADJUST_FAIL);
        }
        adjustOrderRepository.delete(hvWmsAdjustOrder);
        lineRepository.deleteAllByDeliverOrderId(id);
    }

    @Override
    public Page<AdjustOrderDTO> getOrderPage(AdjustOrderQuery query) {
        return PageHelperUtil.getPage(adjustOrderMapper::getOrderByQuery, query);
    }

    @Override
    public List<AdjustOrderLineDTO> getLine(int id) {
        return adjustOrderLineMapper.getLine(id);
    }

    @Override
    public Integer createLine(AdjustOrderLineDTO lineDTO) {
        validLocation.valid(lineDTO.getLocationId());
        Integer deliverOrderId = lineDTO.getDeliverOrderId();
        if (deliverOrderId == null) {
            //id为空新建
            HvWmsAdjustOrder hvWmsAdjustOrder = new HvWmsAdjustOrder();
            hvWmsAdjustOrder.setCode(lineDTO.getCode());
            hvWmsAdjustOrder.setState(AdjustEnum.CREATE.getName());
            deliverOrderId = adjustOrderRepository.saveAndFlush(hvWmsAdjustOrder).getId();
            lineDTO.setDeliverOrderId(deliverOrderId);
        }
        //新增
        if (lineDTO.getId() == null) {
            Optional<HvWmsAdjustOrder> adjustOrder = adjustOrderRepository.findById(deliverOrderId);
            if (!adjustOrder.isPresent()) {
                throw new BaseKnownException(WmsExceptionEnum.ADJUST_ORDER_NOT_EXIST);
            }
            HvWmsAdjustOrder hvWmsAdjustOrder = adjustOrder.get();
            if (!hvWmsAdjustOrder.getState().contains(AdjustEnum.CREATE.getName())) {
                throw new BaseKnownException(10000, "调整单已经结束，不允许操作。");
            }
            if (!StringUtils.contains(hvWmsAdjustOrder.getState(), AdjustEnum.CREATE.getName())) {
                throw new BaseKnownException(WmsExceptionEnum.ADJUST_NOT_OPERATION);
            }
            //验证是否有相同物料，相同库位，相同批号的数据。如果有，报错
            List<HvWmsAdjustOrderLine> lines = lineRepository.findAllByDeliverOrderId(deliverOrderId);
            if (lines.stream()
                    .filter(t -> t.getMaterialId().equals(lineDTO.getMaterialId()))
                    .filter(t -> t.getLocationId().equals(lineDTO.getLocationId()))
                    .anyMatch(t -> t.getMaterialBatchNum().equals(lineDTO.getMaterialBatchNum()))) {
                throw new BaseKnownException(10000, "已经存在相同的物料信息，请检查后输入");
            }
            HvWmsAdjustOrderLine hvWmsAdjustOrderLine = DtoMapper.convert(lineDTO, HvWmsAdjustOrderLine.class);
            hvWmsAdjustOrderLine.setState(AdjustEnum.NOT_FINISH.getName());
            lineRepository.save(hvWmsAdjustOrderLine).getId();
        }
        //修改
        else {
            HvWmsAdjustOrderLine hvWmsAdjustOrderLine = lineRepository.getOne(lineDTO.getId());
            if (!StringUtils.contains(AdjustEnum.NOT_FINISH.getName(), hvWmsAdjustOrderLine.getState())) {
                throw new BaseKnownException(WmsExceptionEnum.UPDATE_ADJUST_FAIL);
            }
            hvWmsAdjustOrderLine = DtoMapper.convert(lineDTO, HvWmsAdjustOrderLine.class);
            hvWmsAdjustOrderLine.setState(AdjustEnum.NOT_FINISH.getName());
            lineRepository.save(hvWmsAdjustOrderLine).getId();
        }
        return deliverOrderId;
    }

    @Override
    public void deleteLineById(int id) {
        HvWmsAdjustOrderLine orderline = lineRepository.getOne(id);
        if (!orderline.getState().contains(AdjustEnum.NOT_FINISH.getName())) {
            throw new BaseKnownException(WmsExceptionEnum.DELETE_ADJUST_FAIL);
        }
        lineRepository.delete(orderline);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirm(int id) {
        List<HvWmsAdjustOrderLine> lineEntities = lineRepository.findAllByDeliverOrderId(id);
        if (CollectionUtils.isEmpty(lineEntities)) {
            throw new BaseKnownException(WmsExceptionEnum.ADJUST_LIVE_NOT_EXIST);
        }
        HvWmsAdjustOrder orderEntity = adjustOrderRepository.getOne(id);
        List<AdjustStorageDTO> addStorage = new ArrayList<>();
        List<AdjustStorageDTO> removeStorage = new ArrayList<>();
        if (!orderEntity.getState().contains(AdjustEnum.CREATE.getName())) {
            throw new BaseKnownException(WmsExceptionEnum.CONFIRM_ADJUST_FAIL);
        }
        for (HvWmsAdjustOrderLine line : lineEntities) {
            AdjustStorageDTO adjustStorageDTO = DtoMapper.convert(line, AdjustStorageDTO.class);
            adjustStorageDTO.setOperation(AdjustEnum.ADJUST_ORDER.getName());
            adjustStorageDTO.setOrderCode(orderEntity.getCode());
            line.setState(AdjustEnum.FINISH.getName());
            //通过物料id,库房id,批次号查找库存信息减少库存
            List<HvWmsStocks> findStocks = stocksRepository.findAllByMaterialIdAndLocationIdAndMaterialBatchNum(line.getMaterialId(),
                    line.getLocationId(), line.getMaterialBatchNum());
            if (!CollectionUtils.isEmpty(findStocks)) {
                Optional<HvWmsStocks> stock = findStocks.stream().findAny();
                adjustStorageDTO.setSupplierId(stock.get().getSupplierId());
                adjustStorageDTO.setPreMaterialBatchNum(stock.get().getPreMaterialBatchNum());
            } else {
                adjustStorageDTO.setSupplierId(0);
                adjustStorageDTO.setPreMaterialBatchNum("");
            }
            if (line.getQuantity().compareTo(BigDecimal.ZERO) >= 0) {
                addStorage.add(adjustStorageDTO);
            } else {
                adjustStorageDTO.setQuantity(BigDecimal.ZERO.subtract(adjustStorageDTO.getQuantity()));
                removeStorage.add(adjustStorageDTO);
            }
        }
        orderEntity.setCompleteTime(new Date());
        orderEntity.setState(AdjustEnum.FINISH.getName());
        stocksService.addStorageList(addStorage);
        stocksService.removeStorage(removeStorage);
        lineRepository.saveAll(lineEntities);
        adjustOrderRepository.save(orderEntity);
    }

    /**
     * 查询调整概览
     *
     * @param id 调整单id
     */
    @Override
    public List<SummaryDTO> summary(Integer id) {
        //获取所有的行信息
        List<HvWmsAdjustOrderLine> lineEntities = lineRepository.findAllByDeliverOrderId(id);
        //根据物料id列表查询库存里面的剩余数量
        List<MaterialStockCount> counts = stocksService.findMaterialCount(
                lineEntities
                        .stream()
                        .map(HvWmsAdjustOrderLine::getMaterialId)
                        .collect(Collectors.toList()));
        //获取物料信息
        ResultVO<List<MaterialDTO>> materialResult = materialClient.getMaterialsByIdList(lineEntities.stream()
                .map(HvWmsAdjustOrderLine::getMaterialId)
                .collect(Collectors.toList()));
        List<SummaryDTO> result = new ArrayList<>();
        //汇总所有的物料
        for (HvWmsAdjustOrderLine line : lineEntities) {
            Optional<SummaryDTO> exists = result.stream()
                    .filter(t -> t.getMaterialId().equals(line.getMaterialId()))
                    .findFirst();
            if (!exists.isPresent()) {
                SummaryDTO dto = new SummaryDTO();
                dto.setQuantity(line.getQuantity());
                dto.setMaterialId(line.getMaterialId());
                //找到查询到的物料数量，如果找不到给0
                dto.setPrevious(counts.stream()
                        .filter(t -> t.getMaterialId().equals(dto.getMaterialId()))
                        .findFirst()
                        .map(MaterialStockCount::getQuantity)
                        .orElse(BigDecimal.ZERO));
                //把物料的名称和编码塞进去,找不到就给N/A
                if (materialResult.isSuccess()) {
                    Optional<MaterialDTO> materialDTO = materialResult.getData()
                            .stream()
                            .filter(t -> t.getId().equals(dto.getMaterialId()))
                            .findFirst();
                    dto.setMaterialCode(materialDTO.map(BaseMaterialDTO::getMaterialCode).orElse("N/A"));
                    dto.setMaterialName(materialDTO.map(BaseMaterialDTO::getMaterialName).orElse("N/A"));
                }
                result.add(dto);
            } else {
                exists.get().setQuantity(exists.get().getQuantity().add(line.getQuantity()));
            }
        }
        return result;
    }

    /**
     * 根据headerId查询header数据
     * @param id headerId
     * @return header数据
     */
    @Override
    public AdjustOrderDTO getHeaderById(Integer id) {
        return adjustOrderMapper.getHeaderById(id);
    }
}
