package com.hvisions.wms.repository;

import com.hvisions.wms.entity.HvWmsWaresLocationProperty;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: WaresLocationPropertyRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/7/20</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Repository
public interface WaresLocationPropertyRepository extends JpaRepository<HvWmsWaresLocationProperty, Integer> {

    /**
     * 根据编码和属性类型编码查询
     *
     * @param code      属性编码
     * @param classCode 类型编码
     * @return 属性列表
     */
    List<HvWmsWaresLocationProperty> findAllByCodeAndClassCode(String code, String classCode);
}