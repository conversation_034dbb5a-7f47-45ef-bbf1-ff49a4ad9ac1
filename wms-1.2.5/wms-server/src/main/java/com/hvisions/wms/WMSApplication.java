package com.hvisions.wms;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

/**
 * <p>Title: DemoApplication</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/7</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@SpringBootApplication
@EnableDiscoveryClient
@MapperScan("com.hvisions.wms.dao")
@EnableCaching
@EnableFeignClients(basePackages = {"com.hvisions.framework.client",
        "com.hvisions.hiperbase.client","com.hvisions.thridparty.client"})
@ComponentScan(basePackages = {"com.hvisions.wms",
        "com.hvisions.framework.client.fallback", "com.hvisions.hiperbase.client.fallback"})
public class WMSApplication extends SpringBootServletInitializer {

    public static void main(String[] args) {
        SpringApplication.run(WMSApplication.class, args);
    }

    /**
     * 可以使得项目用war包部署
     *
     * @param builder builder
     * @return builder
     */
    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        return builder.sources(WMSApplication.class);
    }


}
