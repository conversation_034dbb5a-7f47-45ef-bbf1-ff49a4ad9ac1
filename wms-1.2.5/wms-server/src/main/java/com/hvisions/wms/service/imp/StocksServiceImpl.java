package com.hvisions.wms.service.imp;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.client.MaterialClient;
import com.hvisions.wms.consts.LocationStockConst;
import com.hvisions.wms.consts.StockConst;
import com.hvisions.wms.dao.StocksMapper;
import com.hvisions.wms.dto.location.WareLocationInfoDTO;
import com.hvisions.wms.dto.stock.*;
import com.hvisions.wms.entity.HvWmsHistory;
import com.hvisions.wms.entity.HvWmsStocks;
import com.hvisions.wms.entity.HvWmsStocksOccupy;
import com.hvisions.wms.entity.HvWmsStocksPre;
import com.hvisions.wms.enums.WmsExceptionEnum;
import com.hvisions.wms.exportdto.LocationStockDetail0ExportDTO;
import com.hvisions.wms.exportdto.LocationStockExportDTO;
import com.hvisions.wms.exportdto.StockMaterialExportDTO;
import com.hvisions.wms.repository.*;
import com.hvisions.wms.service.FrozenMaterialService;
import com.hvisions.wms.service.LockStateService;
import com.hvisions.wms.service.StocksService;
import com.hvisions.wms.service.WaresLocationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>Title: StocksServiceImpl</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/7/20</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Slf4j
@Service
public class StocksServiceImpl implements StocksService {

    private final StocksMapper stocksMapper;
    private final StocksRepository stocksRepository;
    private final HistoryRepository historyRepository;
    private final WaresLocationService waresLocationService;
    private final StocksOccupyRepository stocksOccupyRepository;
    private final FrozenMaterialService frozenMaterialService;
    private final StocksPreRepository stocksPreRepository;
    private final LockStateService lockStateService;
    @Resource
    private MaterialClient materialClient;
    @Resource
    private WaresLocationRepository waresLocationRepository;
    @Autowired
    public StocksServiceImpl(StocksMapper stocksMapper, StocksRepository stocksRepository, HistoryRepository historyRepository, WaresLocationService waresLocationService, StocksOccupyRepository stocksOccupyRepository, FrozenMaterialService frozenMaterialService, StocksPreRepository stocksPreRepository, LockStateService lockStateService) {
        this.stocksMapper = stocksMapper;
        this.stocksRepository = stocksRepository;
        this.historyRepository = historyRepository;
        this.waresLocationService = waresLocationService;
        this.stocksOccupyRepository = stocksOccupyRepository;
        this.frozenMaterialService = frozenMaterialService;
        this.stocksPreRepository = stocksPreRepository;
        this.lockStateService = lockStateService;
    }

    @Override
    public List<StockMaterialDTO> getStocks(StockQueryDTO stockQueryDTO) {
        List<Integer> locationId = waresLocationService.getStockLocation(stockQueryDTO.getLocationId());
        stockQueryDTO.setLocationIds(locationId);
        //优化查询速度，物料id存在时不需要根据物料编码查询
        return stocksMapper.getStocks(stockQueryDTO);
    }

    /**
     * @param stockQueryDTO 查询条件
     * @return 库存分页信息
     */
    @Override
    public Page<StockMaterialDTO> getAllByQuery(StockQueryDTO stockQueryDTO) {
        List<Integer> locationId = waresLocationService.getStockLocation(stockQueryDTO.getLocationId());
        stockQueryDTO.setLocationIds(locationId);
        //优化查询速度，物料id存在时不需要根据物料编码查询
        Page<StockMaterialDTO> page = PageHelperUtil.getPage(stocksMapper::getAllByQuery, stockQueryDTO);
        //设置是否冻结信息
        frozenMaterialService.setFrozenInfo(page);
        return page;
    }

    /**
     * 查询库存信息
     *
     * @param frameCode
     * @return 库存信息
     */
    @Override
    public List<StockMaterialDTO> getStocksByFrame(String frameCode) {
        return stocksMapper.getStocksByFrame(frameCode);
    }

    /**
     * 导出库存信息
     *
     * @return 库存信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @Override
    public ResponseEntity<byte[]> export() throws IOException, IllegalAccessException {
        StockQueryDTO stockQueryDTO = new StockQueryDTO();
        stockQueryDTO.setPageSize(Integer.MAX_VALUE);
        List<StockMaterialDTO> stockMaterialDTOS = DtoMapper.convertList(getAllByQuery(stockQueryDTO).getContent(), StockMaterialDTO.class);
        return ExcelUtil.generateImportFile(stockMaterialDTOS,
                "stocks.xls",
                StockMaterialDTO.class);
    }

    @Override
    @ApiIgnore
    public ResultVO<ExcelExportDto> exportLinker(StockQueryDTO stockQueryDTO) throws IOException, IllegalAccessException {
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(geteExportInfo(stockQueryDTO).getBody());
        excelExportDto.setFileName(StockConst.PLAN_EXPORT_FILE_NAME );
        return ResultVO.success(excelExportDto);
    }

    //获取需要导出的数据内容
    @ApiIgnore
    public ResponseEntity<byte[]> geteExportInfo(StockQueryDTO queryDTO) throws IOException, IllegalAccessException {
        // 获取库存信息
        List<StockMaterialDTO> stockMaterialDTOList = getStockListByQuery(queryDTO);
        // 钢板切割计划主表列表
        List<StockMaterialExportDTO> stockMaterialExportDTOS = DtoMapper.convertList(stockMaterialDTOList, StockMaterialExportDTO.class);
        HSSFWorkbook hssfWorkbook = new HSSFWorkbook();
        ExcelUtil.addSheetToWorkBook(stockMaterialExportDTOS, StockConst.PLAN_EXPORT_SHEET_NAME, StockMaterialExportDTO.class,null, hssfWorkbook);
        return ExcelUtil.generateHttpExcelFile(hssfWorkbook, StockConst.PLAN_EXPORT_FILE_NAME);
    }

    // 根据查询条件获取库存信息
    public List<StockMaterialDTO> getStockListByQuery(StockQueryDTO stockQueryDTO) {
        List<Integer> locationId = waresLocationService.getStockLocation(stockQueryDTO.getLocationId());
        stockQueryDTO.setLocationIds(locationId);
        List<StockMaterialDTO> stockMaterialDTOList = stocksMapper.getAllByQuery(stockQueryDTO);
        //设置是否冻结信息
        frozenMaterialService.setFrozenInfo(stockMaterialDTOList);
        return stockMaterialDTOList;
    }



    /**
     * 添加库存
     *
     * @param adjustStorageDTOS 库存增加信息列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addStorageList(List<AdjustStorageDTO> adjustStorageDTOS) {
        preAddStorage(adjustStorageDTOS);
        //获取operation和OrderCode去重的结果
        List<String> operations = adjustStorageDTOS.stream().map(AdjustStorageDTO::getOperation).distinct().collect(Collectors.toList());
        List<String> orderCodes = adjustStorageDTOS.stream().map(AdjustStorageDTO::getOrderCode).distinct().collect(Collectors.toList());
        //循环调用清算接口
        for (String operation : operations) {
            for (String orderCode : orderCodes) {
                preAddStorageWriteOff(operation, orderCode);
            }
        }
    }

    /**
     * 预先添加库存
     *
     * @param adjustStorageDTOS 添加库存信息
     */
    @Override
    public synchronized void preAddStorage(List<AdjustStorageDTO> adjustStorageDTOS) {
        checkIfLock();
        List<HvWmsStocksPre> stocksPres = DtoMapper.convertList(adjustStorageDTOS, HvWmsStocksPre.class);
        stocksPreRepository.saveAll(stocksPres);
    }

    /**
     * 取消预入库
     *
     * @param operation 业务类型
     * @param orderCode 业务号
     */
    @Override
    public void preAddStorageCancel(String operation, String orderCode) {
        List<HvWmsStocksPre> stockPres = stocksPreRepository.findAllByOperationAndOrderCode(operation, orderCode);
        stocksPreRepository.deleteAll(stockPres);
    }

    /**
     * 预入库核销
     *
     * @param operation 业务类型
     * @param orderCode 业务号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void preAddStorageWriteOff(String operation, String orderCode) {
        List<HvWmsStocksPre> stockPres = stocksPreRepository.findAllByOperationAndOrderCode(operation, orderCode);
        if (stockPres.size() == 0) {
            return;
        }
        List<HvWmsStocks> stocksList = new ArrayList<>();
        List<HvWmsHistory> histories = new ArrayList<>();
        for (HvWmsStocksPre adjustStorageDTO : stockPres) {
            Assert.isTrue(adjustStorageDTO.getQuantity().compareTo(BigDecimal.ZERO) > 0, "新增库存不能为负数");
            List<HvWmsStocks> stocks = stocksRepository.findAllByLocationId(adjustStorageDTO.getLocationId());
            //如果储位上又完全相同的物料，则直接增加数量，如果没有则新增一条数据
            //相同的判断条件。供应商都为空或者相同，物料编码和原物料编码都相同。
            Optional<HvWmsStocks> stock = stocks.stream().filter(t -> t.getMaterialId().equals(adjustStorageDTO.getMaterialId()) &&
                            t.getMaterialBatchNum().equals(adjustStorageDTO.getMaterialBatchNum()) &&
                            t.getPreMaterialBatchNum().equals(adjustStorageDTO.getPreMaterialBatchNum()) &&
                            t.getSupplierId().equals(adjustStorageDTO.getSupplierId()))
                    .findFirst();

            if (stock.isPresent()) {
                HvWmsStocks s = stock.get();
                s.setQuantity((s.getQuantity().add(adjustStorageDTO.getQuantity())));
                stocksList.add(s);
            } else {
                HvWmsStocks s = DtoMapper.convert(adjustStorageDTO, HvWmsStocks.class, "id");
                stocksList.add(s);
            }
            BigDecimal sum = stocksMapper.getSum(adjustStorageDTO.getMaterialId());
            HvWmsHistory history = DtoMapper.convert(adjustStorageDTO, HvWmsHistory.class, "id");
            history.setOriginQuantity(sum);
            histories.add(history);
        }
        stocksRepository.saveAll(stocksList);
        historyRepository.saveAll(histories);
    }

    /**
     * 减少库存
     *
     * @param adjustStorageDTOS 库存减少信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeStorage(List<AdjustStorageDTO> adjustStorageDTOS) {
        occupyStorageList(adjustStorageDTOS);
        //获取operation和OrderCode去重的结果
        List<String> operations = adjustStorageDTOS.stream().map(AdjustStorageDTO::getOperation).distinct().collect(Collectors.toList());
        List<String> orderCodes = adjustStorageDTOS.stream().map(AdjustStorageDTO::getOrderCode).distinct().collect(Collectors.toList());
        //循环调用清算接口
        for (String operation : operations) {
            for (String orderCode : orderCodes) {
                occupyWriteOff(operation, orderCode);
            }
        }
    }

    /**
     * 占用库存
     *
     * @param adjustStorageDTOS 库存信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void occupyStorageList(List<AdjustStorageDTO> adjustStorageDTOS) {
        checkIfLock();
        if (adjustStorageDTOS == null || adjustStorageDTOS.size() == 0) {
            return;
        }
        //增加限制，如果业务类型中的业务号已经占用了库存，为了避免多次占用，直接报错。
        //Set<KeyValue> collect = adjustStorageDTOS.stream().filter((v -> !StringUtils.isBlank(v.getOrderCode())))
        //        .map(t -> new KeyValue(t.getOperation(), t.getOrderCode())).collect(Collectors.toSet());
        //for (KeyValue pair : collect) {
        //    if (stocksOccupyRepository.existsByOperationAndOrderCode(pair.getKey(), pair.getValue())) {
        //        throw new BaseKnownException(WmsExceptionEnum.OCCUPY_EXISTS);
        //    }
        //}

        for (AdjustStorageDTO adjustStorageDTO : adjustStorageDTOS) {
            Assert.isTrue(adjustStorageDTO.getQuantity().compareTo(BigDecimal.ZERO) > 0, "库存占用的数量应大于0");
        }
        Boolean frozen = frozenMaterialService.isFrozen(adjustStorageDTOS.stream().map(AdjustStorageDTO::getLocationId).collect(Collectors.toList()),
                adjustStorageDTOS.stream().map(AdjustStorageDTO::getMaterialBatchNum).collect(Collectors.toList()));
        if (frozen) {
            throw new BaseKnownException(WmsExceptionEnum.LOCATION_FROZEN);
        }
        for (AdjustStorageDTO adjustStorageDTO : adjustStorageDTOS) {
            HvWmsStocks stock;
            if (adjustStorageDTO.getStockId() == null) {
                List<HvWmsStocks> stocks = stocksRepository.findAllByLocationId(adjustStorageDTO.getLocationId());
                Optional<HvWmsStocks> stockOp = stocks.stream().filter(t ->
                                t.getMaterialBatchNum().equals(adjustStorageDTO.getMaterialBatchNum()) &&
                                        t.getMaterialId().equals(adjustStorageDTO.getMaterialId())
//                    && t.getPreMaterialBatchNum().equals(adjustStorageDTO.getPreMaterialBatchNum())
//                    && t.getSupplierId().equals(adjustStorageDTO.getSupplierId())
                ).findFirst();
                if (!stockOp.isPresent()) {
                    throw new BaseKnownException(WmsExceptionEnum.QUANTITY_NOT_ENOUGH);
                }
                stock = stockOp.get();
            } else {
                stock = stocksRepository.getOne(adjustStorageDTO.getStockId());
            }

            //找到已经占用的数据，然后判断是否超限
            List<HvWmsStocksOccupy> originOccupy = stocksOccupyRepository.findAllByStockId(stock.getId());
            BigDecimal occupySum = originOccupy.stream()
                    .map(HvWmsStocksOccupy::getQuantity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (stock.getQuantity().compareTo(adjustStorageDTO.getQuantity().add(occupySum)) < 0) {
                throw new BaseKnownException(WmsExceptionEnum.QUANTITY_NOT_ENOUGH);
            } else {
                HvWmsStocksOccupy occupy = new HvWmsStocksOccupy();
                occupy.setOperation(adjustStorageDTO.getOperation());
                occupy.setOrderCode(adjustStorageDTO.getOrderCode());
                occupy.setQuantity(adjustStorageDTO.getQuantity());
                occupy.setDescription(adjustStorageDTO.getDescription());
                occupy.setStockId(stock.getId());
                stocksOccupyRepository.save(occupy);
            }
        }
    }

    /**
     * 核销占用
     *
     * @param operation 操作
     * @param orderCode 业务号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void occupyWriteOff(String operation, String orderCode) {
        List<HvWmsStocksOccupy> occupies = stocksOccupyRepository.findAllByOperationAndOrderCode(operation, orderCode);
        if (occupies.size() == 0) {
            return;
        }
        List<HvWmsHistory> histories = new ArrayList<>();
        for (HvWmsStocksOccupy occupy : occupies) {
            Optional<HvWmsStocks> stock = stocksRepository.findById(occupy.getStockId());
            stock.ifPresent(t -> {
                t.setQuantity(t.getQuantity().subtract(occupy.getQuantity()));
                if (t.getQuantity().compareTo(BigDecimal.ZERO) < 0) {
                    throw new BaseKnownException(WmsExceptionEnum.ERROR_QUANTITY);
                } else if (t.getQuantity().compareTo(BigDecimal.ZERO) == 0) {
                    stocksRepository.delete(t);
                } else {
                    stocksRepository.save(t);
                }
                BigDecimal sum = stocksMapper.getSum(t.getMaterialId());
                HvWmsHistory history = new HvWmsHistory();
                history.setOriginQuantity(sum);
                history.setPreMaterialBatchNum(t.getPreMaterialBatchNum());
                history.setDescription(occupy.getDescription());
                history.setLocationId(t.getLocationId());
                history.setMaterialId(t.getMaterialId());
                history.setQuantity(BigDecimal.ZERO.subtract(occupy.getQuantity()));
                history.setMaterialBatchNum(t.getMaterialBatchNum());
                history.setOperation(operation);
                history.setOrderCode(orderCode);
                histories.add(history);
            });
        }
        stocksOccupyRepository.deleteAll(occupies);
        historyRepository.saveAll(histories);
    }

    /**
     * 根据库存id查询物料占用信息
     *
     * @param stockId 库存id
     * @return 物料占用信息
     */
    @Override
    public List<StockOccupyDTO> getOccupy(Integer stockId) {
        List<HvWmsStocksOccupy> stockOccupies = stocksOccupyRepository.findAllByStockId(stockId);
        return DtoMapper.convertList(stockOccupies, StockOccupyDTO.class);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<HvWmsStocksDTO> getStocksByMaterialOrLocation(MaterialAndLocationIdDto materialAndLocationIdDto) {
        List<Integer> locationIds = waresLocationService.getStockLocation(materialAndLocationIdDto.getLocationId());
        materialAndLocationIdDto.setLocationIds(locationIds);
        return stocksMapper.getAllByMaterialOrLocation(materialAndLocationIdDto);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void occupyCancel(String operation, String orderCode) {
        List<HvWmsStocksOccupy> occupies = stocksOccupyRepository.findAllByOperationAndOrderCode(operation, orderCode);
        stocksOccupyRepository.deleteAll(occupies);
    }

    /**
     * 根据物料编码或者物料名称查询库存信息
     *
     * @param codeOrName 编码或者名称
     * @return 库存信息
     */
    @Override
    public List<StockMaterialMsgDTO> findStocksByCodeOrName(String codeOrName) {
        return stocksMapper.getStocksByCodeOrName(codeOrName);
    }

    /**
     * 查询物料数量
     *
     * @param materialIds 物料id列表
     * @return 物料数量
     */
    @Override
    public List<MaterialStockCount> findMaterialCount(List<Integer> materialIds) {
        return stocksMapper.findMaterialCount(materialIds);
    }

    /**
     * 获取库存概览(以物料为统计口径）
     *
     * @param query 查询条件
     * @return 分页信息
     */
    @Override
    public Page<StockInfoDTO> materialStock(StockBriefQuery query) {
        Page<StockInfoDTO> page = PageHelperUtil.getPage(stocksMapper::getMaterialStock, query);
        //填充状态数据
        List<FrozenInfo> frozenInfo = frozenMaterialService.getFrozenInfo(null, null,null);
        for (StockInfoDTO stockInfoDTO : page.getContent()) {
            Optional<FrozenInfo> frozen = frozenInfo.stream()
                    .filter(FrozenInfo::getFrozen)
                    .filter(t -> t.getLocationId().equals(stockInfoDTO.getLocationId()))
                    .findFirst();
            stockInfoDTO.setState(frozen.isPresent() ? "冻结" : "正常");
        }
        return page;
    }

    /**
     * 获取库存概览(以库位为统计口径）
     *
     * @param query 查询条件
     * @return 分页信息
     */
    @Override
    public Page<StockInfoDTO> locationStock(StockBriefQuery query) {
        Page<StockInfoDTO> locationStock = PageHelperUtil.getPage(stocksMapper::getLocationStock, query);
        //填充状态数据
        List<FrozenInfo> frozenInfo = frozenMaterialService.getFrozenInfo(null,null, null);
        for (StockInfoDTO stockInfoDTO : locationStock) {
            Optional<FrozenInfo> frozen = frozenInfo.stream()
                    .filter(FrozenInfo::getFrozen)
                    .filter(t -> t.getLocationId().equals(stockInfoDTO.getLocationId()))
                    .findFirst();
            stockInfoDTO.setState(frozen.isPresent() ? "冻结" : "正常");
        }
        return locationStock;
    }

    /**
     * 是否在锁库中
     */
    private void checkIfLock() {
        if (lockStateService.getLockState() == 1) {
            throw new BaseKnownException(WmsExceptionEnum.CHECKING);
        }
    }

    @Override
    @Transactional
    public void updateMaterialStock(String orderCode, List<StockMaterialDTO> stockMaterials) {
        List<HvWmsStocks> stocksList = new ArrayList<>();
        HvWmsStocks stocks = null;
        List<HvWmsHistory> histories = new ArrayList<>();
        boolean flag = true;
        for (StockMaterialDTO stockMaterial : stockMaterials) {
            //根据物料编码查询物料是否存在
            Integer materialId = getMaterialId(stockMaterial);
            //根据库区编码查询库区id
            Integer lId = getLocationArea(stockMaterial);
            //查询库位id
            WareLocationInfoDTO wareLocationInfoDTO = waresLocationService.getWareLocationInfoByLocationCode(stockMaterial.getLocationCode());

            //库位不为空，就带上库位进行查询
            if (wareLocationInfoDTO != null && (StringUtils.isBlank(stockMaterial.getFrameCode()) || stockMaterial.getFrameCode() == null)) {
                //不传料框，仅计算数量
                HvWmsStocks stocks1 = stocksRepository.findByMaterialIdAndLocationIdAndLocationPointId(materialId, lId, wareLocationInfoDTO.getMaterialPointId());
                if (stocks1 != null) {
                    stocks = stocks1;
                    stocks.setQuantity(stocks1.getQuantity().add(stockMaterial.getQuantity()));
                    flag = false;
                }
            } else {
                //TODO 这里等海康修改完以后再进行添加
                stocks = stocksRepository.findByMaterialIdAndLocationIdAndFrameCodeAndBlockCodeAndShipNo(materialId, lId, stockMaterial.getFrameCode(), stockMaterial.getBlockCode(), stockMaterial.getShipNo());
                //stocks = stocksRepository.findByMaterialIdAndLocationIdAndFrameCode(materialId, lId, stockMaterial.getFrameCode());
                if (stocks != null) {
                    //stocks.setQuantity(stocks.getQuantity().add(stockMaterial.getQuantity()));
                    flag = false;
                }
            }

            //上述两个都不满足，新增记录
            if (flag) {
                stocks = new HvWmsStocks();
                stocks.setLocationId(lId);//库区id
                stocks.setMaterialBatchNum(stockMaterial.getMaterialBatchNum());
                stocks.setQuantity(stockMaterial.getQuantity());
                stocks.setPreMaterialBatchNum(stockMaterial.getPreMaterialBatchNum());
                stocks.setSupplierId(0);
                stocks.setMaterialId(materialId);
                stocks.setShipNo(stockMaterial.getShipNo());
                stocks.setBlockCode(stockMaterial.getBlockCode());
                if (stockMaterial.getFrameCode() == null) {
                    stocks.setFrameCode("");
                } else {
                    stocks.setFrameCode(stockMaterial.getFrameCode());
                }
                if (wareLocationInfoDTO != null && wareLocationInfoDTO.getMaterialPointId() != null) {
                    stocks.setLocationPointId(wareLocationInfoDTO.getMaterialPointId()); //库位id
                }
            }
            if (stocks != null) {
                stocksRepository.save(stocks);
            }

            HvWmsHistory history = new HvWmsHistory();
            BigDecimal sum = null;
            if (stocks != null) {
                sum = stocksMapper.getSum(stocks.getMaterialId());
                history.setPreMaterialBatchNum(stocks.getPreMaterialBatchNum());
            }
            history.setOriginQuantity(sum);
            history.setDescription("");
            history.setLocationId(stocks.getLocationId());//库区id
            if (wareLocationInfoDTO != null && wareLocationInfoDTO.getMaterialPointId() != null) {
                history.setLocationPointId(wareLocationInfoDTO.getMaterialPointId());//库位id
            }
            history.setMaterialId(stocks.getMaterialId());
            history.setQuantity(stocks.getQuantity());
            history.setMaterialBatchNum(stocks.getMaterialBatchNum());
            history.setOperation("入库");//入库
            history.setOrderCode(orderCode);
            history.setShipNo(stocks.getShipNo());
            history.setBlockCode(stocks.getBlockCode());
            histories.add(history);
        }
        if (!histories.isEmpty())
            //记录
            historyRepository.saveAll(histories);
    }

    private Integer getLocationArea(StockMaterialDTO stockMaterial) {
        Integer lId = waresLocationRepository.findIdByCode(stockMaterial.getLocationAreaCode());
        if (lId == null) {
            throw new BaseKnownException("库区编号：" + stockMaterial.getLocationAreaCode() + "未维护！");
        }
        return lId;
    }

    private Integer getMaterialId(StockMaterialDTO stockMaterial) {
        ResultVO<Integer> m = materialClient.getMaterialIdByMaterialCodeAndEigenvalue(stockMaterial.getMaterialCode(), "1");
        Integer materialId = m.getData();
        if (materialId == null) {
            throw new BaseKnownException("物料:" + stockMaterial.getMaterialCode() + "未维护！");
        }
        return materialId;
    }


    @Override
    public void deleteStock(String orderCode, List<StockMaterialDTO> stockMaterials) {
        List<HvWmsStocks> stocksList = new ArrayList<>();
        List<Integer> stocksIdList = new ArrayList<>();
        HvWmsStocks stocks = null;

        boolean haveFrame = true, deleteFlag = false;
        List<HvWmsHistory> histories = new ArrayList<>();
        List<HvWmsStocks> deleteList = new ArrayList<>();
        List<Integer> stockIds = new ArrayList<>();
        for (StockMaterialDTO stockMaterial : stockMaterials) {

            Integer materialId = getMaterialId(stockMaterial);
            //根据库区编码查询库区id
            Integer lId = getLocationArea(stockMaterial);
            Integer locationId = waresLocationRepository.findIdByCode(stockMaterial.getLocationAreaCode());
            //没有料框
            if (StringUtils.isBlank(stockMaterial.getFrameCode())) {
                //查询库位id
                WareLocationInfoDTO wareLocationInfoDTO = waresLocationService.getWareLocationInfoByLocationCode(stockMaterial.getLocationCode());
                if (wareLocationInfoDTO == null) {
                    throw new BaseKnownException("库位编号：" + stockMaterial.getLocationCode() + "未维护！");
                }
                haveFrame = false;
                //正常就一条数据
                HvWmsStocks stocks3 = stocksRepository.findByMaterialIdAndLocationIdAndLocationPointId(materialId, locationId, wareLocationInfoDTO.getMaterialPointId());
                if (stocks3 != null){
                    stocks = stocks3;
                    //剩余数量 = 库存 - 出库数量
                    BigDecimal q = stocks3.getQuantity().subtract(stockMaterial.getQuantity());
                    //剩余0 时，需要将记录删除
                    if (q.compareTo(BigDecimal.ZERO) <= 0) {
                        deleteFlag = true;
                        deleteList.add(stocks3);
                        //需要将占用库存表删除
                        stockIds.add(stocks3.getId());
                        break;
                    } else {
                        stocks.setQuantity(q);
                    }
                }
            } else {
                //TODO 这里等海康修改完以后再进行添加
                //stocks = stocksRepository.findByMaterialIdAndLocationIdAndFrameCodeAndBlockCodeAndShipNo(materialId, locationId, stockMaterial.getFrameCode(), stockMaterial.getBlockCode(), stockMaterial.getShipNo());
                stocks = stocksRepository.findByMaterialIdAndLocationIdAndFrameCode(materialId, locationId, stockMaterial.getFrameCode());
                if (stocks == null) {
                    log.warn("物料:{},库区:{}没有记录", stockMaterial.getMaterialCode(), stockMaterial.getLocationCode());
                }
            }
            if (stocks != null) {
                //删除的记录不更新
                if (!deleteFlag) {
                    stocksList.add(stocks);
                    stocksIdList.add(stocks.getId());
                }
                HvWmsHistory history = new HvWmsHistory();
                BigDecimal sum = stocksMapper.getSum(stocks.getMaterialId());
                history.setOriginQuantity(sum);
                history.setPreMaterialBatchNum(stocks.getPreMaterialBatchNum());
                history.setDescription("");
                history.setLocationId(stocks.getLocationId());
                history.setMaterialId(stocks.getMaterialId());
                //出库数量
                history.setQuantity(BigDecimal.ZERO.subtract(stockMaterial.getQuantity()));
                history.setMaterialBatchNum(stocks.getMaterialBatchNum());
                history.setOperation("出库");//入库
                history.setOrderCode(orderCode);
                history.setShipNo(stocks.getShipNo());
                history.setBlockCode(stocks.getBlockCode());
                histories.add(history);
            }
        }
        //删除剩余为 0 的记录
        if (!deleteList.isEmpty())
            stocksRepository.deleteAll(deleteList);
        //删除占用库存
        if (!stockIds.isEmpty()) {
            stocksOccupyRepository.deleteByStockIdIn(stockIds);
        }
        //有料框的零件出库，也是直接删除
        if (haveFrame) {
            if (!stocksList.isEmpty())
                stocksRepository.deleteInBatch(stocksList);
            if (!stocksIdList.isEmpty())
                stocksOccupyRepository.deleteByStockIdIn(stockIds);
        } else {
            if (!stocksList.isEmpty())
                stocksRepository.saveAll(stocksList);
        }
        //添加历史出入库记录
        if (!histories.isEmpty())
            historyRepository.saveAll(histories);

    }

    @Override
    public void occupyRemove(Integer occupyId) {
        stocksOccupyRepository.deleteById(occupyId);
    }


    @Override
    public void batchAddOccupyStorage(List<StockOccupyDTO> stockOccupyDTOS) {

        for (StockOccupyDTO stockOccupyDTO : stockOccupyDTOS) {
            HvWmsStocksOccupy hvWmsStocksOccupy = DtoMapper.convert(stockOccupyDTO, HvWmsStocksOccupy.class);
            stocksOccupyRepository.saveAndFlush(hvWmsStocksOccupy);
        }

    }

    @Override
    public void batchUpOccupyStorage(List<StockOccupyDTO> stockOccupyDTOS) {
        for (StockOccupyDTO stockOccupyDTO : stockOccupyDTOS) {
            HvWmsStocksOccupy hvWmsStocksOccupy = DtoMapper.convert(stockOccupyDTO, HvWmsStocksOccupy.class);
            stocksOccupyRepository.saveAndFlush(hvWmsStocksOccupy);
        }
    }

    @Override
    public List<String> getStockPallet() {
        return stocksMapper.getStockPallet();
    }

    @Override
    public StockMaterialDTO getStocksById(Integer id) {
        HvWmsStocks stocks = stocksRepository.getOne(id);
        return DtoMapper.convert(stocks, StockMaterialDTO.class);
    }

    @Override
    public StockMaterialPreparationDTO getQuantityByMaterialCodeAndLocationCode(String materialCode, String locationCode) {
        return stocksMapper.getQuantityByMaterialCodeAndLocationCode(materialCode, locationCode);
    }

    @Override
    public StockMaterialPreparationDTO getUsedCountByMaterialCodeAndLocationCode(String materialCode, String locationCode) {
        return stocksMapper.getUsedCountByMaterialCodeAndLocationCode(materialCode, locationCode);
    }

    @Override
    public void addOccupyStock(StockOccupyDTO stockOccupyDTO) {
        HvWmsStocksOccupy hvWmsStocksOccupy = DtoMapper.convert(stockOccupyDTO, HvWmsStocksOccupy.class);
        stocksOccupyRepository.saveAndFlush(hvWmsStocksOccupy);
    }

    @Override
    public void upOccupyStock(StockOccupyDTO stockOccupyDTO) {
        HvWmsStocksOccupy hvWmsStocksOccupy = DtoMapper.convert(stockOccupyDTO, HvWmsStocksOccupy.class);
        stocksOccupyRepository.saveAndFlush(hvWmsStocksOccupy);
    }

    @Override
    public List<StockMaterialDTO> getLocationList() {
        return stocksMapper.getLocationList();
    }

    @Override
    public List<StockMaterialDTO> getListByFrameCode(String frameCode) {
        return stocksMapper.getListByFrameCode(frameCode);
    }

    @Override
    public List<StockOccupyDTO> getStockOccupyByOrderCodeAndMaterialCode(String orderCode, String materialCode) {
        return stocksMapper.getStockOccupyByOrderCodeAndMaterialCode(orderCode, materialCode);
    }

    /**
     * 导出库区库存
     * @param stockBriefQuery
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    @Override
    @ApiIgnore
    public ResultVO<ExcelExportDto> exportLocationStock(StockBriefQuery stockBriefQuery) throws IOException, IllegalAccessException {
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(getLocationStockExportInfo(stockBriefQuery).getBody());
        excelExportDto.setFileName(LocationStockConst.PLAN_EXPORT_FILE_NAME );
        return ResultVO.success(excelExportDto);
    }

    @Override
    public void deleteStockOccupyByOrderCode(String orderCode) {
        stocksMapper.deleteStockOccupyByOrderCode(orderCode);
    }

    @Override
    @Transactional
    public void deleteStockByFrameCode(String palletCode) {
        stocksRepository.removeByFrameCode(palletCode);
    }

    //获取库区库存导出的数据内容
    @ApiIgnore
    public ResponseEntity<byte[]> getLocationStockExportInfo(StockBriefQuery query) throws IOException, IllegalAccessException {
        // 1.查询库存信息
        List<StockInfoDTO> stockInfoDTOList =  stocksMapper.getLocationStock(query);
        //填充状态数据
        List<FrozenInfo> frozenInfo = frozenMaterialService.getFrozenInfo(null, null,null);
        for (StockInfoDTO stockInfoDTO : stockInfoDTOList) {
            Optional<FrozenInfo> frozen = frozenInfo.stream()
                    .filter(FrozenInfo::getFrozen)
                    .filter(t -> t.getLocationId().equals(stockInfoDTO.getLocationId()))
                    .findFirst();
            stockInfoDTO.setState(frozen.isPresent() ? "冻结" : "正常");
        }

        //2.查询库存详情信息
        StockQueryDTO stockQueryDTO = new StockQueryDTO();
        stockQueryDTO.setLocationIds(query.getLocationId());
        if (query.getMaterialId() != null) {
            stockQueryDTO.setMaterialId(Integer.valueOf(query.getMaterialId()));
        }
        List<StockMaterialDTO> stockMaterialDTOList = stocksMapper.getAllByQuery(stockQueryDTO);
        //设置是否冻结信息
        frozenMaterialService.setFrozenInfo(stockMaterialDTOList);

        //3.设置导出数据
        //库存列表
        List<LocationStockExportDTO> locationStockExportDTOS = DtoMapper.convertList(stockInfoDTOList, LocationStockExportDTO.class);
        //库存详情
        List<LocationStockDetail0ExportDTO> locationStockDetail0ExportDTOS = DtoMapper.convertList(stockMaterialDTOList, LocationStockDetail0ExportDTO.class);
        HSSFWorkbook hssfWorkbook = new HSSFWorkbook();
        ExcelUtil.addSheetToWorkBook(locationStockExportDTOS, LocationStockConst.PLAN_EXPORT_SHEET_NAME, LocationStockExportDTO.class,null, hssfWorkbook);
        ExcelUtil.addSheetToWorkBook(locationStockDetail0ExportDTOS, LocationStockConst.PLAN_DETAIL0_EXPORT_SHEET_NAME, LocationStockDetail0ExportDTO.class,null, hssfWorkbook);
        return ExcelUtil.generateHttpExcelFile(hssfWorkbook, LocationStockConst.PLAN_EXPORT_FILE_NAME);
    }
}
