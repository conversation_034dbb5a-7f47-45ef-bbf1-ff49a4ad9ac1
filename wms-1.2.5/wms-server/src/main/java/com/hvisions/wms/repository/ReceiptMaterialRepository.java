package com.hvisions.wms.repository;

import com.hvisions.wms.entity.receipt.HvWmsReceiptMaterial;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: ReceiptMaterialRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/8/28</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Repository
public interface ReceiptMaterialRepository extends JpaRepository<HvWmsReceiptMaterial, Integer> {


    /**
     * 根据收货单ID查询收货单物料
     *
     * @param receiptId 收货单Id
     * @return 收货单物料信息
     */
    List<HvWmsReceiptMaterial> getAllByReceiptId(int receiptId);

    /**
     * 根据收货单货物信息Id查询收货单货物信息
     *
     * @param receiptMaterialIds 收货单货物信息Id
     * @return 收货单货物信息
     */
    List<HvWmsReceiptMaterial> getAllByIdIn(List<Integer> receiptMaterialIds);
}