package com.hvisions.wms.repository;

/**
 * <p>Title: HvWmsInWarehouseOrderDetail</p>
 * <p>Description: 入库单详情表</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/9/3</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */

import com.hvisions.wms.entity.HvWmsInWarehouseOrderDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface InWarehouseOrderDetailRepository extends JpaRepository<HvWmsInWarehouseOrderDetail, Integer> {
    /**
     * 查询明细
     *
     * @param headerId 头表id
     * @return 明细信息
     */
    List<HvWmsInWarehouseOrderDetail> findAllByHeaderId(Integer headerId);

    /**
     * 查询明细
     *
     * @param headerIds 头表列表
     * @return 明细信息
     */
    List<HvWmsInWarehouseOrderDetail> findAllByHeaderIdIn(List<Integer> headerIds);
    /**
     * 查询明细
     *
     * @param lineId 行表id
     * @return 明细信息
     */
    List<HvWmsInWarehouseOrderDetail> findAllByLineId(Integer lineId);
}









