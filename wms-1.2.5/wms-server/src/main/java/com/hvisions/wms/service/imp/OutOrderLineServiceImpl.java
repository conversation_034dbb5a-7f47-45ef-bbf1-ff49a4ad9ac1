package com.hvisions.wms.service.imp;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.wms.dao.OutOrderLineMapper;
import com.hvisions.wms.dto.outstock.OutLineDTO;
import com.hvisions.wms.dto.outstock.OutMaterialDTO;
import com.hvisions.wms.dto.stock.HvWmsStocksDTO;
import com.hvisions.wms.dto.stock.MaterialAndLocationIdDto;
import com.hvisions.wms.entity.HvWmsOrderLine;
import com.hvisions.wms.entity.HvWmsOutMaterial;
import com.hvisions.wms.entity.HvWmsOutOrder;
import com.hvisions.wms.entity.HvWmsStocks;
import com.hvisions.wms.enums.OutOrderTypeEnum;
import com.hvisions.wms.enums.WmsExceptionEnum;
import com.hvisions.wms.repository.OutMaterialRepository;
import com.hvisions.wms.repository.OutOrderLineRepository;
import com.hvisions.wms.repository.OutOrderRepository;
import com.hvisions.wms.repository.StocksRepository;
import com.hvisions.wms.service.OutOrderLineService;
import com.hvisions.wms.service.StocksService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 出库单
 * Description:
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OutOrderLineServiceImpl implements OutOrderLineService {
    private final OutOrderLineRepository outOrderLineRepository;
    private final OutOrderLineMapper outStockMapper;
    private final StocksRepository stocksRepository;
    private final StocksService stocksService;
    private final OutOrderRepository outOrderRepository;
    private final OutMaterialRepository outMaterialRepository;

    @Autowired
    public OutOrderLineServiceImpl(OutOrderLineRepository outOrderLineRepository, OutOrderLineMapper outStockMapper, StocksRepository stocksRepository, StocksService stocksService, OutOrderRepository outOrderRepository, OutMaterialRepository outMaterialRepository) {
        this.outOrderLineRepository = outOrderLineRepository;
        this.outStockMapper = outStockMapper;
        this.stocksRepository = stocksRepository;
        this.stocksService = stocksService;
        this.outOrderRepository = outOrderRepository;
        this.outMaterialRepository = outMaterialRepository;
    }

    @Override
    public Integer createReceiptMaterial(OutLineDTO outLineDTO) {
        HvWmsOutOrder hvWmsOutOrder = outOrderRepository.getOne(outLineDTO.getOrderId());
        if (outLineDTO.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BaseKnownException(WmsExceptionEnum.QUANTITY_NOT_ILLEGAL);
        }
        if (!StringUtils.equals(hvWmsOutOrder.getState(), OutOrderTypeEnum.CREATE.getName())) {
            throw new BaseKnownException(WmsExceptionEnum.OPERATION_FAIL);
        }
        if (StringUtils.equals(hvWmsOutOrder.getApproveState(), OutOrderTypeEnum.APPROVE_SUCCESS.getName()) &&
                StringUtils.equals(hvWmsOutOrder.getApproveState(), OutOrderTypeEnum.APPROVE_FAIL.getName())) {
            throw new BaseKnownException(WmsExceptionEnum.CREATE_NOT_APPROVE);
        }
        HvWmsOrderLine hvWmsOrderLine = DtoMapper.convert(outLineDTO, HvWmsOrderLine.class);
        return outOrderLineRepository.save(hvWmsOrderLine).getId();
    }

    @Override
    public void createOutLine(List<OutLineDTO> outLineDTOS) {
        for (OutLineDTO outLineDTO : outLineDTOS) {
            createReceiptMaterial(outLineDTO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void autoCreateLine(Integer stockId, Integer orderId, BigDecimal quality) {
        //查询工单，如果工单不存在，报错,如果出库单不在新建状态下。报错
        HvWmsOutOrder order = outOrderRepository.findById(orderId)
                .orElseThrow(() -> new BaseKnownException(WmsExceptionEnum.OUT_ORDER_NOT_EXISTS));
        if (!StringUtils.equals(order.getState(), OutOrderTypeEnum.CREATE.getName())) {
            throw new BaseKnownException(WmsExceptionEnum.OUT_ORDER_NOT_IN_CREATE_STATE);
        }
        //如果审批不通过不允许操作
        if (StringUtils.equals(order.getApproveState(), OutOrderTypeEnum.APPROVE_FAIL.getName())) {
            throw new BaseKnownException(WmsExceptionEnum.APPROVE_FAIL);
        }
        //查询添加的物料信息
        HvWmsStocks stock = stocksRepository.getOne(stockId);
        //查询有没有相同物料的line
        Optional<HvWmsOrderLine> orderLine = outOrderLineRepository.findAllByOrderId(orderId)
                .stream()
                .filter(item -> item.getMaterialId().equals(stock.getMaterialId()))
                .findFirst();
        //如果工单还没有被审批,允许增加物料明细，如果工单已经审批通过，只允许增加详情中具体的物料不允许新增明细
        if (StringUtils.equals(order.getApproveState(), OutOrderTypeEnum.NOT_APPROVE.getName())) {
            //需要添加的物料
            HvWmsOutMaterial hvWmsOutMaterial = new HvWmsOutMaterial();
            hvWmsOutMaterial.setState(OutOrderTypeEnum.NOT_FINISH.getName());
            hvWmsOutMaterial.setMaterialBatchNum(stock.getMaterialBatchNum());
            hvWmsOutMaterial.setPreMaterialBatchNum(stock.getPreMaterialBatchNum());
            hvWmsOutMaterial.setLocationId(stock.getLocationId());
            hvWmsOutMaterial.setSupplierId(stock.getSupplierId());
            hvWmsOutMaterial.setMaterialId(stock.getMaterialId());
            hvWmsOutMaterial.setQuantity(quality);
            hvWmsOutMaterial.setOrderLineId(orderLine.orElseGet(() -> {
                HvWmsOrderLine line = new HvWmsOrderLine();
                line.setQuantity(quality);
                line.setMaterialId(stock.getMaterialId());
                line.setOrderId(orderId);
                return outOrderLineRepository.save(line);
            }).getId());
            outMaterialRepository.save(hvWmsOutMaterial);
        } else {
            //如果行不存在,直接报错
            if (!orderLine.isPresent()) {
                throw new BaseKnownException(WmsExceptionEnum.ADD_NOT_APPROVE);
            }
            //找之前是否已经添加过同样的物料，如果有，更新数量即可，如果没有就要添加物料
            Optional<HvWmsOutMaterial> material = outMaterialRepository
                    .findAllByOrderLineId(orderLine.get().getId())
                    .stream()
                    .filter(t -> t.getMaterialId().equals(stock.getMaterialId()) &&
                            t.getLocationId().equals(stock.getLocationId()) &&
                            t.getMaterialBatchNum().equals(stock.getMaterialBatchNum()))
                    .findFirst();
            if (material.isPresent()) {
                material.get().setQuantity(quality);
                outMaterialRepository.save(material.get());
            } else {
                //需要添加的物料
                HvWmsOutMaterial hvWmsOutMaterial = new HvWmsOutMaterial();
                hvWmsOutMaterial.setState(OutOrderTypeEnum.NOT_FINISH.getName());
                hvWmsOutMaterial.setMaterialBatchNum(stock.getMaterialBatchNum());
                hvWmsOutMaterial.setPreMaterialBatchNum(stock.getPreMaterialBatchNum());
                hvWmsOutMaterial.setLocationId(stock.getLocationId());
                hvWmsOutMaterial.setSupplierId(stock.getSupplierId());
                hvWmsOutMaterial.setMaterialId(stock.getMaterialId());
                hvWmsOutMaterial.setQuantity(quality);
                hvWmsOutMaterial.setOrderLineId(orderLine.get().getId());
                outMaterialRepository.save(hvWmsOutMaterial);
            }
        }


    }

    @Override
    public Integer updateReceiptMaterial(OutLineDTO outLineDTO) {
        HvWmsOutOrder hvWmsOutOrder = outOrderRepository.getOne(outLineDTO.getOrderId());
        if (!StringUtils.equals(hvWmsOutOrder.getState(), OutOrderTypeEnum.CREATE.getName())) {
            throw new BaseKnownException(WmsExceptionEnum.OPERATION_FAIL);
        }
        if (StringUtils.equals(hvWmsOutOrder.getApproveState(), OutOrderTypeEnum.APPROVE_SUCCESS.getName()) &&
                StringUtils.equals(hvWmsOutOrder.getApproveState(), OutOrderTypeEnum.APPROVE_FAIL.getName())) {
            throw new BaseKnownException(WmsExceptionEnum.CREATE_NOT_APPROVE);
        }
        HvWmsOrderLine hvWmsOrderLine = DtoMapper.convert(outLineDTO, HvWmsOrderLine.class);
        return outOrderLineRepository.save(hvWmsOrderLine).getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(int id) {
        Optional<HvWmsOrderLine> hvWmsOrderLine = outOrderLineRepository.findById(id);
        hvWmsOrderLine.ifPresent(t -> {
            Optional<HvWmsOutOrder> hvWmsOutOrder = outOrderRepository.findById(t.getOrderId());
            hvWmsOutOrder.ifPresent(f -> {
                if (!StringUtils.equals(f.getState(), OutOrderTypeEnum.CREATE.getName())) {
                    throw new BaseKnownException(WmsExceptionEnum.OUT_ORDER_DELETE_FAIL);
                }
                if (!StringUtils.equals(f.getApproveState(), OutOrderTypeEnum.NOT_APPROVE.getName())) {
                    throw new BaseKnownException(WmsExceptionEnum.OUT_ORDER_IS_APPROVED);
                }
                outOrderLineRepository.deleteById(id);
                outMaterialRepository.deleteAllByOrderLineId(id);
            });

        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void autoLocate(int id, int stockWarning) {
        List<HvWmsOrderLine> hvWmsOrderLines = outOrderLineRepository.findAllByOrderId(id);
        HvWmsOutOrder hvWmsOutOrder = outOrderRepository.getOne(id);
        if (CollectionUtils.isEmpty(hvWmsOrderLines)) {
            throw new BaseKnownException(WmsExceptionEnum.IS_NOT_MATERIAL);
        }
        if (!StringUtils.equals(hvWmsOutOrder.getState(), OutOrderTypeEnum.CREATE.getName())) {
            throw new BaseKnownException(WmsExceptionEnum.OPERATION_FAIL);
        }
        MaterialAndLocationIdDto materialAndLocationIdDto = new MaterialAndLocationIdDto();
        hvWmsOrderLines.forEach(t -> {
            List<HvWmsOutMaterial> hvWmsOutMaterials = outMaterialRepository.findAllByOrderLineId(t.getId());
            if (!CollectionUtils.isEmpty(hvWmsOutMaterials)) {
                throw new BaseKnownException(WmsExceptionEnum.MATERIAL_IS_EXIST);
            }
            materialAndLocationIdDto.setMaterialId(t.getMaterialId());
            List<HvWmsStocksDTO> stockInfo = stocksService.getStocksByMaterialOrLocation(materialAndLocationIdDto);
            List<HvWmsStocksDTO> stocks = new ArrayList<>();
            BigDecimal quantity = t.getQuantity();
            for (HvWmsStocksDTO hvWmsStocksDTO : stockInfo) {
                if ((hvWmsStocksDTO.getEffectivePeriod()) != null && (hvWmsStocksDTO.getEffectivePeriod() != 0)) {
                    Long date = System.currentTimeMillis() - hvWmsStocksDTO.getCreateTime().getTime();
                    long days = date / (1000 * 60 * 60 * 24);
                    if (days <= hvWmsStocksDTO.getEffectivePeriod().longValue()) {
                        HvWmsOutMaterial hvWmsOutMaterial = new HvWmsOutMaterial();
                        hvWmsOutMaterial.setMaterialBatchNum(hvWmsStocksDTO.getMaterialBatchNum());
                        hvWmsOutMaterial.setPreMaterialBatchNum(hvWmsStocksDTO.getPreMaterialBatchNum());
                        hvWmsOutMaterial.setOrderLineId(t.getId());
                        hvWmsOutMaterial.setState(OutOrderTypeEnum.NOT_FINISH.getName());
                        hvWmsOutMaterial.setLocationId(hvWmsStocksDTO.getLocationId());
                        hvWmsOutMaterial.setSupplierId(hvWmsStocksDTO.getSupplierId());
                        hvWmsOutMaterial.setMaterialId(hvWmsStocksDTO.getMaterialId());
                        if (hvWmsStocksDTO.getQuantity().compareTo(BigDecimal.ZERO) > 0) {
                            if (hvWmsStocksDTO.getQuantity().compareTo(quantity) >= 0) {
                                hvWmsOutMaterial.setQuantity(quantity);
                                outMaterialRepository.save(hvWmsOutMaterial);
                                stocks.add(hvWmsStocksDTO);
                                break;
                            } else {
                                hvWmsOutMaterial.setQuantity(hvWmsStocksDTO.getQuantity());
                                stocks.add(hvWmsStocksDTO);
                                quantity = quantity.subtract(hvWmsStocksDTO.getQuantity());
                                outMaterialRepository.save(hvWmsOutMaterial);
                            }
                        }
                    }
                } else {
                    HvWmsOutMaterial hvWmsOutMaterial = new HvWmsOutMaterial();
                    hvWmsOutMaterial.setMaterialBatchNum(hvWmsStocksDTO.getMaterialBatchNum());
                    hvWmsOutMaterial.setPreMaterialBatchNum(hvWmsStocksDTO.getPreMaterialBatchNum());
                    hvWmsOutMaterial.setOrderLineId(t.getId());
                    hvWmsOutMaterial.setSupplierId(hvWmsStocksDTO.getSupplierId());
                    hvWmsOutMaterial.setState(OutOrderTypeEnum.NOT_FINISH.getName());
                    hvWmsOutMaterial.setLocationId(hvWmsStocksDTO.getLocationId());
                    hvWmsOutMaterial.setMaterialId(hvWmsStocksDTO.getMaterialId());
                    if (hvWmsStocksDTO.getQuantity().compareTo(BigDecimal.ZERO) > 0) {
                        if (hvWmsStocksDTO.getQuantity().compareTo(quantity) >= 0) {
                            hvWmsOutMaterial.setQuantity(quantity);
                            outMaterialRepository.save(hvWmsOutMaterial);
                            stocks.add(hvWmsStocksDTO);
                            break;
                        } else {
                            hvWmsOutMaterial.setQuantity(hvWmsStocksDTO.getQuantity());
                            stocks.add(hvWmsStocksDTO);
                            quantity = quantity.subtract(hvWmsStocksDTO.getQuantity());
                            outMaterialRepository.save(hvWmsOutMaterial);
                        }
                    }
                }
            }
            if (CollectionUtils.isEmpty(stocks)) {
                throw new BaseKnownException(WmsExceptionEnum.QUANTITY_NOT_ENOUGH);
            }
        });
    }

    @Override
    public void setStockState(int id, Integer state) {
        throw new BaseKnownException();
    }


    @Override
    public List<OutLineDTO> findStock(int id) {
        List<OutLineDTO> outLineDTOS = outStockMapper.getOutMaterial(id);
        return outLineDTOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetStock(int id) {
        Optional<HvWmsOutOrder> hvWmsOutOrder = outOrderRepository.findById(id);
        hvWmsOutOrder.ifPresent(f -> {
            if (!StringUtils.equals(f.getState(), OutOrderTypeEnum.CREATE.getName())) {
                throw new BaseKnownException(WmsExceptionEnum.OPERATION_FAIL);
            } else {
                if (StringUtils.equals(f.getApproveState(), OutOrderTypeEnum.APPROVE_FAIL.getName())) {
                    throw new BaseKnownException(WmsExceptionEnum.DELETE_NOT_APPROVE);
                } else {
                    List<OutLineDTO> lines = findStock(id);
                    lines.forEach(t -> {
                        deleteMaterial(t.getId());
                    });
                }
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMaterial(int id) {
        Optional<HvWmsOrderLine> hvWmsOrderLine = outOrderLineRepository.findById(id);
        List<HvWmsOutMaterial> materials = outMaterialRepository.findAllByOrderLineId(id);

        hvWmsOrderLine.ifPresent(f -> {
            HvWmsOutOrder hvWmsOutOrder = outOrderRepository.getOne(f.getOrderId());
            if (StringUtils.equals(hvWmsOutOrder.getState(), OutOrderTypeEnum.CREATE.getName())) {
                materials.forEach(t -> {
                    if (StringUtils.equals(t.getState(), OutOrderTypeEnum.FINISH.getName())) {
                        throw new BaseKnownException(WmsExceptionEnum.STOCK_FINISH);
                    } else {
                        if (f.getQuantity().compareTo(BigDecimal.ZERO) == 0) {
                            outOrderLineRepository.delete(f);
                        }
                        outMaterialRepository.delete(t);
                    }
                });
            } else {
                throw new BaseKnownException(WmsExceptionEnum.OPERATION_FAIL);
            }
        });
    }

    @Override
    public void deleteMaterialById(int id) {
        Optional<HvWmsOutMaterial> hvWmsOutMaterial = outMaterialRepository.findById(id);
        hvWmsOutMaterial.ifPresent(t -> {
            Optional<HvWmsOrderLine> hvWmsOrderLine = outOrderLineRepository.findById(t.getOrderLineId());
            hvWmsOrderLine.ifPresent(f -> {
                HvWmsOutOrder hvWmsOutOrder = outOrderRepository.getOne(f.getOrderId());
                if (StringUtils.equals(hvWmsOutOrder.getState(), OutOrderTypeEnum.FINISH.getName())) {
                    throw new BaseKnownException(WmsExceptionEnum.STOCK_FINISH);
                }
                if (StringUtils.equals(hvWmsOutOrder.getState(), OutOrderTypeEnum.PIKING.getName())) {
                    throw new BaseKnownException(WmsExceptionEnum.DELIVER_IN_PICKING);
                }
                if (t.getQuantity().compareTo(BigDecimal.ZERO) == 0) {
                    outOrderLineRepository.delete(f);
                }
                outMaterialRepository.delete(t);

            });
        });
    }

    @Override
    public void verifyStock(int orderId) {
        List<OutLineDTO> lines = findStock(orderId);
        lines.forEach(t -> {
            List<OutMaterialDTO> outMaterialList = t.getOutMaterialList();
            outMaterialList.forEach(f -> {
                if (!StringUtils.contains(f.getState(), OutOrderTypeEnum.FINISH.getName())) {
                    throw new BaseKnownException(WmsExceptionEnum.STOCK_NOT_FINISH);
                }
            });
        });
    }

    @Override
    public void finishByBatchNumber(Integer orderId, String batchNumber) {
        Optional<HvWmsOutOrder> outOrder = outOrderRepository.findById(orderId);
        outOrder.ifPresent(k -> {
            if (StringUtils.contains(k.getState(), OutOrderTypeEnum.PIKING.getName())) {
                List<OutLineDTO> outLineDTOS = outStockMapper.getOutMaterial(orderId);
                outLineDTOS.forEach(t -> {
                    List<OutMaterialDTO> outMaterialList = t.getOutMaterialList();
                    Optional<OutMaterialDTO> material = outMaterialList.stream().filter(item -> item.getMaterialBatchNum().equals(batchNumber)
                            && item.getState().equals(OutOrderTypeEnum.NOT_FINISH.getName())).findFirst();
                    material.ifPresent(f -> {
                        f.setState(OutOrderTypeEnum.FINISH.getName());
                        HvWmsOutMaterial outMaterial = DtoMapper.convert(f, HvWmsOutMaterial.class);
                        outMaterialRepository.save(outMaterial);
                    });
                });
            } else if (StringUtils.contains(k.getState(), OutOrderTypeEnum.FINISH.getName())) {
                throw new BaseKnownException(WmsExceptionEnum.STOCK_FINISH);
            } else {
                throw new BaseKnownException(WmsExceptionEnum.PICK_NOT_START);
            }
        });

    }

    @Override
    public void setMaterialState(Integer id) {
        Optional<HvWmsOutMaterial> outMaterial = outMaterialRepository.findById(id);
        outMaterial.ifPresent(t -> {
            HvWmsOrderLine hvWmsOrderLine = outOrderLineRepository.getOne(t.getOrderLineId());
            HvWmsOutOrder hvWmsOutOrder = outOrderRepository.getOne(hvWmsOrderLine.getOrderId());
            if (StringUtils.contains(hvWmsOutOrder.getState(), OutOrderTypeEnum.PIKING.getName())) {
                if (StringUtils.equals(OutOrderTypeEnum.NOT_FINISH.getName(), t.getState())) {
                    t.setState(OutOrderTypeEnum.FINISH.getName());
                    outMaterialRepository.save(t);
                } else {
                    throw new BaseKnownException(WmsExceptionEnum.STOCK_FINISH);
                }
            } else if (StringUtils.contains(hvWmsOutOrder.getState(), OutOrderTypeEnum.FINISH.getName())) {
                throw new BaseKnownException(WmsExceptionEnum.STOCK_FINISH);
            } else {
                throw new BaseKnownException(WmsExceptionEnum.PICK_NOT_START);
            }
        });
    }

}
