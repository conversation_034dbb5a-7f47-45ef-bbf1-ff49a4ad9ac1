package com.hvisions.thirdparty.common.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class LineSchedulingReceiveReqDTO {
    /**
     * 请求任务编号
     * 功能说明: 产线系统生成唯一码,用于后续反馈给产线
     */
    @NotBlank(message = "请求任务编号不能为空")
    @Size(max = 32, message = "请求任务编号长度不能超过32个字符")
    private String requestCode;

    /**
     * 请求时间
     * 功能说明: 请求时间
     */
    @NotBlank(message = "请求时间不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss")
    private String reqTime;

    /**
     * 任务类型
     * 功能说明: 10=请求空框、20=满框调度、30=半框调度（板材线）,40=请求物料（焊接线），50=空框调度（焊接线）
     */
    @NotBlank(message = "任务类型不能为空")
    @Size(max = 16, message = "任务类型长度不能超过16个字符")
    private String taskType;

    /**
     * 托盘编号
     * 功能说明: 请求满框调度时不得为空
     */
    @Size(max = 16, message = "托盘编号长度不能超过16个字符")
    private String frameCode;

    /**
     * 托盘类型(托盘类型)
     * 功能说明: 请求空框时不得为空
     */
    @Size(max = 16, message = "托盘类型长度不能超过16个字符")
    private String frameType;

    /**
     * 起点料点编号
     * 功能说明: 托盘转运任务时必输起点编号。20=满框调度
     */
    @Size(max = 16, message = "起点料点编号长度不能超过16个字符")
    private String startpointCode;

    /**
     * 终点料点编号
     * 功能说明: 请求空框时，料点编号不为空。10=请求空框
     */
    @Size(max = 16, message = "终点料点编号长度不能超过16个字符")
    private String pointCode;

    /**
     * 调度优先级别
     * 功能说明: 默认为1
     */
    private Integer priority = 1;

    /**
     * 请求方系统编号
     * 功能说明: 产线使用线体编码
     */
    @Size(max = 16, message = "请求方系统编号长度不能超过16个字符")
    private String requestSystem;

    /**
     * 工位
     * 功能说明: 请求调度工位,保留字段,实际可不使用
     */
    @Size(max = 16, message = "工位长度不能超过16个字符")
    private String stationCode;

    /**
     * 物料信息
     * 功能说明: 满框调度时需包含物料信息
     */
    @Size(max = Integer.MAX_VALUE, message = "物料信息长度不能超过最大值")
    private List<LineSchedulingMatListDTO> materialList;

}
