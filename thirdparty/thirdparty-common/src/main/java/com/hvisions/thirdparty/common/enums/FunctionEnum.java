package com.hvisions.thirdparty.common.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 功能代码 第三方功能类型 枚举
 * 用于 [接收] 第三方系统数据时区分功能
 */
public enum FunctionEnum {

    UNKNOWN("UNKNOWN", "未定义"),

    COMMON01("COMMON01", "产线调度"),

    MES01("MES-ZK-INTF001", "MES-生产工单"),
    //    MES02("MES-ZK-INTF002", "MES工序报工接口（工序级开工/完工）"),
    MES03("MES-ZK-INTF003", "MES-工序报工"),
    //    MES04("MES-ZK-INTF004", "组立BOM基础数据下发接口（组立、零件工艺路线）"),
    MES05("MES-ZK-INTF005", "MES-组立BOM基础数据"),
    MES06("MES-ZK-INTF006", "MES-零件基础数据"),
    MES07("MES-ZK-INTF007", "MES-工厂建模基础数据"),
    //    MES08("MES-ZK-INTF008", "钢板/型材原料库存信息同步（智能堆场内钢板/型材库存信息上传）"),
MES26("MES-ZK-INTF026", "MES-外发料框回库"),

    MES09("MES-ZK-INTF009", "MES-库存信息同步"),

    //    MES10("MES-ZK-INTF010", "型材立库内型材库存信息上传（立库出入库、在库信息上传）"),
//    MES11("MES-ZK-INTF011", "钢板-零件追溯信息上传（钢板号-零件编号对应关系）"),
    MES12("MES-ZK-INTF012", "MES-场内物流执行结果"),
    MES13("MES-ZK-INTF013", "MES-缓存区库存信息上传"),
    //    MES14("MES-ZK-INTF014", "钢板余料信息上传（钢板余料码、重量、料框、库位等信息）"),
    MES15("MES-ZK-INTF015", "MES-钢板余料信息上传"),
    //    MES16("MES-ZK-INTF016", "外协入库信息下发（场外缓存区转场内缓存区入库信息）"),
    MES17("MES-ZK-INTF017", "外协入库信息下发（场外缓存区转场内缓存区入库信息）"),
    MES18("MES-ZK-INTF018", "外发库存信息上传（场内转场外时，上传场内库存信息）"),
    MES19("MES-ZK-INTF019", "MES-外发库存信息更新（外发库执行物流转运时，同步转运结果库存更新信息）"),
    //    MES20("MES-ZK-INTF020", "焊接工艺参数上传接口"),
//    MES21("MES-ZK-INTF021", "质量数据上传（焊缝、工艺参数）"),
//    MES22("MES-ZK-INTF022", "设备自检结果上传（开工自检、点检）"),
//    MES23("MES-ZK-INTF023", "设备状态信息上传（异常报警、正常、待机、启动等状态）"),
    MES24("MES-ZK-INTF024", "MES-生产工单开工"),

    MES30("MES-ZK-INTF030", "MES-外协件立库库存同步"),
    MES32("MES-ZK-INTF032", "MES-外协件入外协平库通知"),
    MES34("MES-ZK-INTF034", "MES-同步空托返回"),
//    MES25("MES-ZK-INTF024", "交付入库正点率指标上传"),
    MES40("MES-LES-INTF010","MES-生产工单"),
    MES41("MES-LES-INTF012","MES-焊接物料配送指令下发"),
    MES42("MES-LES-INTF013","MES-焊接线空框返回"),
//    XC02("ZK-XC-INTF002", "大界-套料图同步"),
    XC03("ZK-XC-INTF003", "大界-任务取消（或冻结）"),
    XC04("ZK-XC-INTF004", "大界-任务报工"),
    XC06("ZK-XC-INTF006", "大界-允许投料"),
    XC07("ZK-XC-INTF007", "大界-备料内容上传"),
//    XC08("ZK-XC-INTF008", "大界-分拣信息上传"),
    XC09("ZK-XC-INTF009", "大界-满框调度入库"),
//    XC10("ZK-XC-INTF010", "大界-空框请求"),
    XC11("ZK-XC-INTF011", "大界-调度反馈"),

    XL01("ZK-XL-INTF001", "华工-钢板切割任务下发"),
    //    XL02("ZK-XL-INTF002", "华工-套料图同步"),
    XL03("ZK-XL-INTF003", "华工-切割任务报工"),
    //    XL04("ZK-XL-INTF004", "华工-空框请求"),
//    XL05("ZK-XL-INTF005", "华工-钢板防错结果（异常报警）"),
//    XL06("ZK-XL-INTF006", "华工-钢板/零件追溯关系上传（钢板对应的切割零件追溯关系）"),
//    XL07("ZK-XL-INTF007", "华工-切割工艺参数上传"),
//    XL08("ZK-XL-INTF008", "华工-料框-零件绑定关系上传"),
//    XL09("ZK-XL-INTF009", "华工-零件分拣结果上传（完成/不完成）"),
    XL10("ZK-XL-INTF010", "华工-满框调度请求"),
    XL11("ZK-XL-INTF011", "华工-调度执行反馈"),
    //    XL12("ZK-XL-INTF012", "华工-切割线设备状态上传（异常报警、正常、待机、启动等状态）"),
    XL13("ZK-XL-INTF013", "华工-打磨计划下发"),

//    HJ01("ZK-HJ-INTF001", "华工-组立任务下发"),
    //    HJ03("ZK-HJ-INTF003", "组对物料绑定上传（物料校验、组立/零件编码绑定关系）"),
    HJ04("ZK-HJ-INTF004", "华工-组立工单报工"),
//    HJ05("ZK-HJ-INTF005", "华工-成品下线"),
    HJ06("ZK-HJ-INTF006", "华工-返修调度"),//取消 ?
    HJ07("ZK-HJ-INTF007", "华工-焊接过程工艺"),
    HJ08("ZK-HJ-INTF008", "华工-焊接生产叫料"),
    HJ09("ZK-HJ-INTF009", "华工-空框回库"),
    HJ10("ZK-HJ-INTF010", "华工-执行反馈"),
    HJ12("ZK-HJ-INTF012", "华工-打磨计划报工"),
//    HJ11("ZK-HJ-INTF011", "华工-异常信息上报"),

    WL04("ZK-WL-INTF004", "I-WMS-物流任务执行结果"),
    WL06("ZK-WL-INTF006", "I-WMS库存信息上传"),
    WL07("ZK-WL-INTF007", "I-WMS-AGV状态上传"),
    WL12("ZK-WL-INTF012", "I-WMS-料框主数据同步"),
    WL13("ZK-WL-INTF013", "I-WMS-库区库位主数据同步"),
    WL14("ZK-WL-INTF014", "I-WMS-立库出库货架走出储位"),
    WL17("ZK-WL-INTF017", "I-WMS-RCMS搬运结束通知"),
    WL18("ZK-WL-INTF018", "I-WMS-空料框预警信息"),

    TL02("ZK-TL-INTF002", "套料系统-钢板切割任务接收"),
    TL06("ZK-TL-INTF006", "套料系统-型材切割任务接收"),

    WMS03("ZK-iWMS-INTF003", "WMS-堆场库存更新"),

    WMS07("ZK-iWMS-INTF007", "预处理-钢板/型材过点信息上传"),
    WMS12("ZK-iWMS-INTF012", "型材立库-库存信息更新"),
    WMS13("ZK-iWMS-INTF013", "华工-型材立库出库结果"),
    WMS14("ZK-iWMS-INTF014", "大界-型材零件工单下发"),
    iWMS15("ZK-iWMS-INTF015", "型材立库备料请求下发"),
    iWMS16("ZK-iWMS-INTF016", "型材立库备料结果接收"),

    WMS18("ZK-iWMS-INTF018", "华工-理料间库存"),

    LK01("ZK-LK-INTF001", "立库-库存信息上传"),
    LK02("ZK-LK-INTF002", "立库-出库到位信息上传"),
    LK03("ZK-LK-INTF003", "立库-入库到位信息"),
    LK04("ZK-LK-INTF004", "立库-上料点状态上传"),
    LK05("ZK-LK-INTF005", "立库-出库任务下发"),
    LK06("ZK-LK-INTF006", "立库-搬出结果下发"),
    RCS01("RCS-MOM-INTF001","RCS任务执行过程回馈"),
    RCS02("RCS-MOM-INTF002","RCS任务执行过程回馈"),
    MOM01("IF_CMWH_MOM_01","库存信息同步"),
    MOM02("IF_CMWH_MOM_02","同步出入库记录数据"),
    Xk001("XK-MOM-INTF001", "线控-MOM板材切割任务报工"),
    BCX001("BCX-MOM-INTF001","板材线-MOM产线调度请求"),
    XCX001("CCX-MOM-INTF001","型材线-MOM产线调度请求"),
    LES02("IF_CMWH_JPLES_MOM_02","西部车间到总装车间转运工单完成反馈"),
    LES04("IF_CMWH_JPLES_MOM_04","舾装件托盘到货反馈"),
    //下发产线调度反馈通用接口
    MOM100("MOM-INTF100","下发产线调度反馈（通用）")
    ;

    static Map<String, FunctionEnum> enumMap = new HashMap<>();

    static {
        for (FunctionEnum value : FunctionEnum.values()) {
            enumMap.put(value.code, value);
            enumMap.put(value.desc, value);
        }
    }

    String code;
    String desc;

    FunctionEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static FunctionEnum getByCode(String code) {
        return enumMap.get(code);
    }

    public static FunctionEnum getByDesc(String desc) {
        return enumMap.get(desc);
    }
}
