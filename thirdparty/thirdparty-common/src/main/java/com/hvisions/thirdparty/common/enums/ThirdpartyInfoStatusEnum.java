package com.hvisions.thirdparty.common.enums;

public enum ThirdpartyInfoStatusEnum {

    WAIT(0, "待处理"),

    OK(1, "已处理"),

    NG(2, "处理失败");

    int code;
    String desc;

    ThirdpartyInfoStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
