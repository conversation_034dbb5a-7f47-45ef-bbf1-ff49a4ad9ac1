package com.hvisions.thirdparty.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum LineSchedulingTypeEnum {
    //    10=请求空框、20=满框调度、30=空框回库
    EMPTY("10", "请求空框"),
    FULL("20", "满框调度"),
    EMPTY_RETURN("30", "空框回库"),
    OUTBOUND("40", "叫料出库"),
    EMPTY_ARRIVED("50", "空框流转"),
    TRANSPORT("60", "托盘转运"),
    SURPLUS_RETURN("70", "焊接线余料回库"),
    PRODUCT_BELOW("90","焊接线成品下料")
    ;
    static Map<String, LineSchedulingTypeEnum> enumMap = new HashMap<>();

    static {

        for (LineSchedulingTypeEnum value : LineSchedulingTypeEnum.values()) {
            enumMap.put(value.code, value);
            enumMap.put(value.desc, value);
        }
    }

    String code;
    String desc;

    LineSchedulingTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static LineSchedulingTypeEnum getByCode(String code) {
        return enumMap.get(code);
    }

    public static LineSchedulingTypeEnum getByDesc(String desc) {
        return enumMap.get(desc);
    }
}
