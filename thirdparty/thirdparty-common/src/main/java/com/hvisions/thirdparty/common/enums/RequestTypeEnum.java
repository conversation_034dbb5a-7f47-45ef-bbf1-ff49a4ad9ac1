package com.hvisions.thirdparty.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum RequestTypeEnum {

    GET(0, "GET"),

    POST(1, "POST"),

    PUT(2, "PUT"),

    DEL(3, "DELETE");

    int code;
    String desc;

    RequestTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    static Map<Integer, RequestTypeEnum> enumMap = new HashMap<>();

    static {
        for (RequestTypeEnum value : RequestTypeEnum.values()) {
            enumMap.put(value.code, value);
        }
    }

    public static RequestTypeEnum getByCode(int code) {
        return enumMap.get(code);
    }
}
