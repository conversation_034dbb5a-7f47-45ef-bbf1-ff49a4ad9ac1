package com.hvisions.thirdparty.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum RcsSendMsgCodeEnum {
    Err_TaskTypeNotSupport("Err_TaskTypeNotSupport", "任务类型不支持"),

    Err_RobotGroupsNotMatch("Err_RobotGroupsNotMatch", "机器人资源组编号与任务不匹配，无法调度"),

    Err_RobotCodeNotMatch("Err_RobotCodeNotMatch", "机器人编号与任务不匹配，无法调度"),

    Err_TargetRouteError("Err_TargetRouteError", "任务路径参数有误"),

    SUCCESS("SUCCESS","任务发送成功"),
    ERROR("ERROR","任务发送失败");

    String code;
    String desc;

    RcsSendMsgCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    static Map<String, RcsSendMsgCodeEnum> enumMap = new HashMap<>();

    static {
        for (RcsSendMsgCodeEnum value : RcsSendMsgCodeEnum.values()) {
            enumMap.put(value.getCode(), value);
        }
    }

    public static RcsSendMsgCodeEnum getByCode(String code) {
        return enumMap.get(code);
    }
}
