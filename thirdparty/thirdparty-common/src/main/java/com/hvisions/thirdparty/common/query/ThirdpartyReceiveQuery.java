package com.hvisions.thirdparty.common.query;


import com.hvisions.common.dto.PageInfo;
import lombok.Data;

import java.util.Date;

@Data
public class ThirdpartyReceiveQuery extends PageInfo {

    //内容类型
    private String infoType;

    //内容
    private String contentInfo;

    //状态
    private Integer status;

    //创建开始时间
    private Date beginTime;

    //创建结束时间
    private Date endTime;

    //更新开始时间
    private Date updateBeginTime;

    //更新结束时间
    private Date updateEndTime;
}
