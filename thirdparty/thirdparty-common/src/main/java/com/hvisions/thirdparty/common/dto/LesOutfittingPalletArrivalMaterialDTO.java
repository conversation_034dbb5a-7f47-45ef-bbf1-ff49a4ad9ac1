package com.hvisions.thirdparty.common.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
public class LesOutfittingPalletArrivalMaterialDTO {

    /**
     * 预艏装件编码
     * 说明：舱装件的编码标识
     * 错误消息：预艏装件编码不能为空
     */
    @NotBlank(message = "预艏装件编码不能为空")
    @Size(max = 10, message = "预艏装件编码长度不能超过10个字符")
    @JsonAlias("materialCode")
    private String materialCode;

    /**
     * 预艏装件名称
     * 说明：舱装件的名称
     * 错误消息：预艏装件名称不能为空
     */
    @NotBlank(message = "预艏装件名称不能为空")
    @Size(max = 100, message = "预艏装件名称长度不能超过100个字符")
    @JsonAlias("materialName")
    private String materialName;

    /**
     * 规格型号
     * 说明：舱装件的规格型号
     * 错误消息：规格型号不能为空
     */
    @NotBlank(message = "规格型号不能为空")
    @Size(max = 100, message = "规格型号长度不能超过100个字符")
    @JsonAlias("materialModel")
    private String materialModel;

    /**
     * 数量
     * 说明：舱装件的数量
     */
    @Digits(integer = 10,fraction = 0,message = "最大长度不超过10")
    @JsonAlias("quantity")
    private Integer quantity;

    /**
     * 数量单位
     * 说明：数量的单位（中文名称）
     * 错误消息：数量单位长度不能超过50个字符
     */
    @Size(max = 50, message = "数量单位长度不能超过50个字符")
    @JsonAlias("unit")
    private String unit;
}
