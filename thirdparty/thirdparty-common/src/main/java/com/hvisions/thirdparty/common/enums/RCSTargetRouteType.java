package com.hvisions.thirdparty.common.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum RCSTargetRouteType {
    ZONE("ZONE"),//目标所称处区域编号
    SITE("SITE"),//站点别名
    STORAGE("STORAGE"),//仓位别名
    MAT_LABEL("MAT_LABEL"),//物料标签
    CARRIER("CARRIER"),//载具编号
    STACK("STACK"),//巷道编号
    STACK_LABEL("STACK_LABEL"),//巷道特征值
    CHANNEL("CHANNEL"),//通道编号
    EQPT("EQPT"),//外部设备
    PTL_WALL("PTL_WALL"),//CTU缓存架
    CARRIER_TYPE("CARRIER_TYPE"),//载具类型
    BIN_TYPE("BIN_TYPE"),//仓位类型
    AREA_STATION("AREA_STATION"),//区域工作台
    PILE_COUNT("PILE_COUNT"),//一次搬几个
    MIX_CONDITION("MIX_CONDITION"),//组合条件
    DEGR("DEGR"),//热度
    MULTI_BIN_GROUP("MULTI_BIN_GROUP");//多深位组号

    private final String val;

    RCSTargetRouteType(String val){
        this.val = val;
    }


}
