package com.hvisions.thirdparty.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.List;

@Data
public class MaterialCuttingLineReportToZFDTO {

    @ApiModelProperty("工单号")
    private String orderCode;

    @ApiModelProperty("报工类型")
    private String reportType;

    @ApiModelProperty("物料编码")
    private String materialCode;

    @ApiModelProperty("物料型号")
    private String materialType;

    @ApiModelProperty("船号")
    private String shipNumber;

    @ApiModelProperty("分段号")
    private String segmentationCode;

    @ApiModelProperty("报工产线")
    private String lineCode;

    @ApiModelProperty("工位编号")
    private String stationCode;

    @ApiModelProperty("工位名称")
    private String stationName;

    @ApiModelProperty("上报时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String reportTime;

    @ApiModelProperty("实际开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String actualStartTime;

    @ApiModelProperty("实际完成时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String actualCompletionTime;

    @ApiModelProperty("消耗工时（分）")
    private Integer depleteTime;

    @ApiModelProperty("生产设备编码")
    private String equipmentCode;

    @ApiModelProperty("报工用户编码")
    private String reportUserCode;

    @ApiModelProperty("报工用户姓名")
    private String reportUserName;

    @ApiModelProperty("报工数量")
    private Integer reportQty;

    @ApiModelProperty("合格数")
    private Integer qualifiedQty;

    @ApiModelProperty("报废数量")
    private Integer scrapQty;

    @ApiModelProperty("返修数量")
    private Integer repairQty;

    @ApiModelProperty("补充报工")
    private Integer supplement;

    @ApiModelProperty(value = "原料库位编码")
    private String pickingPositionCode;

    @ApiModelProperty("开始上料（yyyy-MM-dd HH:mm:ss）")
    private String startFeeding;

    @ApiModelProperty("结束上料 （yyyy-MM-dd HH:mm:ss）")
    private String endFeeding;

    @ApiModelProperty("喷码画线开始 （yyyy-MM-dd HH:mm:ss）")
    private String startCodingMarking;

    @ApiModelProperty("喷码画线结束 （yyyy-MM-dd HH:mm:ss）")
    private String endCodingMarking;

    @ApiModelProperty("切割开始 （yyyy-MM-dd HH:mm:ss）")
    private String startCutting;

    @ApiModelProperty("切割结束 （yyyy-MM-dd HH:mm:ss）")
    private String endCutting;

    @ApiModelProperty("齐套时间 （yyyy-MM-dd HH:mm:ss）")
    private String completeTime;

    @ApiModelProperty("零件信息")
    private List<MaterialsDTO> materialList;
}
