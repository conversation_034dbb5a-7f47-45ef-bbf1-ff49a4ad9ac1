package com.hvisions.thirdparty.common.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 00 表示：位置编号 01 表示：物料批次号 02 表示：策略编号（含多个区域） 03 表示：货架编号，通过货架编号找到货架所在位置
 *  * 04 表示：区域编号，在区域中查找 可用位置 如：第一个区域放不下, 可以放第二 个区域
 */
public enum AGVPositionTypeEnum {



    LOCATION("00", "位置编号"),
    MATERIAL_BATCH("01", "物料批次号"),
    STRATEGY("02", "策略编号"),
    FRAME_CODE("03", "货架编号"),
    AREA_CODE("04", "区域编号"),
    ;

    static Map<String, AGVPositionTypeEnum> enumMap = new HashMap<>();

    static {
        for (AGVPositionTypeEnum value : AGVPositionTypeEnum.values()) {
            enumMap.put(value.code, value);
        }
    }

    String code;
    String desc;

    AGVPositionTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static AGVPositionTypeEnum getByCode(String code) {
        return enumMap.get(code);
    }
}
