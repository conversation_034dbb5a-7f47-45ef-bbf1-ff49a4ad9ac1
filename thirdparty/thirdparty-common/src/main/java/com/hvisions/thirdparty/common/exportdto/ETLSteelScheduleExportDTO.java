package com.hvisions.thirdparty.common.exportdto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <P>    <P>
 *
 * <AUTHOR>
 * @date 2025/2/8
 */
@Data
public class ETLSteelScheduleExportDTO {
    @ApiModelProperty(value = "id")
    private Long id;

    //产线编码
    @ApiModelProperty(value = "产线编码")
    private String lineCode;

    //产线名称
    @ApiModelProperty(value = "产线名称")
    private String lineName;

    //日期
    @ApiModelProperty(value = "日期")
    private String statisticalDate;

    //进度
    @ApiModelProperty(value = "进度")
    private Double schedule;

    //零件总重量
    @ApiModelProperty(value = "零件总重量")
    private Double totalPartWeight;

    //零件个数
    @ApiModelProperty(value = "零件个数")
    private Integer partNumber;
}
