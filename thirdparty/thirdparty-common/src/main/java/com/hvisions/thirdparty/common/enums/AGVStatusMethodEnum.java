package com.hvisions.thirdparty.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum AGVStatusMethodEnum {

    //start : 任务开始 outbin : 走出储位 end : 任务结束 cancel : 任务单取消 apply：CTU 料箱取放申请
    start("start", "任务开始"),
    out("outbin", "走出储位"),
    end("end", "任务结束"),
    cancel("cancel", "任务单取消"),
    apply("apply", "料箱取放申请"),
    end4JH("end4JH", "任务结束"),
    outbin4JH("outbin4JH", "走出储位");

    static Map<String, AGVStatusMethodEnum> enumMap = new HashMap<>();

    static {
        for (AGVStatusMethodEnum value : AGVStatusMethodEnum.values()) {
            enumMap.put(value.code, value);
        }
    }

    String code;
    String desc;

    AGVStatusMethodEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static AGVStatusMethodEnum getByCode(String code) {
        return enumMap.get(code);
    }
}
