package com.hvisions.thirdparty.common.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class LesOutfittingPalletArrivalFeedBackDTO {

    /**
     * 工单编号
     * 说明：用于唯一标识一个工单
     * 错误消息：工单编号不能为空
     */
    @NotBlank(message = "工单编号不能为空")
    @Size(max = 50, message = "工单编号长度不能超过50个字符")
    private String workOrderNumber;

    /**
     * 托盘集合
     * 说明：包含多个托盘信息的列表
     * 错误消息：托盘集合不能为空
     */
    @NotNull(message = "托盘集合不能为空")
    private List<LesOutfittingPalletArrivalTrayDTO> trayList;
}
