package com.hvisions.thirdparty.common.dto;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Size;

@Data
public class MesWeldingEmptyFreamReturnMaterialDTO {
    // 物料编号
    @Size(max = 32, message = "物料编号长度不能超过32")
    private String materialCode;

    // 物料名称
    @Size(max = 32, message = "物料名称长度不能超过32")
    private String materialName;

    // 物料数量（质量字段实际应为数量）
    @Min(value = 1, message = "数量最小值为1")
    @Max(value = 999, message = "数量最大值为999")
    private Integer quality;

}
