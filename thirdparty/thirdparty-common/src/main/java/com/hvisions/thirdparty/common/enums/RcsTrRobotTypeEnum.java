package com.hvisions.thirdparty.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum RcsTrRobotTypeEnum {
    GROUPS("GROUPS", "机器人资源组编号"),

    ROBOTS("ROBOTS", "机器人编号");

    String code;
    String desc;

    RcsTrRobotTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    static Map<String, RcsTrRobotTypeEnum> enumMap = new HashMap<>();

    static {
        for (RcsTrRobotTypeEnum value : RcsTrRobotTypeEnum.values()) {
            enumMap.put(value.getCode(), value);
        }
    }

    public static RcsTrRobotTypeEnum getByCode(String code) {
        return enumMap.get(code);
    }

}
