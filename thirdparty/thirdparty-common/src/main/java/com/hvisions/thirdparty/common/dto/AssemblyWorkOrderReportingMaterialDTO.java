package com.hvisions.thirdparty.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 组立工单报工零件详情
 * <AUTHOR>
 * @date 2024-04-07 9:21
 */
@Data
public class AssemblyWorkOrderReportingMaterialDTO {
    /**
     * 物料号
     */
    @ApiModelProperty(value = "物料号")
    private String materialCode;
    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    private String materialName;
    /**
     * 物料sn码
     */
    @ApiModelProperty(value = "物料pn码")
    private String pn;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private int quality;
    /**
     * 合格数
     */
    @ApiModelProperty(value = "合格数")
    private int qualifiedQty;
    /**
     * 丢失数量
     */
    @ApiModelProperty(value = "丢失数量")
    private int lossQty;
    /**
     * 报废数量
     */
    @ApiModelProperty(value = "报废数量")
    private int scrapQty;
    /**
     * 返修数量
     */
    @ApiModelProperty(value = "返修数量")
    private int repairQty;

    @ApiModelProperty("托盘编号")
    private String palletCode;

    /**
     * 零件信息(组立零件才有)
     */
    @ApiModelProperty(value = "零件信息(组立零件才有)")
    private List<AssemblyWorkOrderReportingMaterialDTO> materialList;
}
