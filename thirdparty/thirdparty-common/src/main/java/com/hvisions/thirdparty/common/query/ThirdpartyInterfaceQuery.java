package com.hvisions.thirdparty.common.query;

import com.hvisions.common.dto.PageInfo;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-08-29 10:37
 */
@Data
public class ThirdpartyInterfaceQuery extends PageInfo {
    //接口
    private String interfaceCode;
    //对接系统
    private Integer systemId;
    //请求/反馈
    private Integer requestOrResponse;
    //队列
    private String queue;
    //主题
    private String topic;
    //地址
    private String url;
    //请求类型
    private String requestType;
    //通讯类型
    private Integer communicationType;
    //启用
    private Integer enable;
    //起始时间
    private String startTime;
    //截止时间
    private String endTime;

}
