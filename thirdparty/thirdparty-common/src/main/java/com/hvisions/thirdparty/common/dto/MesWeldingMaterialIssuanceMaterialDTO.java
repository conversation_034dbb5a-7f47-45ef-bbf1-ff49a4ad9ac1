package com.hvisions.thirdparty.common.dto;

import lombok.Data;

import javax.validation.constraints.*;

@Data
public class MesWeldingMaterialIssuanceMaterialDTO {
    // 物料编号（满框调度时必填）
    @NotBlank(message = "物料编号不能为空")
    @Size(max = 32, message = "物料编号长度不能超过32")
    private String materialCode;

    // 物料名称
    @Size(max = 32, message = "物料名称长度不能超过32")
    private String materialName;

    // 数量（满框调度时必填）
    @NotNull(message = "数量不能为空")
    @Min(value = 1, message = "数量至少为1")
    @Max(value = 999, message = "数量不能超过999") // 长度为3
    private Integer quality;

    // 物料摆放顺序（从1开始）
    //满框调度时需包含物料信息，从1开始，1为最底下，越往上数字越大，仅用于大板切割线大件（底板）下料
    @Min(value = 1, message = "物料摆放顺序至少为1")
    private Integer matOrder;
}
