package com.hvisions.thirdparty.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum StockRequestTypeEnum {

    ADD("ADD", "新增" ),
    SUB("SUB", "出库" ),


    ;

    StockRequestTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    static Map<String, StockRequestTypeEnum> enumMap = new HashMap<>();

    static {
        for (StockRequestTypeEnum value : StockRequestTypeEnum.values()) {
            enumMap.put(value.code, value);
        }
    }
    public static StockRequestTypeEnum getByCode(String code) {
        return enumMap.get(code);
    }

    private String code;

    private String desc;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
