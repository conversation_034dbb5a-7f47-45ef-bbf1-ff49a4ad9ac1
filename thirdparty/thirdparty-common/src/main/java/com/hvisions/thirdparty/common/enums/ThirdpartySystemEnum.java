package com.hvisions.thirdparty.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum ThirdpartySystemEnum {

    MES("MES", "MES"),

    HG_DC("HG_DC","华工-堆场"),
    HJ4("HJ4", "华工-异形小组立"),
    HJ1("HJ1", "华工-先行组立焊接B线"),
    HJ2("HJ2", "华工-先行组立焊接A线"),
    HJ3("HJ3", "华工-板架激光复合焊接线"),
    HG_DM("HG_DM","华工-打磨"),

    HG_QG_C("HG_QG_C","华工-大板切割线线控"),
    HG_QG_B("HG_QG_B","华工-部件切割B线线控"),
    HG_QG_A("HG_QG_A","华工-部件切割A线线控"),

    DJ_QG_B("DJ_QG_B","大界-型材切割B线线控"),
    DJ_QG_A("DJ_QG_A","大界-型材切割A线线控"),

    AGV_I_WMS("AGV/I-WMS","海康-RCS/I-WMS"),

    NEST("NEST","兰特克-套料系统"),

    HG_XC_LK("HG_XC_LK","华工-型材立库"),
    ;

    ThirdpartySystemEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    static Map<String, ThirdpartySystemEnum> enumMap = new HashMap<>();

    static {
        for (ThirdpartySystemEnum value : ThirdpartySystemEnum.values()) {
            enumMap.put(value.code, value);
        }
    }
    public static ThirdpartySystemEnum getByCode(String code) {
        return enumMap.get(code);
    }

    private String code;

    private String desc;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
