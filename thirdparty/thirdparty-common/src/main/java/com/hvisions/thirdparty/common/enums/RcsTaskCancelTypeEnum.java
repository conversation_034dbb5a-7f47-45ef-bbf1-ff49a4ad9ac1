package com.hvisions.thirdparty.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum RcsTaskCancelTypeEnum {
    SUCCESS("SUCCESS", "操作成功"),
    ERROR("ERROR","操作失败"),

    ERR_TASK_FINISHED("Err_TaskFinished", "任务已结束"),
    ERR_TASK_NOT_FOUND("Err_TaskNotFound", "任务找不到"),
    ERR_TASK_MODIFY_REJECT("Err_TaskModifyReject", "任务当前无法变更"),
    ERR_TASK_TYPE_NOT_SUPPORT("Err_TaskTypeNotSupport", "新任务任务类型不支持"),
    ERR_ROBOT_GROUPS_NOT_MATCH("Err_RobotGroupsNotMatch", "机器人资源组编号与新任务不匹配，无法调度"),
    ERR_ROBOT_CODES_NOT_MATCH("Err_RobotCodesNotMatch", "机器人编号与新任务不匹配，无法调度");

    String code;
    String desc;

    RcsTaskCancelTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    static Map<String, RcsTaskCancelTypeEnum> enumMap = new HashMap<>();

    static {
        for (RcsTaskCancelTypeEnum value : RcsTaskCancelTypeEnum.values()) {
            enumMap.put(value.getCode(), value);
        }
    }

    public static RcsTaskCancelTypeEnum getByCode(String code) {
        return enumMap.get(code);
    }

}
