package com.hvisions.thirdparty.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum CommunicationTypeEnum {


    IBM_MQ("0", "IBM MQ"),
    SOAP("1", "HTTP/SOAP"),
    RESTFUL("2", "HTTP/RESTFUL"),
    MQTT("3", "MQTT"),
    ETL("4", "ETL"),
    RabbitMQ("5", "RabbitMQ"),
    ;

    static Map<String, CommunicationTypeEnum> enumMap = new HashMap<>();

    static {
        for (CommunicationTypeEnum value : CommunicationTypeEnum.values()) {
            enumMap.put(value.code, value);
        }
    }

    String code;
    String desc;

    CommunicationTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static CommunicationTypeEnum getByCode(String code) {
        return enumMap.get(code);
    }
}
