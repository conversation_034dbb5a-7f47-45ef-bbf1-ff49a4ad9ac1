package com.hvisions.thirdparty.common.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 第三方接口 枚举
 * 用于 [发送] 数据给第三方系统和内容接口划分使用
 */
public enum InterfaceCodeEnum {


    WL01("ZK-WL-INTF001", "I-WMS-满箱物流调度"),
    WL02("ZK-WL-INTF002", "I-WMS-空框请求"),
    WL03("ZK-WL-INTF003", "I-WMS-空框回库请求"),
    WL08("ZK-WL-INTF008", "I-WMS-物料同步"),
    WL09("ZK-WL-INTF009", "I-WMS-解绑出库"),
    WL10("ZK-WL-INTF010", "I-WMS-转发RCMS"),
    WL11("ZK-WL-INTF011", "I-WMS-叫料出库"),
    WL15("ZK-WL-INTF015", "I-WMS-查询空储位"),
    WL16("ZK-WL-INTF016", "I-WMS-空货架始化入库"),

    ZK03("MES-ZK-INTF003", "MES-工单工序报工"),
    ZK09("MES-ZK-INTF009", "MES-库存信息同步"),
    ZK10("MES-ZK-INTF010", "MES-库存调拨接口"),
    ZK20("MES-ZK-INTF020", "MES-外发转运消息通知接口"),
    ZK24("MES-ZK-INTF024", "MES-生产工单开工"),

    ZK25("MES-ZK-INTF025", "MES-型材工单开工"),
    ZK26("MES-ZK-INTF026", "MES-型材工单完工"),

    ZK29("MES-ZK-INTF029", "运营看板-零件切割报工数据转发"),
    ZK40("MES-ZK-INTF040", "运营看板-组立焊接报工数据转发"),
    ZK41("MES-ZK-INTF041", "运营看板-型材切割报工数据转发"),
    ZK42("MES-ZK-INTF042", "运营看板-同步型材立库报工"),

    ZK31("MES-ZK-INTF031", "MES-外协件齐套叫料"),
    ZK33("MES-ZK-INTF033", "MES-满框调度"),
    ZK34("MES-LES-INTF003", "MES-库存信息同步(招商)"),
    
    XL01("ZK-XL-INTF001", "华工-钢板切割计划下发"),
    XL11("ZK-XL-INTF011", "华工-钢板调度执行反馈"),
    XL13("ZK-XL-INTF013", "华工-钢板打磨计划下发"),
    XL14("ZK-XL-INTF014", "华工-零件切割任务取消"),

    XC01("ZK-XC-INTF001", "大界-型材切割任务下发"),
    XC03("ZK-XC-INTF003", "大界-型材任务取消"),
    XC05("ZK-XC-INTF005", "大界-型材上料点信息下发"),
    XC11("ZK-XC-INTF011", "大界-型材调度执行反馈"),
    XC13("ZK-XC-INTF013", "大界-产线备料完成通知"),
    XC14("ZK-XC-INTF014", "大界-型材编码更新"),


    HJ01("ZK-HJ-INTF001", "华工-组立工单下发"),
    HJ02("ZK-HJ-INTF002", "华工-组立BOM数据下发"),
    HJ10("ZK-HJ-INTF010", "华工-组立调度执行反馈"),
    HJ11("ZK-HJ-INTF011", "华工-打磨计划下发"),

    iWMS04("ZK-iWMS-INTF004", "堆场-推送物料主数据"),

    iWMS13("ZK-iWMS-INTF013", "华工-立库出库信息"),
    iWMS14("ZK-iWMS-INTF014", "华工-立库出库任务"),
    iWMS15("ZK-iWMS-INTF015", "华工-型材立库备料请求"),

    ZL01("ZL-KPI-INTF01", "数据大屏-交付入库正点率与直通率"),

    TL03("ZK-TL-INTF003", "套料-库存信息同步(钢板原料)"),
    TL04("ZK-TL-INTF004", "套料-切割任务报工"),
    TL05("ZK-TL-INTF005", "套料-库存信息同步（型材原料）"),

    LK03("ZK-LK-INTF003", "立库-入库到位信息"),
    LK05("ZK-LK-INTF005", "立库-出库任务下发"),
    LK06("ZK-LK-INTF006", "立库-搬出结果下发"),
    LK07("ZK-LK-INTF007", "立库-查询空储位"),
    LK08("ZK-LK-INTF008", "立库-查询空料框信息"),

    XCLK01("ZK-XCLK-INTF001", "型材立库-型材立库物料主数据更新"),

    MOM01("MES-MOM-INTF007", "库区主数据同步到MES"),
    MOM02("MES-MOM-INTF008", "同步出入库记录数据"),//同步出入库记录数据===型材立库-MOM-MES===同步数据到MES部分（2025.07.16新增）
    RCS01("MOM-RCS-INTF001","RCS任务下发接口"),
    RCS02("MOM-RCS-INTF002","RCS任务取消接口"),
    RCS03("MOM-RCS-INTF003","RCS查询任务状态接口"),
    RCS04("MOM-RCS-INTF004","RCS查询机器人状态接口"),
    RCS05("MOM-RCS-INTF005","RCS查询载具状态接口"),
    RCS06("MOM-RCS-INTF006","RCS轻量任务下发"),
    RCS07("MOM-RCS-INTF007","RCS轻量任务取消"),
    BCQG01("IF_MOM_BC_01", "Mom-板材线切割任务下发到线控"),
    BCQG02("IF_MOM_BC_02", "Mom-板材线切割任务报工到MES"),

    BCX01("MOM-BC-INTF001", "Mom-板材线料点数据同步"),
    BCX02("MOM-BC-INTF002", "Mom-板材线料框基础数据同步"),

    XCX01("MOM-XC-INTF001", "Mom-型材线料点数据同步"),
    XCX02("MOM-XC-INTF002", "Mom-型材线料框基础数据同步"),

    LES01("IF_CMWH_JPLES_MOM_01","西部车间到总装车间转运工单下发"),
    LES03("IF_CMWH_JPLES_MOM_03","预舾装全宽分段工单下发"),
    MES35("MES-LES-INTF011","工单集配反馈"),
    BCX001("BCX-MOM-INTF001", "板材线-MOM产线调度请求"),
    MOM100("MOM-INTF100","产线调度反馈（通用）")

    ;

    static Map<String, InterfaceCodeEnum> enumMap = new HashMap<>();

    static {

        for (InterfaceCodeEnum value : InterfaceCodeEnum.values()) {
            enumMap.put(value.code, value);
            enumMap.put(value.desc, value);
        }
    }

    String code;
    String desc;

    InterfaceCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static InterfaceCodeEnum getByCode(String code) {
        return enumMap.get(code);
    }

    public static InterfaceCodeEnum getByDesc(String desc) {
        return enumMap.get(desc);
    }

}
