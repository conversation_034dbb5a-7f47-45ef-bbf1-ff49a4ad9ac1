package com.hvisions.thirdparty.common.query;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <P>    <P>
 *
 * <AUTHOR>
 * @date 2025/2/8
 */
@Data
public class ETLSteelScheduleQuery extends PageInfo {

    /**
     * 产线
     */
    @ApiModelProperty(value = "产线")
    private String lineCode;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private Date beginTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private Date endTime;


}
