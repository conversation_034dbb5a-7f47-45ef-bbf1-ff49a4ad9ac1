package com.hvisions.thirdparty.service.imp;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.client.BomClient;
import com.hvisions.hiperbase.client.LocationExtendClient;
import com.hvisions.hiperbase.client.MaterialClient;
import com.hvisions.pms.client.AssemblyProductionClient;
import com.hvisions.pms.client.PmIWMSClient;
import com.hvisions.pms.client.ProductWorkOrderClient;
import com.hvisions.pms.client.WorkOrderClient;
import com.hvisions.pms.dto.WorkOrderDTO;
import com.hvisions.pms.dto.productWorkOrder.ProductWorkOrderDTO;
import com.hvisions.thirdparty.common.dto.*;
import com.hvisions.thirdparty.common.dto.rcs.GenAgvSchedulingTaskRequest;
import com.hvisions.thirdparty.common.enums.FunctionEnum;
import com.hvisions.thirdparty.common.enums.InterfaceCodeEnum;
import com.hvisions.thirdparty.entity.ThirdpartyInterface;
import com.hvisions.thirdparty.entity.ThirdpartySystem;
import com.hvisions.thirdparty.service.IWMSService;
import com.hvisions.thirdparty.service.MesService;
import com.hvisions.thirdparty.service.RcsService;
import com.hvisions.thirdparty.service.StockService;
import com.hvisions.thirdparty.util.HttpSendUtil;
import com.hvisions.thirdparty.util.MD5Utils;
import com.hvisions.wms.client.HistoryClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.UUID;

@Slf4j
@Service
public class MesServiceImpl extends ReceiveAndSendServiceImpl implements MesService {

    @Resource
    private WorkOrderClient workOrderClient;

    @Resource
    private MaterialClient materialClient;

    @Resource
    private LocationExtendClient locationExtendClient;

    @Resource
    private BomClient bomClient;

    @Autowired
    private HistoryClient historyClient;

    @Autowired
    private IWMSService iwmsService;

    @Resource
    private PmIWMSClient pmIWMSClient;

    @Resource
    private StockService stockService;
    @Resource
    private AssemblyProductionClient assemblyProductionClient;
    @Resource
    private ProductWorkOrderClient productWorkOrderClient;
    @Resource
    private RcsService rcsService;

    @Override
    public void sendMesReportingTask(List<MesSendReportingDTO> mesSendReportingDTO) {
        for (MesSendReportingDTO sendReportingDTO : mesSendReportingDTO) {
            if (!StringUtils.isNoneBlank(sendReportingDTO.getProcessOrderId())) {
                throw new BaseKnownException("工序工单ID processOrderId 不能为空！");
            }
        }
        sendByInterfaceCode(InterfaceCodeEnum.ZK03.getCode(), mesSendReportingDTO);
    }

    @Override
    public void OrderReceiving(List<MesOrderDTO> mesOrderDTO) {
        ReceiveCommonDTO receiveCommonDTO = new ReceiveCommonDTO();
        receiveCommonDTO.setType(FunctionEnum.MES01.getCode());
        receiveCommonDTO.setData(mesOrderDTO);
        saveAndDoByType(receiveCommonDTO);
    }

    @Override
    public void materialReceiving(List<MesMaterialDTO> mesMaterialDTOS) {
        ReceiveCommonDTO receiveCommonDTO = new ReceiveCommonDTO();
        receiveCommonDTO.setType(FunctionEnum.MES06.getCode());
        receiveCommonDTO.setData(mesMaterialDTOS);
        saveAndDoByType(receiveCommonDTO);
    }

    @Override
    public void factoryReceiving(List<MesFactoryDTO> mesFactoryDTOS) {
        ReceiveCommonDTO receiveCommonDTO = new ReceiveCommonDTO();
        receiveCommonDTO.setType(FunctionEnum.MES07.getCode());
        receiveCommonDTO.setData(mesFactoryDTOS);
        saveAndDoByType(receiveCommonDTO);
    }

    @Override
    public void MaterBomReceiving(List<MaterialBomDTO> materialBomDTO) {
        ReceiveCommonDTO receiveCommonDTO = new ReceiveCommonDTO();
        receiveCommonDTO.setType(FunctionEnum.MES05.getCode());
        receiveCommonDTO.setData(materialBomDTO);
        saveAndDoByType(receiveCommonDTO);
    }

    @Override
    public void stockSend(StockInfoDTO stockInfoDTO) {
        if (StringUtils.isBlank(stockInfoDTO.getReqType())) {
            throw new BaseKnownException("请求类型不能为空！");
        }
        if (StringUtils.isBlank(stockInfoDTO.getReqNumber())) {
            throw new BaseKnownException("单号不能为空！");
        }
        if (stockInfoDTO.getList() == null || stockInfoDTO.getList().isEmpty()) {
            throw new BaseKnownException("零件信息不能为空");
        }
        try {
            sendByInterfaceCode(InterfaceCodeEnum.ZK09.getCode(), stockInfoDTO);
        } catch (Exception e) {
            //TODO 同步失败 将失败的库存信息存入redis
            historyClient.stockSendFailedSaveRedis(stockInfoDTO);
        }
    }


    @Override
    public void orderStart(ProductionOrderStartDTO productionOrderStartDTO) {
        List<String> processOrderIdList = productionOrderStartDTO.getProcessOrderIdList();
        if (processOrderIdList.isEmpty()) {
            throw new BaseKnownException("工序工单ID不能为空！");
        }
        sendByInterfaceCode(InterfaceCodeEnum.ZK24.getCode(), productionOrderStartDTO);
    }

    @Override
    public void sendMesStockTransfer(MesStockTransferDTO mesStockTransferDTO) {
        sendByInterfaceCode(InterfaceCodeEnum.ZK10.getCode(), mesStockTransferDTO);
    }

    @Override
    public void xcSendProductionOrderStart(ProductionOrderStartDTO productionOrderStartDTO) {
        sendByInterfaceCode(InterfaceCodeEnum.ZK25.getCode(), productionOrderStartDTO);
    }

    @Override
    public void xcSendMesReportingTask(MesSendReportingDTO mesSendReportingDTO) {
        sendByInterfaceCode(InterfaceCodeEnum.ZK26.getCode(), mesSendReportingDTO);
    }

    @Override
    public void outboundTransport(List<OutboundTransportDTO>  outboundTransportDTOS) {
        sendByInterfaceCode(InterfaceCodeEnum.ZK20.getCode(), outboundTransportDTOS);
    }

    @Override
    public void processOutboundFrames(OutboundMaterialFramesDTO outboundMaterialFramesDTO) {
        ReceiveCommonDTO receiveCommonDTO = new ReceiveCommonDTO();
        receiveCommonDTO.setType(FunctionEnum.MES26.getCode());
        receiveCommonDTO.setData(outboundMaterialFramesDTO);
        saveAndDoByType(receiveCommonDTO);
    }

    @Override
    public void receiveOutboundStock(OutboundStockDTO outboundStockDTO) {
        ReceiveCommonDTO receiveCommonDTO = new ReceiveCommonDTO();
        receiveCommonDTO.setType(FunctionEnum.MES19.getCode());
        receiveCommonDTO.setData(outboundStockDTO);
        saveAndDoByType(receiveCommonDTO);
    }

    @Override
    public void outCoordinationStandStockSyn(StockInfoDTO stockInfoDTO) {
        ReceiveCommonDTO receiveCommonDTO = new ReceiveCommonDTO();
        receiveCommonDTO.setType(FunctionEnum.MES30.getCode());
        receiveCommonDTO.setData(stockInfoDTO);
        saveAndDoByType(receiveCommonDTO);
    }

    @Override
    public void sendOutCoordinationCall(OutCoordinationCallDTO outCoordinationCallDTO) {
        sendByInterfaceCode(InterfaceCodeEnum.ZK31.getCode(), outCoordinationCallDTO);
    }

    @Override
    public void outCoordinationFlatStock(OutCoordinationFlatStockDTO outCoordinationFlatStockDTO) {
        ReceiveCommonDTO receiveCommonDTO = new ReceiveCommonDTO();
        receiveCommonDTO.setType(FunctionEnum.MES32.getCode());
        receiveCommonDTO.setData(outCoordinationFlatStockDTO);
        saveAndDoByType(receiveCommonDTO);
    }

    @Override
    public void sendFullPallet(FullPalletDTO fullPalletDTO) {
        sendByInterfaceCode(InterfaceCodeEnum.ZK33.getCode(), fullPalletDTO);
    }

    @Override
    public void receiveCuttingEmptyFrames(List<CuttingEmptyFramesDTO> cuttingEmptyFramesDTOS) {
        ReceiveCommonDTO receiveCommonDTO = new ReceiveCommonDTO();
        receiveCommonDTO.setType(FunctionEnum.MES34.getCode());
        receiveCommonDTO.setData(cuttingEmptyFramesDTOS);
        saveAndDoByType(receiveCommonDTO);
    }

    @Override
    public void receiveProductionWorkOrder(MesProductionWorkOrderDTO mesProductionWorkOrderDTO) {
        ReceiveCommonDTO receiveCommonDTO = new ReceiveCommonDTO();
        receiveCommonDTO.setType(FunctionEnum.MES40.getCode());
        receiveCommonDTO.setData(mesProductionWorkOrderDTO);
        saveAndDoByType(receiveCommonDTO);
    }

    @Override
    public void sendWorkOrderKittingFeedback(MesWorkOrderKittingFeedbackDTO mesWorkOrderKittingFeedbackDTO) {
        sendByInterfaceCode(InterfaceCodeEnum.MES35.getCode(), mesWorkOrderKittingFeedbackDTO);
    }


    @Override
    public void receiveWeldingEmptyFreamReturn(MesWeldingEmptyFreamReturnDTO mesWeldingEmptyFreamReturnDTO) {
        ReceiveCommonDTO receiveCommonDTO = new ReceiveCommonDTO();
        receiveCommonDTO.setType(FunctionEnum.MES42.getCode());
        receiveCommonDTO.setData(mesWeldingEmptyFreamReturnDTO);
        saveAndDoByType(receiveCommonDTO);
    }

    @Override
    public void stockSend(MomStockSyncDTO momStockSyncDTO) {
        if (momStockSyncDTO==null|| StrUtil.isBlank(momStockSyncDTO.getReqNumber())||StrUtil.isBlank(momStockSyncDTO.getRequestSystem())){
            throw new BaseKnownException("信息同步参数异常！-----"+InterfaceCodeEnum.ZK34.getDesc());
        }
        sendByInterfaceCode(InterfaceCodeEnum.ZK34.getCode(), momStockSyncDTO);
    }

    @Override
    public ResponseEntity<String> restFulSend(ThirdpartySystem system, ThirdpartyInterface thirdpartyInterface, Object data) {
        HttpHeaders headers = new HttpHeaders();
        // appkey的值
        String appKey = "Foljzf2vmpasShbw";
        // 生成一个随机数作为xReqNonce
        String xReqNonce = UUID.randomUUID().toString().replace("-", ""); // 移除UUID中的短横线
        // 生成时间戳
        long timestamp = System.currentTimeMillis();
        // 使用MD5Utils生成签名
        String signature = MD5Utils.transToMD5(appKey + timestamp + xReqNonce);
        // 将这些值添加到HttpHeaders
        headers.add("appkey", appKey);
        headers.add("xReqNonce", xReqNonce);
        headers.add("timestamp", String.valueOf(timestamp));
        headers.add("signature", signature);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(data), headers);
        return HttpSendUtil.send(thirdpartyInterface, request);
    }

    //发送消息后，反馈内容解析
    @Override
    public ResultVO responseVo(ResponseEntity<String> r, ThirdpartyInterface thirdpartyInterface) {
        InterfaceCodeEnum interfaceCodeEnum = InterfaceCodeEnum.getByCode(thirdpartyInterface.getInterfaceCode());
        if (interfaceCodeEnum == null) {
            throw new BaseKnownException("接口：" + thirdpartyInterface.getInterfaceCode() + "未在系统内定义！");
        }
        ResultVO<?> resultVO = null;
        switch (interfaceCodeEnum) {
            case ZK03:
            case ZK09:
            case ZK10:
            case ZK20:
            case ZK24:
            case ZK25:
            case ZK31:
                MesResultDTO mesResultDTO = JSON.toJavaObject(JSON.parseObject(r.getBody()), MesResultDTO.class);
                if (mesResultDTO.isSuccess()) {
                    log.info("发送成功啦！" + JSON.toJSONString(mesResultDTO));
                    resultVO = ResultVO.success(mesResultDTO);
                } else {
                    log.error("发送失败了！" + JSON.toJSONString(mesResultDTO));
                    resultVO = ResultVO.error(interfaceCodeEnum.getDesc() + "执行异常!MES反馈:" + mesResultDTO.getMsg());
                }
                break;
            case ZK33:
                MesDispatchResultDTO mesDispatchResultDTO = JSON.toJavaObject(JSON.parseObject(r.getBody()), MesDispatchResultDTO.class);
                if (mesDispatchResultDTO.isSuccess()) {
                    log.info("发送成功！" + JSON.toJSONString(mesDispatchResultDTO));
                    resultVO = ResultVO.success(mesDispatchResultDTO);
                } else {
                    log.error("发送失败！" + JSON.toJSONString(mesDispatchResultDTO));
                    resultVO = ResultVO.error(interfaceCodeEnum.getDesc() + "执行异常!MES反馈:" + mesDispatchResultDTO.getErrmsg());
                }
                break;
            case MES35:
                MesWeldingEmptyFreamReturnResDTO resDTO = JSON.toJavaObject(JSON.parseObject(r.getBody()),MesWeldingEmptyFreamReturnResDTO.class);
                if (resDTO.isSuccess()){
                    log.info("工单集配反馈发送成功"+JSON.toJSONString(resDTO));
                    resultVO = ResultVO.success(resDTO);
                }else {
                    log.error("工单集配反馈发送失败！" + JSON.toJSONString(resDTO));
                    resultVO = ResultVO.error(interfaceCodeEnum.getDesc() + "执行异常!MES反馈:" + resDTO.getMessage());
                }
                break;
            case ZK34://发送库存同步信息到mes
                MesResponseDTO mesResponseDTO = JSON.toJavaObject(JSON.parseObject(r.getBody()), MesResponseDTO.class);
                if (mesResponseDTO.isSuccess()) {
                    log.info("物料主数据发送成功："+JSON.toJSONString(mesResponseDTO));
                    resultVO = ResultVO.success(mesResponseDTO);
                }else {
                    log.error("物料主数据发送失败："+JSON.toJSONString(mesResponseDTO));
                    resultVO = ResultVO.error(interfaceCodeEnum.getDesc() + "执行异常!MES反馈:" + mesResponseDTO.getMessage());
                }
                break;
        }
        return resultVO==null ? ResultVO.error("未知错误,请稍后重试~") : resultVO;
    }



    public static <T> List<List<T>> splitList(List<T> list, int size) {
        List<List<T>> lists = new ArrayList<>();
        int listSize = list.size();
        int startIndex = 0;

        for (int i = 0; i < listSize; i += size) {
            int endIndex = Math.min(i + size, listSize);
            lists.add(list.subList(startIndex, endIndex));
            startIndex = endIndex;
        }

        return lists;
    }

    @Override
    public ThirdpartResultDTO doByType(ReceiveCommonDTO receiveCommonDTO) {
        FunctionEnum functionEnum = FunctionEnum.getByCode(receiveCommonDTO.getType());
        if (functionEnum == null) {
            throw new BaseKnownException("功能编号：" + receiveCommonDTO.getType() + ":未找到对应的执行方法");
        }
        log.info(functionEnum.getDesc());
        ResultVO<?> r = null;
        switch (functionEnum) {
            case MES01:
                List<MesOrderDTO> orderDTOS = JSON.parseArray(JSON.toJSONString(receiveCommonDTO.getData()), MesOrderDTO.class);
                r = workOrderClient.saveWorkOrder(orderDTOS);
                break;
            case MES05:
                List<MaterialBomDTO> materialBomDTO = JSON.parseArray(JSON.toJSONString(receiveCommonDTO.getData()), MaterialBomDTO.class);
                r = bomClient.saveBom(materialBomDTO);
                break;
            case MES06:
                //接收mes数据
                List<MesMaterialDTO> materialDTOS = JSON.parseArray(JSON.toJSONString(receiveCommonDTO.getData()), MesMaterialDTO.class);
                r = materialClient.saveMaterialByReceiving(materialDTOS);
                break;
            case MES07:
                List<MesFactoryDTO> factoryDTOS = JSON.parseArray(JSON.toJSONString(receiveCommonDTO.getData()), MesFactoryDTO.class);
                r = locationExtendClient.saveLocationByReceiving(factoryDTOS);
                break;
            case MES26:
                List<OutboundMaterialFramesDTO> outboundMaterialFramesDTO = JSON.parseArray(JSON.toJSONString(receiveCommonDTO.getData()), OutboundMaterialFramesDTO.class);
                for (OutboundMaterialFramesDTO materialFramesDTO : outboundMaterialFramesDTO) {
                    IWMSInStockTaskDTO iwmsInStockTask = new IWMSInStockTaskDTO();
                    iwmsInStockTask.setTaskCode(materialFramesDTO.getTaskCode());
                    iwmsInStockTask.setPodCode(materialFramesDTO.getPodCode());
                    iwmsInStockTask.setWbCode(materialFramesDTO.getWbCode());
                    iwmsInStockTask.setStgCategory("PK");
                    iwmsInStockTask.setInitPodFlag("1");
                    iwmsService.sendFullMaterialInStockTask(iwmsInStockTask);
                }
                break;
            case MES19:
                OutboundStockDTO outboundStockDTO = JSON.toJavaObject(JSON.parseObject(JSON.toJSONString(receiveCommonDTO.getData())), OutboundStockDTO.class);
                break;
            case MES30:
                StockInfoDTO stockInfoDTO = JSON.toJavaObject(JSON.parseObject(JSON.toJSONString(receiveCommonDTO.getData())), StockInfoDTO.class);
                //更新库存
                stockService.updateStock(stockInfoDTO);
                for (StockDetailDTO stockDetailDTO : stockInfoDTO.getList()) {
                    stockDetailDTO.setLocationCode(null);
                }
                break;
            case MES32:
                OutCoordinationFlatStockDTO outCoordinationFlatStockDTO= JSON.toJavaObject(JSON.parseObject(JSON.toJSONString(receiveCommonDTO.getData())), OutCoordinationFlatStockDTO.class);
                // 外协平库入场内平库
                r = pmIWMSClient.outFlatStockToIwmsInStock(outCoordinationFlatStockDTO);
                break;
            case MES34: //MES-下料空框回库

                List<CuttingEmptyFramesDTO>  cuttingEmptyFramesDTOS = JSON.parseArray(JSON.toJSONString(receiveCommonDTO.getData()), CuttingEmptyFramesDTO.class);
                //同步空托返回信息给焊接线
                r = assemblyProductionClient.cuttingEmptyFrames(cuttingEmptyFramesDTOS);
                break;
            case MES40:
                //todo 接收-生产工单业务逻辑实现
                MesProductionWorkOrderDTO mesProductionWorkOrderDTO = JSON.parseObject(JSON.toJSONString(receiveCommonDTO.getData()), MesProductionWorkOrderDTO.class);
                ProductWorkOrderDTO productWorkOrderDTO = mesProductionWorkOrderDTO.convertToMesOrderDTO();
                productWorkOrderClient.addProductWorkOrder(productWorkOrderDTO);
                //新增

                    GenAgvSchedulingTaskRequest genAgvSchedulingTaskRequest = new GenAgvSchedulingTaskRequest();
                    String rc = IdUtil.simpleUUID().substring(0,10);
                    String tc = IdUtil.simpleUUID().substring(0,10);
                    genAgvSchedulingTaskRequest.setReqCode(rc);
                    genAgvSchedulingTaskRequest.setReqTime("");
                    genAgvSchedulingTaskRequest.setTaskTyp("A01");
                    genAgvSchedulingTaskRequest.setWbCode("");
                    genAgvSchedulingTaskRequest.setPriority("");
                    genAgvSchedulingTaskRequest.setPodCode("");
                    genAgvSchedulingTaskRequest.setPodDir("");
                    genAgvSchedulingTaskRequest.setPodTyp("");
                    genAgvSchedulingTaskRequest.setAgvCode("");
                    genAgvSchedulingTaskRequest.setTaskCode(tc);
                    List<GenAgvSchedulingTaskRequest.PositionPath> positionCodePaths = new ArrayList<>();
                    Random random = new Random();
                    int randomNumber = random.nextInt(19);
                    int randomNumber2 = random.nextInt(21);

                        GenAgvSchedulingTaskRequest.PositionPath startPositionPath = new GenAgvSchedulingTaskRequest.PositionPath();
                        startPositionPath.setPositionCode("CS"+randomNumber+1);
                        startPositionPath.setType("00");
                        positionCodePaths.add(startPositionPath);
                        GenAgvSchedulingTaskRequest.PositionPath endPosttionPath = new GenAgvSchedulingTaskRequest.PositionPath();
                        endPosttionPath.setPositionCode("CS"+(22-randomNumber2));
                        endPosttionPath.setType("00");
                        positionCodePaths.add(endPosttionPath);

                    genAgvSchedulingTaskRequest.setPositionCodePath(positionCodePaths);
                    rcsService.submitTask(genAgvSchedulingTaskRequest);
                    r = ResultVO.success(null);
                //新增结束
                break;

            case MES42:
                //todo 接收-焊接线空框返回业务逻辑实现
                r = ResultVO.success(null);
                break;
        }
        //job目前是false,如果后续数量量过大，可考虑使用job的方式，定时分批处理
        return checkAndReturn(false, r);
    }
}
