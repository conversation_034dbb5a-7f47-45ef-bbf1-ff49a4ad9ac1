<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.qms.dao.StatisticsMapper">
    <select id="findRate" resultType="java.lang.Float" databaseId="mysql">
        SELECT SUM(CASE WHEN t1.inspection_result = 1 THEN 1 ELSE 0 END) /
        SUM(CASE
        WHEN t1.inspection_result = 1 OR t1.inspection_result = 2 THEN 1
        ELSE 0 END) * 100 result
        FROM hv_qm_quality_inspection_order t1
        JOIN hiper_base.hv_bm_material t2 ON t1.material_id = t2.id
        JOIN hiper_base.hv_bm_material_type t3 ON t2.material_type = t3.id
        <where>
            <if test="materialTypeCode!=null and materialTypeCode!=''">
                t3.material_type_code = #{materialTypeCode}
            </if>
            AND t1.create_time BETWEEN #{beginTime} and #{endTime}
        </where>
    </select>
    <select id="findRate" resultType="java.lang.Float" databaseId="sqlserver">
        SELECT SUM(CASE WHEN t1.inspection_result = 1 THEN 1 ELSE 0 END) /
        SUM(CASE
        WHEN t1.inspection_result = 1 OR t1.inspection_result = 2 THEN 1
        ELSE 0 END) * 100 result
        FROM hv_qm_quality_inspection_order t1
        JOIN hiper_base.dbo.hv_bm_material t2 ON t1.material_id = t2.id
        JOIN hiper_base.dbo.hv_bm_material_type t3 ON t2.material_type = t3.id
        <where>
            <if test="materialTypeCode!=null and materialTypeCode!=''">
                t3.material_type_code = #{materialTypeCode}
            </if>
            AND t1.create_time BETWEEN #{beginTime} and #{endTime}
        </where>
    </select>


    <resultMap id="stepCount" type="com.hvisions.qms.dto.defects.StepDefectDTO">
        <result column="step_name" property="stepName"/>
        <collection property="stepDefectCountDTOS"
                    ofType="com.hvisions.qms.dto.defects.StepDefectCountDTO">
            <result column="defect_id" property="defectId"/>
            <result column="defect_name" property="defectName"/>
            <result column="quantity" property="quantity"/>
        </collection>
    </resultMap>
    <select id="getDefectCountByTime" resultMap="stepCount"
            parameterType="com.hvisions.qms.dto.defects.StepDefectQueryDTO" >
        SELECT c.defect_id as defect_id,
        sum(c.c_quantity) as quantity,
        case when c.step_name is null then "其他" else c.step_name END as step_name,
        c.defect_name as defect_name
        FROM (
        SELECT i.defect_id,i.defect_name,
        i.quantity as c_quantity,
        o.step_name
        FROM
        hv_qm_defect_instance i
        JOIN hv_qm_quality_inspection_order o
        ON i.inspection_id = o.id
        <where>
            <if test="dto.beginTime !=null and dto.endTime !=null">
                and i.create_time BETWEEN #{dto.beginTime} and #{dto.endTime}
            </if>
            <if test="dto.stepName !=null">
                and o.step_name = #{dto.stepName}
            </if>
            and i.quantity > 0
            and o.hide = false
        </where>
        ) as c
        GROUP BY c.defect_id,c.step_name,c.defect_name;
    </select>

    <resultMap id="materialDefectCount" type="com.hvisions.qms.dto.defects.MaterialDefectCountDTO">
        <result column="material_name" property="materialName"/>
        <collection property="materialDefectDTOS"
                    ofType="com.hvisions.qms.dto.defects.MaterialDefectDTO">
            <result column="step_name" property="stepName"/>
            <result column="defect_id" property="defectId"/>
            <result column="defect_name" property="defectName"/>
            <result column="quantity" property="quantity"/>
            <result column="material_id" property="materialId"/>
            <result column="material_name" property="materialName"/>
        </collection>
    </resultMap>
    <select id="getMaterialDefectByQuery" resultMap="materialDefectCount"
            parameterType="com.hvisions.qms.dto.defects.MaterialDefectQueryDTO" >
        SELECT
        material_id,
        defect_id,
        defect_name,
        SUM(quantity) as quantity,
        step_name,
        material_name
        FROM
        hv_qm_defect_instance i
        JOIN hv_qm_quality_inspection_order o on i.inspection_id = o.id
        <where>
            <if test="dto.beginTime !=null and dto.endTime !=null">
                and i.create_time BETWEEN #{dto.beginTime} and #{dto.endTime}
            </if>
            <if test="dto.stepName !=null">
                and step_name = #{dto.stepName}
            </if>
            <if test="dto.materialId !=null">
                and material_id = #{dto.materialId}
            </if>
            and i.quantity > 0
            and o.hide = false
        </where>
        GROUP BY material_id,defect_id,defect_name,step_name,material_name
    </select>

    <resultMap id="count" type="com.hvisions.qms.dto.defects.DefectCountDTO">
        <result column="defect_id" property="defectId"/>
        <result column="defect_name" property="defectName"/>
        <result column="sum_quantity" property="quantity"/>
    </resultMap>
    <select id="getCountByTime" resultMap="count" >
        SELECT
        defect_name,
        defect_id,
        SUM(quantity) as sum_quantity
        FROM
        hv_qm_defect_instance i
        JOIN hv_qm_quality_inspection_order o on i.inspection_id = o.id
        <where>
            <if test="beginTime !=null and  endTime !=null">
                AND i.create_time BETWEEN #{beginTime} and #{endTime}
            </if>
            and i.quantity > 0
            and o.hide = false
        </where>
        GROUP BY defect_id,defect_name
        order by sum_quantity desc
        LIMIT 0,10
    </select>
    <resultMap id="typeCount" type="com.hvisions.qms.dto.defects.DefectTypeCountDTO">
        <result column="defect_type_name" property="defectTypeName"/>
        <result column="quantity" property="quantity"/>
    </resultMap>
    <select id="getTypeCount" resultMap="typeCount">
        SELECT
        defect_type_name,
        SUM(quantity) as quantity
        FROM
        hv_qm_defect_instance i
        JOIN hv_qm_quality_inspection_order o on i.inspection_id = o.id
        <where>
            <if test="beginTime !=null and  endTime !=null">
                AND i.create_time BETWEEN #{beginTime} and #{endTime}
            </if>
            AND quantity > 0
        </where>
        GROUP BY defect_type_name
    </select>

    <resultMap id="parameterLimit" type="com.hvisions.qms.dto.parameter.ParameterLimitDTO">
        <result column="collection_code" property="collectionCode"></result>
        <result column="parameter_min_value" property="minValue"></result>
        <result column="parameter_max_value" property="maxValue"></result>
    </resultMap>
    <select id="getParameterValue" resultMap="parameterLimit" >
        select collection_code, parameter_min_value, parameter_max_value
        from hv_qm_inspection_standards_parameters p
        where p.is_collection = 1
    </select>


    <select id="getCheck" resultType="java.util.HashMap"
            parameterType="com.hvisions.qms.dto.statistics.InspectionOrderQueryCountDateDTO">
        SELECT
        COUNT( o.id ) AS check_count_num,
        substring( o.create_time, 1,#{dto.dateTypeNum}) AS check_count_time
        FROM
        hv_qm_quality_inspection_order AS o
        <where>
            and o.hide = 0
            <choose>
                <when test="dto.result != null">
                    and o.inspection_result = #{dto.result}
                </when>
                <otherwise>
                    and o.inspection_result in (0,1,2)
                </otherwise>
            </choose>
            <if test="dto.checkStartTime != null and dto.checkEndTime != null">
                and o.create_time between #{dto.checkStartTime} and #{dto.checkEndTime}
            </if>
            <if test="dto.checkType != null and dto.checkType >0">
                and o.inspection_order_type_id=#{dto.checkType}
            </if>
        </where>
        GROUP BY substring(o.create_time,1,#{dto.dateTypeNum})
    </select>

    <resultMap id="parameterInfo" type="com.hvisions.qms.dto.statistics.ParameterInfo"/>
    <select id="findParameterInfoPage" resultMap="parameterInfo"
            parameterType="com.hvisions.qms.dto.statistics.ParameterQuery" >
        SELECT
        t1.parameter_value parameterValue,
        t4.data_type dataType,
        t4.parameter_code CODE,
        t4.parameter_name NAME,
        t4.parameter_unit unit,
        t4.parameter_max_value max,
        t4.parameter_min_value min,
        t4.parameter_description description,
        t1.parameter_note memo,
        t1.create_time createTime,
        CASE WHEN t5.NAME IS NULL THEN 'N/A' ELSE t5.NAME END point_name,
        CASE WHEN t6.NAME IS NULL THEN 'N/A' ELSE t6.NAME END position_name
        FROM
        hv_qm_parameter_value t1
        JOIN hv_qm_quality_inspection_order t2 ON t1.order_id = t2.id
        JOIN hv_qm_inspection_standard t3 ON t2.standard_id = t3.id
        JOIN hv_qm_inspection_standards_parameters t4 ON t1.parameter_code = t4.parameter_code AND
        t4.inspection_standard_id = t2.standard_id
        LEFT JOIN hv_qm_point t5 ON t1.point_id = t5.id
        LEFT JOIN hv_qm_position t6 ON t1.position_id = t6.id
        <where>
            t1.parameter_value IS NOT NULL
            and t2.standard_id = #{query.templateId}
            <if test="query.createTimeStart != null and query.createTimeEnd != null">
                AND t1.create_time BETWEEN #{query.createTimeStart} and #{query.createTimeEnd}
            </if>
            <if test="query.parameterCode != null and query.parameterCode != ''">
                AND t1.parameter_code = #{query.parameterCode}
            </if>
        </where>
        ORDER BY t1.create_time DESC
    </select>

    <resultMap id="parameterValue" type="com.hvisions.qms.dto.statistics.ParameterValueInfo"/>
    <select id="findParameterInfo" resultMap="parameterValue"
            parameterType="com.hvisions.qms.dto.statistics.ParameterListQuery">
        SELECT t1.actual_value_decimal parameterValue, t1.create_time createTime
        FROM hv_qm_parameter_value t1
        JOIN hv_qm_quality_inspection_order t2 ON t1.order_id = t2.id
        <where>
            t2.standard_id = #{query.templateId}
            AND t1.parameter_code = #{query.parameterCode}
            AND t1.actual_value_decimal is not null
            <if test="query.createTimeStart != null and query.createTimeEnd != null">
                and t1.create_time BETWEEN #{query.createTimeStart} and #{query.createTimeEnd}
            </if>
        </where>
        ORDER BY t1.create_time ASC
    </select>
</mapper>