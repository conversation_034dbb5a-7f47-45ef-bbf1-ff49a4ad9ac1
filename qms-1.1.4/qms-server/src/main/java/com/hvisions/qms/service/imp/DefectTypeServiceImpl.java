package com.hvisions.qms.service.imp;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.qms.dto.DefectTypeDTO;
import com.hvisions.qms.dto.DefectTypeQueryDTO;
import com.hvisions.qms.entity.HvQmQualityDefect;
import com.hvisions.qms.entity.HvQmQualityDefectType;
import com.hvisions.qms.enums.DefectExceptionEnum;
import com.hvisions.qms.repository.DefectRepository;
import com.hvisions.qms.repository.DefectTypeRepository;
import com.hvisions.qms.service.DefectTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class DefectTypeServiceImpl implements DefectTypeService {


    @Autowired
    private DefectTypeRepository defectTypeRepository;
    @Autowired
    private DefectRepository defectRepository;

    @Override
    public Integer addDefectType(DefectTypeDTO defectTypeDTO) {
        return defectTypeRepository.save(DtoMapper.convert(defectTypeDTO, HvQmQualityDefectType.class)).getId();
    }

    @Override
    public Integer updateDefectType(DefectTypeDTO defectTypeDTO) {
        return defectTypeRepository.save(DtoMapper.convert(defectTypeDTO, HvQmQualityDefectType.class)).getId();
    }

    @Override
    public void deleteDefectTypeById(Integer id) {
        List<HvQmQualityDefect> byType = defectRepository.findByTypeId(id);
        System.out.println("缺陷是否被引入"+byType);
        if (byType.isEmpty()){
            defectTypeRepository.deleteById(id);
        }else{
            throw new BaseKnownException( DefectExceptionEnum.DEFECT_TYPES_ARE_INTRODUCED);
        }
    }

    @Override
    public Page<DefectTypeDTO> getDefectTypePageQuery(DefectTypeQueryDTO defectGradeQueryDTO) {
        ExampleMatcher exampleMatcher = ExampleMatcher.matching()
                //包含，忽略大小写
                .withMatcher("code", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("name", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase());
        Example<HvQmQualityDefectType> example = Example.of(DtoMapper.convert(defectGradeQueryDTO, HvQmQualityDefectType.class), exampleMatcher);
        return DtoMapper.convertPage(defectTypeRepository.findAll(example, defectGradeQueryDTO.getRequest()), DefectTypeDTO.class);
    }

    @Override
    public List<DefectTypeDTO> getAllDefectType() {
        return DtoMapper.convertList(defectTypeRepository.findAll(), DefectTypeDTO.class);
    }

    @Override
    public HvQmQualityDefectType getByName(String name) {
        return defectTypeRepository.findByName(name);
    }

    @Override
    public DefectTypeDTO getById(Integer id) {
        return DtoMapper.convert(defectTypeRepository.getOne(id), DefectTypeDTO.class);
    }
    @Override
    public DefectTypeDTO getDefectTypeByCode(String code) {
        HvQmQualityDefectType byCode = defectTypeRepository.findByCode(code);
        if (null!=byCode){
            return DtoMapper.convert(byCode,DefectTypeDTO.class);
        }
        return null;
    }
}
