package com.hvisions.eam.configuration.maintain;

import com.hvisions.eam.service.maintain.MaintainPlanService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.listener.api.RabbitListenerErrorHandler;
import org.springframework.amqp.rabbit.support.ListenerExecutionFailedException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * <p>Title: TopicReceiver</p>
 * <p>Description: TopicReceiver</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/2/18</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
@Slf4j
public class MaintainTopicReceiver {
    private final MaintainPlanService maintainPlanService;

    @Autowired
    public MaintainTopicReceiver(MaintainPlanService maintainPlanService) {
        this.maintainPlanService = maintainPlanService;
    }


    @Bean
    public RabbitListenerErrorHandler maintainErrorHandler() {
        return new RabbitListenerErrorHandler() {
            @Override
            public Object handleError(Message message, org.springframework.messaging.Message<?> message1, ListenerExecutionFailedException e) throws Exception {
                log.error("maintain" + message.toString(), e);
                return null;
            }
        };
    }

    /**
     * 监听队列
     *
     * @param id 传入参数
     */
    @RabbitListener(queues = MaintainRabbitConfig.EQUIPMENT_MAINTAIN_QUEUE_NAME, errorHandler = "maintainErrorHandler")
    public void process1(Integer id) {
        log.info("保养监听参数" + id);
        //根据timerId找已经启用的计划状态为审批通过或者无需审批的计划
        maintainPlanService.triggerPlanByTimerId(id);
    }

}









