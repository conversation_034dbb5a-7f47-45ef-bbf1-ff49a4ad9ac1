package com.hvisions.eam.service.spare.imp;

import com.hvisions.framework.client.UserClient;
import com.hvisions.framework.dto.user.UserDTO;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.enums.StoreExceptionEnum;
import com.hvisions.eam.consts.SpareConsts;
import com.hvisions.eam.dto.spare.ImportSpareTypeDTO;
import com.hvisions.eam.dto.spare.SpareTypeDTO;
import com.hvisions.eam.query.spare.SpareTypeQueryDTO;
import com.hvisions.eam.entity.spare.HvEamSpareType;
import com.hvisions.eam.repository.sprare.SpareEntityRepository;
import com.hvisions.eam.repository.sprare.SpareTypeEntityRepository;
import com.hvisions.eam.service.spare.SpareTypeService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;
import java.util.Optional;

/**
 * <p>Title:SpareTypeServiceImpl</p>
 * <p>Description:备件类型</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/3/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Service
public class SpareTypeServiceImpl implements SpareTypeService {

    /**
     * 备件类型jpa
     */
    private final SpareTypeEntityRepository spareTypeEntityRepository;

    /**
     * 设备service
     */
    private final SpareEntityRepository spareEntityRepository;
    @Autowired
    UserClient userClient;

    @Autowired
    public SpareTypeServiceImpl(SpareTypeEntityRepository spareTypeEntityRepository, SpareEntityRepository spareEntityRepository) {
        this.spareTypeEntityRepository = spareTypeEntityRepository;
        this.spareEntityRepository = spareEntityRepository;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SpareTypeDTO save(SpareTypeDTO spareTypeDTO) {
        //如父级ID未传值 则 父级ID默认为 0
        if (spareTypeDTO.getParentId() == null) {
            spareTypeDTO.setParentId(0);
        }
        if (spareTypeDTO.getBatchManagementOrNot() == null) {
            throw new BaseKnownException(10000, "管理类型不能为空");
        }
        if (StringUtils.isEmpty(spareTypeDTO.getTypeName())){
            throw new BaseKnownException(10000, "类型名称不能为空");
        }
        //如果存在父类,只能使用父类的管理类型
        if (spareTypeDTO.getParentId() != 0) {
            Optional<HvEamSpareType> parentType = spareTypeEntityRepository.findById(spareTypeDTO.getParentId());
            if (!parentType.isPresent()) {
                throw new BaseKnownException(10000, "父级类型不存在");
            }
            HvEamSpareType type = parentType.get();
            //获取父类中的属性 并赋值给子类
            if (type.getBatchManagementOrNot() == null) {
                throw new BaseKnownException(10000, "数据异常：备件类型父级类型没有管理类型,id:" + type.getId());
            }
            spareTypeDTO.setBatchManagementOrNot(type.getBatchManagementOrNot());
            if (!type.getBatchManagementOrNot().equals(spareTypeDTO.getBatchManagementOrNot())) {
                throw new BaseKnownException(10000, "管理类型错误，只能配置和父类相同的管理类型");
            }
        }
        HvEamSpareType spareType = DtoMapper.convert(spareTypeDTO, HvEamSpareType.class);
        return DtoMapper.convert(spareTypeEntityRepository.save(spareType), SpareTypeDTO.class);
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public void deleteById(Integer id) {
        //查询所有类型
        if (spareTypeEntityRepository.existsByParentId(id)) {
            //有子层级，不可删除
            throw new BaseKnownException(StoreExceptionEnum.THERE_ARE_SUBIEVEIS_THAT_CANNOT_BE_DELETED);
        }
        //检查备件中是否应用 被应用删除失败
        if (spareEntityRepository.existsBySpareTypeId(id)) {
            throw new BaseKnownException(StoreExceptionEnum.IN_USE);
        }
        spareTypeEntityRepository.deleteById(id);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SpareTypeDTO findById(Integer id) {
        Optional<HvEamSpareType> optional = spareTypeEntityRepository.findById(id);
        if (optional.isPresent()) {
            HvEamSpareType type = optional.get();
            return DtoMapper.convert(type, SpareTypeDTO.class);
        }
        return null;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Page<SpareTypeDTO> findAllByTypeName(SpareTypeQueryDTO spareTypeQueryDTO) {

        //把DTO转换成普通类
        HvEamSpareType spareType = DtoMapper.convert(spareTypeQueryDTO, HvEamSpareType.class);
        //查询条件为父级
        spareType.setParentId(0);
        //匹配方式       xxx 采用开始匹配的方式查询 忽略大小写
        ExampleMatcher exampleMatcher = ExampleMatcher.matching()
                .withMatcher("typeCode", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("typeName", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase());
        //匹配器`
        Example<HvEamSpareType> example = Example.of(spareType, exampleMatcher);
        Page<HvEamSpareType> page = spareTypeEntityRepository.findAll(example, spareTypeQueryDTO.getRequest());
        Page<SpareTypeDTO> spareTypeDTOS = DtoMapper.convertPage(page, SpareTypeDTO.class);
        for (SpareTypeDTO spareTypeDTO : spareTypeDTOS) {
            if (spareTypeDTO.getCreatorId() != null && spareTypeDTO.getCreatorId() != 0) {
                ResultVO<UserDTO> user = userClient.getUser(spareTypeDTO.getCreatorId());
                if (user.isSuccess()) {
                    if (user.getData() != null) {
                        spareTypeDTO.setCreateUserName(user.getData().getUserName());
                    }
                }
            }
        }
        return spareTypeDTOS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<SpareTypeDTO> getChildNode(Integer parentId) {
        List<HvEamSpareType> spareTypes = spareTypeEntityRepository.findAllByParentId(parentId);
        List<SpareTypeDTO> spareTypeDTOS = DtoMapper.convertList(spareTypes, SpareTypeDTO.class);
        for (SpareTypeDTO spareTypeDTO : spareTypeDTOS) {
            if (spareTypeDTO.getCreatorId() != null && spareTypeDTO.getCreatorId() != 0) {
                ResultVO<UserDTO> user = userClient.getUser(spareTypeDTO.getCreatorId());
                if (user.isSuccess()) {
                    if (user.getData() != null) {
                        spareTypeDTO.setCreateUserName(user.getData().getUserName());
                    }
                }
            }
        }
        return spareTypeDTOS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<SpareTypeDTO> getAll() {
        List<HvEamSpareType> all = spareTypeEntityRepository.findAll();
        return DtoMapper.convertList(all, SpareTypeDTO.class);

    }

    /**
     * 通过名称查询
     *
     * @param typeName 类型名称
     * @return id
     */
    @Override
    public Integer findByTypeName(String typeName) {
        HvEamSpareType byTypeName = spareTypeEntityRepository.findByTypeName(typeName);
        if (byTypeName != null) {
            return byTypeName.getId();
        }
        return -1;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SpareTypeDTO getSpareTypeBySpareTypeCode(String spareTypeCode) {
        HvEamSpareType type = spareTypeEntityRepository.findByTypeCode(spareTypeCode);
        if (type != null) {
            return DtoMapper.convert(type, SpareTypeDTO.class);
        }
        return null;
    }

    /**
     * 获取导入模板
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @Override
    public ResultVO<ExcelExportDto> getTypeImportTemplate() throws IOException, IllegalAccessException {
        ResponseEntity<byte[]> result =
                getTypeImportTemplateLink();
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(result.getBody());
        excelExportDto.setFileName(SpareConsts.SPARE_TYPE_FILE_NAME);
        return ResultVO.success(excelExportDto);
    }

    /**
     * 获取导入模板
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @Override
    public ResponseEntity<byte[]> getTypeImportTemplateLink() throws IOException, IllegalAccessException {
        return ExcelUtil.generateImportFile(ImportSpareTypeDTO.class,
                SpareConsts.SPARE_TYPE_FILE_NAME
        );
    }

    /**
     * 导入
     *
     * @param file 信息excel
     * @return 导入信息
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    @Override
    public ImportResult importSpareType(MultipartFile file) throws IllegalAccessException, ParseException, IOException {
        return ExcelUtil.importEntity(file,
                ImportSpareTypeDTO.class,
                this);
    }

    /**
     * 导出
     *
     * @return 信息excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException 访问异常
     */
    @Override
    public ResponseEntity<byte[]> exportSpareTypeLink() throws IOException, IllegalAccessException {
        List<HvEamSpareType> all = spareTypeEntityRepository.findAll();
        List<SpareTypeDTO> convert = DtoMapper.convertList(all, SpareTypeDTO.class);
        for (SpareTypeDTO hvEamSpareType : convert) {
            if (hvEamSpareType.getParentId() != null) {
                if (hvEamSpareType.getParentId() != 0) {
                    for (HvEamSpareType eamSpareType : all) {
                        if (hvEamSpareType.getParentId().equals(eamSpareType.getId())) {
                            hvEamSpareType.setParentCode(eamSpareType.getTypeCode());
                        }
                    }
                }
            }
        }
        return ExcelUtil.generateImportFile(convert, SpareConsts.SPARE_TYPE_FILE_NAME,
                SpareTypeDTO.class);
    }

    /**
     * 导出信息
     *
     * @return 信息excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException 访问异常
     */
    @Override
    public ResultVO<ExcelExportDto> exportSpareType() throws IOException, IllegalAccessException {
        ResponseEntity<byte[]> result =
                exportSpareTypeLink();
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(result.getBody());
        excelExportDto.setFileName(SpareConsts.SPARE_TYPE_FILE_NAME);
        return ResultVO.success(excelExportDto);
    }

    @Override
    public void saveOrUpdate(ImportSpareTypeDTO importSpareTypeDTO) {
        SpareTypeDTO spareTypeBySpareTypeCode = getSpareTypeBySpareTypeCode(importSpareTypeDTO.getTypeCode());
        Integer parentId = 0;
        if (importSpareTypeDTO.getParentCode() != null) {
            SpareTypeDTO typeDTO = getSpareTypeBySpareTypeCode(importSpareTypeDTO.getParentCode());
            parentId = typeDTO.getId();
        }
        SpareTypeDTO convert = DtoMapper.convert(importSpareTypeDTO, SpareTypeDTO.class);
        convert.setParentId(parentId);
        if (spareTypeBySpareTypeCode != null) {
            convert.setId(spareTypeBySpareTypeCode.getId());
        }
        save(convert);
    }
}
