package com.hvisions.eam.client.autonomy;

import com.hvisions.eam.dto.autonomy.activitiIdentity.JsonGetGroup;
import com.hvisions.eam.dto.autonomy.activitiIdentity.QueryDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

@FeignClient(name = "activiti")
@RequestMapping("/identity")
public interface ActivitiIdentityClient {

    /**
     * 查询用户组列表
     * <AUTHOR>
     * @param queryDTO 查询条件
     * @return com.hvisions.eam.dto.autonomy.activitiIdentity.JsonGetGroup
     */
    @PostMapping(value = "/getGroupByQuery")
    JsonGetGroup getGroupByQuery(QueryDTO queryDTO);
}

    
    
    
    