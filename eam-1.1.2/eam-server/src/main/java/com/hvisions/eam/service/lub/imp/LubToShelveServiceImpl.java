package com.hvisions.eam.service.lub.imp;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.eam.enums.StoreExceptionEnum;
import com.hvisions.eam.dao.LubToShelveMapper;
import com.hvisions.eam.dto.lub.LubToShelveDTO;
import com.hvisions.eam.query.lub.LubToShelveQueryDTO;
import com.hvisions.eam.entity.lub.HvEamLubToShelve;
import com.hvisions.eam.entity.lub.HvEamLubToShelveView;
import com.hvisions.eam.entity.lub.HvEamLubricating;
import com.hvisions.eam.repository.lub.LubEntityRepository;
import com.hvisions.eam.repository.lub.LubToShelveEntityRepository;
import com.hvisions.eam.repository.lub.LubToShelveViewEntityRepository;
import com.hvisions.eam.service.lub.LubToShelveService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>Title:SpareToShelveServiceImpl</p>
 * <p>Description:油品库位关系</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/3/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Slf4j
@Service
public class LubToShelveServiceImpl implements LubToShelveService {

    @Value(value = "${spring.datasource.url}")
    private String url;
    /**
     * 关系jpa
     */
    private final LubToShelveEntityRepository lubToShelveEntityRepository;

    /**
     * 联表jpa
     */
    private final LubToShelveViewEntityRepository lubToShelveViewEntityRepository;

    /**
     * 油品库房 mapper
     */
    private final LubToShelveMapper lubToShelveMapper;

    /**
     * 油品jpa
     */
    private final LubEntityRepository lubEntityRepository;

    @Autowired
    public LubToShelveServiceImpl(LubToShelveEntityRepository lubToShelveEntityRepository,
                                  LubToShelveViewEntityRepository lubToShelveViewEntityRepository,
                                  LubToShelveMapper lubToShelveMapper,
                                  LubEntityRepository lubEntityRepository) {
        this.lubToShelveEntityRepository = lubToShelveEntityRepository;
        this.lubToShelveViewEntityRepository = lubToShelveViewEntityRepository;
        this.lubToShelveMapper = lubToShelveMapper;
        this.lubEntityRepository = lubEntityRepository;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer save(LubToShelveDTO lubToShelveDTO) {
        log.info(lubToShelveDTO.toString());
        return addSpareToShelve(DtoMapper.convert(lubToShelveDTO, HvEamLubToShelve.class));
    }

    private Integer addSpareToShelve(HvEamLubToShelve lubToShelveDTO) {
        HvEamLubToShelve lub = lubToShelveEntityRepository.findAllByLubIdAndShelveIdAndBatchNumber(lubToShelveDTO.getLubId(), lubToShelveDTO.getShelveId(), lubToShelveDTO.getBatchNumber());
        if (lub == null) {
            return lubToShelveEntityRepository.save(lubToShelveDTO).getId();
        } else {
            lub.setNumber(lubToShelveDTO.getNumber());
            return lubToShelveEntityRepository.save(lub).getId();
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void deleteById(Integer id) {
        //当前库存不为0不能删除
        Optional<HvEamLubToShelve> lubToShelve1 = lubToShelveEntityRepository.findById(id);
        if (!lubToShelve1.isPresent()) {
            //数据错误
            log.info("库房 id：" + id + " 不存在");
            throw new BaseKnownException(StoreExceptionEnum.DATE_ERROR_EXCEPTION);
        }
        HvEamLubToShelve newLubToShelve = lubToShelve1.get();
        //删除为清空库房 ， 但是数据保留
        newLubToShelve.setNumber(new BigDecimal(0));
        HvEamLubToShelve save = lubToShelveEntityRepository.save(newLubToShelve);
        log.info("修改：" + save.toString());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LubToShelveDTO findById(Integer id) {
        try {
            return DtoMapper.convert(lubToShelveEntityRepository.getOne(id), LubToShelveDTO.class);
        } catch (Exception e) {
            log.info("id" + id + "不存在");
            return null;
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public BigDecimal sumLubNumByLubId(Integer spareId) {
        BigDecimal zreo = new BigDecimal("0");
        //查询当前备件所在的备件库存关系表
        List<HvEamLubToShelve> spareIds = lubToShelveEntityRepository.findShelveIdByLubId(spareId);
        for (HvEamLubToShelve s : spareIds) {
            zreo = zreo.add(s.getNumber());
        }
        return zreo;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Set<Integer> findAllByLubId(Integer spareId) {
        List<HvEamLubToShelve> spareToShelves = lubToShelveEntityRepository.findAllByLubId(spareId);
        Set<Integer> ids = new HashSet<>();
        for (HvEamLubToShelve s : spareToShelves) {
            ids.add(s.getId());
        }
        return ids;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Set<Integer> findAllByShelveId(Integer shelveId) {
        List<HvEamLubToShelve> spareToShelves = lubToShelveEntityRepository.findAllByShelveId(shelveId);
        Set<Integer> ids = new HashSet<>();
        for (HvEamLubToShelve s : spareToShelves) {
            ids.add(s.getId());
        }
        return ids;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public BigDecimal findAllByShelveIds(Integer shelveId) {
        List<HvEamLubToShelve> spareToShelves = lubToShelveEntityRepository.findAllByShelveId(shelveId);
        BigDecimal zero = new BigDecimal("0");
        for (HvEamLubToShelve s : spareToShelves) {
            zero = zero.add(s.getNumber());
        }
        return zero;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Page<LubToShelveQueryDTO> findAllByLubCodeAndLubNameAndShelveCodeAndShelveNameAndBatchNumber(LubToShelveQueryDTO lubToShelveQueryDTO) {

        //把DTO转换成普通类
        HvEamLubToShelveView lubToShelveView = DtoMapper.convert(lubToShelveQueryDTO, HvEamLubToShelveView.class);

        //匹配方式       xxx 采用开始匹配的方式查询 忽略大小写
        ExampleMatcher exampleMatcher = ExampleMatcher.matching()
                .withMatcher("lubCode", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("lubName", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("shelveCode", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("shelveName", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("batchNumber", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase());

        //匹配器
        Example<HvEamLubToShelveView> example = Example.of(lubToShelveView, exampleMatcher);
        Page<HvEamLubToShelveView> page = lubToShelveViewEntityRepository.findAll(example, lubToShelveQueryDTO.getRequest());
        return DtoMapper.convertPage(page, LubToShelveQueryDTO.class);
    }

    /**
     * 油品库房 查询
     *
     * @param lubToShelveQueryDTO 查询条件
     * @return 分页
     */
    @Override
    public Page<LubToShelveDTO> getLubToShelve(LubToShelveQueryDTO lubToShelveQueryDTO) {
        Page<LubToShelveDTO>
                page = PageHelperUtil.getPage(this.lubToShelveMapper::getLubToShelve, lubToShelveQueryDTO);
        List<HvEamLubricating> lub = lubEntityRepository.findAllById(page.stream().map(LubToShelveDTO::getId).collect(Collectors.toList()));

        for (LubToShelveDTO spareToShelveDTO : page) {
            for (HvEamLubricating hvEamSpare : lub) {
                if (spareToShelveDTO.getLubId().equals(hvEamSpare.getId())) {
                    spareToShelveDTO.setLubCode(hvEamSpare.getLubCode());
                }
            }
            //当前数量状态
            spareToShelveDTO.setInventoryQuantityStatus(getInventoryQuantityStatus(spareToShelveDTO.getLubId()));
        }
        return page;
    }

    /**
     * 通过备件id获取当前数量的状态
     * 库存数量状态 0 数据错误或为填写最大最小值  1 库存不足 2 库存超限
     */
    private Integer getInventoryQuantityStatus(Integer lubId) {
        Optional<HvEamLubricating> opt = lubEntityRepository.findById(lubId);
        if (!opt.isPresent()) {
            log.info("备件id 找不到");
            throw new BaseKnownException(StoreExceptionEnum.DATE_ERROR_EXCEPTION);
        }
        //获取油品定义
        HvEamLubricating lub = opt.get();
        //当前总库存数量
        Double inventoryQuantity;
        //获取当前备件在库房的全部信息
        List<HvEamLubToShelve> lubToShelvesList = lubToShelveEntityRepository.findAllByLubId(lubId);
        //计算求和
        double sum = 0.0;
        for (HvEamLubToShelve spareToShelve : lubToShelvesList) {
            double number = spareToShelve.getNumber().doubleValue();
            sum += number;
        }
        inventoryQuantity = sum;
        if (lub.getCargoMin() == null || lub.getCargoMax() == null) {
            log.info("备件定义中最大最小数量发生异常 :" + lub.toString());
            return 0;
        }
        if (lub.getCargoMax().intValue() == 0 && lub.getCargoMin().intValue() == 0) {
            return 0;
        }

        //库存不足
        if (inventoryQuantity < lub.getCargoMin().doubleValue()) {
            return 1;
        }
        //库存超出
        if (inventoryQuantity > lub.getCargoMax().doubleValue()) {
            return 2;
        }

        return 0;
    }

    /**
     * 判断当前DTO数据是否在数据中存在
     *
     * @param lubToShelveDTO 判断DTO
     * @return true 存在   false不存在
     */
    @Override
    public boolean isExist(LubToShelveDTO lubToShelveDTO) {
        HvEamLubToShelve lubToShelveOpt = lubToShelveEntityRepository.findAllByLubIdAndShelveIdAndBatchNumber(
                lubToShelveDTO.getLubId(),
                lubToShelveDTO.getShelveId(),
                lubToShelveDTO.getBatchNumber());
        return lubToShelveOpt != null;

    }

    /**
     * 通过原油品Id 和当前油品Id  判断是否相等  来确定是否可以更新:不相等不可以更新
     *
     * @param lubToShelveDTO DTO
     * @return true 相等  false 不相等
     */
    @Override
    public boolean compareLubId(LubToShelveDTO lubToShelveDTO) {
        //通过油品库位关系表Id  查询油品Id
        Integer previousLubId = lubToShelveEntityRepository.findById(lubToShelveDTO.getId()).get().getLubId();
        Integer currentLubId = lubToShelveDTO.getLubId();
        return previousLubId.equals(currentLubId);
    }


}