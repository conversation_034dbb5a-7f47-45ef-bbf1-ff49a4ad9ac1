package com.hvisions.eam.controller;

import com.hvisions.eam.dto.lub.LubTypeDTO;
import com.hvisions.eam.query.lub.LubTypeQueryDTO;
import com.hvisions.eam.service.lub.LubTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title:LubTypeController</p>
 * <p>Description:油品型号 </p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/3/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@RestController
@RequestMapping(value = "/lubType")
@Api(description = "油品型号")
@Slf4j
public class LubTypeController {

    /**
     * 油品类型
     */
    private final LubTypeService lubTypeService;

    @Autowired
    public LubTypeController(LubTypeService lubTypeService) {
        this.lubTypeService = lubTypeService;
    }

    /**
     * 增加类型
     *
     * @param lubTypeDTO 油品型号DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "增加类型")
    @PostMapping(value = "/createAddChildNode")
    public LubTypeDTO createAddChildNode(@RequestBody LubTypeDTO lubTypeDTO) {
        return lubTypeService.save(lubTypeDTO);
    }

    /**
     * 修改类型
     *
     * @param lubTypeDTO 油品型号DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "修改类型")
    @PutMapping(value = "/updateLubType")
    public LubTypeDTO updateLubType(@RequestBody LubTypeDTO lubTypeDTO) {
        return lubTypeService.save(lubTypeDTO);
    }

    /**
     * 查询油品类型 通过ID
     *
     * @param id 备件型号DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "通过ID 查询油品类型信息 ")
    @GetMapping(value = "/getLubType/{id}")
    public LubTypeDTO getLubType(@PathVariable Integer id) {
        return lubTypeService.findById(id);
    }

    /**
     * 删除油品型号 通过ID
     *
     * @param id 备件类型ID
     */
    @ApiOperation(value = "删除通过ID")
    @DeleteMapping(value = "/deleteLubTypeById/{id}")
    public void deleteLubTypeById(@PathVariable Integer id) {
        lubTypeService.deleteById(id);
    }

    /**
     * 分页查询 通过类型名称
     *
     * @param lubTypeQueryDTO 分页信息
     * @return 分页信息
     */
    @ApiOperation(value = "分页查询 通过类型名称 查询结果为全部父级")
    @PostMapping(value = "/getSparTypePageByTypeName")
    public Page<LubTypeDTO> getSparTypePageByTypeName(@RequestBody LubTypeQueryDTO lubTypeQueryDTO) {
        return lubTypeService.findAllByTypeName(lubTypeQueryDTO);
    }

    /**
     * 查询全部
     *
     * @return 分页信息
     */
    @ApiOperation(value = "查询全部")
    @GetMapping(value = "/getLubType")
    public List<LubTypeDTO> getLubType() {
        return lubTypeService.getAll();
    }

    /**
     * 通过父级ID查询 子级全部信息
     *
     * @param parentId 父级ID
     * @return 子级全部信息
     */
    @ApiOperation(value = "通过父级ID查询 子级全部信息 ")
    @PostMapping(value = "/getChildNode/{parentId}")
    public List<LubTypeDTO> getChildNode(@PathVariable Integer parentId) {
        return lubTypeService.getChildNode(parentId);
    }
}