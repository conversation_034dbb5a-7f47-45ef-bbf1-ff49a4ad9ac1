package com.hvisions.eam.controller;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.eam.enums.StoreExceptionEnum;
import com.hvisions.eam.dto.spare.SpareToShelveDTO;
import com.hvisions.eam.query.spare.SpareToShelveQueryDTO;
import com.hvisions.eam.service.spare.SpareToShelveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.vavr.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>Title:SpareToShelveController</p>
 * <p>Description:备件库存关系</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/3/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@RestController
@RequestMapping(value = "/spareToShelve")
@Api(description = "备件库存关系")
@Slf4j
public class SpareToShelveController {

    /**
     * 库房jpa
     */
    private final SpareToShelveService spareToShelveService;

    @Autowired
    public SpareToShelveController(SpareToShelveService spareToShelveService) {
        this.spareToShelveService = spareToShelveService;
    }

    /**
     * 增加备件库存关系
     *
     * @param spareToShelveDTO 备件库存关系DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "增加备件库存关系")
    @PostMapping(value = "/createSpareToShelve")
    public Integer createSpareToShelve(@RequestBody SpareToShelveDTO spareToShelveDTO) {
        if (spareToShelveService.isExist(spareToShelveDTO)) {
            //当前数据已存在,请修改对应数据
            throw new BaseKnownException(StoreExceptionEnum.THE_CURRENT_DATA_ALREADY_EXISTS_PLEASE_MODIFY_THE_CORRESPONDING_DATA);
        }
        return spareToShelveService.save(spareToShelveDTO);
    }

    /**
     * 通过ID修改备件库存
     *
     * @param spareToShelveDTO 备件库存
     * @return 修改数据的ID
     */
    @ApiOperation(value = "通过ID修改备件库存关系")
    @PutMapping(value = "/updateSpareToShelve")
    public Integer updateSpareToShelve(@RequestBody SpareToShelveDTO spareToShelveDTO) {
        return spareToShelveService.update(spareToShelveDTO);
    }

    /**
     * 查询备件库存关系 通过ID
     *
     * @param id 备件库存关系DTO
     * @return 关系数据
     */
    @ApiOperation(value = "查询备件库存关系 通过ID")
    @GetMapping(value = "/getSpareToShelveById/{id}")
    public SpareToShelveDTO getSpareToShelveById(@PathVariable Integer id) {
        return spareToShelveService.findById(id);
    }


    /**
     * 删除关系表通过ID
     *
     * @param id 关系ID
     */
    @ApiOperation(value = "删除通过ID")
    @DeleteMapping(value = "/deleteSpareToShelveById/{id}")
    public void deleteSpareToShelveById(@PathVariable Integer id) {
        spareToShelveService.deleteById(id);
    }

    /**
     * 查询全部 通过 备件编码 备件名称 库房编码 库房名称 批次号 备件类型名称
     *
     * @param spareToShelveQueryDTO 关系表分页
     * @return 全部
     */
    @Deprecated
    @ApiOperation(value = "查询全部 通过 备件编码 备件名称 库房编码 库房名称 批次号 备件类型名称")
    @PostMapping(value = "/findAllBySpareCodeAndSpareNameAndShelveCodeAndShelveNameAndBatchNumber")
    public Page<SpareToShelveQueryDTO> findAllBySpareCodeAndSpareNameAndShelveCodeAndShelveNameAndBatchNumber(
        @RequestBody SpareToShelveQueryDTO spareToShelveQueryDTO) {
        return spareToShelveService.findAllBySpareCodeAndSpareNameAndShelveCodeAndShelveNameAndBatchNumber(
            spareToShelveQueryDTO);
    }

    /**
     * 库房查询 通过备件名称 批次号 库房名称 备件类型code 查询
     *
     * @param spareToShelveQueryDTO 关系表分页
     * @return 全部
     */
    @ApiOperation(value = "库房查询 通过备件名称 批次号 库房名称 备件类型code 查询")
    @PostMapping(value = "/getSpareToShelve")
    public Page<SpareToShelveDTO> getSpareToShelve(@RequestBody SpareToShelveQueryDTO spareToShelveQueryDTO) {
        return spareToShelveService.getSpareToShelve(spareToShelveQueryDTO);
    }

    /**
     * 获取备件相关的库存信息
     *
     * @param spareIds 备件id
     * @return 库存分组的库存信息
     */
    @ApiOperation(value = "获取备件相关的库存信息，根据库房分组")
    @PostMapping(value = "/getSpareStoreInfo")
    public List<List<Tuple2<String, List<SpareToShelveDTO>>>> getSpareStoreInfo(@RequestBody List<Integer> spareIds) {
        List<List<Tuple2<String, List<SpareToShelveDTO>>>> result = new ArrayList<>();
        for (Integer spareId : spareIds) {
            result.add(spareToShelveService.getSpareStoreInfo(spareId));
        }
        return result;
    }
}
