package com.hvisions.eam.service.publicstore.imp;

import com.hvisions.activiti.client.RepositoryFeignClient;
import com.hvisions.activiti.dto.process.ProcessDefinationQuery;
import com.hvisions.activiti.dto.process.ProcessDefinitionDTO;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.entity.publicstore.HvEamMatching;
import com.hvisions.eam.repository.publicstore.MatchingRepository;
import com.hvisions.eam.service.publicstore.PublicStoreCheckBpmnService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>Title:CheckActivitiBpmnServiceImpl</p>
 * <p>Description:程序启动流程验证</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/11</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Service
@Slf4j
public class PublicStoreCheckActivitiBpmnServiceImpl implements PublicStoreCheckBpmnService {

    /**
     * Activiti client
     */
    private final RepositoryFeignClient repositoryFeignClient;

    /**
     * 扫码解析规则
     */
    private final MatchingRepository matchingRepository;

    /**
     * 流程文件是否强制更新
     */
    @Value("${h-visions.bpmn-update}")
    private Boolean bpmnUpdate;


    @Autowired
    public PublicStoreCheckActivitiBpmnServiceImpl(RepositoryFeignClient repositoryFeignClient,
                                                   MatchingRepository matchingRepository) {
        this.repositoryFeignClient = repositoryFeignClient;
        this.matchingRepository = matchingRepository;
    }

    /**
     * 设置多个流程 1
     *
     * @return 流程
     */
    private Map<String, String> getProcess() {
        Map<String, String> map = new HashMap<>(2);
        //定义备件
        map.put("store-spare", "spare.bpmn20.xml");
        //定义油品
        map.put("store-lub", "lub.bpmn20.xml");
        return map;
    }


    /**
     * 检查流程验证 2
     */
    @Override
    public void checkActivitiBpmn() {
        log.info("activiti 流程图检查 开始");
        Map<String, String> map = getProcess();
        //遍历流程
        for (String key : map.keySet()) {
            //创建查询类并取出全部的流程
            checkActivitiBpmn(key, map.get(key));
        }
        log.info("activiti 流程图检查 完成");
    }

    /**
     * 开启每个流程的验证 3
     *
     * @param key                 key
     * @param inspectResourceName 文件名
     */
    private void checkActivitiBpmn(String key, String inspectResourceName) {
        //获取流程初始化对象
        ProcessDefinationQuery processDefinationQuery = new ProcessDefinationQuery();
        //设置processDefinitionKey
        processDefinationQuery.setProcessDefinitionKey(key);
        if (bpmnUpdate) {
            log.info("强 更新 ：" + key + ":" + inspectResourceName);
            //发布全部流程
            deployProcess(inspectResourceName);
        } else {
            //开始验证
            ResultVO<List<ProcessDefinitionDTO>> processList = repositoryFeignClient.getAllProcessList(processDefinationQuery);
            if (processList.isSuccess()) {
                //取出流程
                List<ProcessDefinitionDTO> processDefinitionDTOS = processList.getData();
                if (processDefinitionDTOS.size() > 0) {
                    //查询出数据
                    ProcessDefinitionDTO processDefinitionDTO = processDefinitionDTOS.get(0);
                    if (processDefinitionDTO.getKey().equals(key)) {
                        //文件存在
                        log.info(key + "定义文件存在");
                    } else {
                        //文件不存在
                        //发布流程
                        deployProcess(inspectResourceName);
                    }
                } else {
                    //未查询出数据
                    //发布流程
                    deployProcess(inspectResourceName);
                }
            } else {
                log.error(key + "流程定义查询失败{}", inspectResourceName);
            }
        }

    }

    /**
     * 发布流程 4
     *
     * @param inspectResourceName resourceName
     */
    private void deployProcess(String inspectResourceName) {
        //通过流的方式获取文件
        InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("process/" + inspectResourceName);
        String configContent = null;
        try {
            configContent = readFile(inputStream);
        } catch (IOException e) {
            e.printStackTrace();
        }
        ResultVO resultVO = repositoryFeignClient.deployByText(inspectResourceName, configContent);
        if (resultVO.isSuccess()) {
            log.info(inspectResourceName + "流程定义发布成功");
        } else {
            log.info(inspectResourceName + "流程定义发布失败{}", resultVO);
        }
    }

    /**
     * 读取文件 5
     *
     * @param inputStream 输入流
     * @return 文件内容
     * @throws IOException io
     */
    private String readFile(InputStream inputStream) throws IOException {
        StringBuilder builder = new StringBuilder();
        try {
            InputStreamReader reader = new InputStreamReader(inputStream, "UTF-8");
            BufferedReader bfReader = new BufferedReader(reader);
            String tmpContent = null;
            while ((tmpContent = bfReader.readLine()) != null) {
                builder.append(tmpContent);
            }
            bfReader.close();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return this.filter(builder.toString());
    }

    /**
     * 过滤输入字符串, 剔除多行注释以及替换掉反斜杠 6
     *
     * @param input 内容
     * @return 替换后的内容
     */
    private String filter(String input) {
        return input.replaceAll("/\\*[\\s\\S]*?\\*/", "");
    }

    /**
     * 备件解析规则初始化验证 7
     */
    @Override
    public void matching() {
        log.info("-----= 编码解析规则初始化开始 =-----");
        HvEamMatching spare = matchingRepository.findByService("备件");
        addMatching(spare, "备件");
        HvEamMatching lub = matchingRepository.findByService("油品");
        addMatching(lub, "油品");
        log.info("-----= 编码解析规则初始化结束 =-----");
    }

    private void addMatching(HvEamMatching matching, String service) {
        final String regEx = "#(?<code>.{6 })";
        if (matching == null) {
            matching = new HvEamMatching();
            matching.setStart(2);
            matching.setEnd(7);
            matching.setRegEx(regEx);
            matching.setType(1);
            matching.setService(service);
            matchingRepository.save(matching);
            log.info(String.format("添加 %s 解析规则", service));
        } else {
            log.info(String.format("%s 解析规则以存在", service));
        }
    }


}
