<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.eam.dao.FaultMapper">
    <select id="getEquipmentCount" resultType="java.lang.Integer">
     select count(*) from hiper_base.hv_bm_equipment t1
       WHERE t1.equipment_type_id not in
        (SELECT t2.id FROM hiper_base.hv_bm_equipment_type t2 WHERE t2.equipment_type_code
        in ("HCDB","A002","A001","CO001","DB002","YY002","YY001","BW001"))
		or t1.equipment_type_id  IS NULL
    </select>
</mapper>