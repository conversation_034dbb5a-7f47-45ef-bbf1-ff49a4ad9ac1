<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.eam.dao.DataMapper">
    <select id="getMaintainProcessIds" resultType="java.lang.String" databaseId="mysql">
        select id_
        from activiti.ACT_HI_PROCINST t1
        where t1.PROC_DEF_ID_ like 'equipment-maintain%'
          and t1.END_TIME_ is not null
          and not EXISTS(select 1 from hv_eam_maintain_process_data t2 where t2.process_instance_id = t1.id_)
    </select>
    <select id="getMaintainProcessIds" resultType="java.lang.String" databaseId="sqlserver">
        select id_
        from activiti.dbo.ACT_HI_PROCINST t1
        where t1.PROC_DEF_ID_ like 'equipment-maintain%'
          and t1.END_TIME_ is not null
          and not EXISTS(select 1 from hv_eam_maintain_process_data t2 where t2.process_instance_id = t1.id_)
    </select>
    <select id="getRepairProcessIds" resultType="java.lang.String" databaseId="mysql">
        select id_
        from activiti.ACT_HI_PROCINST t1
        where t1.PROC_DEF_ID_ like 'equipment-maintenance%'
          and t1.END_TIME_ is not null
          and not EXISTS(select 1 from hv_eam_repair_data t2 where t2.process_instance_id = t1.id_)
    </select>
    <select id="getRepairProcessIds" resultType="java.lang.String" databaseId="sqlserver">
        select id_
        from activiti.dbo.ACT_HI_PROCINST t1
        where t1.PROC_DEF_ID_ like 'equipment-maintenance%'
          and t1.END_TIME_ is not null
          and not EXISTS(select 1 from hv_eam_repair_data t2 where t2.process_instance_id = t1.id_)
    </select>
    <select id="getLineIdsByEquipmentId" resultType="java.lang.Integer" databaseId="mysql">
        select t1.id
        from hiper_base.hv_bm_location t1
        where exists(select 1
                     from hiper_base.hv_bm_equipment_cell t2
                              join hiper_base.hv_bm_equipment t3
                     where t2.equipment_id = t3.id
                       and t2.cell_id = t1.id
                       and t3.id = #{id})
    </select>
    <select id="getLineIdsByEquipmentId" resultType="java.lang.Integer" databaseId="sqlserver">
        select t1.id
        from hiper_base.dbo.hv_bm_location t1
        where exists(select 1
                     from hiper_base.dbo.hv_bm_equipment_cell t2
                              join hiper_base.dbo.hv_bm_equipment t3
                     on t2.equipment_id = t3.id
                       and t2.cell_id = t1.id
                       and t3.id = #{id})
    </select>
</mapper>