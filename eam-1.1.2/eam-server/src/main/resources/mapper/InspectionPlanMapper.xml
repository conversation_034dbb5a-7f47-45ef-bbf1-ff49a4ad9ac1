<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.eam.dao.InspectionPlanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hvisions.eam.entity.autonomy.HvAmInspectionPlan">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator_id" property="creatorId" />
        <result column="updater_id" property="updaterId" />
        <result column="site_num" property="siteNum" />
        <result column="number" property="number" />
        <result column="name" property="name" />
        <result column="types_of_liability" property="typesOfLiability" />
        <result column="responsibility" property="responsibility" />
        <result column="verifier" property="verifier" />
        <result column="foreman" property="foreman" />
        <result column="leader" property="leader" />
        <result column="timer_id" property="timerId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, update_time, creator_id, updater_id, site_num, number, name, types_of_liability, responsibility, verifier, foreman, leader, timer_id
    </sql>

</mapper>
