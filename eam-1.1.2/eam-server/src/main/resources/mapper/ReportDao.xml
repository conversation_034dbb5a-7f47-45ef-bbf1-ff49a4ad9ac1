<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.eam.dao.ReportDao">
    <select id="getEquipmentSparePart" resultType="com.hvisions.eam.dto.publicstore.SparePartInfo" databaseId="mysql">
        select spare_id       as sparePartId,
               t1.number      as count,
               t4.unit_price  as sparePartPrice,
               t4.spare_name  as sparePartName,
               t4.spare_code  as sparePartCode,
               t5.unit_name   as sparePartUnit,
               t1.create_time as createTime
        from hv_eam_actual_use t1
                 join hv_eam_spare t4 on t1.spare_id = t4.id
                 left join hv_eam_spare_unit t5 on t4.unit_id = t5.id
        where t1.type = 1
	     and
         exists(select 1
                     from activiti.ACT_HI_PROCINST t2
                              join activiti.ACT_RE_PROCDEF t3 on t2.proc_def_id_ = t3.id_
                     where (t3.key_ = 'equipment-maintain' or t3.key_ = 'equipment-maintenance')
                       and t2.id_ = t1.process_instance_id
                       and exists(select 1
                                  from activiti.ACT_HI_VARINST t7
                                  where t7.proc_inst_id_ = t2.id_
                                    and t7.name_ = 'equipmentId'
                                    and t7.long_ = #{equipmentId}))
        order by t1.create_time desc
    </select>
    <select id="getEquipmentSparePart" resultType="com.hvisions.eam.dto.publicstore.SparePartInfo"
            databaseId="sqlserver">
        select spare_id       as sparePartId,
               t1.number      as count,
               t4.unit_price  as sparePartPrice,
               t4.spare_name  as sparePartName,
               t4.spare_code  as sparePartCode,
               t5.unit_name   as sparePartUnit,
               t1.create_time as createTime
        from hv_eam_actual_use t1
                 join hv_eam_spare t4 on t1.spare_id = t4.id
                 left join hv_eam_spare_unit t5 on t4.unit_id = t5.id
        where t1.type = 1
	    and
        exists(select 1
                     from activiti.dbo.ACT_HI_PROCINST t2
                              join activiti.dbo.ACT_RE_PROCDEF t3 on t2.proc_def_id_ = t3.id_
                     where (t3.key_ = 'equipment-maintain' or t3.key_ = 'equipment-maintenance')
                       and t2.id_ = t1.process_instance_id
                       and exists(select 1
                                  from activiti.dbo.ACT_HI_VARINST t7
                                  where t7.proc_inst_id_ = t2.id_
                                    and t7.name_ = 'equipmentId'
                                    and t7.long_ = #{equipmentId}))
        order by t1.create_time desc
    </select>
</mapper>