<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.eam.dao.MaintainPlanMapper">
    <select id="getPage" resultType="com.hvisions.eam.dto.maintain.MaintainPlanDTO">
        select * from hv_eam_maintain_plan t1
        <where>
            <if test="query.equipmentId != null and query.equipmentId != ''">
                and exists (
                select 1
                from hv_eam_maintain_plan_equipment t2
                where t2.equipment_id = #{query.equipmentId}
                and t2.maintain_plan_id = t1.id)
            </if>

            <if test="query.maintainPlanName != null and query.maintainPlanName != ''">
                and t1.maintain_plan_name like concat ('%',#{query.maintainPlanName},'%')
            </if>
            <if test="query.maintainPlanConditionId != null ">
                and t1.maintain_plan_condition_id = #{query.maintainPlanConditionId}
            </if>
            <if test="query.timerId != null ">
                and t1.timer_id = #{query.timerId}
            </if>
            <if test="query.createTimeBegin != null and query.createTimeEnd != null">
                and t1.create_time between #{query.createTimeBegin} and #{query.createTimeEnd}
            </if>
        </where>
    </select>
    <select id="getSparesInfo" resultType="com.hvisions.eam.dto.maintain.SpareOrLubInfo">
        (
        SELECT '备件' as type,
        spare_part_code as code,
        spare_part_name as name,
        spare_part_id as id,
        spare_part_num as quantity,
        maintain_item_id as item_id
        FROM hv_eam_item_spare_part
        <where>
            maintain_item_id in
            <foreach item="item" index="index" collection="ids"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        )
        UNION ALL
        (SELECT '油品' as type,
        lub_code as code,
        lub_name as name,
        lub_num as quantity,
        lub_id as id,
        maintain_item_id as item_id
        FROM hv_eam_item_lub
        <where>
            maintain_item_id in
            <foreach item="item" index="index" collection="ids"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        )
    </select>
    <select id="getPlanManHour" resultType="java.lang.Float">
        select sum(t1.man_hour)
        from hv_eam_maintain_item t1
                 join hv_eam_maintain_plan_equipment t2 on t2.maintain_item_id = t1.id
        where t2.maintain_plan_id = #{id}
          and t1.man_hour is not null
    </select>
</mapper>