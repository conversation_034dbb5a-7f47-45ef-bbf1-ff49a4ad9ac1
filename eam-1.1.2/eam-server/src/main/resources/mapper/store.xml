<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<!-- dao层 -->
<mapper namespace="com.hvisions.eam.dao.StoreDao">
    <select id="getAllSparePart" resultType="com.hvisions.eam.dto.spare.SpareItemDTO"
            parameterType="com.hvisions.eam.dto.publicstore.StoreQuery">
        select t3.batch_number,
               t4.shelve_name,
               t3.shelve_id,
               t3.spare_id,
               t3.sum              number,
               t5.spare_name,
               t5.specifications,
               t7.spare_brand_name brand,
               t6.type_name,
               t5.supplier
        from (select batch_number, shelve_id, spare_id, sum(number) sum
              from hv_eam_store_line t1
              where EXISTS (select 1
                      from hv_eam_store_header t2
                      where t2.process_instance_id = #{query.processInstanceId}
                and t2.complete = #{query.isComplete}
                and t2.reject is null
                and t2.in_out = #{query.inOrOut}
                and t2.type_class = #{query.type}
                and t1.header_id = t2.id)
              GROUP BY batch_number, shelve_id, spare_id) t3
                     join hv_eam_shelve t4 on t3.shelve_id = t4.id
                     join hv_eam_spare t5 on t3.spare_id = t5.id
                     left join hv_eam_spare_type t6 on t5.spare_type_id = t6.id
                     left join hv_eam_spare_brand t7 on t5.brand = t7.spare_brand_code
    </select>

    <select id="getAllLub" resultType="com.hvisions.eam.dto.spare.SpareItemDTO"
            parameterType="com.hvisions.eam.dto.publicstore.StoreQuery">
        SELECT t3.batch_number,
               t4.shelve_name,
               t3.shelve_id,
               t3.spare_id,
               t3.sum         number,
               t5.lub_name AS spare_name,
               t6.type_name,
               t5.supplier
        FROM (SELECT batch_number,
                     shelve_id,
                     spare_id,
                     sum(number) sum
              FROM
                      hv_eam_store_line t1
              WHERE
                      EXISTS (
                      SELECT
                      1
                      FROM
                      hv_eam_store_header t2
                      WHERE
                      t2.process_instance_id = #{query.processInstanceId}
                AND t2.complete = #{query.isComplete}
                AND t2.reject IS NULL
                AND t2.in_out = #{query.inOrOut}
                AND t2.type_class = #{query.type}
                AND t1.header_id = t2.id
                      )
              GROUP BY
                      batch_number,
                      shelve_id,
                      spare_id) t3
                     JOIN hv_eam_shelve t4 ON t3.shelve_id = t4.id
                     JOIN hv_eam_lubricating t5 ON t3.spare_id = t5.id
                     LEFT JOIN hv_eam_lub_type t6 ON t5.lub_type_id = t6.id
    </select>
</mapper>