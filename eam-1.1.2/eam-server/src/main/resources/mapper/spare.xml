<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.eam.dao.SpareMapper">
    <resultMap id="spareDTO" type="com.hvisions.eam.dto.spare.SpareDTO">
    </resultMap>
    <select id="getSpareByQuery" resultMap="spareDTO" parameterType="com.hvisions.eam.query.spare.SpareQueryDTO"
            databaseId="mysql">
        select t1.* ,
        t1.metype as metype ,
        t4.user_name as purchaseUserName ,
        t5.user_name as skillUserName ,
        t6.user_name as createName ,
        t3.unit_name as unitName ,
        t3.unit_symbol as unitSymbol ,
        t2.type_name as typeName
        from hv_eam_spare t1
        left join hv_eam_spare_type t2 on t1.spare_type_id = t2.id
        left join hv_eam_spare_unit t3 on t1.unit_id = t3.id
        left join framework.sys_user t4 on t1.purchase_user_id = t4.id
        left join framework.sys_user t5 on t1.skill_user_id = t5.id
        left join framework.sys_user t6 on t1.creator_id = t6.id
        <where>
            <if test="dto.spareCode != null  and dto.spareCode != &quot;&quot;">
                and t1.spare_code like concat('%', #{dto.spareCode}, '%')
            </if>
            <if test="dto.spareName != null  and dto.spareName != &quot;&quot;">
                and t1.spare_name like concat('%', #{dto.spareName}, '%')
            </if>
            <if test="dto.spareTypeCode != null  and dto.spareTypeCode != &quot;&quot;">
                and t2.type_code like concat('%', #{dto.spareTypeCode}, '%')
            </if>
            <if test="dto.unitName != null  and dto.unitName != &quot;&quot;">
                and t3.unit_name like concat('%', #{dto.unitName}, '%')
            </if>
            <if test="dto.unitSymbol != null  and dto.unitSymbol != &quot;&quot;">
                and t3.unit_symbol like concat('%', #{dto.unitSymbol}, '%')
            </if>
            <if test="dto.remarks != null  and dto.remarks != &quot;&quot;">
                and t1.remarks like concat('%', #{dto.remarks}, '%')
            </if>
            <if test="dto.supplier != null  and dto.supplier != &quot;&quot;">
                and t1.supplier like concat('%', #{dto.supplier}, '%')
            </if>
            <if test="dto.brand != null  and dto.brand != &quot;&quot;">
                and t1.brand like concat('%', #{dto.brand}, '%')
            </if>
            <if test="dto.expectedNumber != null">
                and t1.expected_number = #{dto.expectedNumber}
            </if>
            <if test="dto.alarmRemainingNumber != null">
                and t1.alarm_remaining_number = #{dto.alarmRemainingNumber}
            </if>
            <if test="dto.purchaseUserId != null">
                and t1.purchase_user_id = #{dto.purchaseUserId}
            </if>
            <if test="dto.skillUserId != null">
                and t1.skill_user_id = #{dto.skillUserId}
            </if>
            <if test="dto.metype != null  and dto.metype != &quot;&quot;">
                and t1.metype like concat('%', #{dto.metype}, '%')
            </if>
            <if test="dto.specifications != null  and dto.specifications != &quot;&quot;">
                and t1.specifications like concat('%', #{dto.specifications}, '%')
            </if>
            <if test="dto.createTimeStart != null and dto.createTimeEnd != null">
                and t1.create_time between #{dto.createTimeStart} and #{dto.createTimeEnd}
            </if>
            <if test="dto.orderNum != null and dto.orderNum != &quot;&quot;">
                and t1.order_num like concat('%', #{dto.orderNum}, '%')
            </if>
        </where>
    </select>
    <select id="getSpareByQuery" resultMap="spareDTO"
            parameterType="com.hvisions.eam.query.spare.SpareQueryDTO" databaseId="sqlserver">
        select t1.* ,
        t4.user_name as purchaseUserName ,
        t5.user_name as skillUserName ,
        t6.user_name as createName ,
        t3.unit_name as unitName ,
        t3.unit_symbol as unitSymbol ,
        t2.type_name as typeName
        from hv_eam_spare t1
        left join hv_eam_spare_type t2 on t1.spare_type_id = t2.id
        left join hv_eam_spare_unit t3 on t1.unit_id = t3.id
        left join framework.dbo.sys_user t4 on t1.purchase_user_id = t4.id
        left join framework.dbo.sys_user t5 on t1.skill_user_id = t5.id
        left join framework.dbo.sys_user t6 on t1.creator_id = t6.id
        <where>
            <if test="dto.spareCode != null  and dto.spareCode != &quot;&quot;">
                and t1.spare_code like concat('%', #{dto.spareCode}, '%')
            </if>
            <if test="dto.spareName != null  and dto.spareName != &quot;&quot;">
                and t1.spare_name like concat('%', #{dto.spareName}, '%')
            </if>
            <if test="dto.spareTypeCode != null  and dto.spareTypeCode != &quot;&quot;">
                and t2.type_code like concat('%', #{dto.spareTypeCode}, '%')
            </if>
            <if test="dto.unitName != null  and dto.unitName != &quot;&quot;">
                and t3.unit_name like concat('%', #{dto.unitName}, '%')
            </if>
            <if test="dto.unitSymbol != null  and dto.unitSymbol != &quot;&quot;">
                and t3.unit_symbol like concat('%', #{dto.unitSymbol}, '%')
            </if>
            <if test="dto.remarks != null  and dto.remarks != &quot;&quot;">
                and t1.remarks like concat('%', #{dto.remarks}, '%')
            </if>
            <if test="dto.supplier != null  and dto.supplier != &quot;&quot;">
                and t1.supplier like concat('%', #{dto.supplier}, '%')
            </if>
            <if test="dto.brand != null  and dto.brand != &quot;&quot;">
                and t1.brand like concat('%', #{dto.brand}, '%')
            </if>
            <if test="dto.expectedNumber != null">
                and t1.expected_number = #{dto.expectedNumber}
            </if>
            <if test="dto.alarmRemainingNumber != null">
                and t1.alarm_remaining_number = #{dto.alarmRemainingNumber}
            </if>
            <if test="dto.purchaseUserId != null">
                and t1.purchase_user_id = #{dto.purchaseUserId}
            </if>
            <if test="dto.skillUserId != null">
                and t1.skill_user_id = #{dto.skillUserId}
            </if>
            <if test="dto.metype != null  and dto.metype != &quot;&quot;">
                and t1.metype like concat('%', #{dto.metype}, '%')
            </if>
            <if test="dto.specifications != null  and dto.specifications != &quot;&quot;">
                and t1.specifications like concat('%', #{dto.specifications}, '%')
            </if>
            <if test="dto.createTimeStart != null and dto.createTimeEnd != null">
                and t1.create_time between #{dto.createTimeStart} and #{dto.createTimeEnd}
            </if>
            <if test="dto.orderNum != null and dto.orderNum != &quot;&quot;">
                and t1.order_num like concat('%', #{dto.orderNum}, '%')
            </if>
        </where>
    </select>
</mapper>