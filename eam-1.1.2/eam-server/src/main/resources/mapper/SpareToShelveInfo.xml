<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<!-- dao层 -->
<mapper namespace="com.hvisions.eam.dao.SpareToShelveMapper">
    <!--对象-->
    <resultMap id="f" type="com.hvisions.eam.dto.spare.SpareToShelveDTO">
        <result column="shelve_id" jdbcType="INTEGER" property="shelveId"/>
        <result column="spare_id" jdbcType="INTEGER" property="spareId"/>
        <result column="number" jdbcType="INTEGER" property="number"/>
        <result column="batch_number" jdbcType="VARCHAR" property="batchNumber"/>
        <result column="img" jdbcType="INTEGER" property="img"/>
        <result column="type_name" jdbcType="VARCHAR" property="typeName"/>
        <result column="shelve_name" jdbcType="VARCHAR" property="shelveName"/>
        <result column="type_code" jdbcType="VARCHAR" property="typeCode"/>
        <result column="spare_name" jdbcType="VARCHAR" property="spareName"/>
        <result column="spare_code" jdbcType="VARCHAR" property="spareCode"/>
        <result column="specifications" jdbcType="VARCHAR" property="specifications"/>
        <result column="brand" jdbcType="VARCHAR" property="brand"/>
    </resultMap>

    <select id="getSpareToShelve" resultMap="f" databaseId="mysql">
        SELECT stos.id,
        stos.shelve_id,
        stos.spare_id,
        stos.batch_number,
        spare.img,
        spare.spare_name,
        spare.spare_code,
        spare.supplier,
        shelve.shelve_name,
        type.type_code,
        type.type_name,
        spare.brand,
        spare.specifications,
        stos.number,
        spare.plan_price
        FROM hv_eam_spare_to_shelve stos
        JOIN hv_eam_spare spare ON stos.spare_id = spare.id
        JOIN hv_eam_shelve shelve ON stos.shelve_id = shelve.id
        JOIN hv_eam_spare_type type ON spare.spare_type_id = type.id
        WHERE 1 = 1
        <if test="spareId != null">
            AND spare_id = #{spareId}
        </if>
        <if test="shelveName != null and shelveName != ''">
            AND shelve_name LIKE '%${shelveName}%'
        </if>
        <if test="typeCode != null and typeCode != ''">
            AND type_code like '%${typeCode}%'
        </if>
        <if test="batchNumber != null and batchNumber != ''">
            AND batch_number LIKE '%${batchNumber}%'
        </if>
        <if test="spareName != null and spareName != ''">
            AND spare_name LIKE '%${spareName}%'
        </if>
        <if test="spareCode != null and spareCode != ''">
            AND spare_code LIKE '%${spareCode}%'
        </if>
        <if test="shelveId != null and shelveId != ''">
            AND shelve_id = #{shelveId}
        </if>
        <if test="showNumberZero != true">
            AND stos.number > 0
        </if>
        <if test="brand != null">
            AND brand like '%${brand}%'
        </if>
        <if test="typeName != null and typeName != ''">
            AND type_name like '%${typeName}%'
        </if>
        <if test="specifications != null and specifications != ''">
            AND specifications like '%${specifications}%'
        </if>
        <if test="keyword != null and keyword != ''">
            and shelve.shelve_name LIKE concat('%', #{keyword}, '%')
            or type.type_code LIKE concat('%', #{keyword}, '%')
            or stos.batch_number LIKE concat('%', #{keyword}, '%')
            or spare.spare_name LIKE concat('%', #{keyword}, '%')
            or spare.spare_code LIKE concat('%', #{keyword}, '%')
            or spare.brand LIKE concat('%', #{keyword}, '%')
            or type.type_name LIKE concat('%', #{keyword}, '%')
            or spare.supplier LIKE concat('%', #{keyword}, '%')
        </if>
    </select>
    <!--//databaseId="sql2008"-->
    <select id="getSpareToShelve" resultMap="f" databaseId="sqlserver">
        SELECT stos.id,
        stos.shelve_id,
        stos.spare_id,
        stos.batch_number,
        spare.img,
        spare.spare_name,
        spare.spare_code,
        spare.supplier,
        shelve.shelve_name,
        type.type_code,
        type.type_name,
        spare.brand,
        spare.specifications,
        stos.number,
        spare.plan_price
        FROM hv_eam_spare_to_shelve stos
        JOIN hv_eam_spare spare ON stos.spare_id = spare.id
        JOIN hv_eam_shelve shelve ON stos.shelve_id = shelve.id
        JOIN hv_eam_spare_type type ON spare.spare_type_id = type.id
        WHERE 1 = 1
        <if test="spareId != null">
            AND spare_id = #{spareId}
        </if>
        <if test="shelveName != null and shelveName != ''">
            AND shelve_name LIKE '%${shelveName}%'
        </if>
        <if test="typeCode != null and typeCode != ''">
            AND type_code like '%${typeCode}%'
        </if>
        <if test="batchNumber != null and batchNumber != ''">
            AND batch_number LIKE '%${batchNumber}%'
        </if>
        <if test="spareName != null and spareName != ''">
            AND spare_name LIKE '%${spareName}%'
        </if>
        <if test="spareCode != null and spareCode != ''">
            AND spare_code LIKE '%${spareCode}%'
        </if>
        <if test="shelveId != null and shelveId != ''">
            AND shelve_id = #{shelveId}
        </if>
        <if test="showNumberZero != true">
            AND stos.number > 0
        </if>
        <if test="brand != null">
            AND brand like '%${brand}%'
        </if>
        <if test="typeName != null and typeName != ''">
            AND type_name like '%${typeName}%'
        </if>
        <if test="specifications != null and specifications != ''">
            AND specifications like '%${specifications}%'
        </if>
        <if test="keyword != null and keyword != ''">
            and shelve.shelve_name LIKE concat('%', #{keyword}, '%')
            or type.type_code LIKE concat('%', #{keyword}, '%')
            or stos.batch_number LIKE concat('%', #{keyword}, '%')
            or spare.spare_name LIKE concat('%', #{keyword}, '%')
            or spare.spare_code LIKE concat('%', #{keyword}, '%')
            or spare.brand LIKE concat('%', #{keyword}, '%')
            or type.type_name LIKE concat('%', #{keyword}, '%')
            or spare.supplier LIKE concat('%', #{keyword}, '%')
        </if>
    </select>
</mapper>