<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.eam.dao.inspect.InspectDataMapper">
    <select id="getProcessInstanceIds" resultType="java.lang.String" databaseId="mysql">
        select id_
        from activiti.ACT_HI_PROCINST t1
        where t1.PROC_DEF_ID_ like 'equipment-inspect%'
          and t1.END_TIME_ is not null
          and not EXISTS(select 1 from hv_eam_inspect_process_data t2 where t2.process_instance_id = t1.id_)
    </select>
    <select id="getProcessInstanceIds" resultType="java.lang.String" databaseId="sqlserver">
        select id_
        from activiti.dbo.ACT_HI_PROCINST t1
        where t1.PROC_DEF_ID_ like 'equipment-inspect%'
          and t1.END_TIME_ is not null
          and not EXISTS(select 1 from hv_eam_inspect_process_data t2 where t2.process_instance_id = t1.id_)
    </select>
</mapper>