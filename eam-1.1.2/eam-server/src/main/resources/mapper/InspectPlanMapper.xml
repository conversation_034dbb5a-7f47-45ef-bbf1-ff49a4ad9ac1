<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.eam.dao.inspect.InspectPlanMapper">
    <select id="getPage" resultType="com.hvisions.eam.dto.inspect.plan.InspectPlanDTO">
        select * from hv_eam_inspect_plan t1
        <where>
            <if test="query.equipmentId != null ">
                and exists (select 1 from hv_eam_inspect_plan_item t2
                where t2.inspect_plan_id = t1.id and t2.equipment_id = #{query.equipmentId})
            </if>

            <if test="query.inspectPlanName != null and query.inspectPlanName != ''">
                and t1.inspect_plan_name like concat ('%',#{query.inspectPlanName},'%')
            </if>
            <if test="query.inspectPlanConditionId != null ">
                and t1.inspect_plan_condition_id = #{query.inspectPlanConditionId}
            </if>
            <if test="query.timerId != null ">
                and t1.timer_id = #{query.timerId}
            </if>
            <if test="query.createTimeBegin != null and query.createTimeEnd != null">
                and t1.create_time between #{query.createTimeBegin} and #{query.createTimeEnd}
            </if>
        </where>
    </select>

</mapper>