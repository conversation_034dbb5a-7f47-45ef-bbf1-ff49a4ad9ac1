<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.eam.dao.inspect.BoardMapper">
    <select id="getTaskCount" resultType="java.lang.Integer" databaseId="mysql">
        select count(*)
        from activiti.ACT_HI_PROCINST t1
        where t1.PROC_DEF_ID_ like 'equipment-inspect%'
          and t1.START_TIME_ > #{date}
    </select>
    <select id="getTaskCount" resultType="java.lang.Integer" databaseId="sqlserver">
        select count(*)
        from activiti.dbo.ACT_HI_PROCINST t1
        where t1.PROC_DEF_ID_ like 'equipment-inspect%'
          and t1.START_TIME_ > #{date}
    </select>
    <select id="getItemCount" resultType="java.lang.Integer" databaseId="mysql">
        select count(*)
        from activiti.ACT_HI_PROCINST t1
                 join hv_eam_inspect_plan t2 on t1.business_key_ = t2.id
                 join hv_eam_inspect_plan_content t3 on t3.inspect_plan_id = t2.id
                 join hv_eam_inspect_content t4 on t4.id = t3.inspect_content_id
                 join hv_eam_inspect_content_item t5 on t5.inspect_content_id = t4.id
        where t1.PROC_DEF_ID_ like 'equipment-inspect%'
          and t1.START_TIME_ > #{date}
    </select>
    <select id="getItemCount" resultType="java.lang.Integer" databaseId="sqlserver">
        select count(*)
        from activiti.dbo.ACT_HI_PROCINST t1
                 join hv_eam_inspect_plan t2 on t1.business_key_ = t2.id
                 join hv_eam_inspect_plan_content t3 on t3.inspect_plan_id = t2.id
                 join hv_eam_inspect_content t4 on t4.id = t3.inspect_content_id
                 join hv_eam_inspect_content_item t5 on t5.inspect_content_id = t4.id
        where t1.PROC_DEF_ID_ like 'equipment-inspect%'
          and t1.START_TIME_ > #{date}
    </select>
    <select id="getFinishedItemCount" resultType="java.lang.Integer" databaseId="mysql">
        select count(*)
        from activiti.ACT_HI_PROCINST t1
                 join hv_eam_inspect_plan t2 on t1.business_key_ = t2.id
                 join hv_eam_inspect_plan_content t3 on t3.inspect_plan_id = t2.id
                 join hv_eam_inspect_content t4 on t4.id = t3.inspect_content_id
                 join hv_eam_inspect_content_item t5 on t5.inspect_content_id = t4.id
        where t1.PROC_DEF_ID_ like 'equipment-inspect%'
          and t1.END_TIME_ is not null
          and t1.START_TIME_ > #{date}
    </select>
    <select id="getFinishedItemCount" resultType="java.lang.Integer" databaseId="sqlserver">
        select count(*)
        from activiti.dbo.ACT_HI_PROCINST t1
                 join hv_eam_inspect_plan t2 on t1.business_key_ = t2.id
                 join hv_eam_inspect_plan_content t3 on t3.inspect_plan_id = t2.id
                 join hv_eam_inspect_content t4 on t4.id = t3.inspect_content_id
                 join hv_eam_inspect_content_item t5 on t5.inspect_content_id = t4.id
        where t1.PROC_DEF_ID_ like 'equipment-inspect%'
          and t1.END_TIME_ is not null
          and t1.START_TIME_ > #{date}
    </select>
    <select id="getLastInspectTime" resultType="java.time.LocalDateTime" databaseId="mysql">
        select t1.END_TIME_
        from activiti.ACT_HI_PROCINST t1
        where t1.END_TIME_ is not null
        order by t1.END_TIME_ desc
        limit 1
    </select>
    <select id="getLastInspectTime" resultType="java.time.LocalDateTime" databaseId="sqlserver">
        select t1.END_TIME_
        from activiti.dbo.ACT_HI_PROCINST t1
        where t1.END_TIME_ is not null
        order by t1.END_TIME_ desc
        limit 1
    </select>
</mapper>