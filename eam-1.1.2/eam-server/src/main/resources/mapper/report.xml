<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.eam.dao.ReportStatisticalMapper">
    <resultMap id="usage" type="com.hvisions.eam.dto.report.UrgentRepairParetoInfo">
    </resultMap>

    <select id="urgentMainParetoAnalysis" resultMap="usage"
            parameterType="com.hvisions.eam.dto.report.ReportUrgentRepairParetoAnalysisQuery" databaseId="mysql">
        SELECT t5.equipment_name AS equipmentName,
        ROUND(sum(t4.finishTime - t4.startTime) / 1000 / 60, 2) AS repairTime
        FROM (
        SELECT task_id_,
        PROC_INST_ID_,
        sum(CASE
        WHEN NAME_ = 'h_visions_task_start_time_variable_name' THEN long_
        ELSE 0 END) startTime,
        sum(CASE WHEN NAME_ = 'h-visions-task-finish_time' THEN long_ ELSE 0 END) finishTime
        FROM (
        SELECT *
        FROM activiti.ACT_HI_VARINST va
        WHERE TASK_ID_ IS NOT NULL
        AND NOT EXISTS(
        SELECT 1
        FROM activiti.ACT_HI_VARINST t2
        WHERE t2.task_id_ = va.TASK_ID_
        AND t2.NAME_ = va.NAME_
        AND t2.id_ > va.ID_
        )
        ) t3
        GROUP BY task_id_,
        PROC_INST_ID_
        ) t4
        JOIN hv_eam_repair_data t5 ON t4.PROC_INST_ID_ = t5.process_instance_id
        <where>
            t4.startTime <![CDATA[ > ]]> 0
            AND t4.finishTime <![CDATA[ > ]]> 0
            AND EXISTS ( SELECT 1
            FROM activiti.ACT_HI_PROCINST t8 WHERE t8.PROC_INST_ID_ = t4.PROC_INST_ID_
            <if test="dto.startTime != null and dto.endTime != null">
                and t8.END_TIME_ BETWEEN #{dto.startTime,jdbcType=TIMESTAMP} and #{dto.endTime,jdbcType=TIMESTAMP}
            </if>
            )
            <if test="dto.cellId !=null">
                AND EXISTS ( SELECT *
                from `hiper_base`.hv_bm_equipment j1
                left join `hiper_base`.hv_bm_equipment_cell j2 on j1.id = j2.equipment_id
                left join `hiper_base`.hv_bm_location j3 on j2.cell_id = j3.id
                WHERE j1.id = t5.equipment_id
                AND j3.id = #{dto.cellId})
            </if>
        </where>
        GROUP BY t5.equipment_name
        ORDER BY repairTime DESC
    </select>
    <select id="urgentMainParetoAnalysis" resultMap="usage"
            parameterType="com.hvisions.eam.dto.report.ReportUrgentRepairParetoAnalysisQuery" databaseId="sqlserver">
        SELECT t5.equipment_name AS equipmentName,
        ROUND(sum(t4.finishTime - t4.startTime) / 1000 / 60, 2) AS repairTime
        FROM (
        SELECT task_id_,
        PROC_INST_ID_,
        sum(CASE
        WHEN NAME_ = 'h_visions_task_start_time_variable_name' THEN long_
        ELSE 0 END) startTime,
        sum(CASE WHEN NAME_ = 'h-visions-task-finish_time' THEN long_ ELSE 0 END) finishTime
        FROM (
        SELECT *
        FROM activiti.dbo.ACT_HI_VARINST va
        WHERE TASK_ID_ IS NOT NULL
        AND NOT EXISTS(
        SELECT 1
        FROM activiti.dbo.ACT_HI_VARINST t2
        WHERE t2.task_id_ = va.TASK_ID_
        AND t2.NAME_ = va.NAME_
        AND t2.id_ > va.ID_
        )
        ) t3
        GROUP BY task_id_,
        PROC_INST_ID_
        ) t4
        JOIN hv_eam_repair_data t5 ON t4.PROC_INST_ID_ = t5.process_instance_id
        <where>
            t4.startTime <![CDATA[ > ]]> 0
            AND t4.finishTime <![CDATA[ > ]]> 0
            AND EXISTS ( SELECT 1
            FROM activiti.dbo.ACT_HI_PROCINST t8 WHERE t8.PROC_INST_ID_ = t4.PROC_INST_ID_
            <if test="dto.startTime != null and dto.endTime != null">
                and t8.END_TIME_ BETWEEN #{dto.startTime,jdbcType=TIMESTAMP} and #{dto.endTime,jdbcType=TIMESTAMP}
            </if>
            )
            <if test="dto.cellId !=null">
                AND EXISTS ( SELECT *
                from hiper_base.dbo.hv_bm_equipment j1
                left join hiper_base.dbo.hv_bm_equipment_cell j2 on j1.id = j2.equipment_id
                left join hiper_base.dbo.hv_bm_location j3 on j2.cell_id = j3.id
                WHERE j1.id = t5.equipment_id
                AND j3.id = #{dto.cellId})
            </if>
        </where>
        GROUP BY t5.equipment_name
        ORDER BY repairTime DESC
    </select>


    <resultMap id="equipmentFault" type="com.hvisions.eam.dto.report.EquipmentFaultInfo">
    </resultMap>
    <select id="equipmentFaultStatistical" resultMap="equipmentFault"
            parameterType="com.hvisions.eam.dto.report.ReportEquipmentFaultStatisticalQuery" databaseId="mysql">
        SELECT t11.NAME AS productionLine,
        t8.equipment_code AS equipmentCode,
        t8.equipment_name AS equipmentName,
        ROUND((t4.finishTime - t4.startTime) / 1000 / 60, 2) AS faultTime,
        t6.fault_name fault
        FROM (
        SELECT task_id_,
        PROC_INST_ID_,
        sum(CASE
        WHEN NAME_ = 'h_visions_task_start_time_variable_name' THEN long_
        ELSE 0 END) startTime,
        sum(CASE WHEN NAME_ = 'h-visions-task-finish_time' THEN long_ ELSE 0 END) finishTime
        FROM (
        SELECT *
        FROM activiti.ACT_HI_VARINST va
        WHERE TASK_ID_ IS NOT NULL
        AND NOT EXISTS(
        SELECT 1
        FROM activiti.ACT_HI_VARINST t2
        WHERE t2.task_id_ = va.TASK_ID_
        AND t2.NAME_ = va.NAME_
        AND t2.id_ > va.ID_
        )
        ) t3
        GROUP BY task_id_,
        PROC_INST_ID_
        ) t4
        JOIN hv_eam_repair_data t5
        ON t4.PROC_INST_ID_ = t5.process_instance_id
        JOIN hv_em_fault t6 ON t5.fault_apply_id = t6.id
        JOIN hv_em_fault_class t7 ON t6.fault_class_id = t7.id
        JOIN hiper_base.hv_bm_equipment t8 ON t7.equipment_class_id = t8.equipment_type_id
        JOIN (
        SELECT t9.cell_id,
        t9.equipment_id,
        t10.NAME
        FROM hiper_base.hv_bm_equipment_cell t9
        JOIN hiper_base.hv_bm_location t10 ON t9.cell_id = t10.id
        WHERE t10.type = 40
        GROUP BY t9.cell_id,
        t9.equipment_id
        ) t11 ON t8.id = t11.equipment_id
        <where>
            t4.startTime <![CDATA[ > ]]> 0
            AND t4.finishTime <![CDATA[ > ]]> 0
            AND EXISTS ( SELECT 1
            FROM activiti.ACT_HI_PROCINST t12 WHERE t12.PROC_INST_ID_ = t4.PROC_INST_ID_
            <if test="dto.startTime != null and dto.endTime != null">
                AND t12.END_TIME_ BETWEEN #{dto.startTime,jdbcType=TIMESTAMP}
                AND #{dto.endTime,jdbcType=TIMESTAMP}
            </if>
            )
        </where>
        ORDER BY faultTime DESC
    </select>

    <select id="equipmentFaultStatistical" resultMap="equipmentFault"
            parameterType="com.hvisions.eam.dto.report.ReportEquipmentFaultStatisticalQuery" databaseId="sqlserver">
        SELECT t11.NAME AS productionLine,
        t8.equipment_code AS equipmentCode,
        t8.equipment_name AS equipmentName,
        ROUND((t4.finishTime - t4.startTime) / 1000 / 60, 2) AS faultTime,
        t6.fault_name fault
        FROM (
        SELECT task_id_,
        PROC_INST_ID_,
        sum(CASE
        WHEN NAME_ = 'h_visions_task_start_time_variable_name' THEN long_
        ELSE 0 END) startTime,
        sum(CASE WHEN NAME_ = 'h-visions-task-finish_time' THEN long_ ELSE 0 END) finishTime
        FROM (
        SELECT *
        FROM activiti.dbo.ACT_HI_VARINST va
        WHERE TASK_ID_ IS NOT NULL
        AND NOT EXISTS(
        SELECT 1
        FROM activiti.dbo.ACT_HI_VARINST t2
        WHERE t2.task_id_ = va.TASK_ID_
        AND t2.NAME_ = va.NAME_
        AND t2.id_ > va.ID_
        )
        ) t3
        GROUP BY task_id_,
        PROC_INST_ID_
        ) t4
        JOIN hv_eam_repair_data t5
        ON t4.PROC_INST_ID_ = t5.process_instance_id
        JOIN hv_em_fault t6 ON t5.fault_apply_id = t6.id
        JOIN hv_em_fault_class t7 ON t6.fault_class_id = t7.id
        JOIN hiper_base.dbo.hv_bm_equipment t8 ON t7.equipment_class_id = t8.equipment_type_id
        JOIN (
        SELECT t9.cell_id,
        t9.equipment_id,
        t10.NAME
        FROM hiper_base.dbo.hv_bm_equipment_cell t9
        JOIN hiper_base.dbo.hv_bm_location t10 ON t9.cell_id = t10.id
        WHERE t10.type = 40
        GROUP BY t9.cell_id,
        t9.equipment_id
        ) t11 ON t8.id = t11.equipment_id
        <where>
            t4.startTime <![CDATA[ > ]]> 0
            AND t4.finishTime <![CDATA[ > ]]> 0
            AND EXISTS ( SELECT 1
            FROM activiti.dbo.ACT_HI_PROCINST t12 WHERE t12.PROC_INST_ID_ = t4.PROC_INST_ID_
            <if test="dto.startTime != null and dto.endTime != null">
                AND t12.END_TIME_ BETWEEN #{dto.startTime,jdbcType=TIMESTAMP}
                AND #{dto.endTime,jdbcType=TIMESTAMP}
            </if>
            )
        </where>
        ORDER BY faultTime DESC
    </select>

    <resultMap id="repairUserJob" type="com.hvisions.eam.dto.report.RepairUserJobInfo">
    </resultMap>
    <select id="repairUserJobFromRepair" resultMap="repairUserJob"
            parameterType="com.hvisions.eam.dto.report.ReportRepairUserJobStatisticalQuery" databaseId="mysql">
        SELECT
        t12.userName userName,
        t12.type type,
        ROUND( sum( t12.time ), 2 ) sumRepairTime,
        COUNT(*) repairCount,
        ROUND( AVG( t12.time ), 2 ) avgRepairTime,
        0 sumMaintainTime,
        0 maintainCount,
        0 avgMaintainTime,
        ROUND( sum( t12.useAmount ), 2 ) sparePartUseAmount
        FROM
        (
        SELECT
        1 type,
        t5.apply_user_id AS userId,
        t5.process_instance_id AS processId,
        t6.user_name userName,
        ( t4.finishTime - t4.startTime )/ 1000 / 60 AS time,
        ( CASE WHEN t9.useAmount IS NULL THEN 0 ELSE t9.useAmount END ) AS useAmount
        FROM
        (
        SELECT
        task_id_,
        PROC_INST_ID_,
        sum( CASE WHEN NAME_ = 'h_visions_task_start_time_variable_name' THEN long_ ELSE 0 END ) startTime,
        sum( CASE WHEN NAME_ = 'h-visions-task-finish_time' THEN long_ ELSE 0 END ) finishTime
        FROM
        (
        SELECT
        *
        FROM
        activiti.ACT_HI_VARINST va
        WHERE
        TASK_ID_ IS NOT NULL
        AND NOT EXISTS (
        SELECT
        1
        FROM
        activiti.ACT_HI_VARINST t2
        WHERE
        t2.task_id_ = va.TASK_ID_
        AND t2.NAME_ = va.NAME_
        AND t2.id_ > va.ID_
        )
        ) t3
        GROUP BY
        task_id_,
        PROC_INST_ID_
        ) t4
        JOIN hv_eam_repair_data t5 ON t4.PROC_INST_ID_ = t5.process_instance_id
        JOIN framework.sys_user t6 ON t5.apply_user_id = t6.id
        LEFT JOIN (
        SELECT
        t7.process_instance_id,
        sum( t7.number * t8.unit_price ) useAmount
        FROM
        hv_eam_spare t8
        JOIN hv_eam_actual_use t7 ON t8.id = t7.spare_id
        GROUP BY
        t7.process_instance_id
        ) t9 ON t9.process_instance_id = t5.process_instance_id
        <where>
            t4.startTime <![CDATA[ > ]]> 0
            AND t4.finishTime <![CDATA[ > ]]> 0
            AND t5.apply_user_id IS NOT NULL
            AND EXISTS (
            SELECT
            1
            FROM
            `hiper_base`.hv_bm_crew t10
            JOIN `hiper_base`.hv_bm_crew_member t2 ON t10.id = t2.crew_id
            WHERE
            t2.user_id = t5.apply_user_id
            <if test="dto.crewId != null">
                AND t10.id = #{dto.crewId,jdbcType=INTEGER} -- 班组id
            </if>
            )

            <if test="dto.startTime != null and dto.endTime != null">
                AND EXISTS ( SELECT 1 FROM activiti.ACT_HI_PROCINST t11 WHERE t11.PROC_INST_ID_ = t4.PROC_INST_ID_
                AND t11.END_TIME_ BETWEEN #{dto.startTime,jdbcType=TIMESTAMP}
                AND #{dto.endTime,jdbcType=TIMESTAMP}
                )
            </if>
            <if test="dto.cellId !=null ">
                AND EXISTS ( SELECT *
                from `hiper_base`.hv_bm_equipment j1
                left join `hiper_base`.hv_bm_equipment_cell j2 on j1.in = j2.equipment_id
                left join `hiper_base`.hv_bm_location j3 on j2.cell_id = j3.id
                WHERE j1.id = t5.equipment_id
                AND j3.id = #{dto.cellId})
            </if>
        </where>
        ) t12
        GROUP BY
        t12.userId
    </select>

    <select id="repairUserJobFromRepair" resultMap="repairUserJob"
            parameterType="com.hvisions.eam.dto.report.ReportRepairUserJobStatisticalQuery" databaseId="sqlserver">
        SELECT
        t12.userName userName,
        t12.type type,
        ROUND( sum( t12.time ), 2 ) sumRepairTime,
        COUNT(*) repairCount,
        ROUND( AVG( t12.time ), 2 ) avgRepairTime,
        0 sumMaintainTime,
        0 maintainCount,
        0 avgMaintainTime,
        ROUND( sum( t12.useAmount ), 2 ) sparePartUseAmount
        FROM
        (
        SELECT
        1 type,
        t5.apply_user_id AS userId,
        t5.process_instance_id AS processId,
        t6.user_name userName,
        ( t4.finishTime - t4.startTime )/ 1000 / 60 AS time,
        ( CASE WHEN t9.useAmount IS NULL THEN 0 ELSE t9.useAmount END ) AS useAmount
        FROM
        (
        SELECT
        task_id_,
        PROC_INST_ID_,
        sum( CASE WHEN NAME_ = 'h_visions_task_start_time_variable_name' THEN long_ ELSE 0 END ) startTime,
        sum( CASE WHEN NAME_ = 'h-visions-task-finish_time' THEN long_ ELSE 0 END ) finishTime
        FROM
        (
        SELECT
        *
        FROM
        activiti.dbo.ACT_HI_VARINST va
        WHERE
        TASK_ID_ IS NOT NULL
        AND NOT EXISTS (
        SELECT
        1
        FROM
        activiti.dbo.ACT_HI_VARINST t2
        WHERE
        t2.task_id_ = va.TASK_ID_
        AND t2.NAME_ = va.NAME_
        AND t2.id_ > va.ID_
        )
        ) t3
        GROUP BY
        task_id_,
        PROC_INST_ID_
        ) t4
        JOIN hv_eam_repair_data t5 ON t4.PROC_INST_ID_ = t5.process_instance_id
        JOIN framework.dbo.sys_user t6 ON t5.apply_user_id = t6.id
        LEFT JOIN (
        SELECT
        t7.process_instance_id,
        sum( t7.number * t8.unit_price ) useAmount
        FROM
        hv_eam_spare t8
        JOIN hv_eam_actual_use t7 ON t8.id = t7.spare_id
        GROUP BY
        t7.process_instance_id
        ) t9 ON t9.process_instance_id = t5.process_instance_id
        <where>
            t4.startTime <![CDATA[ > ]]> 0
            AND t4.finishTime <![CDATA[ > ]]> 0
            AND t5.apply_user_id IS NOT NULL
            AND EXISTS (
            SELECT
            1
            FROM
            hiper_base.dbo.hv_bm_crew t10
            JOIN hiper_base.dbo.hv_bm_crew_member t2 ON t10.id = t2.crew_id
            WHERE
            t2.user_id = t5.apply_user_id
            <if test="dto.crewId != null">
                AND t10.id = #{dto.crewId,jdbcType=INTEGER} -- 班组id
            </if>
            )

            <if test="dto.startTime != null and dto.endTime != null">
                AND EXISTS ( SELECT 1 FROM activiti.dbo.ACT_HI_PROCINST t11 WHERE t11.PROC_INST_ID_ = t4.PROC_INST_ID_
                AND t11.END_TIME_ BETWEEN #{dto.startTime,jdbcType=TIMESTAMP}
                AND #{dto.endTime,jdbcType=TIMESTAMP}
                )
            </if>
            <if test="dto.cellId !=null ">
                AND EXISTS ( SELECT *
                from hiper_base.dbo.hv_bm_equipment j1
                left join hiper_base.dbo.hv_bm_equipment_cell j2 on j1.in = j2.equipment_id
                left join hiper_base.dbo.hv_bm_location j3 on j2.cell_id = j3.id
                WHERE j1.id = t5.equipment_id
                AND j3.id = #{dto.cellId})
            </if>
        </where>
        ) t12
        GROUP BY
        t12.userId
    </select>

    <select id="repairUserJobFromMaintain" resultMap="repairUserJob"
            parameterType="com.hvisions.eam.dto.report.ReportRepairUserJobStatisticalQuery" databaseId="mysql">
        SELECT
        t13.userName userName,
        t13.type type,
        0 sumRepairTime,
        0 repairCount,
        0 avgRepairTime,
        ROUND( sum( t13.time ), 2 ) sumMaintainTime,
        COUNT(*) maintainCount,
        ROUND( AVG( t13.time ), 2 ) avgMaintainTime,
        sum( t13.useAmount ) sparePartUseAmount
        FROM
        (
        SELECT
        2 type,
        t5.executor_id AS userId,
        t5.process_instance_id AS processId,
        t6.user_name userName,
        ( t4.finishTime - t4.startTime )/ 1000 / 60 AS time,
        ( CASE WHEN t9.useAmount IS NULL THEN 0 ELSE t9.useAmount END ) AS useAmount
        FROM
        (
        SELECT
        task_id_,
        PROC_INST_ID_,
        sum( CASE WHEN NAME_ = 'h_visions_task_start_time_variable_name' THEN long_ ELSE 0 END ) startTime,
        sum( CASE WHEN NAME_ = 'h-visions-task-finish_time' THEN long_ ELSE 0 END ) finishTime
        FROM
        (
        SELECT
        *
        FROM
        activiti.ACT_HI_VARINST va
        WHERE
        TASK_ID_ IS NOT NULL
        AND NOT EXISTS (
        SELECT
        1
        FROM
        activiti.ACT_HI_VARINST t2
        WHERE
        t2.task_id_ = va.TASK_ID_
        AND t2.NAME_ = va.NAME_
        AND t2.id_ > va.ID_
        )
        ) t3
        GROUP BY
        task_id_,
        PROC_INST_ID_
        ) t4
        JOIN hv_eam_maintain_process_data t5 ON t4.PROC_INST_ID_ = t5.process_instance_id
        JOIN framework.sys_user t6 ON t5.executor_id = t6.id
        LEFT JOIN (
        SELECT
        t7.process_instance_id,
        sum( CASE WHEN t7.number * t8.unit_price IS NULL THEN 0 ELSE t7.number * t8.unit_price END ) useAmount
        FROM
        hv_eam_spare t8
        JOIN hv_eam_actual_use t7 ON t8.id = t7.spare_id
        GROUP BY
        t7.process_instance_id
        ) t9 ON t9.process_instance_id = t5.process_instance_id
        <where>
            t4.startTime <![CDATA[ > ]]> 0
            AND t4.finishTime <![CDATA[ > ]]> 0
            AND t5.executor_id IS NOT NULL
            AND EXISTS (
            SELECT
            1
            FROM
            hiper_base.hv_bm_crew t10
            JOIN hiper_base.hv_bm_crew_member t11 ON t10.id = t11.crew_id
            WHERE
            t11.user_id = t5.executor_id
            <if test="dto.crewId != null ">
                AND t10.id = #{dto.crewId,jdbcType=INTEGER} -- 班组id
            </if>
            )

            <if test="dto.startTime != null and dto.endTime != null">
                AND EXISTS ( SELECT 1 FROM activiti.ACT_HI_PROCINST t12 WHERE t12.PROC_INST_ID_ = t4.PROC_INST_ID_
                AND t12.END_TIME_ BETWEEN #{dto.startTime,jdbcType=TIMESTAMP} AND #{dto.endTime,jdbcType=TIMESTAMP}
                )
            </if>
        </where>
        ) t13
        GROUP BY
        t13.userId
    </select>

    <select id="repairUserJobFromMaintain" resultMap="repairUserJob"
            parameterType="com.hvisions.eam.dto.report.ReportRepairUserJobStatisticalQuery" databaseId="sqlserver">
        SELECT
        t13.userName userName,
        t13.type type,
        0 sumRepairTime,
        0 repairCount,
        0 avgRepairTime,
        ROUND( sum( t13.time ), 2 ) sumMaintainTime,
        COUNT(*) maintainCount,
        ROUND( AVG( t13.time ), 2 ) avgMaintainTime,
        sum( t13.useAmount ) sparePartUseAmount
        FROM
        (
        SELECT
        2 type,
        t5.executor_id AS userId,
        t5.process_instance_id AS processId,
        t6.user_name userName,
        ( t4.finishTime - t4.startTime )/ 1000 / 60 AS time,
        ( CASE WHEN t9.useAmount IS NULL THEN 0 ELSE t9.useAmount END ) AS useAmount
        FROM
        (
        SELECT
        task_id_,
        PROC_INST_ID_,
        sum( CASE WHEN NAME_ = 'h_visions_task_start_time_variable_name' THEN long_ ELSE 0 END ) startTime,
        sum( CASE WHEN NAME_ = 'h-visions-task-finish_time' THEN long_ ELSE 0 END ) finishTime
        FROM
        (
        SELECT
        *
        FROM
        activiti.dbo.ACT_HI_VARINST va
        WHERE
        TASK_ID_ IS NOT NULL
        AND NOT EXISTS (
        SELECT
        1
        FROM
        activiti.dbo.ACT_HI_VARINST t2
        WHERE
        t2.task_id_ = va.TASK_ID_
        AND t2.NAME_ = va.NAME_
        AND t2.id_ > va.ID_
        )
        ) t3
        GROUP BY
        task_id_,
        PROC_INST_ID_
        ) t4
        JOIN hv_eam_maintain_process_data t5 ON t4.PROC_INST_ID_ = t5.process_instance_id
        JOIN framework.dbo.sys_user t6 ON t5.executor_id = t6.id
        LEFT JOIN (
        SELECT
        t7.process_instance_id,
        sum( CASE WHEN t7.number * t8.unit_price IS NULL THEN 0 ELSE t7.number * t8.unit_price END ) useAmount
        FROM
        hv_eam_spare t8
        JOIN hv_eam_actual_use t7 ON t8.id = t7.spare_id
        GROUP BY
        t7.process_instance_id
        ) t9 ON t9.process_instance_id = t5.process_instance_id
        <where>
            t4.startTime <![CDATA[ > ]]> 0
            AND t4.finishTime <![CDATA[ > ]]> 0
            AND t5.executor_id IS NOT NULL
            AND EXISTS (
            SELECT
            1
            FROM
            hiper_base.dbo.hv_bm_crew t10
            JOIN hiper_base.dbo.hv_bm_crew_member t11 ON t10.id = t11.crew_id
            WHERE
            t11.user_id = t5.executor_id
            <if test="dto.crewId != null ">
                AND t10.id = #{dto.crewId,jdbcType=INTEGER} -- 班组id
            </if>
            )

            <if test="dto.startTime != null and dto.endTime != null">
                AND EXISTS ( SELECT 1 FROM activiti.dbo.ACT_HI_PROCINST t12 WHERE t12.PROC_INST_ID_ = t4.PROC_INST_ID_
                AND t12.END_TIME_ BETWEEN #{dto.startTime,jdbcType=TIMESTAMP} AND #{dto.endTime,jdbcType=TIMESTAMP}
                )
            </if>
        </where>
        ) t13
        GROUP BY
        t13.userId
    </select>

    <resultMap id="report" type="com.hvisions.eam.dto.report.MaintainReportDTO"></resultMap>

    <select id="getReportByQuery" resultMap="report" parameterType="com.hvisions.eam.dto.report.ReportQuery" databaseId="mysql">
        select t1.* from hv_eam_maintain_report t1
        left join hiper_base.hv_bm_location t2 on t1.cell_code = t2.code and t2.type =40
        left join hiper_base.hv_bm_location t3 on t2.parent_id = t3.id and t3.type =30
        <where>
            <if test="dto.equipmentCode !=null">
                and t1.equipment_code like concat ('%',#{dto.equipmentCode},'%')
            </if>
            <if test="dto.equipmentName !=null">
                and t1.equipment_name like concat ('%',#{dto.equipmentName},'%')
            </if>
            <if test="dto.cellCode !=null">
                and t1.cell_code = #{dto.cellCode}
            </if>
            <if test="dto.operationCode !=null">
                and t1.operation_code like concat ('%',#{dto.operationCode},'%')
            </if>
            <if test="dto.dateStart !=null and dto.dateEnd !=null">
                and t1.order_time between #{dto.dateStart} and #{dto.dateEnd}
            </if>
            <if test="dto.areaCode !=null ">
                and t3.code = #{dto.areaCode}
            </if>
        </where>
    </select>
    <select id="getReportByQuery" resultMap="report" parameterType="com.hvisions.eam.dto.report.ReportQuery" databaseId="sqlserver">
        select t1.* from hv_eam_maintain_report t1
        left join hiper_base.dbo.hv_bm_location t2 on t1.cell_code = t2.code and t2.type =40
        left join hiper_base.dbo.hv_bm_location t3 on t2.parent_id = t3.id and t3.type =30
        <where>
            <if test="dto.equipmentCode !=null">
                and t1.equipment_code like concat ('%',#{dto.equipmentCode},'%')
            </if>
            <if test="dto.equipmentName !=null">
                and t1.equipment_name like concat ('%',#{dto.equipmentName},'%')
            </if>
            <if test="dto.cellCode !=null">
                and t1.cell_code = #{dto.cellCode}
            </if>
            <if test="dto.operationCode !=null">
                and t1.operation_code like concat ('%',#{dto.operationCode},'%')
            </if>
            <if test="dto.dateStart !=null and dto.dateEnd !=null">
                and t1.order_time between #{dto.dateStart} and #{dto.dateEnd}
            </if>
            <if test="dto.areaCode !=null ">
                and t3.code = #{dto.areaCode}
            </if>
        </where>
    </select>
</mapper>