<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.eam.dao.SpareBrandMapper">


    <resultMap id="brand" type="com.hvisions.eam.dto.spare.SpareBrandDTO">

    </resultMap>


    <select id="getBrandByQuery" resultMap="brand" parameterType="com.hvisions.eam.dto.spare.SpareBrandQuery">
        select t1.* from hv_eam_spare_brand t1
        <where>
            <if test="dto.spareBrandCode != null and dto.spareBrandCode != &quot;&quot;">
                and t1.spare_brand_code like concat('%',#{dto.spareBrandCode},'%')
            </if>
            <if test="dto.spareBrandName != null and dto.spareBrandName != &quot;&quot;">
                and t1.spare_brand_name like concat('%',#{dto.spareBrandName},'%')
            </if>
        </where>
    </select>
</mapper>