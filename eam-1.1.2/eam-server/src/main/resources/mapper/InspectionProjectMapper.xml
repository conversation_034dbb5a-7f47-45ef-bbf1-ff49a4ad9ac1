<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.eam.dao.InspectionProjectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hvisions.eam.entity.autonomy.HvAmInspectionProject">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator_id" property="creatorId" />
        <result column="updater_id" property="updaterId" />
        <result column="site_num" property="siteNum" />
        <result column="number" property="number" />
        <result column="name" property="name" />
        <result column="test_content" property="testContent" />
        <result column="test_cycle" property="testCycle" />
        <result column="test_method" property="testMethod" />
        <result column="enable_flag" property="enableFlag" />
        <result column="abnormal_times" property="abnormalTimes" />
        <result column="group_id" property="groupId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, update_time, creator_id, updater_id, site_num, number, name, test_content, test_cycle, test_method, enable_flag, abnormal_times, group_id, delete_flag
    </sql>
    <select id="getTestCycle" resultType="java.lang.String">
        select distinct test_cycle from hv_am_inspection_project
    </select>

    <select id="getInspectionProjectS" resultType="com.hvisions.eam.dto.autonomy.InspectionProjectDTO">
        select * from hv_am_inspection_project
        <where>
            <if test="dto.number != null and dto.number != '' ">
                and number = #{dto.number}
            </if>
            <if test="dto.name != null and dto.name != '' ">
                and name like concat('%',#{dto.name},'%')
            </if>
            <if test="dto.testCycle != null and dto.testCycle != '' ">
                and test_cycle = #{dto.testCycle}
            </if>
            <if test="dto.groupIds.size() > 0">
                and group_id in
                <foreach  collection="dto.groupIds" item="id" open="("  separator=","  close=")"> #{id} </foreach>
            </if>
        </where>
    </select>

</mapper>
