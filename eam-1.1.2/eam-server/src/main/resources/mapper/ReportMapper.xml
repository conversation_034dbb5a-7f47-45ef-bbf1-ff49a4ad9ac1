<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.eam.dao.ReportMapper">
    <select id="getSummary" resultType="com.hvisions.eam.dto.maintain.TaskSummaryDTO" databaseId="mysql">
        SELECT t2.key_                                                AS definitionKey,
               sum(CASE WHEN t1.end_time_ IS NULL THEN 0 ELSE 1 END) AS finishedCount,
               count(*)                                                AS totalCount
        FROM activiti.ACT_HI_PROCINST t1
                 LEFT JOIN activiti.ACT_RE_PROCDEF t2 ON t1.proc_def_id_ = t2.id_
        WHERE t1.START_TIME_ > #{start}
        GROUP BY t2.key_
    </select>
    <select id="getSummary" resultType="com.hvisions.eam.dto.maintain.TaskSummaryDTO" databaseId="sqlserver">
        SELECT t2.key_                                                AS definitionKey,
               sum(CASE WHEN t1.end_time_ IS NULL THEN 0 ELSE 1 END) AS finishedCount,
               count(*)                                                AS totalCount
        FROM activiti.dbo.ACT_HI_PROCINST t1
                 LEFT JOIN activiti.dbo.ACT_RE_PROCDEF t2 ON t1.proc_def_id_ = t2.id_
        WHERE t1.START_TIME_ > #{start}
        GROUP BY t2.key_
    </select>
    <select id="getMaintainPlanCount" resultType="java.lang.Integer">
        select count(*) from  hv_eam_maintain_plan
    </select>
    <select id="getInspectionPlanCount" resultType="java.lang.Integer" databaseId="mysql">
        select count(*) from hv_eam_inspect_plan
    </select>
    <select id="getInspectionPlanCount" resultType="java.lang.Integer" databaseId="sqlserver">
        select count(*) from hv_eam_inspect_plan
    </select>
    <select id="getAutoPlanCount" resultType="java.lang.Integer" databaseId="mysql">
        select count(*) from hv_am_inspection_plan
    </select>
    <select id="getAutoPlanCount" resultType="java.lang.Integer" databaseId="sqlserver">
        select count(*) from hv_am_inspection_plan
    </select>
    <select id="getFaultKnowledge" resultType="java.lang.Integer" databaseId="mysql">
        select count(*) from hv_em_fault_solution
    </select>
    <select id="getFaultKnowledge" resultType="java.lang.Integer" databaseId="sqlserver">
        select count(*) from hv_em_fault_solution
    </select>
</mapper>