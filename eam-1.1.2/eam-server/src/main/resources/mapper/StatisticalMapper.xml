<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.eam.dao.StatisticalMapper">
    <select id="getdataList" resultType="com.hvisions.eam.dto.autonomy.MaintenanceProcessDataDTO">
        select *
        from hv_am_autonomy_maintenance_process_data
        <where>
            <if test="dto.startTime != null and dto.endTime != null and dto.startTime != '' and dto.endTime != ''">
                and end_time &lt;= #{dto.endTime}
                and end_time >= #{dto.startTime}
            </if>
            <if test="dto.hasError != null and dto.hasError != ''">
                and has_error = #{dto.hasError}
            </if>
        </where>
    </select>


    <select id="getItemList" resultType="com.hvisions.eam.dto.autonomy.MaintenanceProcessItemDTO">
        select number
        from hv_am_autonomy_maintenance_process_item
        <where>
            <if test="dto.ids.size() > 0">
                and process_data_id in
                <foreach collection="dto.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            group by number
        </where>
    </select>
    <select id="fuzzyQueryItem"
            resultType="com.hvisions.eam.entity.autonomy.HvAmAutonomyMaintenanceProcessItem" databaseId="mysql">
        select distinct name, number
        from hv_am_autonomy_maintenance_process_item ${ew.customSqlSegment}
        limit 0,10
    </select>

    <select id="fuzzyQueryItem"
            resultType="com.hvisions.eam.entity.autonomy.HvAmAutonomyMaintenanceProcessItem"
            databaseId="sqlserver">
        SELECT TOP 10 *
        FROM (SELECT DISTINCT name, number FROM dbo.hv_am_autonomy_maintenance_process_item) tmp
    </select>
</mapper>
