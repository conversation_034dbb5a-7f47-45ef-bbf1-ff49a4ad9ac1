<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.eam.dao.UtilsMapper">
    <select id="getUserId" resultType="java.lang.Integer">
        select t2.user_id
        from hiper_base.hv_bm_equipment_extend t1
                     join framework.sys_user_login t2 on t1.hv_response = t2.user_account
        where t1.equipment_id = #{equipmentId}
    </select>
</mapper>