<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.eam.dao.inspect.InspectReportMapper">
    <resultMap id="rm1" type="com.hvisions.eam.dto.inspect.report.ProcessInfoDTO"/>
    <resultMap id="rm2" type="com.hvisions.eam.dto.inspect.report.ItemFinishFail"/>
    <select id="getInspectDayInfo" resultMap="rm1" parameterType="java.time.LocalDate" databaseId="mysql">
        select t1.id_ processInstanceId, t1.START_TIME_ createTime, t1.END_TIME_ finishTime, t2.deadline period
        from activiti.ACT_HI_PROCINST t1
                 left join hv_eam_inspect_plan t2 on t1.business_key_ = t2.id
        where t1.PROC_DEF_ID_ like 'equipment-inspect%'
          and t1.START_TIME_ > #{query}
    </select>
    <select id="getInspectDayInfo" resultMap="rm1" parameterType="java.time.LocalDate" databaseId="sqlserver">
        select t1.id_ processInstanceId, t1.START_TIME_ createTime, t1.END_TIME_ finishTime, t2.deadline period
        from activiti.dbo.ACT_HI_PROCINST t1
                 left join hv_eam_inspect_plan t2 on t1.business_key_ = t2.id
        where t1.PROC_DEF_ID_ like 'equipment-inspect%'
          and t1.START_TIME_ > #{query}
    </select>
    <select id="getPassRate" resultMap="rm2" parameterType="java.time.LocalDate">

        select t1.end_time finishTime, case when t3.done is null then 1 else t3.done end fail
        from hv_eam_inspect_process_data t1
                 join hv_eam_inspect_process_plan_content t2 on t1.process_instance_id = t2.process_instance_id
                 join hv_eam_inspect_process_content_item t3 on t2.id = t3.content_id
        where t1.start_time > #{query}
    </select>

    <select id="getTop10Items" resultType="com.hvisions.eam.dto.inspect.report.ItemInfo"
            parameterType="java.time.LocalDate">
        select t4.name itemName, t4.count happenTime
        from (select max(t3.inspect_item_name) name, sum(1) count
              from hv_eam_inspect_process_data t1
                       join hv_eam_inspect_process_plan_content t2 on t1.process_instance_id = t2.process_instance_id
                       join hv_eam_inspect_process_content_item t3 on t2.id = t3.content_id
              where t3.done = 2
                and t1.start_time > #{query}
              group by t3.item_id) t4
        order by count desc
        limit 10
    </select>
    <select id="getTop10Worker" resultType="com.hvisions.eam.dto.inspect.report.PersonInfo"
            parameterType="java.time.LocalDate" databaseId="mysql">

        select t11.user_name personName, t10.count completeCount
        from (select t1.executor_id id, sum(1) count
              from hv_eam_inspect_process_data t1
                       join hv_eam_inspect_process_plan_content t2 on t1.process_instance_id = t2.process_instance_id
                       join hv_eam_inspect_process_content_item t3 on t2.id = t3.content_id
              where t1.start_time > #{query}
              group by t1.executor_id) t10
                 join framework.sys_user t11 on t10.id = t11.id
        order by count desc
        limit 10

    </select>
    <select id="getTop10Worker" resultType="com.hvisions.eam.dto.inspect.report.PersonInfo"
            parameterType="java.time.LocalDate" databaseId="sqlserver">

        select t11.user_name personName, t10.count completeCount
        from (select t1.executor_id id, sum(1) count
              from hv_eam_inspect_process_data t1
                       join hv_eam_inspect_process_plan_content t2 on t1.process_instance_id = t2.process_instance_id
                       join hv_eam_inspect_process_content_item t3 on t2.id = t3.content_id
              where t1.start_time > #{query}
              group by t1.executor_id) t10
                 join framework.dbo.sys_user t11 on t10.id = t11.id
        order by count desc
        limit 10

    </select>
    <select id="getProcessInstanceIds" resultType="java.lang.String">
        select process_instance_id from hv_eam_inspect_statistical where equipment_id = #{equipmentId}
    </select>
</mapper>