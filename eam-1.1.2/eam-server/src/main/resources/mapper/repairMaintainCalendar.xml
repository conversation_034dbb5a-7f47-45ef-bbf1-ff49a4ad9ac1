<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.eam.dao.CalendarMapper">

    <resultMap id="taskDto" type="com.hvisions.eam.dto.maintain.HiTaskDTO"></resultMap>
    <select id="getHiTask" resultMap="taskDto" databaseId="mysql">
        SELECT t1.ID_ as taskId,
        t1.DESCRIPTION_ as taskDesc,
        t1.START_TIME_ as taskStartTime,
        t1.END_TIME_ as taskEndTime,
        t2.KEY_ as defKey
        FROM
        activiti.ACT_HI_TASKINST t1
        LEFT JOIN activiti.ACT_RE_PROCDEF t2 on t1.PROC_DEF_ID_ = t2.ID_
        <where>
            t1.START_TIME_ BETWEEN #{after} AND #{before}
            and t2.KEY_ = #{key}
        </where>
    </select>
    <select id="getHiTask" resultMap="taskDto" databaseId="sqlserver">
        SELECT t1.ID_ as taskId,
        t1.DESCRIPTION_ as taskDesc,
        t1.START_TIME_ as taskStartTime,
        t1.END_TIME_ as taskEndTime,
        t2.KEY_ as defKey
        FROM
        activiti.dbo.ACT_HI_TASKINST t1
        LEFT JOIN activiti.dbo.ACT_RE_PROCDEF t2 on t1.PROC_DEF_ID_ = t2.ID_
        <where>
            t1.START_TIME_ BETWEEN #{after} AND #{before}
            and t2.KEY_ = #{key}
        </where>
    </select>

    <select id="getTask" resultMap="taskDto" databaseId="mysql">
        SELECT t1.ID_ as taskId,
        t1.DESCRIPTION_ as taskDesc,
        t1.CREATE_TIME_ as taskStartTime,
        t2.KEY_ as defKey,
        t3.TEXT_ as endTime,
        t4.TEXT_ as state
        FROM
        activiti.ACT_RU_TASK t1
        LEFT JOIN activiti.ACT_RE_PROCDEF t2 on t1.PROC_DEF_ID_ = t2.ID_
        LEFT join activiti.ACT_RU_VARIABLE t3 on t1.PROC_INST_ID_ = t3.PROC_INST_ID_ and t3.NAME_ ='taskEndTime'
        LEFT join activiti.ACT_RU_VARIABLE t4 on t1.PROC_INST_ID_ = t4.PROC_INST_ID_ and t4.NAME_ ='hvisionsTaskState'
        <where>
            t1.CREATE_TIME_ BETWEEN #{after} AND #{before}
            and t2.KEY_ = #{key}
        </where>
    </select>
    <select id="getTask" resultMap="taskDto" databaseId="sqlserver">
        SELECT t1.ID_ as taskId,
        t1.DESCRIPTION_ as taskDesc,
        t1.CREATE_TIME_ as taskStartTime,
        t2.KEY_ as defKey,
        t3.TEXT_ as endTime,
        t4.TEXT_ as state
        FROM
        activiti.dbo.ACT_RU_TASK t1
        LEFT JOIN activiti.dbo.ACT_RE_PROCDEF t2 on t1.PROC_DEF_ID_ = t2.ID_
        LEFT join activiti.dbo.ACT_RU_VARIABLE t3 on t1.PROC_INST_ID_ = t3.PROC_INST_ID_ and t3.NAME_ ='taskEndTime'
        LEFT join activiti.dbo.ACT_RU_VARIABLE t4 on t1.PROC_INST_ID_ = t4.PROC_INST_ID_ and t4.NAME_ ='hvisionsTaskState'
        <where>
            t1.CREATE_TIME_ BETWEEN #{after} AND #{before}
            and t2.KEY_ = #{key}
        </where>
    </select>

    <select id="getTimeIdByPlan" resultType="java.lang.Integer" databaseId="mysql">
        SELECT t1.timer_id
        FROM hv_eam_maintain_plan t1
                 JOIN hiper_tools.hv_bm_timer t2 ON t1.timer_id = t2.id
        WHERE t2.active = true
    </select>

    <select id="getTimeIdByPlan" resultType="java.lang.Integer" databaseId="sqlserver">
        SELECT t1.timer_id
        FROM hv_eam_maintain_plan t1
                 JOIN hiper_tools.dbo.hv_bm_timer t2 ON t1.timer_id = t2.id
        WHERE t2.active = true
    </select>

    <resultMap id="planEquipment" type="com.hvisions.eam.dto.maintain.process.PlanEquipmentDTO"></resultMap>

    <select id="getPlanEquipment" resultMap="planEquipment" databaseId="mysql">
        SELECT
        t1.maintain_plan_id as planId,
        t1.equipment_id as equipmentId,
        t2.equipment_name as equipmentName
        FROM hv_eam_maintain_plan_equipment t1
        LEFT JOIN hiper_base.hv_bm_equipment t2 on t1.equipment_id = t2.id
        <where>
            t1.maintain_plan_id in
            <foreach item="item" index="index" collection="ids"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>
    <select id="getPlanEquipment" resultMap="planEquipment" databaseId="sqlserver">
        SELECT
        t1.maintain_plan_id as planId,
        t1.equipment_id as equipmentId,
        t2.equipment_name as equipmentName
        FROM hv_eam_maintain_plan_equipment t1
        LEFT JOIN hiper_base.dbo.hv_bm_equipment t2 on t1.equipment_id = t2.id
        <where>
            t1.maintain_plan_id in
            <foreach item="item" index="index" collection="ids"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>

</mapper>