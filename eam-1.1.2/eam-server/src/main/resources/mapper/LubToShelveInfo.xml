<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<!-- dao层 -->
<mapper namespace="com.hvisions.eam.dao.LubToShelveMapper">
    <!--对象-->
    <resultMap id="ff" type="com.hvisions.eam.dto.lub.LubToShelveDTO">
        <result column="shelve_id" jdbcType="INTEGER" property="shelveId"/>
        <result column="lub_id" jdbcType="INTEGER" property="lubId"/>
        <result column="number" jdbcType="DECIMAL" property="number"/>
        <result column="batch_number" jdbcType="VARCHAR" property="batchNumber"/>
        <result column="img" jdbcType="INTEGER" property="img"/>
        <result column="type_name" jdbcType="VARCHAR" property="typeName"/>
        <result column="shelve_name" jdbcType="VARCHAR" property="shelveName"/>
        <result column="type_code" jdbcType="VARCHAR" property="typeCode"/>
        <result column="lub_name" jdbcType="VARCHAR" property="lubName"/>
        <result column="lub_code" jdbcType="VARCHAR" property="lubCode"/>
    </resultMap>

    <select id="getLubToShelve" resultMap="ff" >
        SELECT
        ltos.id,
        ltos.shelve_id shelve_id,
        ltos.lub_id,
        ltos.batch_number,
        lub.img,
        lub.lub_code,
        ltos.number,
        lub.lub_name,
        shelve.shelve_name,
        type.type_code,
        type.type_name
        FROM
        hv_eam_lub_to_shelve ltos
        LEFT JOIN hv_eam_lubricating lub ON ltos.lub_id = lub.id
        JOIN hv_eam_shelve shelve ON ltos.shelve_id = shelve.id
        JOIN hv_eam_lub_type type ON lub.lub_type_id = type.id
        <where>
            <if test="shelveName != null and shelveName != ''">
                AND shelve_name LIKE '%${shelveName}%'
            </if>
            <if test="typeCode != null and typeCode != ''">
                AND type_code = #{typeCode}
            </if>
            <if test="batchNumber != null and batchNumber != ''">
                AND batch_number LIKE '%${batchNumber}%'
            </if>
            <if test="lubName != null and lubName != ''">
                AND lub_name LIKE '%${lubName}%'
            </if>
            <if test="lubCode != null and lubCode != ''">
                AND lub_code LIKE '%${lubCode}%'
            </if>
            <if test="shelveId != null and shelveId != ''">
                AND shelve_id = #{shelveId}
            </if>
            <if test="showNumberZero != true">
                AND ltos.number > 0
            </if>
        </where>
    </select>

</mapper>