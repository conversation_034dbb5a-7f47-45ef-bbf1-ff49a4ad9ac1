<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.eam.dao.inspect.InspectItemMapper">


    <select id="getInspectItemByQuery" resultType="com.hvisions.eam.dto.inspect.item.InspectItemDTO"
            parameterType="com.hvisions.eam.dto.inspect.item.InspectItemQueryDTO">
        select *
        from
        hv_eam_inspect_item t1
        <where>
            <if test="query.keyword != null and query.keyword != ''">
                and (
                t1.inspect_item_code like concat('%',#{query.keyword},'%') or
                t1.inspect_item_code like concat('%',#{query.keyword},'%')
                )
            </if>
            <if test="query.inspectItemCode != null and query.inspectItemCode != ''">
                and t1.inspect_item_code like concat('%',#{query.inspectItemCode},'%')
            </if>
            <if test="query.inspectItemName != null and query.inspectItemName != ''">
                and t1.inspect_item_name like concat('%',#{query.inspectItemName},'%')
            </if>
            <if test="query.inspectWork != null and query.inspectWork != ''">
                and t1.inspect_work like concat('%',#{query.inspectWork},'%')
            </if>
            <if test="query.startUsing != null">
                and t1.start_using = #{query.startUsing}
            </if>
            <if test="query.cycle != null and query.cycle != ''">
                and t1.cycle = #{query.cycle}
            </if>
            <if test="query.equipmentId != null">
                and (exists ( select 1 from hv_eam_inspect_item_equipment t2 where t2.item_id = t1.id and
                t2.equipment_id = #{query.equipmentId}
                )
                <if test="query.equipmentTypeIds != null and query.equipmentTypeIds.size > 0">
                    or exists (select 1 from hv_eam_inspect_item_equipment_type t3 where t3.item_id = t1.id and
                    t3.equipment_type_id in
                    <foreach collection="query.equipmentTypeIds" item="typeId" index="index" open="(" close=")"
                             separator=",">
                        #{typeId}
                    </foreach>
                    )
                </if>
                )
            </if>
        </where>
    </select>
</mapper>