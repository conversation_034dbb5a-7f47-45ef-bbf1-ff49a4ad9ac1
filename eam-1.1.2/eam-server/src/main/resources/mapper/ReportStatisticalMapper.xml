<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.eam.dao.inspect.InspectReportStatisticalMapper">
    <resultMap id="tableReslut" type="com.hvisions.eam.dto.inspect.table.InspectProcessResultDTO">
    </resultMap>

    <resultMap id="itemStatisticalDTO" type="com.hvisions.eam.dto.inspect.table.ItemStatisticalDTO">
    </resultMap>

    <resultMap id="taskStatisticalDTO" type="com.hvisions.eam.dto.inspect.table.TaskStatisticalDTO">
    </resultMap>

    <resultMap id="inspectTaskStaDTO" type="com.hvisions.eam.dto.inspect.table.InspectTaskStaDTO">
    </resultMap>

    <select id="queryForInspectProcessTable" resultMap="tableReslut"
            parameterType="com.hvisions.eam.dto.inspect.table.InspectProcessQueryDTO" databaseId="mysql">
        SELECT
        pd.start_time AS startDate ,
        pc.inspect_content_name AS inspectContentName,
        u.user_name AS checker,
        pci.inspect_item_name AS taskName,
        ep.inspect_plan_name AS inspectPlanName,
        pd.task_end_time AS executeDate,
        pci.shut_down AS shutDown,
        CASE WHEN f.id IS NOT NULL THEN 1 ELSE 0
        END AS withPic,
        f.file_id AS picId
        FROM
        hv_eam_inspect_process_data pd
        INNER JOIN hv_eam_inspect_process_plan_content pc ON pc.process_instance_id = pd.process_instance_id
        INNER JOIN hv_eam_inspect_process_content_item pci ON pci.content_id = pc.id
        LEFT JOIN hv_eam_inspect_item_file f ON f.inspect_item_id = pci.item_id
        LEFT JOIN framework.sys_user u ON u.id = pd.checker_id
        LEFT JOIN hv_eam_inspect_plan ep ON ep.id = pc.inspect_plan_id
        <where>
            <if test="dto.startDate != null and dto.endDate != null">
                AND pd.start_time between #{dto.startDate} AND #{dto.endDate}
            </if>
            <if test="dto.withPic != null ">
                AND ( #{dto.withPic} = 1 OR f.file_id is null ) AND ( f.file_id is not null OR #{dto.withPic} = 0)
            </if>
            <if test="dto.shutDown != null ">
                and pci.shut_down = #{dto.shutDown}
            </if>
            <if test="dto.inspectContentName != null and dto.inspectContentName !=''">
                and pci.inspect_item_name like concat('%',#{dto.inspectContentName},'%')
            </if>
        </where>
        order by pd.start_time desc
    </select>
    <select id="queryForInspectProcessTable" resultMap="tableReslut"
            parameterType="com.hvisions.eam.dto.inspect.table.InspectProcessQueryDTO" databaseId="sqlserver">
        SELECT
        pd.start_time AS startDate ,
        pc.inspect_content_name AS inspectContentName,
        u.user_name AS checker,
        pci.inspect_item_name AS taskName,
        ep.inspect_plan_name AS inspectPlanName,
        pd.task_end_time AS executeDate,
        pci.shut_down AS shutDown,
        CASE WHEN f.id IS NOT NULL THEN 1 ELSE 0
        END AS withPic,
        f.file_id AS picId
        FROM
        hv_eam_inspect_process_data pd
        INNER JOIN hv_eam_inspect_process_plan_content pc ON pc.process_instance_id = pd.process_instance_id
        INNER JOIN hv_eam_inspect_process_content_item pci ON pci.content_id = pc.id
        LEFT JOIN hv_eam_inspect_item_file f ON f.inspect_item_id = pci.item_id
        LEFT JOIN framework.dbo.sys_user u ON u.id = pd.checker_id
        LEFT JOIN hv_eam_inspect_plan ep ON ep.id = pc.inspect_plan_id
        <where>
            <if test="dto.startDate != null and dto.endDate != null">
                AND pd.start_time between #{dto.startDate} AND #{dto.endDate}
            </if>
            <if test="dto.withPic != null ">
                AND ( #{dto.withPic} = 1 OR f.file_id is null ) AND ( f.file_id is not null OR #{dto.withPic} = 0)
            </if>
            <if test="dto.shutDown != null ">
                and pci.shut_down = #{dto.shutDown}
            </if>
            <if test="dto.inspectContentName != null and dto.inspectContentName !=''">
                and pci.inspect_item_name like concat('%',#{dto.inspectContentName},'%')
            </if>
        </where>
        order by pd.start_time desc
    </select>

    <resultMap id="inspectTaskResultDTO" type="com.hvisions.eam.dto.inspect.table.InspectTaskResultDTO">
    </resultMap>
    <select id="queryForInspectTaskTable" resultMap="inspectTaskResultDTO"
            parameterType="com.hvisions.eam.dto.inspect.table.InspectTaskQueryDTO" databaseId="mysql">
        SELECT
        pd.start_time AS startDate ,
        pd.task_name AS taskName ,
        pd.task_num AS taskCode ,
        '一般' AS priority,
        u.user_name AS checker,
        pci.man_hour AS manHour,
        CASE pd.task_end_time
        WHEN not NULL THEN
        ( pd.task_end_time - pd.start_time )/ 1000 / 60
        ELSE
        ( now() - pd.start_time )/ 1000 / 60
        END as duration ,
        pd.task_end_time AS executeDate,
        pc.equipment_name AS equipmentName,
        pci.inspect_work AS inspectWork,
        pd.description AS description
        FROM
        hv_eam_inspect_process_data pd
        INNER JOIN hv_eam_inspect_process_plan_content pc ON pc.process_instance_id = pd.process_instance_id
        INNER JOIN hv_eam_inspect_process_content_item pci ON pci.content_id = pc.id
        LEFT JOIN hv_eam_inspect_item_file f ON f.inspect_item_id = pci.item_id
        LEFT JOIN framework.sys_user u ON u.id = pd.checker_id
        LEFT JOIN hv_eam_inspect_plan ep ON ep.id = pc.inspect_plan_id
        <where>
            <if test="query.startDate != null and dto.endDate != null">
                AND pd.start_time between #{query.startDate} AND #{query.endDate}
            </if>
            <if test="query.task_name != null and query.task_name !=''">
                and pd.task_name like concat('%',#{query.task_name},'%')
            </if>
            <if test="query.checkerId != null ">
                and pd.checker_id = #{query.checkerId}
            </if>
        </where>
        order by pd.start_time desc
    </select>
    <select id="queryForInspectTaskTable" resultMap="inspectTaskResultDTO"
            parameterType="com.hvisions.eam.dto.inspect.table.InspectTaskQueryDTO" databaseId="sqlserver">
        SELECT
        pd.start_time AS startDate ,
        pd.task_name AS taskName ,
        pd.task_num AS taskCode ,
        '一般' AS priority,
        u.user_name AS checker,
        pci.man_hour AS manHour,
        CASE pd.task_end_time
        WHEN not NULL THEN
        ( pd.task_end_time - pd.start_time )/ 1000 / 60
        ELSE
        ( now() - pd.start_time )/ 1000 / 60
        END as duration ,
        pd.task_end_time AS executeDate,
        pc.equipment_name AS equipmentName,
        pci.inspect_work AS inspectWork,
        pd.description AS description
        FROM
        hv_eam_inspect_process_data pd
        INNER JOIN hv_eam_inspect_process_plan_content pc ON pc.process_instance_id = pd.process_instance_id
        INNER JOIN hv_eam_inspect_process_content_item pci ON pci.content_id = pc.id
        LEFT JOIN hv_eam_inspect_item_file f ON f.inspect_item_id = pci.item_id
        LEFT JOIN framework.dbo.sys_user u ON u.id = pd.checker_id
        LEFT JOIN hv_eam_inspect_plan ep ON ep.id = pc.inspect_plan_id
        <where>
            <if test="query.startDate != null and dto.endDate != null">
                AND pd.start_time between #{query.startDate} AND #{query.endDate}
            </if>
            <if test="query.task_name != null and query.task_name !=''">
                and pd.task_name like concat('%',#{query.task_name},'%')
            </if>
            <if test="query.checkerId != null ">
                and pd.checker_id = #{query.checkerId}
            </if>
        </where>
        order by pd.start_time desc
    </select>

    <select id="countForAllItem" resultType="integer"
            parameterType="com.hvisions.eam.dto.inspect.table.MonthQueryDTO">
        SELECT COUNT(i.id) FROM hv_eam_inspect_process_content_item i
        <where>
            YEAR ( i.create_time ) = #{monthQueryDTO.yearTime} AND MONTH ( i.create_time ) = #{monthQueryDTO.monthTime}
        </where>
    </select>

    <select id="countForQualifiedItem" resultType="integer"
            parameterType="com.hvisions.eam.dto.inspect.table.MonthQueryDTO">
        SELECT COUNT(i.id) FROM hv_eam_inspect_process_content_item i
        <where>
            YEAR ( i.create_time ) = #{monthQueryDTO.yearTime} AND MONTH ( i.create_time ) = #{monthQueryDTO.monthTime}
            AND i.shut_down = 0
        </where>
    </select>

    <select id="countForUnqualifiedItem" resultType="integer"
            parameterType="com.hvisions.eam.dto.inspect.table.MonthQueryDTO">
        SELECT COUNT(i.id) FROM hv_eam_inspect_process_content_item i
        <where>
            YEAR ( i.create_time ) = #{monthQueryDTO.yearTime} AND MONTH ( i.create_time ) = #{monthQueryDTO.monthTime}
            AND i.shut_down = 1
        </where>
    </select>


    <select id="countForDailyItem" resultMap="itemStatisticalDTO"
            parameterType="com.hvisions.eam.dto.inspect.table.MonthQueryDTO" databaseId="mysql">
        SELECT COUNT(i.id) as num,
        DATE_FORMAT(i.create_time,'%Y-%m-%d') as startDate
        FROM hv_eam_inspect_process_content_item i
        <where>
            YEAR ( i.create_time ) = #{monthQueryDTO.yearTime} AND MONTH ( i.create_time ) = #{monthQueryDTO.monthTime}
        </where>
        GROUP BY DATE_FORMAT(i.create_time,'%Y-%m-%d')
    </select>
    <select id="countForDailyItem" resultMap="itemStatisticalDTO"
            parameterType="com.hvisions.eam.dto.inspect.table.MonthQueryDTO" databaseId="sqlserver">
        SELECT COUNT(i.id) as num,
        CONVERT(nvarchar(10),i.create_time,112) as startDate
        FROM hv_eam_inspect_process_content_item i
        <where>
            YEAR ( i.create_time ) = #{monthQueryDTO.yearTime} AND MONTH ( i.create_time ) = #{monthQueryDTO.monthTime}
        </where>
        GROUP BY CONVERT(nvarchar(10),i.create_time,112)
    </select>

    <select id="countForUnqualifiedItemGroupByEquipment" resultMap="itemStatisticalDTO"
            parameterType="com.hvisions.eam.dto.inspect.table.MonthQueryDTO">
        SELECT COUNT(i.id) as num,
        pc.equipment_code as equipmentName
        FROM hv_eam_inspect_process_content_item i
        left join hv_eam_inspect_process_plan_content pc ON i.content_id = pc.id
        <where>
            YEAR ( i.create_time ) = #{monthQueryDTO.yearTime} AND MONTH ( i.create_time ) = #{monthQueryDTO.monthTime}
            AND i.shut_down = 1
        </where>
        GROUP BY pc.equipment_code
        order by num desc
    </select>

    <select id="countForUnqualifiedItemGroupByItem" resultMap="itemStatisticalDTO"
            parameterType="com.hvisions.eam.dto.inspect.table.MonthQueryDTO">
        SELECT COUNT(i.id) as num,
        i.inspect_item_name as inspectItemName
        FROM hv_eam_inspect_process_content_item i
        <where>
            YEAR ( i.create_time ) = #{monthQueryDTO.yearTime} AND MONTH ( i.create_time ) = #{monthQueryDTO.monthTime}
            AND i.shut_down = 1
        </where>
        GROUP BY i.inspect_item_name
        order by num desc
    </select>

    <select id="countForUnqualifiedItemGroupByItemAndEquipment" resultMap="itemStatisticalDTO"
            parameterType="com.hvisions.eam.dto.inspect.table.MonthQueryDTO">
        SELECT COUNT(i.id) as num,
        pc.equipment_code as equipmentName,
        i.inspect_item_name as inspectItemName
        FROM hv_eam_inspect_process_content_item i
        left join hv_eam_inspect_process_plan_content pc ON i.content_id = pc.id
        <where>
            YEAR ( i.create_time ) = #{monthQueryDTO.yearTime} AND MONTH ( i.create_time ) = #{monthQueryDTO.monthTime}
            AND i.shut_down = 1
        </where>
        GROUP BY pc.equipment_code , i.inspect_item_name
    </select>

    <select id="countForTask" resultMap="taskStatisticalDTO"
            parameterType="com.hvisions.eam.dto.inspect.table.MonthQueryDTO" databaseId="mysql">
        SELECT
        num as num,
        checkerNum as checkerNum,
        duration as duration,
        ROUND(duration / num,2) as perTask,
        ROUND(duration / checkerNum,2) as perChecker
        FROM
        (
        SELECT
        COUNT( DISTINCT t.id ) AS num,
        COUNT( DISTINCT IFNULL( t.checker_id, 0 ) ) AS checkerNum,
        SUM( duration ) AS duration
        FROM
        (
        SELECT
        pc.id AS id,
        pd.checker_id AS checker_id,
        CASE
        pd.task_end_time
        WHEN NOT NULL THEN
        ( pd.task_end_time - pd.start_time ) / 1000 / 60 ELSE ( now( ) - pd.start_time ) / 1000 / 60
        END AS duration
        FROM
        hv_eam_inspect_process_data pd
        INNER JOIN hv_eam_inspect_process_plan_content pc ON pc.process_instance_id = pd.process_instance_id
        LEFT JOIN framework.sys_user u ON u.id = pd.checker_id
        LEFT JOIN hv_eam_inspect_plan ep ON ep.id = pc.inspect_plan_id
        <where>
            YEAR ( pd.start_time ) = #{monthQueryDTO.yearTime} AND MONTH (pd.start_time ) = #{monthQueryDTO.monthTime}
        </where>
        ) t
        ) t1

    </select>
    <select id="countForTask" resultMap="taskStatisticalDTO"
            parameterType="com.hvisions.eam.dto.inspect.table.MonthQueryDTO" databaseId="sqlserver">
        SELECT
        num as num,
        checkerNum as checkerNum,
        duration as duration,
        ROUND(duration / num,2) as perTask,
        ROUND(duration / checkerNum,2) as perChecker
        FROM
        (
        SELECT
        COUNT( DISTINCT t.id ) AS num,
        COUNT( DISTINCT IFNULL( t.checker_id, 0 ) ) AS checkerNum,
        SUM( duration ) AS duration
        FROM
        (
        SELECT
        pc.id AS id,
        pd.checker_id AS checker_id,
        CASE
        pd.task_end_time
        WHEN NOT NULL THEN
        ( pd.task_end_time - pd.start_time ) / 1000 / 60 ELSE ( now( ) - pd.start_time ) / 1000 / 60
        END AS duration
        FROM
        hv_eam_inspect_process_data pd
        INNER JOIN hv_eam_inspect_process_plan_content pc ON pc.process_instance_id = pd.process_instance_id
        LEFT JOIN framework.dbo.sys_user u ON u.id = pd.checker_id
        LEFT JOIN hv_eam_inspect_plan ep ON ep.id = pc.inspect_plan_id
        <where>
            YEAR ( pd.start_time ) = #{monthQueryDTO.yearTime} AND MONTH (pd.start_time ) = #{monthQueryDTO.monthTime}
        </where>
        ) t
        ) t1

    </select>
    <select id="countDurationByPerson" resultMap="inspectTaskStaDTO"
            parameterType="com.hvisions.eam.dto.inspect.table.MonthQueryDTO" databaseId="mysql">
        SELECT
        t.user_name as checker,
        round(AVG( t.duration ),2) as duration
        FROM
        (
        SELECT
        pc.ID,
        IFNULL( u.user_name, '未记录' ) AS user_name,
        CASE
        pd.task_end_time
        WHEN NOT NULL THEN
        ( pd.task_end_time - pd.start_time ) / 1000 / 60 ELSE ( now( ) - pd.start_time ) / 1000 / 60
        END AS duration
        FROM
        hv_eam_inspect_process_data pd
        INNER JOIN hv_eam_inspect_process_plan_content pc ON pc.process_instance_id = pd.process_instance_id
        LEFT JOIN framework.sys_user u ON u.id = pd.checker_id
        LEFT JOIN hv_eam_inspect_plan ep ON ep.id = pc.inspect_plan_id
        <where>
            YEAR ( pd.start_time ) = #{monthQueryDTO.yearTime} AND MONTH (pd.start_time ) = #{monthQueryDTO.monthTime}
        </where>
        ) t
        GROUP BY
        t.user_name
    </select>
    <select id="countDurationByPerson" resultMap="inspectTaskStaDTO"
            parameterType="com.hvisions.eam.dto.inspect.table.MonthQueryDTO" databaseId="sqlserver">
        SELECT
        t.user_name as checker,
        ROUND(AVG( t.duration ),2) as duration
        FROM
        (
        SELECT
        pc.ID,
        ISNULL(u.user_name, '未记录' ) AS user_name,
        CASE
        pd.task_end_time
        WHEN NOT NULL THEN
        ( pd.task_end_time - pd.start_time ) / 1000 / 60 ELSE ( now( ) - pd.start_time ) / 1000 / 60
        END AS duration
        FROM
        hv_eam_inspect_process_data pd
        INNER JOIN hv_eam_inspect_process_plan_content pc ON pc.process_instance_id = pd.process_instance_id
        LEFT JOIN framework.dbo.sys_user u ON u.id = pd.checker_id
        LEFT JOIN hv_eam_inspect_plan ep ON ep.id = pc.inspect_plan_id
        <where>
            YEAR ( pd.start_time ) = #{monthQueryDTO.yearTime} AND MONTH (pd.start_time ) = #{monthQueryDTO.monthTime}
        </where>
        ) t
        GROUP BY
        t.user_name
    </select>

    <select id="countTaskNumByPerson" resultMap="inspectTaskStaDTO"
            parameterType="com.hvisions.eam.dto.inspect.table.MonthQueryDTO" databaseId="mysql">
        SELECT
        COUNT( pc.ID ) AS num,
        IFNULL( u.user_name, '未记录' ) AS checker
        FROM
        hv_eam_inspect_process_data pd
        INNER JOIN hv_eam_inspect_process_plan_content pc ON pc.process_instance_id = pd.process_instance_id
        LEFT JOIN framework.sys_user u ON u.id = pd.checker_id
        LEFT JOIN hv_eam_inspect_plan ep ON ep.id = pc.inspect_plan_id
        <where>
            YEAR ( pd.start_time ) = #{monthQueryDTO.yearTime} AND MONTH (pd.start_time ) = #{monthQueryDTO.monthTime}
        </where>
        GROUP BY
        IFNULL( u.user_name, '未记录' )
    </select>
    <select id="countTaskNumByPerson" resultMap="inspectTaskStaDTO"
            parameterType="com.hvisions.eam.dto.inspect.table.MonthQueryDTO" databaseId="sqlserver">
        SELECT
        COUNT( pc.ID ) AS num,
        ISNULL( u.user_name, '未记录' ) AS checker
        FROM
        hv_eam_inspect_process_data pd
        INNER JOIN hv_eam_inspect_process_plan_content pc ON pc.process_instance_id = pd.process_instance_id
        LEFT JOIN framework.dbo.sys_user u ON u.id = pd.checker_id
        LEFT JOIN hv_eam_inspect_plan ep ON ep.id = pc.inspect_plan_id
        <where>
            YEAR ( pd.start_time ) = #{monthQueryDTO.yearTime} AND MONTH (pd.start_time ) = #{monthQueryDTO.monthTime}
        </where>
        GROUP BY
        IFNULL( u.user_name, '未记录' )
    </select>

    <select id="countTaskNumByPersonAndTaskName" resultMap="inspectTaskStaDTO"
            parameterType="com.hvisions.eam.dto.inspect.table.MonthQueryDTO" databaseId="mysql">
        SELECT
        COUNT( pc.ID ) AS num,
        IFNULL( pd.task_name, '未记录' ) AS taskName,
        DATE_FORMAT( pd.start_time, '%Y-%m-%d' ) AS startDate
        FROM
        hv_eam_inspect_process_data pd
        INNER JOIN hv_eam_inspect_process_plan_content pc ON pc.process_instance_id = pd.process_instance_id
        LEFT JOIN framework.sys_user u ON u.id = pd.checker_id
        LEFT JOIN hv_eam_inspect_plan ep ON ep.id = pc.inspect_plan_id
        <where>
            YEAR ( pd.start_time ) = #{monthQueryDTO.yearTime} AND MONTH (pd.start_time ) = #{monthQueryDTO.monthTime}
        </where>
        GROUP BY
        IFNULL( pd.task_name, '未记录' ),
        DATE_FORMAT( pd.start_time, '%Y-%m-%d' )
    </select>
    <select id="countTaskNumByPersonAndTaskName" resultMap="inspectTaskStaDTO"
            parameterType="com.hvisions.eam.dto.inspect.table.MonthQueryDTO" databaseId="sqlserver">
        SELECT
        COUNT( pc.ID ) AS num,
        ISNULL( pd.task_name, '未记录' ) AS taskName,
        CONVERT(nvarchar(10),pd.start_time,112) AS startDate
        FROM
        hv_eam_inspect_process_data pd
        INNER JOIN hv_eam_inspect_process_plan_content pc ON pc.process_instance_id = pd.process_instance_id
        LEFT JOIN framework.dbo.sys_user u ON u.id = pd.checker_id
        LEFT JOIN hv_eam_inspect_plan ep ON ep.id = pc.inspect_plan_id
        <where>
            YEAR ( pd.start_time ) = #{monthQueryDTO.yearTime} AND MONTH (pd.start_time ) = #{monthQueryDTO.monthTime}
        </where>
        GROUP BY
        ISNULL( pd.task_name, '未记录' ),
        CONVERT(nvarchar(10),pd.start_time,112)
    </select>

    <select id="countDurationByPersonAndTaskName" resultMap="inspectTaskStaDTO"
            parameterType="com.hvisions.eam.dto.inspect.table.MonthQueryDTO" databaseId="mysql">
        SELECT
        t.taskName AS taskName,
        t.startDate AS startDate,
        ROUND( AVG( t.duration ), 2 ) AS duration
        FROM
        (
        SELECT
        pc.ID AS id,
        IFNULL( pd.task_name, '未记录' ) AS taskName,
        DATE_FORMAT( pd.start_time, '%Y-%m-%d' ) AS startDate,
        CASE
        pd.task_end_time
        WHEN NOT NULL THEN
        ( pd.task_end_time - pd.start_time ) / 1000 / 60 ELSE ( now( ) - pd.start_time ) / 1000 / 60
        END AS duration
        FROM
        hv_eam_inspect_process_data pd
        INNER JOIN hv_eam_inspect_process_plan_content pc ON pc.process_instance_id = pd.process_instance_id
        LEFT JOIN framework.sys_user u ON u.id = pd.checker_id
        LEFT JOIN hv_eam_inspect_plan ep ON ep.id = pc.inspect_plan_id
        <where>
            YEAR ( pd.start_time ) = #{monthQueryDTO.yearTime} AND MONTH (pd.start_time ) = #{monthQueryDTO.monthTime}
        </where>
        ) t
        GROUP BY
        t.taskName,
        t.startDate
    </select>
    <select id="countDurationByPersonAndTaskName" resultMap="inspectTaskStaDTO"
            parameterType="com.hvisions.eam.dto.inspect.table.MonthQueryDTO" databaseId="sqlserver">
        SELECT
        t.taskName AS taskName,
        t.startDate AS startDate,
        ROUND( AVG( t.duration ), 2 ) AS duration
        FROM
        (
        SELECT
        pc.ID AS id,
        ISNULL( pd.task_name, '未记录' ) AS taskName,
        CONVERT(nvarchar(10),pd.start_time,112) AS startDate,
        CASE
        pd.task_end_time
        WHEN NOT NULL THEN
        ( pd.task_end_time - pd.start_time ) / 1000 / 60 ELSE ( now( ) - pd.start_time ) / 1000 / 60
        END AS duration
        FROM
        hv_eam_inspect_process_data pd
        INNER JOIN hv_eam_inspect_process_plan_content pc ON pc.process_instance_id = pd.process_instance_id
        LEFT JOIN framework.dbo.sys_user u ON u.id = pd.checker_id
        LEFT JOIN hv_eam_inspect_plan ep ON ep.id = pc.inspect_plan_id
        <where>
            YEAR ( pd.start_time ) = #{monthQueryDTO.yearTime} AND MONTH (pd.start_time ) = #{monthQueryDTO.monthTime}
        </where>
        ) t
        GROUP BY
        t.taskName,
        t.startDate
    </select>

    <select id="countDelayByPerson" resultMap="inspectTaskStaDTO"
            parameterType="com.hvisions.eam.dto.inspect.table.MonthQueryDTO" databaseId="mysql">
        SELECT
        ROUND( AVG( t1.delay ), 2 ) AS delay,
        t1.checker AS checker
        FROM
        (
        SELECT
        t.id AS id,
        t.checker AS checker,
        CASE
        WHEN t.delay <![CDATA[< ]]>  0 THEN
        0 ELSE t.delay
        END AS delay
        FROM
        (
        SELECT
        pc.ID AS id,
        IFNULL( u.user_name, '未记录' ) AS checker,
        CASE
        pd.task_end_time
        WHEN NOT NULL THEN
        ( pd.task_end_time - pd.start_time - pc.man_hour ) / 1000 / 60 ELSE ( now( ) - pd.start_time - pc.man_hour ) /
        1000 / 60
        END AS delay
        FROM
        hv_eam_inspect_process_data pd
        INNER JOIN hv_eam_inspect_process_plan_content pc ON pc.process_instance_id = pd.process_instance_id
        LEFT JOIN framework.sys_user u ON u.id = pd.checker_id
        LEFT JOIN hv_eam_inspect_plan ep ON ep.id = pc.inspect_plan_id
        <where>
            YEAR ( pd.start_time ) = #{monthQueryDTO.yearTime} AND MONTH (pd.start_time ) = #{monthQueryDTO.monthTime}
        </where>
        ) t
        ) t1
        GROUP BY
        t1.checker
    </select>
    <select id="countDelayByPerson" resultMap="inspectTaskStaDTO"
            parameterType="com.hvisions.eam.dto.inspect.table.MonthQueryDTO" databaseId="sqlserver">
        SELECT
        ROUND( AVG( t1.delay ), 2 ) AS delay,
        t1.checker AS checker
        FROM
        (
        SELECT
        t.id AS id,
        t.checker AS checker,
        CASE
        WHEN t.delay <![CDATA[< ]]>  0 THEN
        0 ELSE t.delay
        END AS delay
        FROM
        (
        SELECT
        pc.ID AS id,
        ISNULL( u.user_name, '未记录' ) AS checker,
        CASE
        pd.task_end_time
        WHEN NOT NULL THEN
        ( pd.task_end_time - pd.start_time - pc.man_hour ) / 1000 / 60 ELSE ( GETDATE() - pd.start_time - pc.man_hour )
        / 1000 / 60
        END AS delay
        FROM
        hv_eam_inspect_process_data pd
        INNER JOIN hv_eam_inspect_process_plan_content pc ON pc.process_instance_id = pd.process_instance_id
        LEFT JOIN framework.dbo.sys_user u ON u.id = pd.checker_id
        LEFT JOIN hv_eam_inspect_plan ep ON ep.id = pc.inspect_plan_id
        <where>
            YEAR ( pd.start_time ) = #{monthQueryDTO.yearTime} AND MONTH (pd.start_time ) = #{monthQueryDTO.monthTime}
        </where>
        ) t
        ) t1
        GROUP BY
        t1.checker
    </select>


</mapper>