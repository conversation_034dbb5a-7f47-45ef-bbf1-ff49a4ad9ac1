<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.eam.dao.SpareReportMapper">

    <resultMap id="usage" type="com.hvisions.eam.dto.repair.spare.SparePartUsageDTO"></resultMap>

    <select id="sparePartUsage" resultMap="usage"
            parameterType="com.hvisions.eam.dto.repair.spare.SparePartUsageQueryDTO" databaseId="mysql">
        SELECT
        SUM( t3.sum_number ) as sparePartNum ,
        t5.TEXT_ as cellName,
        SUM(t3.numberSum) as spareQuantity
        FROM
        hv_eam_spare t1
        LEFT JOIN hv_eam_spare_type t2 ON t1.spare_type_id = t2.id
        LEFT JOIN (
        SELECT
        sum(j1.number * j3.unit_price ) AS sum_number,
        sum(j1.number) as numberSum,
        j1.spare_id,
        j2.ASSIGNEE_,
        j1.process_instance_id
        FROM
        hv_eam_actual_use j1
        LEFT JOIN `activiti`.ACT_HI_TASKINST j2 ON j1.process_instance_id = j2.PROC_INST_ID_
        LEFT JOIN hv_eam_spare j3 on j3.id = j1.spare_id
        GROUP BY
        spare_id,
        j2.ASSIGNEE_,
        j1.process_instance_id
        ) t3 ON t3.spare_id = t1.id
        LEFT JOIN activiti.ACT_HI_PROCINST t4 ON t4.PROC_INST_ID_ = t3.process_instance_id
        LEFT JOIN ( SELECT k1.NAME_, k1.TEXT_, k1.PROC_INST_ID_ FROM activiti.ACT_HI_VARINST k1 WHERE k1.NAME_ =
        "line" AND k1.TEXT_ is not null
        ) t5 ON t5.PROC_INST_ID_ = t3.process_instance_id
        LEFT JOIN ( SELECT t9.id AS cellId, t9.`name` AS cellLocationName, t9.parent_id AS pId FROM
        `hiper_base`.hv_bm_location t9 WHERE t9.`type` = 40 ) t8 ON t5.TEXT_ = t8.cellLocationName
        LEFT JOIN ( SELECT k2.TEXT_ as applyUserId, k2.PROC_INST_ID_ FROM activiti.ACT_HI_VARINST k2 WHERE
        k2.NAME_ =
        "applyUserId" AND k2.TEXT_ is not null
        ) t10 ON t10.PROC_INST_ID_ = t3.process_instance_id
        <where>
            <if test="dto.sparePartModel !=null ">
                and t1.id = #{dto.sparePartModel}
            </if>
            <if test="dto.sparePartBrand !=null and dto.sparePartBrand != &apos;&apos;">
                and t1.brand = #{dto.sparePartBrand}
            </if>
            <if test="dto.sparePartType !=null ">
                and t1.spare_type_id = #{dto.sparePartType}
            </if>
            <if test="dto.userId !=null ">
                and t10.applyUserId = #{dto.userId}
            </if>
            <if test="dto.crewId !=null ">
                and EXISTS (SELECT l1.crew_id FROM `schedule`.hv_bm_crew_member l1 WHERE t3.ASSIGNEE_ = l1.user_id and
                l1.crew_id = #{dto.crewId})
            </if>
            <if test="dto.cellId !=null ">
                and t8.cellId = #{dto.cellId}
            </if>
            <if test="dto.areaId !=null ">
                and t8.pId = #{dto.areaId}
            </if>
            <if test="dto.startTime !=null and dto.endTime !=null">
                and t4.START_TIME_ between #{dto.startTime} and #{dto.endTime}
            </if>
            <if test="dto.supplier !=null and dto.supplier != &apos;&apos;">
                t1.supplier = like concat ('%',#{dto.supplier},'%')
            </if>
        </where>
        GROUP BY t5.TEXT_
    </select>
    <select id="sparePartUsage" resultMap="usage"
            parameterType="com.hvisions.eam.dto.repair.spare.SparePartUsageQueryDTO" databaseId="mysql">
        SELECT
        SUM( t3.sum_number ) as sparePartNum ,
        t5.TEXT_ as cellName,
        SUM(t3.numberSum) as spareQuantity
        FROM
        hv_eam_spare t1
        LEFT JOIN hv_eam_spare_type t2 ON t1.spare_type_id = t2.id
        LEFT JOIN (
        SELECT
        sum(j1.number * j3.unit_price ) AS sum_number,
        sum(j1.number) as numberSum,
        j1.spare_id,
        j2.ASSIGNEE_,
        j1.process_instance_id
        FROM
        hv_eam_actual_use j1
        LEFT JOIN activiti.dbo.ACT_HI_TASKINST j2 ON j1.process_instance_id = j2.PROC_INST_ID_
        LEFT JOIN hv_eam_spare j3 on j3.id = j1.spare_id
        GROUP BY
        spare_id,
        j2.ASSIGNEE_,
        j1.process_instance_id
        ) t3 ON t3.spare_id = t1.id
        LEFT JOIN activiti.dbo.ACT_HI_PROCINST t4 ON t4.PROC_INST_ID_ = t3.process_instance_id
        LEFT JOIN ( SELECT k1.NAME_, k1.TEXT_, k1.PROC_INST_ID_ FROM activiti.dbo.ACT_HI_VARINST k1 WHERE k1.NAME_ =
        "line" AND k1.TEXT_ is not null
        ) t5 ON t5.PROC_INST_ID_ = t3.process_instance_id
        LEFT JOIN ( SELECT t9.id AS cellId, t9.`name` AS cellLocationName, t9.parent_id AS pId FROM
        hiper_base.dbo.hv_bm_location t9 WHERE t9.`type` = 40 ) t8 ON t5.TEXT_ = t8.cellLocationName
        LEFT JOIN ( SELECT k2.TEXT_ as applyUserId, k2.PROC_INST_ID_ FROM activiti.dbo.ACT_HI_VARINST k2 WHERE
        k2.NAME_ =
        "applyUserId" AND k2.TEXT_ is not null
        ) t10 ON t10.PROC_INST_ID_ = t3.process_instance_id
        <where>
            <if test="dto.sparePartModel !=null ">
                and t1.id = #{dto.sparePartModel}
            </if>
            <if test="dto.sparePartBrand !=null and dto.sparePartBrand != &apos;&apos;">
                and t1.brand = #{dto.sparePartBrand}
            </if>
            <if test="dto.sparePartType !=null ">
                and t1.spare_type_id = #{dto.sparePartType}
            </if>
            <if test="dto.userId !=null ">
                and t10.applyUserId = #{dto.userId}
            </if>
            <if test="dto.crewId !=null ">
                and EXISTS (SELECT l1.crew_id FROM [schedule].dbo.hv_bm_crew_member l1 WHERE t3.ASSIGNEE_ = l1.user_id
                and
                l1.crew_id = #{dto.crewId})
            </if>
            <if test="dto.cellId !=null ">
                and t8.cellId = #{dto.cellId}
            </if>
            <if test="dto.areaId !=null ">
                and t8.pId = #{dto.areaId}
            </if>
            <if test="dto.startTime !=null and dto.endTime !=null">
                and t4.START_TIME_ between #{dto.startTime} and #{dto.endTime}
            </if>
            <if test="dto.supplier !=null and dto.supplier != &apos;&apos;">
                t1.supplier = like concat ('%',#{dto.supplier},'%')
            </if>
        </where>
        GROUP BY t5.TEXT_
    </select>

    <resultMap id="faultTime" type="com.hvisions.eam.dto.repair.spare.EquipmentFailureDTO">
        <result property="equipmentId" column="equipment_id"/>
        <result property="equipmentCode" column="equipment_code"/>
        <result property="equipmentName" column="equipment_name"/>
        <result property="stopCellTime" column="stopCellTime"/>
        <result property="failureTime" column="failureTime"/>
    </resultMap>

    <select id="equipmentFailure" resultMap="faultTime"
            parameterType="com.hvisions.eam.dto.repair.spare.EquipmentFailureQueryDTO" databaseId="mysql">
        SELECT
        l1.TEXT_ AS equipment_id,
        l2.equipment_code,
        l2.equipment_name,
        sum( l1.shoutdownTime ) AS stopCellTime,
        sum( l1.faultDiagnosisTime ) AS failureTime
        FROM
        (
        SELECT
        k1.TEXT_,
        k1.PROC_INST_ID_,
        k3.TEXT_ AS shoutdownTime,
        k4.TEXT_ AS faultDiagnosisTime
        FROM
        (
        SELECT
        j2.TEXT_,
        j2.PROC_INST_ID_
        FROM
        `activiti`.ACT_HI_PROCINST j1
        LEFT JOIN `activiti`.ACT_HI_VARINST j2 ON j1.ID_ = j2.PROC_INST_ID_
        WHERE
        j2.NAME_ = "equipmentId"
        AND j1.BUSINESS_KEY_ = "设备-维修"
        ) k1
        LEFT JOIN (
        SELECT
        t5.PROC_INST_ID_,
        t5.TEXT_
        FROM
        `activiti`.ACT_HI_PROCINST t4
        LEFT JOIN `activiti`.ACT_HI_VARINST t5 ON t4.ID_ = t5.PROC_INST_ID_
        WHERE
        t5.NAME_ = "shutdownTime"
        AND t4.BUSINESS_KEY_ = "设备-维修"
        ) k3 ON k3.PROC_INST_ID_ = k1.PROC_INST_ID_
        LEFT JOIN (
        SELECT
        t7.PROC_INST_ID_,
        t7.TEXT_
        FROM
        `activiti`.ACT_HI_PROCINST t6
        LEFT JOIN `activiti`.ACT_HI_VARINST t7 ON t6.ID_ = t7.PROC_INST_ID_
        WHERE
        t7.NAME_ = "faultDiagnosisTime"
        AND t6.BUSINESS_KEY_ = "设备-维修"
        ) k4 ON k4.PROC_INST_ID_ = k1.PROC_INST_ID_
        ) l1
        LEFT JOIN `hiper_base`.hv_bm_equipment l2 ON l1.TEXT_ = l2.id
        LEFT JOIN `activiti`.ACT_HI_PROCINST l3 on l1.PROC_INST_ID_ = l3.PROC_INST_ID_
        <where>
            <if test="dto.startTime !=null and dto.endTime != null">
                l3.START_TIME_ between #{dto.startTime} and #{dto.endTime}
            </if>
            <if test="dto.cellId !=null ">
                AND EXISTS ( SELECT z1.cell_id FROM `hiper_base`.hv_bm_equipment_cell z1 left join
                `hiper_base`.hv_bm_location z2 on z1.cell_id = z2.id and z2.type = 40
                WHERE z1.equipment_id = l2.id
                AND z2.id = #{dto.cellId})
            </if>
        </where>
        GROUP BY
        l1.TEXT_
    </select>
    <select id="equipmentFailure" resultMap="faultTime"
            parameterType="com.hvisions.eam.dto.repair.spare.EquipmentFailureQueryDTO" databaseId="sqlserver">
        SELECT
        l1.TEXT_ AS equipment_id,
        l2.equipment_code,
        l2.equipment_name,
        sum( l1.shoutdownTime ) AS stopCellTime,
        sum( l1.faultDiagnosisTime ) AS failureTime
        FROM
        (
        SELECT
        k1.TEXT_,
        k1.PROC_INST_ID_,
        k3.TEXT_ AS shoutdownTime,
        k4.TEXT_ AS faultDiagnosisTime
        FROM
        (
        SELECT
        j2.TEXT_,
        j2.PROC_INST_ID_
        FROM
        activiti.dbo.ACT_HI_PROCINST j1
        LEFT JOIN activiti.dbo.ACT_HI_VARINST j2 ON j1.ID_ = j2.PROC_INST_ID_
        WHERE
        j2.NAME_ = "equipmentId"
        AND j1.BUSINESS_KEY_ = "设备-维修"
        ) k1
        LEFT JOIN (
        SELECT
        t5.PROC_INST_ID_,
        t5.TEXT_
        FROM
        activiti.dbo.ACT_HI_PROCINST t4
        LEFT JOIN activiti.dbo.ACT_HI_VARINST t5 ON t4.ID_ = t5.PROC_INST_ID_
        WHERE
        t5.NAME_ = "shutdownTime"
        AND t4.BUSINESS_KEY_ = "设备-维修"
        ) k3 ON k3.PROC_INST_ID_ = k1.PROC_INST_ID_
        LEFT JOIN (
        SELECT
        t7.PROC_INST_ID_,
        t7.TEXT_
        FROM
        activiti.dbo.ACT_HI_PROCINST t6
        LEFT JOIN activiti.dbo.ACT_HI_VARINST t7 ON t6.ID_ = t7.PROC_INST_ID_
        WHERE
        t7.NAME_ = "faultDiagnosisTime"
        AND t6.BUSINESS_KEY_ = "设备-维修"
        ) k4 ON k4.PROC_INST_ID_ = k1.PROC_INST_ID_
        ) l1
        LEFT JOIN hiper_base.dbo.hv_bm_equipment l2 ON l1.TEXT_ = l2.id
        LEFT JOIN activiti.dbo.ACT_HI_PROCINST l3 on l1.PROC_INST_ID_ = l3.PROC_INST_ID_
        <where>
            <if test="dto.startTime !=null and dto.endTime != null">
                l3.START_TIME_ between #{dto.startTime} and #{dto.endTime}
            </if>
            <if test="dto.cellId !=null ">
                AND EXISTS ( SELECT z1.cell_id FROM hiper_base.dbo.hv_bm_equipment_cell z1 left join
                hiper_base.dbo.hv_bm_location z2 on z1.cell_id = z2.id and z2.type = 40
                WHERE z1.equipment_id = l2.id
                AND z2.id = #{dto.cellId})
            </if>
        </where>
        GROUP BY
        l1.TEXT_
    </select>

    <resultMap id="number" type="com.hvisions.eam.dto.repair.spare.FaultNumDTO">
        <result property="faultNumber" column="num"/>
        <result property="equipmentId" column="TEXT_"/>
    </resultMap>
    <select id="getFaultNum" resultMap="number"
            parameterType="com.hvisions.eam.dto.repair.spare.EquipmentFailureQueryDTO" databaseId="mysql">
        SELECT
        COUNT(*) AS num,
        t3.TEXT_
        FROM
        `activiti`.ACT_HI_PROCINST t2
        LEFT JOIN `activiti`.ACT_HI_VARINST t3 ON t2.ID_ = t3.PROC_INST_ID_ and t3.NAME_ = "equipmentId"
        <where>
            t2.BUSINESS_KEY_ ="设备-维修"
            <if test="dto.startTime !=null and dto.endTime != null">
                and t2.START_TIME_ between #{dto.startTime} and #{dto.endTime}
            </if>
            <if test="dto.cellId !=null ">
                AND EXISTS ( SELECT t4.cell_id FROM `hiper_base`.hv_bm_equipment_cell t4 left join
                `hiper_base`.hv_bm_location t5 on t4.cell_id = t5.id and t5.type = 40
                WHERE t4.equipment_id = t3.TEXT_
                AND t5.id = #{dto.cellId})
            </if>
        </where>
        GROUP BY
        t3.TEXT_
    </select>
    <select id="getFaultNum" resultMap="number"
            parameterType="com.hvisions.eam.dto.repair.spare.EquipmentFailureQueryDTO" databaseId="sqlserver">
        SELECT
        COUNT(*) AS num,
        t3.TEXT_
        FROM
        activiti.dbo.ACT_HI_PROCINST t2
        LEFT JOIN activiti.dbo.ACT_HI_VARINST t3 ON t2.ID_ = t3.PROC_INST_ID_ and t3.NAME_ = "equipmentId"
        <where>
            t2.BUSINESS_KEY_ ="设备-维修"
            <if test="dto.startTime !=null and dto.endTime != null">
                and t2.START_TIME_ between #{dto.startTime} and #{dto.endTime}
            </if>
            <if test="dto.cellId !=null ">
                AND EXISTS ( SELECT t4.cell_id FROM hiper_base.dbo.hv_bm_equipment_cell t4 left join
                hiper_base.dbo.hv_bm_location t5 on t4.cell_id = t5.id and t5.type = 40
                WHERE t4.equipment_id = t3.TEXT_
                AND t5.id = #{dto.cellId})
            </if>
        </where>
        GROUP BY
        t3.TEXT_
    </select>

    <resultMap id="turnover" type="com.hvisions.eam.dto.repair.spare.MonthTurnoverDTO"></resultMap>
    <select id="spareTurnover" resultMap="turnover"
            parameterType="com.hvisions.eam.dto.repair.spare.TurnoverQueryDTO" databaseId="mysql">
        SELECT
        t1.spare_code as sparePartCode,
        t1.spare_name as sparePartName,
        sum( t2.number ) as monthStock,
        MONTH ( t2.create_time ) as monthTime,
        t1.unit_price as unitPrice
        FROM
        hv_eam_spare t1
        LEFT JOIN hv_eam_spare_to_shelve t2 ON t1.id = t2.spare_id
        <where>
            YEAR ( t2.create_time ) = #{dto.yearTime}
            <if test="dto.monthTime != null">
                AND MONTH ( t2.create_time ) = #{dto.monthTime}
            </if>
            <if test="dto.spareTypeId !=null">
                and t1.spare_type_id = #{dto.spareTypeId}
            </if>
        </where>
        GROUP BY
        MONTH ( t2.create_time ),
        t1.spare_code,t1.spare_name,t1.unit_price
    </select>
    <select id="spareTurnover" resultMap="turnover"
            parameterType="com.hvisions.eam.dto.repair.spare.TurnoverQueryDTO" databaseId="sqlserver">
        SELECT
        t1.spare_code as sparePartCode,
        t1.spare_name as sparePartName,
        sum( t2.number ) as monthStock,
        MONTH ( t2.create_time ) as monthTime,
        t1.unit_price as unitPrice
        FROM
        hv_eam_spare t1
        LEFT JOIN hv_eam_spare_to_shelve t2 ON t1.id = t2.spare_id
        <where>
            YEAR ( t2.create_time ) = #{dto.yearTime}
            <if test="dto.monthTime != null">
                AND MONTH ( t2.create_time ) = #{dto.monthTime}
            </if>
            <if test="dto.spareTypeId !=null">
                and t1.spare_type_id = #{dto.spareTypeId}
            </if>
        </where>
        GROUP BY
        MONTH ( t2.create_time ),
        t1.spare_code,t1.spare_name,t1.unit_price
    </select>

    <resultMap id="store" type="com.hvisions.eam.dto.repair.spare.StoreNumDTO"></resultMap>
    <select id="getStoreNum" resultMap="store" parameterType="com.hvisions.eam.dto.repair.spare.TurnoverQueryDTO"
            databaseId="mysql">
        SELECT
        sum(t2.number) as number ,t3.spare_code as spareCode FROM
        hv_eam_store_header t1
        LEFT JOIN
        hv_eam_store_line t2 on t1.id = t2.header_id
        LEFT JOIN
        hv_eam_spare t3 on t2.spare_id = t3.id
        <where>
            t1.type_class = 1
            and t1.complete = 1
            and t1.in_out = 2
            and YEAR ( t1.create_time ) = #{dto.yearTime}
            <if test="dto.monthTime != null">
                AND MONTH ( t1.create_time ) = #{dto.monthTime}
            </if>
            <if test="dto.spareCode != null">
                and t3.spare_code = #{dto.spareCode}
            </if>

        </where>
        group by t3.spare_code
    </select>
    <select id="getStoreNum" resultMap="store" parameterType="com.hvisions.eam.dto.repair.spare.TurnoverQueryDTO"
            databaseId="sqlserver">
        SELECT
        sum(t2.number) as number ,t3.spare_code as spareCode FROM
        hv_eam_store_header t1
        LEFT JOIN
        hv_eam_store_line t2 on t1.id = t2.header_id
        LEFT JOIN
        hv_eam_spare t3 on t2.spare_id = t3.id
        <where>
            t1.type_class = 1
            and t1.complete = 1
            and t1.in_out = 2
            and YEAR ( t1.create_time ) = #{dto.yearTime}
            <if test="dto.monthTime != null">
                AND MONTH ( t1.create_time ) = #{dto.monthTime}
            </if>
            <if test="dto.spareCode != null">
                and t3.spare_code = #{dto.spareCode}
            </if>

        </where>
        group by t3.spare_code
    </select>
</mapper>