<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.activiti.org/processdef">
  <process id="equipment-maintain" name="设备保养" isExecutable="true">
    <documentation>设备保养</documentation>
    <startEvent id="sid-E0DD383C-8B05-4860-B5B5-7F997E06C486" name="开始"></startEvent>
    <userTask id="sid-3CA25210-1C78-4D1B-A73A-68F17BCB7291" name="设备保养执行" activiti:assignee="${executor_id}" activiti:candidateUsers="${candidateUsers}" activiti:candidateGroups="${candidateGroups}">
      <documentation>设备：${equipmentName},计划：${taskName}(${taskNum})</documentation>
      <extensionElements>
        <activiti:formProperty id="state" name="状态" type="string" variable="state1"></activiti:formProperty>
        <modeler:initiator-can-complete xmlns:modeler="http://activiti.com/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <endEvent id="sid-4386B804-E05D-4D1C-8313-38A792F2E4E3" name="结束"></endEvent>
    <sequenceFlow id="sid-F03B1F6B-9510-4915-B210-A045F6051802" sourceRef="sid-3CA25210-1C78-4D1B-A73A-68F17BCB7291" targetRef="sid-4386B804-E05D-4D1C-8313-38A792F2E4E3"></sequenceFlow>
    <sequenceFlow id="sid-F23A5DF3-154B-4A29-8E05-75E09EA25960" sourceRef="sid-E0DD383C-8B05-4860-B5B5-7F997E06C486" targetRef="sid-3CA25210-1C78-4D1B-A73A-68F17BCB7291"></sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_equipment-maintain">
    <bpmndi:BPMNPlane bpmnElement="equipment-maintain" id="BPMNPlane_equipment-maintain">
      <bpmndi:BPMNShape bpmnElement="sid-E0DD383C-8B05-4860-B5B5-7F997E06C486" id="BPMNShape_sid-E0DD383C-8B05-4860-B5B5-7F997E06C486">
        <omgdc:Bounds height="30.0" width="30.0" x="300.0" y="40.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-3CA25210-1C78-4D1B-A73A-68F17BCB7291" id="BPMNShape_sid-3CA25210-1C78-4D1B-A73A-68F17BCB7291">
        <omgdc:Bounds height="80.0" width="100.0" x="525.0" y="15.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-4386B804-E05D-4D1C-8313-38A792F2E4E3" id="BPMNShape_sid-4386B804-E05D-4D1C-8313-38A792F2E4E3">
        <omgdc:Bounds height="28.0" width="28.0" x="823.0" y="41.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-F03B1F6B-9510-4915-B210-A045F6051802" id="BPMNEdge_sid-F03B1F6B-9510-4915-B210-A045F6051802">
        <omgdi:waypoint x="625.0" y="55.0"></omgdi:waypoint>
        <omgdi:waypoint x="823.0" y="55.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-F23A5DF3-154B-4A29-8E05-75E09EA25960" id="BPMNEdge_sid-F23A5DF3-154B-4A29-8E05-75E09EA25960">
        <omgdi:waypoint x="330.0" y="55.0"></omgdi:waypoint>
        <omgdi:waypoint x="525.0" y="55.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>