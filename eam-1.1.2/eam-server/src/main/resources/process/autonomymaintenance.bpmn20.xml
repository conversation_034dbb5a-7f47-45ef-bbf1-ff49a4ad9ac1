<?xml version='1.0' encoding='UTF-8'?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.activiti.org/processdef">
  <process id="autonomymaintenance" name="自主维护" isExecutable="true">
    <startEvent id="sid-91E77F10-4C20-4B86-A18A-373FF4FD95F6"/>
    <userTask id="执行" name="执行-${taskName}" activiti:assignee="${personLiable}" activiti:candidateGroups="${responsibilityGroup}">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://activiti.com/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="检查" name="检查-${taskName}" activiti:assignee="${verifier}">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://activiti.com/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-F7D57762-3022-4DF4-8F44-3253B7B5CF8C" sourceRef="执行" targetRef="检查"/>
    <userTask id="确认" name="确认-${taskName}" activiti:assignee="${foreman}">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://activiti.com/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <endEvent id="sid-35D5B4C9-02B3-44C8-9635-435A567A3A4F"/>
    <sequenceFlow id="sid-00F13F68-DCD1-49C4-9C4A-7A09679595F4" sourceRef="确认" targetRef="sid-35D5B4C9-02B3-44C8-9635-435A567A3A4F"/>
    <sequenceFlow id="sid-341AC1E8-3708-473E-9D08-3F256B680B6B" sourceRef="sid-91E77F10-4C20-4B86-A18A-373FF4FD95F6" targetRef="执行"/>
    <sequenceFlow id="sid-D3CADDAB-803F-43A7-A85D-A01E54740333" sourceRef="检查" targetRef="确认"/>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_autonomymaintenance">
    <bpmndi:BPMNPlane bpmnElement="autonomymaintenance" id="BPMNPlane_autonomymaintenance">
      <bpmndi:BPMNShape bpmnElement="sid-91E77F10-4C20-4B86-A18A-373FF4FD95F6" id="BPMNShape_sid-91E77F10-4C20-4B86-A18A-373FF4FD95F6">
        <omgdc:Bounds height="30.0" width="30.0" x="15.0" y="70.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="执行" id="BPMNShape_执行">
        <omgdc:Bounds height="80.0" width="100.0" x="135.0" y="45.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="检查" id="BPMNShape_检查">
        <omgdc:Bounds height="80.0" width="100.0" x="285.0" y="45.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="确认" id="BPMNShape_确认">
        <omgdc:Bounds height="80.0" width="100.0" x="450.0" y="45.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-35D5B4C9-02B3-44C8-9635-435A567A3A4F" id="BPMNShape_sid-35D5B4C9-02B3-44C8-9635-435A567A3A4F">
        <omgdc:Bounds height="28.0" width="28.0" x="615.0" y="71.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-00F13F68-DCD1-49C4-9C4A-7A09679595F4" id="BPMNEdge_sid-00F13F68-DCD1-49C4-9C4A-7A09679595F4">
        <omgdi:waypoint x="550.0" y="85.0"/>
        <omgdi:waypoint x="615.0" y="85.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-D3CADDAB-803F-43A7-A85D-A01E54740333" id="BPMNEdge_sid-D3CADDAB-803F-43A7-A85D-A01E54740333">
        <omgdi:waypoint x="385.0" y="85.0"/>
        <omgdi:waypoint x="450.0" y="85.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-F7D57762-3022-4DF4-8F44-3253B7B5CF8C" id="BPMNEdge_sid-F7D57762-3022-4DF4-8F44-3253B7B5CF8C">
        <omgdi:waypoint x="235.0" y="85.0"/>
        <omgdi:waypoint x="285.0" y="85.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-341AC1E8-3708-473E-9D08-3F256B680B6B" id="BPMNEdge_sid-341AC1E8-3708-473E-9D08-3F256B680B6B">
        <omgdi:waypoint x="45.0" y="85.0"/>
        <omgdi:waypoint x="135.0" y="85.0"/>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>