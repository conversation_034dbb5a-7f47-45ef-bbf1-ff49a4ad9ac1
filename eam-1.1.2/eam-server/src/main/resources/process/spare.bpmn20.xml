<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.activiti.org/processdef">
  <process id="store-spare" name="备件" isExecutable="true">
    <documentation>备件</documentation>
    <startEvent id="A1ID" name="spareStare" activiti:initiator="A1initator" activiti:formKey="A1KEY">
      <documentation>A1documentation</documentation>
      <extensionElements>
        <activiti:formProperty id="applyUserId" name="applyUserId" type="string"></activiti:formProperty>
      </extensionElements>
    </startEvent>
    <userTask id="ActivitiEnd" name="${title}" activiti:formKey="A3formkey">
      <documentation>单号: ${receiptNumber} - 标题: ${title}</documentation>
    </userTask>
    <endEvent id="A5id" name="ActivitiEnd">
      <documentation>A5documentation</documentation>
    </endEvent>
    <sequenceFlow id="A2id" name="A2name" sourceRef="A1ID" targetRef="ActivitiEnd">
      <documentation>A2documentation</documentation>
    </sequenceFlow>
    <sequenceFlow id="A4ID" name="A4name" sourceRef="ActivitiEnd" targetRef="A5id">
      <documentation>A4documentation</documentation>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_store-spare">
    <bpmndi:BPMNPlane bpmnElement="store-spare" id="BPMNPlane_store-spare">
      <bpmndi:BPMNShape bpmnElement="A1ID" id="BPMNShape_A1ID">
        <omgdc:Bounds height="30.0" width="30.0" x="225.0" y="145.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="ActivitiEnd" id="BPMNShape_ActivitiEnd">
        <omgdc:Bounds height="80.0" width="100.0" x="330.0" y="120.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="A5id" id="BPMNShape_A5id">
        <omgdc:Bounds height="28.0" width="28.0" x="525.0000000000001" y="146.00000000000003"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="A4ID" id="BPMNEdge_A4ID">
        <omgdi:waypoint x="430.0" y="160.0"></omgdi:waypoint>
        <omgdi:waypoint x="525.0000000000001" y="160.00000000000003"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="A2id" id="BPMNEdge_A2id">
        <omgdi:waypoint x="255.0" y="160.0"></omgdi:waypoint>
        <omgdi:waypoint x="330.0" y="160.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>