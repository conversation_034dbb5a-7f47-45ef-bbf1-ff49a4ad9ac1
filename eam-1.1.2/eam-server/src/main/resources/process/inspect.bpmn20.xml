<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.activiti.org/processdef">
  <process id="equipment-inspect" name="设备点巡检" isExecutable="true">
    <documentation>设备点巡检</documentation>
    <startEvent id="startEvent1" name="开始"></startEvent>
    <userTask id="sid-283B2B17-FFF2-47ED-9EA4-135EB9DF98C9" name="点巡检执行" activiti:assignee="${executor_id}" activiti:candidateUsers="${candidateUsers}" activiti:candidateGroups="${candidateGroups}">
      <documentation>计划名称：${taskName} - 设备名称：${equipmentName}</documentation>
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://activiti.com/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-0735BF72-9575-4699-A188-7DB47A39467E" sourceRef="startEvent1" targetRef="sid-283B2B17-FFF2-47ED-9EA4-135EB9DF98C9"></sequenceFlow>
    <endEvent id="sid-D1ED8303-3E24-4566-9636-5D886BF59FF4" name="结束"></endEvent>
    <sequenceFlow id="sid-25F2BC2E-0035-4088-A76A-3CEBAD30D15B" sourceRef="sid-283B2B17-FFF2-47ED-9EA4-135EB9DF98C9" targetRef="sid-D1ED8303-3E24-4566-9636-5D886BF59FF4"></sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_equipment-inspect">
    <bpmndi:BPMNPlane bpmnElement="equipment-inspect" id="BPMNPlane_equipment-inspect">
      <bpmndi:BPMNShape bpmnElement="startEvent1" id="BPMNShape_startEvent1">
        <omgdc:Bounds height="30.0" width="30.0" x="120.0" y="145.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-283B2B17-FFF2-47ED-9EA4-135EB9DF98C9" id="BPMNShape_sid-283B2B17-FFF2-47ED-9EA4-135EB9DF98C9">
        <omgdc:Bounds height="80.0" width="100.0" x="315.0" y="120.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-D1ED8303-3E24-4566-9636-5D886BF59FF4" id="BPMNShape_sid-D1ED8303-3E24-4566-9636-5D886BF59FF4">
        <omgdc:Bounds height="28.0" width="28.0" x="600.0" y="146.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-25F2BC2E-0035-4088-A76A-3CEBAD30D15B" id="BPMNEdge_sid-25F2BC2E-0035-4088-A76A-3CEBAD30D15B">
        <omgdi:waypoint x="415.0" y="160.0"></omgdi:waypoint>
        <omgdi:waypoint x="600.0" y="160.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-0735BF72-9575-4699-A188-7DB47A39467E" id="BPMNEdge_sid-0735BF72-9575-4699-A188-7DB47A39467E">
        <omgdi:waypoint x="150.0" y="160.0"></omgdi:waypoint>
        <omgdi:waypoint x="315.0" y="160.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>