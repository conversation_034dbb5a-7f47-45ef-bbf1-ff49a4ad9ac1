#dev配置
spring:
  jpa:
    database-platform: org.hibernate.dialect.MySQL5InnoDBDialect
  datasource:
    #数据库驱动，mysql8.0
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: 123456
    url: *******************************************************************************************
  #redis 地址和端口号
  redis:
    host: localhost
    port: 6379
  rabbitmq:
    host: ************
    port: 5672
    username: admin
    password: admin

h-visions:
  #是否添加所有控制器请求记录到log服务
  log:
    enable: false
  #服务名称,可以使用中文，日志服务会将这个字段传递
  service-name: eam
  #流程文件是否强制更新 false
  bpmn-update: false
  #保养润滑计划是否需要审批，true为需要，false为不需要
  approve: false
  # 此处配置为audit信息的创建方式。dto 为当请求控制器的时候如果入参为SysDTO可以自动赋值。jpa为使用jpa的audit方式进行实现。
  #可以使用swagger的接口，使用对应的测试方法，生成api文档，支持markdown和ascii
  swagger:
    # 如果为false或者没有此属性。swagger界面将不会加载
    enable: true
    api-url: http://localhost:${server.port}/v2/api-docs;
    asciidoc-dir: ./build/asciidoc/
    markdown-dir: ./build/markdown/
  maintain:
    #工作流服务器的数据库名称。为了以后可能的调整增加的
    activiti-database: activiti
  equipmentInspect:
    # 点巡检地图中最长的查询时间段
    taskTime: 60
