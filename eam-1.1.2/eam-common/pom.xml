<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>eam</artifactId>
        <groupId>com.hvisions</groupId>
        <version>1.1.2</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>eam-common</artifactId>
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>1.5.9</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>2.9.0</version>
        </dependency>
        <!-- common 基础服务 -->
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>common-interface</artifactId>
            <version>3.4.0</version>
        </dependency>
        <!-- @Valid 注解验证-->
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>4.0.11</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>20.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>activiti-common</artifactId>
            <version>3.0.0</version>
        </dependency>
    </dependencies>

</project>
