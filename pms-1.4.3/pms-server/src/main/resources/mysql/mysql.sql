/*
 Navicat Premium Data Transfer

 Source Server         : dev
 Source Server Type    : MySQL
 Source Server Version : 80012
 Source Host           : ************:3306
 Source Schema         : order_manage

 Target Server Type    : MySQL
 Target Server Version : 80012
 File Encoding         : 65001

 Date: 16/05/2019 14:58:42
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for hv_pm_crew_equipment
-- ----------------------------
CREATE TABLE `hv_pm_crew_equipment`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `equipment_id` int(11) NOT NULL COMMENT '设备ID',
  `crew_id` int(11) NOT NULL COMMENT '班组ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hv_pm_operation_file
-- ----------------------------
CREATE TABLE `hv_pm_operation_file`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `creator_id` int(11) NULL DEFAULT NULL COMMENT '创建人ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `updater_id` int(11) NULL DEFAULT NULL COMMENT '更新人',
  `site_num` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '系统代码',
  `file_id` int(11) NOT NULL COMMENT '文件ID列表',
  `operation_id` int(11) NOT NULL COMMENT '工艺步骤ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 92 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hv_pm_operation_material
-- ----------------------------
CREATE TABLE `hv_pm_operation_material`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  `site_num` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `creator_id` int(11) NULL DEFAULT NULL,
  `updater_id` int(11) NULL DEFAULT NULL,
  `operation_id` int(11) NULL DEFAULT NULL,
  `material_id` int(11) NULL DEFAULT NULL COMMENT '物料id',
  `material_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物料编码',
  `material_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物料名称',
  `material_eigenvalue` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物料特征值',
  `material_actual_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '实际装配物料信息',
  `batch_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物料批次号',
  `actual_count` decimal(10, 4) NULL DEFAULT NULL COMMENT '实际录入数量',
  `plan_count` decimal(10, 4) NULL DEFAULT NULL COMMENT '计划投入物料数量',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `id`(`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 573 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '工序和物料追溯表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hv_pm_operation_out_put_material
-- ----------------------------
CREATE TABLE `hv_pm_operation_out_put_material`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `operation_id` int(11) NOT NULL COMMENT '工序ID',
  `material_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物料编码',
  `material_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物料名称',
  `eigenvalue` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物料特征值',
  `out_put_count` decimal(10, 4) NULL DEFAULT NULL COMMENT '产出数量',
  `batch_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '批次号',
  `create_time` datetime(0) NULL DEFAULT NULL,
  `creator_id` int(11) NULL DEFAULT NULL,
  `update_time` datetime(0) NULL DEFAULT NULL,
  `updater_id` int(11) NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL COMMENT '操作人ID',
  `material_id` int(11) NULL DEFAULT NULL COMMENT '产出物料ID',
  `site_num` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '系统代码',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 74 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hv_pm_operation_parameter
-- ----------------------------
CREATE TABLE `hv_pm_operation_parameter`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `creator_id` int(11) NULL DEFAULT NULL COMMENT '创建人ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `updater_id` int(11) NULL DEFAULT NULL COMMENT '更新人',
  `site_num` int(11) NULL DEFAULT NULL COMMENT '系统代码',
  `operation_id` int(11) NOT NULL COMMENT '工序ID',
  `data_type` int(11) NOT NULL COMMENT '数据类型，1.整数，2.浮点数，3.字符串，4.布尔值，5.日期，6.时间，7.精确小数，\r\n\r\n',
  `max_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '	\r\n最大值，仅录入参数，数值型可用\r\n',
  `min_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最小值，仅录入参数，数值型可用\r\n',
  `parameter_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '参数编码',
  `parameter_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '参数名称',
  `parameter_type` int(11) NULL DEFAULT NULL COMMENT '参数类型-1.手动，2.自动',
  `parameter_usage` int(11) NULL DEFAULT NULL COMMENT '用途，1表示生产，2表示质量',
  `standard_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '标准值最小值，仅录入参数，可用',
  `actual_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `parameter_version` int(11) NULL DEFAULT NULL COMMENT '工序参数版本',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 403 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hv_pm_order_operation
-- ----------------------------

CREATE TABLE `hv_pm_order_operation`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  `site_num` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0',
  `creator_id` int(11) NULL DEFAULT NULL,
  `updater_id` int(11) NULL DEFAULT NULL,
  `order_id` int(11) NULL DEFAULT NULL,
  `serial_num` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '流水号',
  `start_time` datetime(0) NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime(0) NULL DEFAULT NULL COMMENT '工序结束时间',
  `operation_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '工序编码',
  `equipment_id` int(11) NULL DEFAULT NULL COMMENT '设备id',
  `state` int(11) NULL DEFAULT NULL COMMENT '工序状态',
  `area_id` int(11) NULL DEFAULT NULL COMMENT '车间id',
  `cell_id` int(11) NULL DEFAULT NULL COMMENT '产线id',
  `crew_id` int(11) NULL DEFAULT NULL COMMENT '班组id',
  `shift_id` int(11) NULL DEFAULT NULL COMMENT '班次id',
  `route_step_id` int(11) NULL DEFAULT NULL COMMENT '工艺步骤id',
  `operation_order` int(11) NULL DEFAULT NULL COMMENT '工艺步骤顺序',
  `equipment_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设备名称',
  `area_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '车间名称',
  `crew_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '班组名称',
  `shift_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '班次名称',
  `work_order_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '工单编号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `id`(`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 260 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '工单操作记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hv_pm_procedure_record
-- ----------------------------
CREATE TABLE `hv_pm_procedure_record`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `operation_time` datetime(0) NOT NULL COMMENT '操作时间',
  `operation_state` int(11) NULL DEFAULT NULL COMMENT '当前工序状态',
  `user_id` int(11) NOT NULL COMMENT '操作人ID',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作人名称',
  `operation_id` int(11) NULL DEFAULT NULL COMMENT '操作的工序ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 46 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '工序操作追溯表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hv_pm_user_equipment
-- ----------------------------
CREATE TABLE `hv_pm_user_equipment`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '关联关系ID',
  `user_id` int(11) NOT NULL COMMENT '人员ID',
  `equipment_id` int(11) NOT NULL COMMENT '设备ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 33 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hv_pm_work_order
-- ----------------------------
CREATE TABLE `hv_pm_work_order`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `work_order_code` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '工单编号',
  `plan_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '工单计划编号',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单创建时间',
  `work_order_state` int(11) NOT NULL COMMENT '工单执行状态',
  `material_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物料编码',
  `material_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物料名称',
  `plan_end_time` datetime(0) NULL DEFAULT NULL COMMENT '计划结束时间',
  `route_id` int(11) NOT NULL COMMENT '工艺路线',
  `quantity` int(11) NULL DEFAULT NULL COMMENT '数量',
  `route_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '工艺路线名称',
  `route_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '工艺路线编码',
  `route_version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '工艺路线版本',
  `bom_version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'bom版本',
  `issued_time` datetime(0) NULL DEFAULT NULL COMMENT '工单下发时间',
  `actual_end_time` datetime(0) NULL DEFAULT NULL COMMENT '实际结束时间',
  `eigenvalue` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '特征值',
  `plan_start_time` datetime(0) NULL DEFAULT NULL COMMENT '计划开始时间',
  `material_id` int(11) NOT NULL COMMENT '物料ID',
  `serial_number` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '流水号',
  `actual_start_time` datetime(0) NULL DEFAULT NULL COMMENT '实际开始时间',
  `shift_id` int(11) NULL DEFAULT NULL COMMENT '班次ID',
  `shift_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '班次名称',
  `crew_id` int(11) NULL DEFAULT NULL COMMENT '班组ID',
  `crew_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '班组名称',
  `bom_id` int(11) NULL DEFAULT NULL COMMENT 'bomID',
  `area_id` int(11) NULL DEFAULT NULL COMMENT '车间ID',
  `cell_id` int(11) NULL DEFAULT NULL COMMENT '产线ID',
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `creator_id` int(11) NULL DEFAULT NULL COMMENT '创建人',
  `updater_id` int(11) NULL DEFAULT NULL COMMENT '更新人',
  `actual_count` decimal(10, 4) NULL DEFAULT NULL COMMENT '实际产出数量',
  `site_num` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '系统代码',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `code,serial_number,unique`(`work_order_code`, `serial_number`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 117 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hv_pm_work_order_extend
-- ----------------------------

CREATE TABLE `hv_pm_work_order_extend`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `work_order_id` int(11) NOT NULL,
  `string` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'string,string,string',
  `product` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'null,product,产地',
  `asdfasd` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'asdfsdf,asdf,sadfsdf',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 82 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- View structure for material_by_crew
-- ----------------------------

CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `material_by_crew` AS select `hv_pm_work_order`.`crew_name` AS `crew_name`,`hv_pm_order_operation`.`id` AS `operation_oid`,`hv_pm_operation_out_put_material`.`out_put_count` AS `out_put_count`,`hv_pm_operation_out_put_material`.`material_id` AS `material_id`,`hv_pm_work_order`.`actual_end_time` AS `actual_end_time`,`hv_pm_work_order`.`actual_start_time` AS `actual_start_time`,`hv_pm_work_order`.`cell_id` AS `cell_id`,`hv_pm_order_operation`.`operation_code` AS `operation_code`,`hv_pm_order_operation`.`start_time` AS `start_time`,`hv_pm_order_operation`.`end_time` AS `end_time`,`hv_pm_operation_out_put_material`.`material_name` AS `material_name`,`hv_pm_operation_out_put_material`.`material_code` AS `material_code` from ((`hv_pm_work_order` join `hv_pm_order_operation`) join `hv_pm_operation_out_put_material`) where ((`hv_pm_work_order`.`id` = `hv_pm_order_operation`.`order_id`) and (`hv_pm_order_operation`.`id` = `hv_pm_operation_out_put_material`.`operation_id`));

-- ----------------------------
-- View structure for material_consume
-- ----------------------------

CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `material_consume` AS select `hv_pm_operation_material`.`material_code` AS `material_code`,`hv_pm_operation_material`.`material_name` AS `material_name`,`hv_pm_operation_material`.`material_eigenvalue` AS `material_eigenvalue`,`hv_pm_operation_material`.`material_id` AS `material_id`,`hv_pm_operation_material`.`actual_count` AS `actual_count`,`hv_pm_operation_material`.`plan_count` AS `plan_count`,`hv_pm_operation_material`.`operation_id` AS `operation_id`,`hv_pm_order_operation`.`operation_code` AS `operation_code`,`hv_pm_work_order`.`plan_start_time` AS `plan_start_time`,`hv_pm_work_order`.`plan_end_time` AS `plan_end_time`,`hv_pm_work_order`.`work_order_code` AS `work_order_code`,`hv_pm_work_order`.`crew_name` AS `crew_name`,`hv_pm_work_order`.`actual_end_time` AS `actual_end_time`,`hv_pm_work_order`.`actual_start_time` AS `actual_start_time`,`hv_pm_work_order`.`cell_id` AS `cell_id` from ((`hv_pm_operation_material` join `hv_pm_order_operation`) join `hv_pm_work_order`) where ((`hv_pm_operation_material`.`operation_id` = `hv_pm_order_operation`.`id`) and (`hv_pm_order_operation`.`order_id` = `hv_pm_work_order`.`id`));

-- ----------------------------
-- View structure for material_form
-- ----------------------------

CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `material_form` AS select `material_consume`.`material_id` AS `material_id`,`material_consume`.`plan_count` AS `sum_plan_count`,sum(`material_consume`.`actual_count`) AS `sum_actual_count`,`material_consume`.`crew_name` AS `crew_name`,`material_consume`.`operation_code` AS `operation_code`,max(`material_consume`.`operation_id`) AS `operation_id`,max(`material_consume`.`actual_end_time`) AS `actual_end_time`,max(`material_consume`.`actual_start_time`) AS `actual_start_time`,max(`material_consume`.`cell_id`) AS `cell_id`,max(`material_consume`.`material_name`) AS `material_name`,max(`material_consume`.`material_code`) AS `material_code` from `material_consume` group by `material_consume`.`material_id`,`material_consume`.`crew_name`,`material_consume`.`operation_code`,`material_consume`.`plan_count`;

-- ----------------------------
-- View structure for work_time_crew
-- ----------------------------

CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `work_time_crew` AS select `hv_pm_work_order`.`crew_name` AS `crew_name`,`hv_pm_order_operation`.`id` AS `operation_oid`,`hv_pm_work_order`.`cell_id` AS `cell_id`,`hv_pm_order_operation`.`operation_code` AS `operation_code`,`hv_pm_order_operation`.`start_time` AS `start_time`,`hv_pm_order_operation`.`end_time` AS `end_time`,timestampdiff(HOUR,`hv_pm_order_operation`.`start_time`,`hv_pm_order_operation`.`end_time`) AS `work_time` from (`hv_pm_work_order` join `hv_pm_order_operation`) where (`hv_pm_work_order`.`id` = `hv_pm_order_operation`.`order_id`) group by `operation_oid`;

SET FOREIGN_KEY_CHECKS = 1;


CREATE TABLE `hv_pm_agv_task_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_code` varchar(32) DEFAULT NULL COMMENT '任务号',
  `request_code` varchar(255) DEFAULT NULL COMMENT '请求编码',
  `start_point` varchar(32) DEFAULT NULL COMMENT '起点',
  `end_point` varchar(32) DEFAULT NULL COMMENT '终点',
  `work_order_code` varchar(32) DEFAULT NULL COMMENT '工单编号',
  `frame_code` varchar(32) DEFAULT NULL COMMENT '料框编号',
  `frame_type_code` varchar(255) DEFAULT NULL COMMENT '料框类型',
  `task_type` int(1) DEFAULT NULL COMMENT '任务类型',
  `agv_code` varchar(32) DEFAULT NULL COMMENT 'AGV编号',
  `material_code` varchar(32) DEFAULT NULL COMMENT '物料号',
  `material_type` varchar(32) DEFAULT NULL COMMENT '物料类型',
  `scheduling_state` int(1) DEFAULT NULL COMMENT '调度状态 0：调度中、1;调度完成、\r\n2：取消 3：失败',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `create_id` varchar(30) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updater_id` varchar(30) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='调度记录';



CREATE TABLE `hv_bm_frame_material` (
  `ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `frame_code` varchar(32) DEFAULT NULL COMMENT '料框编号',
  `material_code` varchar(32) DEFAULT NULL COMMENT '物料号',
  `quailty` int(11) DEFAULT NULL COMMENT '数量',
  `creator_id` varchar(32) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updater_id` varchar(32) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='料框物料缓存';


CREATE TABLE `hv_pm_line_report` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_code` varchar(32) DEFAULT NULL COMMENT '工单号',
  `report_type` varchar(32) DEFAULT NULL COMMENT '报工类型',
  `material_code` varchar(32) DEFAULT NULL COMMENT '物料编码',
  `material_type` varchar(32) DEFAULT NULL COMMENT '物料型号',
  `ship_number` varchar(32) DEFAULT NULL COMMENT '船号',
  `segmentation_code` varchar(32) DEFAULT NULL COMMENT '分段号',
  `line_code` varchar(32) DEFAULT NULL COMMENT '报工产线',
  `station_code` varchar(32) DEFAULT NULL COMMENT '工序(工位)编号',
  `station_name` varchar(32) DEFAULT NULL COMMENT '工序(工位)名称',
  `report_time` datetime DEFAULT NULL COMMENT '上报时间',
  `actual_start_time` datetime DEFAULT NULL COMMENT '实际开始时间',
  `actual_completion_time` datetime DEFAULT NULL COMMENT '实际完成时间',
  `deplete_time` int(11) DEFAULT NULL COMMENT '消耗工时(分)',
  `equipment_code` varchar(32) DEFAULT NULL COMMENT '生产设备编码',
  `report_user_code` varchar(32) DEFAULT NULL COMMENT '报工用户编码',
  `report_user_name` varchar(32) DEFAULT NULL COMMENT '报工用户姓名',
  `report_qty` int(11) DEFAULT NULL COMMENT '报工数量',
  `qualified_qty` int(11) DEFAULT NULL COMMENT '合格数',
  `scrap_qty` int(11) DEFAULT NULL COMMENT '报废数量',
  `repair_qty` int(11) DEFAULT NULL COMMENT '返修数量',
  `report_mes_flag` int(11) DEFAULT NULL COMMENT 'mes报工0：是，1：否',
  `report_mes_time` datetime DEFAULT NULL COMMENT 'mes报工时间',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COMMENT='产线报工记录';

CREATE TABLE `hv_pm_line_report_material` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `report_id` bigint(20) DEFAULT NULL COMMENT '主表ID',
  `material_code` varchar(32) DEFAULT NULL COMMENT '物料号',
  `sn` varchar(64) DEFAULT NULL COMMENT '物料sn码',
  `quality` int(11) DEFAULT NULL COMMENT '数量',
  `qualified_qty` int(11) DEFAULT NULL COMMENT '合格数',
  `loss_qty` int(11) DEFAULT NULL COMMENT '丢失数量',
  `scrap_qty` int(11) DEFAULT NULL COMMENT '报废数量',
  `repair_qty` int(11) DEFAULT NULL COMMENT '返修数量',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COMMENT='产线报工记录详情';