{\rtf1\ansi\ansicpg936\cocoartf2512
\cocoatextscaling0\cocoaplatform0{\fonttbl\f0\fnil\fcharset0 Monaco;}
{\colortbl;\red255\green255\blue255;\red191\green100\blue38;\red32\green32\blue32;\red153\green168\blue186;
}
{\*\expandedcolortbl;;\csgenericrgb\c74902\c39216\c14902;\csgenericrgb\c12549\c12549\c12549;\csgenericrgb\c60000\c65882\c72941;
}
\paperw11900\paperh16840\margl1440\margr1440\vieww10800\viewh8400\viewkind0
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\pardirnatural\partightenfactor0

\f0\fs24 \cf2 \cb3 INSERT INTO \cf4 hv_pm_order_task (\
	create_time\cf2 ,\
	\cf4 creator_id\cf2 ,\
	\cf4 site_num\cf2 ,\
	\cf4 update_time\cf2 ,\
	\cf4 updater_id\cf2 ,\
	\cf4 area_id\cf2 ,\
	\cf4 area_name\cf2 ,\
	\cf4 cell_id\cf2 ,\
	\cf4 cell_name\cf2 ,\
	\cf4 crew_id\cf2 ,\
	\cf4 crew_name\cf2 ,\
	\cf4 end_time\cf2 ,\
	\cf4 equipment_id\cf2 ,\
	\cf4 equipment_name\cf2 ,\
	\cf4 material_code\cf2 ,\
	\cf4 material_name\cf2 ,\
	\cf4 node_code\cf2 ,\
	\cf4 operation_code\cf2 ,\
	\cf4 operation_order\cf2 ,\
	\cf4 order_id\cf2 ,\
	\cf4 route_step_id\cf2 ,\
	\cf4 serial_num\cf2 ,\
	\cf4 shift_id\cf2 ,\
	\cf4 shift_name\cf2 ,\
	\cf4 start_time \cf2 ,\
	\cf4 state\cf2 ,\
	\cf4 work_order_code\cf2 ,\
	\cf4 work_order_quantity\cf2 ,\
	\cf4 operation_id\
) \cf2 SELECT\
	\cf4 create_time\cf2 ,\
	\cf4 creator_id\cf2 ,\
	\cf4 site_num\cf2 ,\
	\cf4 update_time\cf2 ,\
	\cf4 updater_id\cf2 ,\
	\cf4 area_id\cf2 ,\
	\cf4 area_name\cf2 ,\
	\cf4 cell_id\cf2 ,\
	\cf4 cell_name\cf2 ,\
	\cf4 crew_id\cf2 ,\
	\cf4 crew_name\cf2 ,\
	\cf4 end_time\cf2 ,\
	\cf4 equipment_id\cf2 ,\
	\cf4 equipment_name\cf2 ,\
	\cf4 material_code\cf2 ,\
	\cf4 material_name\cf2 ,\
	\cf4 node_code\cf2 ,\
	\cf4 operation_code\cf2 ,\
	\cf4 operation_order\cf2 ,\
	\cf4 order_id\cf2 ,\
	\cf4 route_step_id\cf2 ,\
	\cf4 serial_num\cf2 ,\
	\cf4 shift_id\cf2 ,\
	\cf4 shift_name\cf2 ,\
	\cf4 start_time \cf2 ,\
	\cf4 state\cf2 ,\
	\cf4 work_order_code\cf2 ,\
	\cf4 work_order_quantity\cf2 ,\
	\cf4 id\
\cf2 FROM\
	\cf4 order_manage.hv_pm_order_operation\cf2 ;\
commit;\
\
\
UPDATE  \cf4 order_manage.hv_pm_operation_material \cf2 as \cf4 m\cf2 , \cf4 order_manage.hv_pm_order_task \cf2 as \cf4 t\
\cf2 SET  \cf4 m.task_id = t.id \
\cf2 WHERE\
	\cf4 t.operation_id = m.operation_id\cf2 ;\
commit;\
\
\
UPDATE  \cf4 order_manage.hv_pm_operation_out_put_material \cf2 as \cf4 m\cf2 , \cf4 order_manage.hv_pm_order_task \cf2 as \cf4 t\
\cf2 SET  \cf4 m.task_id = t.id \
\cf2 WHERE\
	\cf4 t.operation_id = m.operation_id\cf2 ;\
commit;\
\
\
UPDATE  \cf4 order_manage.hv_pm_operation_parameter \cf2 as \cf4 m\cf2 , \cf4 order_manage.hv_pm_order_task \cf2 as \cf4 t\
\cf2 SET  \cf4 m.task_id = t.id \
\cf2 WHERE\
	\cf4 t.operation_id = m.operation_id\cf2 ;\
commit;\
\
UPDATE  \cf4 order_manage.hv_pm_procedure_record \cf2 as \cf4 m\cf2 , \cf4 order_manage.hv_pm_order_task \cf2 as \cf4 t\
\cf2 SET  \cf4 m.task_id = t.id \
\cf2 WHERE\
	\cf4 t.operation_id = m.operation_id\cf2 ;\
commit;\
\
\
\
\
\
\
\
}