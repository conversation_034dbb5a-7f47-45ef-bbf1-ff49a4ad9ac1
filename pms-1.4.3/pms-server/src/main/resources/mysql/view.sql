DROP TABLE if exists material_by_crew;
DROP VIEW if exists material_by_crew;

CREATE VIEW  material_by_crew AS SELECT
t1.crew_name AS crew_name,
t2.id AS operation_oid,
t3.out_put_count AS out_put_count,
t3.material_id AS material_id,
t1.actual_end_time AS actual_end_time,
t1.actual_start_time AS actual_start_time,
t1.cell_id AS cell_id,
t2.operation_code AS operation_code,
t2.start_time AS start_time,
t2.end_time AS end_time,
t3.material_name AS material_name,
t3.material_code AS material_code
FROM
	( ( hv_pm_work_order t1 LEFT JOIN hv_pm_order_task t2 ON t1.id = t2.order_id ) LEFT JOIN hv_pm_operation_out_put_material t3 ON t2.id = t3.operation_id );

----------------
DROP TABLE if exists material_consume;
DROP VIEW if exists material_consume;
CREATE VIEW material_consume AS SELECT
	t1.material_code AS material_code,
	t1.material_name AS material_name,
	t1.material_eigenvalue AS material_eigenvalue,
	t1.material_id AS material_id,
	t1.actual_count AS actual_count,
	t1.plan_count AS plan_count,
	t1.operation_id AS operation_id,
	t2.operation_code AS operation_code,
	t3.plan_start_time AS plan_start_time,
	t3.plan_end_time AS plan_end_time,
	t3.work_order_code AS work_order_code,
	t3.crew_name AS crew_name,
	t3.actual_end_time AS actual_end_time,
	t3.actual_start_time AS actual_start_time,
	t3.cell_id AS cell_id
FROM
( ( hv_pm_operation_material t1 JOIN hv_pm_order_operation t2 ON  t1.operation_id = t2.id ) JOIN hv_pm_work_order t3 on t2.order_id = t3.id );

-- ----------------------------
DROP TABLE if exists material_form;
DROP VIEW  if exists material_form;
CREATE VIEW material_form as SELECT
	t1.material_code AS material_code,
	t1.material_name AS material_name,
	t1.material_eigenvalue AS material_eigenvalue,
	t1.material_id AS material_id,
	t1.actual_count AS actual_count,
	t1.plan_count AS plan_count,
	t1.operation_id AS operation_id,
	t2.operation_code AS operation_code,
	t3.plan_start_time AS plan_start_time,
	t3.plan_end_time AS plan_end_time,
	t3.work_order_code AS work_order_code,
	t3.crew_name AS crew_name,
	t3.actual_end_time AS actual_end_time,
	t3.actual_start_time AS actual_start_time,
	t3.cell_id AS cell_id
FROM
	( ( hv_pm_operation_material t1  JOIN hv_pm_order_operation t2 on t1.operation_id = t2.id ) JOIN hv_pm_work_order t3 on t2.order_id =t3.id );


-------
DROP TABLE  if exists work_time_crew;
DROP VIEW if exists work_time_crew;
CREATE VIEW work_time_crew AS SELECT
	t1.crew_name AS crew_name,
	t2.id AS operation_oid,
	t1.cell_id AS cell_id,
	t2.operation_code AS operation_code,
	t2.start_time AS start_time,
	t2.end_time AS end_time,
	timestampdiff ( MINUTE, t2.start_time, t2.end_time ) AS work_time
FROM
	( hv_pm_work_order t1 JOIN hv_pm_order_task t2 on t1.id = t2.order_id  )