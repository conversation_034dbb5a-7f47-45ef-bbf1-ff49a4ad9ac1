<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.HvPmCallFrameMaterialMapper">

    <select id="getPage" resultType="com.hvisions.pms.entity.plan.HvPmCallFrameMaterial"
            parameterType="com.hvisions.pms.dto.HvPmCallMaterialDTO">
        SELECT
        tp.id,
        tp.material_code AS materialCode,
        tp.frame_code AS frameCode,
        tp.warehouse_code AS warehouseCode,
        tp.quantity,
        tp.used_quantity AS usedQuantity,
        tp.remaining_quantity AS remainingQuantity,
        tp.occupation_quantity AS occupationQuantity,
        tp.work_order_code AS workOrderCode,
        tp.is_present AS isPresent,
        tp.status,
        tp.create_time AS createTime,
        tp.update_time AS updateTime,
        tp.block_code AS blockCode,
        tp.ship_no AS shipNo
        FROM
        hv_pm_call_frame_material tp
        <where>
            <if test="query.materialCode != null">
                AND tp.material_code = #{query.materialCode}
            </if>
            <if test="query.frameCode != null">
                AND tp.frame_code = #{query.frameCode}
            </if>
            <if test="query.blockCode != null">
                AND tp.block_code = #{query.blockCode}
            </if>
            <if test="query.shipNo != null">
                AND tp.ship_no = #{query.shipNo}
            </if>
        </where>
    </select>
</mapper>