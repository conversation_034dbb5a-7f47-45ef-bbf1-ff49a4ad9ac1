<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.RouteParamMapper">
    <select id="findByQuery" resultType="com.hvisions.pms.entity.HvRouteParamData">
        select id,
               record_time,
               status,
               record_type,
               operation_id,
               position_id,
               position_type
        from hv_route_param_data
        where operation_id = #{operationId}
          and record_type = #{recordType}
          and position_id = #{positionId}
          and position_type = #{positionType}
        order by record_time desc
    </select>
</mapper>