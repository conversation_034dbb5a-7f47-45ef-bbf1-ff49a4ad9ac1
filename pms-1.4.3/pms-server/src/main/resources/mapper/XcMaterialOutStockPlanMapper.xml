<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.XcMaterialOutStockPlanMapper">
    <select id="getPage" resultType="com.hvisions.pms.plan.XcMaterialOutStockPlanDTO">
        select p.*,u.user_name from  hv_pm_xc_material_out_stock_plan p
        left join framework.sys_user u
        on p.send_user_id=u.id
        <where>
            <if test="query.taskNo != null">
                and task_no like concat('%',#{query.taskNo},'%')
            </if>
            <if test="query.workOrderCode !=null">
                and work_order_code like concat('%',#{query.workOrderCode},'%')
            </if>
            <if test="query.materialCode != null">
                and material_code like concat('%',#{query.materialCode},'%')
            </if>
            <if test="query.status != null">
                and status = #{query.status}
            </if>
        </where>
    </select>
    <select id="getByWorkOrderCode" resultType="com.hvisions.pms.entity.plan.HvPmXcMaterialOutStockPlan">
        SELECT
            *
        FROM
            hv_pm_xc_material_out_stock_plan
        WHERE
            work_order_code = #{workOrderCode}
        ORDER BY
            id ASC
    </select>
    <select id="getByWorkOrderCodeAndSepces" resultType="com.hvisions.pms.plan.XcMaterialOutStockPlanDTO">
        SELECT
            *
        FROM
            hv_pm_xc_material_out_stock_plan
        WHERE
            work_order_code = #{workOrderCode} and sepces = #{sepces}
            LIMIT 1;
    </select>
</mapper>