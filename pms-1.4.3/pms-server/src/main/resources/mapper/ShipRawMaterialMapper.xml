<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.ShipRawMaterialMapper">
    <select id="getPage" resultType="com.hvisions.pms.dto.ShipRawMaterialDTO">
        select * from hv_pm_ship_raw_material
        <where>
            <if test="query.shipModel != null">
                and ship_model like concat('%',#{query.shipModel},'%')
            </if>
            <if test="query.segmentationCode != null">
                and segmentation_code like concat('%',#{query.segmentationCode},'%')
            </if>
        </where>
    </select>
</mapper>