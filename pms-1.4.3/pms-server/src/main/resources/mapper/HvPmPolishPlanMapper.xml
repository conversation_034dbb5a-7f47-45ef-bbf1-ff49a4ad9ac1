<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.HvPmPolishPlanMapper">
    <!-- 定义resultMap，映射数据库字段到Java对象的属性 -->
    <resultMap id="polishPlanResultMap" type="com.hvisions.pms.plan.HvPmPolishPlanDTO">
        <id property="id" column="id" />
        <result property="work_order_code" column="work_order_code" />
        <result property="pod_code" column="pod_code" />
        <result property="line_code" column="line_code" />
        <result property="ship_number" column="ship_number" />
        <result property="segmentation_code" column="segmentation_code" />
        <result property="station_type" column="station_type" />
        <result property="station_name" column="station_name" />
        <result property="batch_no" column="batch_no" />

        <result property="actual_start_time" column="actual_start_time" />
        <result property="actual_completion_time" column="actual_completion_time" />
        <result property="deplete_time" column="deplete_time" />
        <result property="equipment_code" column="equipment_code" />
        <result property="report_user_code" column="report_user_code" />
        <result property="report_user_name" column="report_user_name" />
    </resultMap>
    <update id="upPolishPlanStatusByCode">
        update hv_pm_polish_plan set status =#{status}  where code = #{code}
    </update>

    <select id="getPage" resultMap="polishPlanResultMap">
        select p.*,u.user_name
        from hv_pm_polish_plan p
        left join framework.sys_user u
        on p.send_user_id=u.id
        <where>
            <if test="query.code != null">
                and code like concat('%',#{query.code},'%')
            </if>
            <if test="query.work_order_code != null and query.work_order_code !='' ">
                and work_order_code like concat('%',#{query.work_order_code},'%')
            </if>
            <if test="query.material_code != null and query.material_code != ''">
                and code in (
                  select code from hv_pm_polish_plan_detail0
                  where material_code = #{query.material_code}
                )
            </if>
            <if test="query.pod_code != null and query.pod_code !='' ">
                and pod_code = #{query.pod_code}
            </if>
            <if test="query.lineId != null">
                and line_id = #{query.lineId}
            </if>
            <if test="query.ship_number != null and query.ship_number !='' ">
                and ship_number = #{query.ship_number}
            </if>
            <if test="query.segmentation_code != null and query.segmentation_code !='' ">
                and segmentation_code like concat('%',#{query.segmentation_code},'%')
            </if>
            <if test="query.station_type != null and query.station_type !='' ">
                and station_type = #{query.station_type}
            </if>
            <if test="query.sendBeginTime != null">
                and send_time >= #{query.sendBeginTime}
            </if>
            <if test="query.sendEndTime != null">
                and send_time &lt;= #{query.sendEndTime}
            </if>
            <if test="query.completionStartTime != null">
                and actual_completion_time >= #{query.completionStartTime}
            </if>
            <if test="query.completionEndTime != null">
                and actual_completion_time &lt;= #{query.completionEndTime}
            </if>

            <if test="query.status != null">
                and status = #{query.status}
            </if>
        </where>
    </select>
    <select id="getByWorkOrderCode" resultMap="polishPlanResultMap">
        select * from hv_pm_polish_plan where work_order_code = #{workOrderCode}
    </select>

</mapper>