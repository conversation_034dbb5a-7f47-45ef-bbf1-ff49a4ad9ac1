<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.ProductWorkOrderMapper">

    <!-- 分页查询 -->
    <select id="getPage" resultType="com.hvisions.pms.dto.productWorkOrder.ProductWorkOrderDTO">
        SELECT
        product_work_order_code AS productWorkOrderCode,
        product_work_order_attribute AS productWorkOrderAttribute,
        product_name AS productName,
        product_code AS productCode,
        plan_quantity AS planQuantity,
        production_line_code AS productionLineCode,
        ship_code AS shipCode,
        plan_start_time AS planStartTime,
        plan_end_time AS planEndTime,
        is_collect AS isCollect
        FROM
        hv_pm_product_work_order
        <where>
            <if test="query.productWorkOrderCode != null and query.productWorkOrderCode != ''">
                AND product_work_order_code LIKE CONCAT('%', #{query.productWorkOrderCode}, '%')
            </if>
            <if test="query.productWorkOrderAttribute != null and query.productWorkOrderAttribute != ''">
                AND product_work_order_attribute LIKE CONCAT('%', #{query.productWorkOrderAttribute}, '%')
            </if>
            <if test="query.productName != null and query.productName != ''">
                AND product_name LIKE CONCAT('%', #{query.productName}, '%')
            </if>
            <if test="query.productCode != null and query.productCode != ''">
                AND product_code LIKE CONCAT('%', #{query.productCode}, '%')
            </if>
            <if test="query.productionLineCode != null and query.productionLineCode != ''">
                AND production_line_code LIKE CONCAT('%', #{query.productionLineCode}, '%')
            </if>
            <if test="query.shipCode != null and query.shipCode != ''">
                AND ship_code LIKE CONCAT('%', #{query.shipCode}, '%')
            </if>
            <if test="query.planStartTime != null and query.planEndTime != null">
                AND plan_start_time BETWEEN #{query.planStartTime} AND #{query.planEndTime}
            </if>
            <if test="query.isCollect != null">
                AND is_collect = #{query.isCollect}
            </if>

        </where>
    </select>

    <sql id="queryCondition">
        product_work_order_code  productWorkOrderCode,
        product_work_order_attribute  productWorkOrderAttribute,
        product_name  productName,
        product_code  productCode,
        plan_quantity  planQuantity,
        production_line_code  productionLineCode,
        ship_code  shipCode,
        plan_start_time  planStartTime,
        plan_end_time  planEndTime,
        is_collect  isCollect,
        update_time  updateTime,
        create_time  createTime,
        creator_name  creatorName,
        updater_name  updaterName
    </sql>

    <select id="findListByCondition" resultType="com.hvisions.pms.entity.productWorkOrder.ProductWorkOrder">
        SELECT
        <include refid="queryCondition"/>
        FROM
        hv_pm_product_work_order
        <where>
            <if test="query.productWorkOrderCode != null and query.productWorkOrderCode != ''">
                AND product_work_order_code LIKE CONCAT('%', #{query.productWorkOrderCode}, '%')
            </if>
            <if test="query.productWorkOrderAttribute != null and query.productWorkOrderAttribute != ''">
                AND product_work_order_attribute LIKE CONCAT('%', #{query.productWorkOrderAttribute}, '%')
            </if>
            <if test="query.productName != null and query.productName != ''">
                AND product_name LIKE CONCAT('%', #{query.productName}, '%')
            </if>
            <if test="query.productCode != null and query.productCode != ''">
                AND product_code LIKE CONCAT('%', #{query.productCode}, '%')
            </if>
            <if test="query.productionLineCode != null and query.productionLineCode != ''">
                AND production_line_code LIKE CONCAT('%', #{query.productionLineCode}, '%')
            </if>
            <if test="query.shipCode != null and query.shipCode != ''">
                AND ship_code LIKE CONCAT('%', #{query.shipCode}, '%')
            </if>
            <if test="query.planStartTime != null and query.planEndTime != null">
                AND plan_start_time BETWEEN #{query.planStartTime} AND #{query.planEndTime}
            </if>
            <if test="query.isCollect != null">
                AND is_collect = #{query.isCollect}
            </if>

        </where>
    </select>
</mapper>