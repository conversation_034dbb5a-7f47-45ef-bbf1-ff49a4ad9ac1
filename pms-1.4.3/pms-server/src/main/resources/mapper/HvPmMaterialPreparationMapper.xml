<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.HvPmMaterialPreparationMapper">
    <select id="getPage" resultType="com.hvisions.pms.dto.HvPmMaterialPreparationDTO">
        select  * from hv_pm_material_preparation
        <where>
            <if test="query.workOrderCode != null">
                and work_order_code like concat('%',#{query.workOrderCode},"%")
            </if>
            <if test="query.status != null">
                and status = #{query.status}
            </if>
            <if test="query.shipCode != null and query.shipCode != ''">
                and ship_code = #{query.shipCode}
            </if>
            <if test="query.segmentationCode != null and query.segmentationCode != ''">
                and  task_code like concat('%',#{query.segmentationCode},'%')
            </if>
            <if test="query.taskCode != null and query.taskCode != ''">
                and id in (
                select preparation_id from hv_pm_material_preparation_detail
                where  task_code like concat('%',#{query.taskCode},'%')
                )
            </if>
        </where>
    </select>

    <select id="getByWorkOrderCode" resultType="com.hvisions.pms.entity.HvPmMaterialPreparation">
        select  * from hv_pm_material_preparation
        where work_order_code = #{workOrderCode}
        order by id
        limit 1;
    </select>
    <select id="getDetailByWorkOrderCodeAndMaterialCode"
            resultType="com.hvisions.pms.dto.HvPmMaterialPreparationDetailDTO">
        SELECT
            d.*
        FROM
            hv_pm_material_preparation p
                LEFT JOIN hv_pm_material_preparation_detail d ON p.id = d.preparation_id
        WHERE
            p.work_order_code = #{workOrderCode}
          AND d.material_code = #{materialCode}
    </select>
    <select id="getListByStatus" resultType="com.hvisions.pms.entity.HvPmMaterialPreparation">
        SELECT
            *
        FROM
            hv_pm_material_preparation p
        WHERE
            p.status = #{status}
    </select>

</mapper>