<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.HvPmXcMaterialOutStockResultMapper">
    <resultMap id="outStockResult" type="com.hvisions.pms.dto.HvPmXcMaterialOutStockResultDTO">
        <id property="id" column="id"></id>
        <result property="taskNo" column="task_no"></result>
        <result property="outTime" column="out_time"></result>
        <result property="operationUserCode" column="operation_user_code"></result>
        <result property="createTime" column="create_time"></result>
        <collection property="detail1List" ofType="com.hvisions.pms.dto.HvPmXcMaterialOutStockResultDetail1DTO">
            <id property="id" column="d_id"></id>
            <result property="resultId" column="result_id"></result>
            <result property="taskNo" column="d_task_no"></result>
            <result property="warehouseCode" column="warehouse_code"></result>
            <result property="locationCode" column="location_code"></result>
            <result property="materialCode" column="material_code"></result>
            <result property="batchCode" column="batch_code"></result>
            <result property="quality" column="quality"></result>
            <result property="palletCode" column="pallet_code"></result>
        </collection>
    </resultMap>

    <select id="getByTaskNo" resultMap="outStockResult">
        select
            r.id,
            r.task_no,
            r.out_time,
            r.operation_user_code,
            r.create_time,
            d.id d_id,
            d.result_id,
            d.task_no d_task_no,
            d.warehouse_code,
            d.location_code,
            d.material_code,
            d.batch_code,
            d.quality,
            d.pallet_code
        from hv_pm_xc_material_out_stock_result r
        left join hv_pm_xc_material_out_stock_result_detail1 d
        on r.id = d.result_id
        where r.task_no = #{taskNo}
    </select>
    <select id="getHvPmXcMaterialOutStockResultDetail1ByWorkOrderCode"
            resultType="com.hvisions.pms.entity.HvPmXcMaterialOutStockResultDetail1">
        SELECT
            srd.id id,
            srd.result_id resultId,
            srd.task_no taskNo,
            srd.warehouse_code warehouseCode,
            srd.location_code locationCode,
            srd.material_code materialCode,
            srd.batch_code batchCode,
            srd.quality quality,
            srd.pallet_code palletCode,
            srd.create_time createTime,
            srd.operation_user_code operationUserCode,
            srd.out_time outTime,
            srd.total_quality totalQuality
        FROM
            hv_pm_xc_material_out_stock_plan sp
                LEFT JOIN hv_pm_xc_material_out_stock_result_detail1 srd  ON  srd.task_no  = sp.task_no
        WHERE
            sp.work_order_code = #{workOrderCode}
        ORDER BY
            srd.out_time desc
    </select>
    <select id="getByTaskNoList" resultType="com.hvisions.pms.dto.HvPmXcMaterialOutStockResultDetail1DTO">
       SELECT
           *
       FROM
           hv_pm_xc_material_out_stock_result_detail1 d1
        WHERE task_no IN 
        <foreach  item="taskNo" index="index" collection="taskNoList" open="(" separator="," close=")">
            #{taskNo}
        </foreach>
    </select>
</mapper>