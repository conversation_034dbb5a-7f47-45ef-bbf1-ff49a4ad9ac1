<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.CutPlanReportMapper">

    <select id="getOneDataSteelCutByLineId" resultType="java.lang.Integer">
        SELECT
            COUNT( id ) AS finishCount
        FROM
            hv_pm_material_cut_plan
        WHERE
            DATE ( finish_time ) = #{date}
            AND line_id = #{lineId};
    </select>

    <select id="getOneDataSteelPlanCutByLineId" resultType="java.lang.Integer">
        SELECT
            COUNT(id) AS planCount
        FROM
            hv_pm_material_cut_plan
        WHERE
            DATE(send_time) = #{date}
            AND line_id =#{lineId}
    </select>


    <select id="getCutSevenDataByLineId" resultType="com.hvisions.pms.dto.HomeSevenDataDTO">
        SELECT
            COALESCE(SUM(CASE WHEN DATE(finish_time) = #{date} THEN 1 ELSE 0 END), 0) AS `today`,
            COALESCE(SUM(CASE WHEN DATE(finish_time) = DATE_SUB(#{date}, INTERVAL 1 DAY) THEN 1 ELSE 0 END), 0) AS `yesterday`,
            COALESCE(SUM(CASE WHEN DATE(finish_time) = DATE_SUB(#{date}, INTERVAL 2 DAY) THEN 1 ELSE 0 END), 0) AS `twoDaysAgo`,
            COALESCE(SUM(CASE WHEN DATE(finish_time) = DATE_SUB(#{date}, INTERVAL 3 DAY) THEN 1 ELSE 0 END), 0) AS `threeDaysAgo`,
            COALESCE(SUM(CASE WHEN DATE(finish_time) = DATE_SUB(#{date}, INTERVAL 4 DAY) THEN 1 ELSE 0 END), 0) AS `fourDaysAgo`,
            COALESCE(SUM(CASE WHEN DATE(finish_time) = DATE_SUB(#{date}, INTERVAL 5 DAY) THEN 1 ELSE 0 END), 0) AS `fiveDaysAgo`,
            COALESCE(SUM(CASE WHEN DATE(finish_time) = DATE_SUB(#{date}, INTERVAL 6 DAY) THEN 1 ELSE 0 END), 0) AS `sixDaysAgo`
        FROM hv_pm_material_cut_plan
        WHERE
            line_id = #{lineId}  AND
            DATE(finish_time) BETWEEN DATE_SUB(#{date}, INTERVAL 6 DAY) AND #{date};
    </select>

    <select id="getOneWeekSteelCutByLineId" resultType="java.lang.Integer">
        SELECT
            COUNT(id) AS finishCount
        FROM
            hv_pm_material_cut_plan
        WHERE
            DATE(finish_time) BETWEEN (
        -- 获取指定日期所在周的周一日期
            DATE_SUB(#{date}, INTERVAL DAYOFWEEK(#{date}) - 1 DAY)
            ) AND (
        -- 获取指定日期所在周的周日日期
            DATE_ADD(#{date}, INTERVAL 7 - DAYOFWEEK(#{date}) DAY)
            )
          AND line_id = #{lineId};
    </select>

    <select id="getOneMonthSteelCutByLineId" resultType="java.lang.Integer">
        SELECT
            COUNT(id) AS planCount
        FROM
            hv_pm_material_cut_plan
        WHERE
            YEAR(finish_time) = YEAR(#{date}) AND
            MONTH(finish_time) = MONTH(#{date}) AND
            line_id = #{lineId};
    </select>

    <select id="getOneYearSteelCutByLineId" resultType="java.lang.Integer">
        SELECT
            COUNT(id) AS planCount
        FROM
            hv_pm_material_cut_plan
        WHERE
            YEAR(finish_time) = YEAR(#{date}) AND
            line_id = #{lineId};
    </select>

    <select id="getOneWeekSteelPlanCutByLineId" resultType="java.lang.Integer">
        SELECT
            COUNT(id) AS planCount
        FROM
            hv_pm_material_cut_plan
        WHERE
            DATE(send_time) BETWEEN (
        -- 获取指定日期所在周的周一日期
            DATE_SUB(#{date}, INTERVAL DAYOFWEEK(#{date}) - 1 DAY)
            ) AND (
        -- 获取指定日期所在周的周日日期
            DATE_ADD(#{date}, INTERVAL 7 - DAYOFWEEK(#{date}) DAY)
            )
          AND line_id = #{lineId};
    </select>

    <select id="getOneMonthSteelPlanCutByLineId" resultType="java.lang.Integer">
        SELECT
            COUNT(id) AS planCount
        FROM
            hv_pm_material_cut_plan
        WHERE
            YEAR(send_time) = YEAR(#{date}) AND
            MONTH(send_time) = MONTH(#{date}) AND
            line_id = #{lineId};
    </select>

    <select id="getOneYearSteelPlanCutByLineId" resultType="java.lang.Integer">
        SELECT
            COUNT(id) AS planCount
        FROM
            hv_pm_material_cut_plan
        WHERE
            YEAR(send_time) = YEAR(#{date}) AND
            line_id = #{lineId};
    </select>



    <select id="getOneDataSteelPartCutByLineId" resultType="java.lang.Integer">
        SELECT
            COALESCE(sum(quality*cut_quality),0)  AS finishCount
        FROM
            hv_pm_material_cut_plan
        WHERE
            DATE ( finish_time ) = #{date}
          AND line_id = #{lineId};
    </select>

    <select id="getOneDataSteelPartPlanCutByLineId" resultType="java.lang.Integer">
        SELECT
            COALESCE(sum(quality*cut_quality),0) AS finishCount
        FROM
            hv_pm_material_cut_plan
        WHERE
            DATE ( send_time ) = #{date}
          AND line_id = #{lineId};
    </select>

    <select id="getPartCutSevenDataByLineId" resultType="com.hvisions.pms.dto.HomeSevenDataDTO">
        SELECT
            COALESCE(SUM(CASE WHEN DATE(finish_time) = #{date} THEN quality*cut_quality ELSE 0 END), 0) AS `today`,
            COALESCE(SUM(CASE WHEN DATE(finish_time) = DATE_SUB(#{date}, INTERVAL 1 DAY) THEN quality*cut_quality ELSE 0 END), 0) AS `yesterday`,
            COALESCE(SUM(CASE WHEN DATE(finish_time) = DATE_SUB(#{date}, INTERVAL 2 DAY) THEN quality*cut_quality ELSE 0 END), 0) AS `twoDaysAgo`,
            COALESCE(SUM(CASE WHEN DATE(finish_time) = DATE_SUB(#{date}, INTERVAL 3 DAY) THEN quality*cut_quality ELSE 0 END), 0) AS `threeDaysAgo`,
            COALESCE(SUM(CASE WHEN DATE(finish_time) = DATE_SUB(#{date}, INTERVAL 4 DAY) THEN quality*cut_quality ELSE 0 END), 0) AS `fourDaysAgo`,
            COALESCE(SUM(CASE WHEN DATE(finish_time) = DATE_SUB(#{date}, INTERVAL 5 DAY) THEN quality*cut_quality ELSE 0 END), 0) AS `fiveDaysAgo`,
            COALESCE(SUM(CASE WHEN DATE(finish_time) = DATE_SUB(#{date}, INTERVAL 6 DAY) THEN quality*cut_quality ELSE 0 END), 0) AS `sixDaysAgo`
        FROM hv_pm_material_cut_plan
        WHERE
            line_id = #{lineId}  AND
            DATE(finish_time) BETWEEN DATE_SUB(#{date}, INTERVAL 6 DAY) AND #{date};
    </select>

    <select id="getOneWeekSteelPartCutByLineId" resultType="java.lang.Integer">
        SELECT
            COALESCE(sum(quality*cut_quality),0) AS finishCount
        FROM
            hv_pm_material_cut_plan
        WHERE
            DATE(finish_time) BETWEEN (
        -- 获取指定日期所在周的周一日期
            DATE_SUB(#{date}, INTERVAL DAYOFWEEK(#{date}) - 1 DAY)
            ) AND (
        -- 获取指定日期所在周的周日日期
            DATE_ADD(#{date}, INTERVAL 7 - DAYOFWEEK(#{date}) DAY)
            )
          AND line_id = #{lineId};
    </select>

    <select id="getOneMonthSteelPartCutByLineId" resultType="java.lang.Integer">
        SELECT
            COALESCE(sum(quality*cut_quality),0)  AS planCount
        FROM
            hv_pm_material_cut_plan
        WHERE
            YEAR(finish_time) = YEAR(#{date}) AND
            MONTH(finish_time) = MONTH(#{date}) AND
            line_id = #{lineId};
    </select>

    <select id="getOneYearSteelPartCutByLineId" resultType="java.lang.Integer">
        SELECT
            COALESCE(sum(quality*cut_quality),0) AS planCount
        FROM
            hv_pm_material_cut_plan
        WHERE
            YEAR(finish_time) = YEAR(#{date}) AND
            line_id = #{lineId};
    </select>

    <select id="getOneWeekSteelPartPlanCutByLineId" resultType="java.lang.Integer">
        SELECT
            COALESCE(sum(quality*cut_quality),0) AS finishCount
        FROM
            hv_pm_material_cut_plan
        WHERE
            DATE(send_time) BETWEEN (
        -- 获取指定日期所在周的周一日期
            DATE_SUB(#{date}, INTERVAL DAYOFWEEK(#{date}) - 1 DAY)
            ) AND (
        -- 获取指定日期所在周的周日日期
            DATE_ADD(#{date}, INTERVAL 7 - DAYOFWEEK(#{date}) DAY)
            )
          AND line_id = #{lineId};
    </select>

    <select id="getOneMonthSteelPartPlanCutByLineId" resultType="java.lang.Integer">
        SELECT
            COALESCE(sum(quality*cut_quality),0)  AS planCount
        FROM
            hv_pm_material_cut_plan
        WHERE
            YEAR(send_time) = YEAR(#{date}) AND
            MONTH(send_time) = MONTH(#{date}) AND
            line_id = #{lineId};
    </select>

    <select id="getOneYearSteelPartPlanCutByLineId" resultType="java.lang.Integer">
        SELECT
            COALESCE(sum(quality*cut_quality),0) AS planCount
        FROM
            hv_pm_material_cut_plan
        WHERE
            YEAR(send_time) = YEAR(#{date}) AND
            line_id = #{lineId};
    </select>

    <select id="getCuttingLengthByDateRange" resultType="com.hvisions.pms.entity.CuttingResultDTO">
        SELECT
        SUM(lr.cutting_length) AS totalCuttingLength,
        SUM(lr.damaged_length) AS totalDamagedLength
        FROM
        hv_pm_line_report lr
        JOIN hv_pm_material_cut_plan cp ON lr.order_code = cp.cut_plan_code
        WHERE
        lr.report_type = 'GB' AND
        cp.status = '10'
        <choose>
            <when test="dateRange == 'last7Days'">
                AND cp.finish_time &gt;= DATE_SUB(NOW(), INTERVAL 7 DAY)
            </when>
            <when test="dateRange == 'last30Days'">
                AND cp.finish_time &gt;= DATE_SUB(NOW(), INTERVAL 30 DAY)
            </when>
            <when test="dateRange == 'currentWeek'">
                AND YEAR(cp.finish_time) = YEAR(NOW())
                AND WEEK(cp.finish_time, 1) = WEEK(NOW(), 1)

            </when>
            <when test="dateRange == 'currentMonth'">
                AND YEAR(cp.finish_time) = YEAR(NOW())
                AND MONTH(cp.finish_time) = MONTH(NOW())
            </when>
            <when test="dateRange == 'custom'">
                AND cp.finish_time &gt;= #{startDate}
                AND cp.finish_time &lt;= #{endDate}
            </when>
            <otherwise>

            </otherwise>
        </choose>
    </select>

    <select id="getCuttingLengthByDate" resultType="com.hvisions.pms.entity.CuttingResultDTO">
        SELECT
        DATE(cp.finish_time) AS date,
        SUM(lr.cutting_length) AS totalCuttingLength,
        SUM(lr.damaged_length) AS totalDamagedLength
        FROM
        hv_pm_line_report lr
        JOIN hv_pm_material_cut_plan cp ON lr.order_code = cp.cut_plan_code
        WHERE
        lr.report_type = 'GB' AND
        cp.status = '10'
        <choose>
            <when test="dateRange == 'last7Days'">
                AND cp.finish_time &gt;= DATE_SUB(NOW(), INTERVAL 7 DAY)
            </when>
            <when test="dateRange == 'last30Days'">
                AND cp.finish_time &gt;= DATE_SUB(NOW(), INTERVAL 30 DAY)
            </when>
            <otherwise>
                -- 默认无时间限制
            </otherwise>
        </choose>
        GROUP BY DATE(cp.finish_time)
        ORDER BY DATE(cp.finish_time);
    </select>


</mapper>