<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.HvPmCutPlanPassPointMapper">

    <select id="getPage" resultType="com.hvisions.pms.dto.HvPmCutPlanPassPointDTO"
            parameterType="com.hvisions.pms.dto.HvPmCutPlanPassPointDTO">
        select tp.id,tp.pass_time passTime,tp.cut_plan_id cutPlanId,tp.cut_plan_no cutPlanNo,tp.station_code
        stationCode,tp.station_name stationName
        from hv_pm_cut_plan_pass_point tp
        <where>
            <if test="query.cutPlanId != null">
                and tp.cut_plan_id = #{query.cutPlanId}
            </if>
            <if test="query.cutPlanNo != null">
                and tp.cut_plan_no like concat('%',#{query.cutPlanNo},'%')
            </if>
            <if test="query.stationCode != null">
                and tp.station_code like concat('%',#{query.stationCode},'%')
            </if>
            <if test="query.stationName != null">
                and tp.station_name = #{query.stationName}
            </if>
            <if test="query.passTime != null">
                and tp.pass_time = #{query.passTime}
            </if>
        </where>
    </select>
</mapper>