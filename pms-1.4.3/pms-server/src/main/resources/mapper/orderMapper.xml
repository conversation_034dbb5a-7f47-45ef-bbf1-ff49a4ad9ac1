<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.OrderMapper">
    <resultMap id="rm1" type="java.util.HashMap">
    </resultMap>

    <update id="updateWorkOrderLine" parameterType="map" databaseId="mysql">
        update hv_pm_work_order
        set cell_id = #{cellId}
        where work_order_code = #{workOrderCode}
    </update>

    <select id="getOrder" resultMap="rm1">
        SELECT hv_pm_work_order.id orderId,
        hv_pm_work_order.ship_no shipNo,
        hv_pm_work_order.ship_model shipModel,
        hv_pm_work_order.segmentation_code segmentationCode,
        hv_pm_work_order.execute_sequence executeSequence,
        hv_pm_work_order.work_order_code workOrderCode,
        hv_pm_work_order.plan_code planCode,
        hv_pm_work_order.create_time createTime,
        hv_pm_work_order.work_order_state workOrderState,
        hv_pm_work_order.material_code materialCode,
        hv_pm_work_order.material_name materialName,
        hv_pm_work_order.plan_end_time planEndTime,
        hv_pm_work_order.quantity ,
        hv_pm_work_order.route_id routeId,
        hv_pm_work_order.route_name routeName,
        hv_pm_work_order.route_code routeCode,
        hv_pm_work_order.route_version routeVersion,
        hv_pm_work_order.bom_version bomVersion,
        hv_pm_work_order.issued_time issuedTime,
        hv_pm_work_order.actual_end_time acturalEndTime,
        hv_pm_work_order.eigenvalue ,
        hv_pm_work_order.plan_start_time planStartTime,
        hv_pm_work_order.serial_number serialNumber,
        hv_pm_work_order.material_id materialId,
        hv_pm_work_order.actual_start_time actualStartTime,
        hv_pm_work_order.actual_end_time actualEndTime,
        hv_pm_work_order.shift_id shiftId,
        hv_pm_work_order.crew_id crewId,
        hv_pm_work_order.shift_name shiftName,
        hv_pm_work_order.crew_name crewName,
        hv_pm_work_order.bom_id bomId,
        hv_pm_work_order.area_id areaId,
        hv_pm_work_order.cell_id cellId,
        hv_pm_work_order.update_time updateTime,
        hv_pm_work_order.creator_id creatorId,
        hv_pm_work_order.updater_id updaterId,
        hv_pm_work_order.actual_count actualCount,
        hv_pm_work_order.site_num siteNum,
        hv_pm_work_order.plan_or_new planOrNew,
        hv_pm_work_order.order_mode orderMode,
        hv_pm_work_order_extend.*,
        hv_pm_order_type.order_type_code as orderTypeCode,
        hv_pm_order_type.order_type_name as orderTypeName,
        hv_pm_order_type.id as orderTypeId,
        hv_pm_work_plan.plan_start_time as workPlanStartTime,
        hv_pm_work_plan.plan_end_time as workPlanEndTime,
        hv_pm_work_order.come_from as comeFrom,
        hv_pm_work_order.complete_set_check_status as completeSetCheckStatus,
        hv_pm_work_order.block_code as blockCode,
        hv_pm_work_order.out_flag as outFlag,
        hv_pm_work_order.special_purchase_type_code as specialPurchaseTypeCode,
        hv_pm_work_order.parent_work_order_code as parentWorkOrderCode,
        hv_pm_work_order.used_type as usedType,
        CASE
            WHEN hv_pm_work_order.complete_set_check_status >= 1 THEN
            (SELECT bom.complete_time FROM `hv_pm_plan_product_bom` bom WHERE bom.plan_code = hv_pm_work_order.work_order_code ORDER BY bom.complete_time DESC LIMIT 1 ) ELSE NULL
        END AS completeTime
        FROM hv_pm_work_order
        left join hv_pm_work_order_extend on hv_pm_work_order.id = hv_pm_work_order_extend.work_order_id
        left join hv_pm_order_type on hv_pm_work_order.order_type_id = hv_pm_order_type.id
        left join hv_pm_work_plan on hv_pm_work_order.plan_code = hv_pm_work_plan.plan_code
        <where>
            <if test="query.planCodeEqual != null and query.planCodeEqual != ''">
                and hv_pm_work_order.plan_code = #{query.planCodeEqual}
            </if>
            <if test="query.planCodeLike != null and query.planCodeLike != ''">
                and hv_pm_work_order.plan_code like concat ('%',#{query.planCodeLike},'%')
            </if>
            <if test="query.materialId != null">
                and hv_pm_work_order.material_id = #{query.materialId}
            </if>
            <if test="query.planStartTimeStart != null and query.planStartTimeEnd != null">
                and hv_pm_work_order.plan_start_time between #{query.planStartTimeStart} and #{query.planStartTimeEnd}
            </if>
            <if test="query.planEndTimeStart != null and query.planEndTimeEnd != null">
                and hv_pm_work_order.plan_end_time between #{query.planEndTimeStart} and #{query.planEndTimeEnd}
            </if>
            <if test="query.timeAreaStart != null and query.timeAreaEnd != null">
                and hv_pm_work_order.plan_start_time between #{query.timeAreaStart} and #{query.timeAreaEnd}
            </if>
            <if test="query.routeId != null">
                and hv_pm_work_order.route_id = #{query.routeId}
            </if>
            <if test="query.routeCode != null and query.routeCode != ''">
                and hv_pm_work_order.route_code = #{query.routeCode}
            </if>
            <if test="query.materialCodeLike != null and query.materialCodeLike != ''">
                and hv_pm_work_order.material_code like concat('%',#{query.materialCodeLike},'%')
            </if>

            <if test="query.materialCodeEqual != null and query.materialCodeEqual != ''">
                and hv_pm_work_order.material_code = #{query.materialCodeEqual}
            </if>
            <if test="query.actualStartTimeStart != null and query.actualStartTimeEnd != null">
                and hv_pm_work_order.actual_start_time between #{query.actualStartTimeStart} and
                #{query.actualStartTimeEnd}
            </if>
            <if test="query.actualEndTimeStart != null and query.actualEndTimeEnd != null">
                and hv_pm_work_order.actual_end_time between #{query.actualEndTimeStart} and #{query.actualEndTimeEnd}
            </if>

            <if test="query.workOrderCodeEqual != null and query.workOrderCodeEqual != ''">
                and hv_pm_work_order.work_order_code = #{query.workOrderCodeEqual}
            </if>

            <if test="query.workOrderCodeLike != null and query.workOrderCodeLike != ''">
                and hv_pm_work_order.work_order_code like concat('%',#{query.workOrderCodeLike},'%')
            </if>

            <if test="query.issuedTimeStart != null and query.issuedTimeEnd != null">
                and hv_pm_work_order.issued_time between #{query.issuedTimeStart} and #{query.issuedTimeEnd}
            </if>

            <if test="query.shiftId != null">
                and hv_pm_work_order.shift_id = #{query.shiftId}
            </if>
            <if test="query.shiftName != null">
                and hv_pm_work_order.shift_name = #{query.shiftName}
            </if>

            <if test="query.areaId != null">
                and hv_pm_work_order.area_id = #{query.areaId}
            </if>
            <if test="query.cellId != null">
                and hv_pm_work_order.cell_id = #{query.cellId}
            </if>
            <if test="query.crewId != null">
                and hv_pm_work_order.crew_id = #{query.crewId}
            </if>
            <if test="query.crewName != null">
                and hv_pm_work_order.crew_name = #{query.crewName}
            </if>

            <if test="query.bomId != null">
                and hv_pm_work_order.bom_id = #{query.bomId}
            </if>
            <if test="query.planOrNew != null">
                and hv_pm_work_order.plan_or_new = #{query.planOrNew}
            </if>
            <if test="query.orderMode != null">
                and hv_pm_work_order.order_mode = #{query.orderMode}
            </if>
            <if test="query.orderTypeId != null">
                and hv_pm_order_type.id = #{query.orderTypeId}
            </if>
            <if test="query.orderTypeCode != null">
                and hv_pm_order_type.order_type_code like concat('%',#{query.orderTypeCode},'%')
            </if>
            <if test="query.orderTypeName != null">
                and hv_pm_order_type.order_type_name like concat('%',#{query.orderTypeName},'%')
            </if>
            <if test="query.workOrderState != null">
                and hv_pm_work_order.work_order_state = #{query.workOrderState}
            </if>
            <if test="query.comeFrom != null">
                and hv_pm_work_order.come_from = #{query.comeFrom}
            </if>
            <if test="query.workOrderStates !=null and query.workOrderStates.size > 0">
                <foreach collection="query.workOrderStates" index="index" item="item"
                         open="and hv_pm_work_order.work_order_state in("
                         separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.extendQueries !=null and query.extendQueries.size > 0">
                <foreach collection="query.extendQueries" index="index" item="item" open=""
                         separator=""
                         close="">
                    <if test="item.andOr == null or item.andOr == 1">
                        and
                    </if>
                    <if test="item.andOr != null and item.andOr == 2">
                        or
                    </if>
                    hv_pm_work_order_extend.${item.columnName}
                    <if test="item.symbol == null or item.symbol == 1">
                        =
                    </if>
                    <if test="item.symbol != null and item.symbol == 2">
                        &gt;
                    </if>
                    <if test="item.symbol != null and item.symbol == 3">
                        &gt;=
                    </if>
                    <if test="item.symbol != null and item.symbol == 4">
                        &lt;
                    </if>
                    <if test="item.symbol != null and item.symbol == 5">
                        &lt;=
                    </if>
                    <if test="item.symbol != null and item.symbol == 6">
                        is null
                    </if>
                    <if test="item.symbol != null and item.symbol == 7">
                        is not null
                    </if>
                    <if test="item.symbol != null and item.symbol == 8">
                        like
                    </if>
                    <if test="item.value != null ">
                        #{item.value}
                    </if>
                </foreach>
            </if>
            <if test="query.usedType != null">
                and hv_pm_work_order.used_type = #{query.usedType}
            </if>

            <if test="query.shipNo != null and query.shipNo != '' ">
                and hv_pm_work_order.ship_no  like concat('%',#{query.shipNo} ,'%')
            </if>
            <if test="query.shipModel != null  and query.shipModel != ''">
                and hv_pm_work_order.ship_model like concat('%',#{query.shipModel} ,'%')
            </if>
            <if test="query.segmentationCode != null  and query.segmentationCode != ''">
                and hv_pm_work_order.segmentation_code  = #{query.segmentationCode}
            </if>
            <if test="query.routeName != null  and query.routeName != ''">
                and hv_pm_work_order.route_name like concat('%',#{query.routeName},'%')
            </if>
            <if test="query.partMaterialCode !=null and query.partMaterialCode != ''">
                AND EXISTS (
                SELECT 1 FROM hv_pm_plan_product_bom
                WHERE plan_code = hv_pm_work_order.work_order_code
                AND material_code = #{query.partMaterialCode}
                )
            </if>

        </where>
    </select>


    <resultMap id="type" type="com.hvisions.pms.type.OrderTypeMaterialDTO"></resultMap>

    <select id="getTypeMaterial" resultMap="type" parameterType="com.hvisions.pms.type.TypeMaterialQuery"
            databaseId="mysql">
        select t1.*,t2.material_code,t2.material_name
        from hv_pm_order_type_material t1
        left join
        hiper_base.hv_bm_material t2 on t1.material_id =t2.id
        <where>
            <if test="query.materialCode != null">
                t2.material_code like concat('%',#{query.materialCode},'%')
                or
                t2.material_name like concat('%',#{query.materialCode},'%')
            </if>
            <if test="query.orderTypeId != null">
                and t1.order_type_id = #{query.orderTypeId}
            </if>
        </where>
    </select>

    <select id="getTypeMaterial" resultMap="type" parameterType="com.hvisions.pms.type.TypeMaterialQuery"
            databaseId="sqlserver">
        select t1.*,t2.material_code,t2.material_name
        from hv_pm_order_type_material t1
        left join
        hiper_base.dbo.hv_bm_material t2 on t1.material_id =t2.id
        <where>
            <if test="query.materialCode != null">
                t2.material_code like concat('%',#{query.materialCode},'%')
                or
                t2.material_name like concat('%',#{query.materialCode},'%')
            </if>
            <if test="query.orderTypeId != null">
                and t1.order_type_id = #{query.orderTypeId}
            </if>
        </where>
    </select>

    <select id="getMaterial" resultMap="type" parameterType="com.hvisions.pms.type.WorkOrderMaterialQuery"
            databaseId="mysql">
        select t2.material_code,t2.material_name,t2.id as material_id from hiper_base.hv_bm_material t2
        <where>
            and t2.id not in (select t3.material_id from hv_pm_order_type_material t3 left join hv_pm_order_type t4 on
            t3.order_type_id = t4.id where t4.order_type_code &lt;&gt; 'default')
            <if test="dto.keyword !=null">
                and t2.material_code like concat('%',#{dto.keyword},'%')
                or t2.material_name like concat('%',#{dto.keyword},'%')
            </if>
        </where>
    </select>

    <select id="getMaterial" resultMap="type" parameterType="com.hvisions.pms.type.WorkOrderMaterialQuery"
            databaseId="sqlserver">
        select t2.material_code,t2.material_name,t2.id as material_id from hiper_base.dbo.hv_bm_material t2
        <where>
            and t2.id not in (select t3.material_id from hv_pm_order_type_material t3 left join hv_pm_order_type t4 on
            t3.order_type_id = t4.id where t4.order_type_code &lt;&gt; 'default')
            <if test="dto.keyword !=null">
                and t2.material_code like concat('%',#{dto.keyword},'%')
                or t2.material_name like concat('%',#{dto.keyword},'%')
            </if>
        </where>
    </select>


    <select id="findByWorkOrders" resultType="com.hvisions.pms.entity.HvPmWorkOrder">
        select
        route_id routeId
        from hv_pm_work_order
        where work_order_code in
        <foreach item="item" collection="workOrderCodes" separator="," open="(" close=")" >
            #{item}
        </foreach>
        group by route_id

    </select>

    <select id="getPlanCodeByMaterialIds" resultType="java.lang.String">
      select
      plan_code planCode
      from hv_pm_work_order
      where work_order_state = 0 and material_id in
      <foreach collection="materialIdList" item="id" open="(" separator="," close=")">
          #{id}
      </foreach>
        group by plan_code
    </select>

    <select id="getWorkOrderCountByBatchNo" resultType="java.lang.Integer">
      select
      count(1)
      from hv_pm_work_order
      where plan_code = #{batchNo}
    </select>
    <select id="getWorkOrderByPlanCode" resultType="com.hvisions.pms.entity.HvPmWorkOrder"
            parameterType="java.lang.String">
        select * from hv_pm_work_order where plan_code = #{planCode}
    </select>

    <select id="updateCompleteSetCheckStatusByWorkOrderCode">
        update
        hv_pm_work_order
        set complete_set_check_status = #{completeSetCheckStatus} where work_order_code = #{workOrderCode}
    </select>
    <select id="getByWorkOrderCode" resultType="com.hvisions.pms.entity.HvPmWorkOderTao">
        SELECT
            ship_model,
            segmentation_code,
            COUNT(*) AS quantity
        FROM
            hv_pm_work_order
        WHERE
            plan_code = #{planCode}
        GROUP BY
            ship_model,
            segmentation_code
    </select>



    <resultMap id="m1" type="com.hvisions.pms.dto.WorkOrderDTO">
        <result property="workOrderCode" column="work_order_code"/>
        <association property="bomDTOList" column="work_order_code" select="getProductBomByWorkOrder"/>
    </resultMap>

    <select id="findNotIssueParentWorkOrderList" resultMap="m1">
        select *
        from hv_pm_work_order
        where used_type = 0 and work_order_state = 0
    </select>

    <select id="getProductBomByWorkOrder" resultType="com.hvisions.pms.dto.HvPmPlanProductBomDTO">
        select * from hv_pm_plan_product_bom where plan_code =#{workOrderCode} and frame_code is null
    </select>

    <select id="getTodayCountByUsedType" resultType="com.hvisions.pms.dto.TodayWorkOrderCountDTO">
        SELECT
            COALESCE(SUM(CASE WHEN DATE(plan_start_time) = CURDATE() THEN quantity ELSE 0 END), 0) AS `count`,
            COALESCE(SUM(CASE WHEN DATE(actual_end_time) = CURDATE() AND work_order_state = 5 THEN quantity ELSE 0 END), 0) AS `complete`
        FROM
            hv_pm_work_order
        WHERE
            used_type = #{usedType}
    </select>

    <select id="getSevenDataByRouteName" resultType="com.hvisions.pms.dto.HomeSevenDataDTO">
        SELECT
            COALESCE(SUM(CASE WHEN DATE(actual_end_time) = #{date} AND work_order_state = 5 THEN quantity ELSE 0 END), 0) `today`,
            COALESCE(SUM(CASE WHEN DATE(actual_end_time) = DATE_SUB(#{date}, INTERVAL 1 DAY) AND work_order_state = 5 THEN quantity ELSE 0 END), 0) `yesterday`,
            COALESCE(SUM(CASE WHEN DATE(actual_end_time) = DATE_SUB(#{date}, INTERVAL 2 DAY) AND work_order_state = 5 THEN quantity ELSE 0 END), 0) `twoDaysAgo`,
            COALESCE(SUM(CASE WHEN DATE(actual_end_time) = DATE_SUB(#{date}, INTERVAL 3 DAY) AND work_order_state = 5 THEN quantity ELSE 0 END), 0) `threeDaysAgo`,
            COALESCE(SUM(CASE WHEN DATE(actual_end_time) = DATE_SUB(#{date}, INTERVAL 4 DAY) AND work_order_state = 5 THEN quantity ELSE 0 END), 0) `fourDaysAgo`,
            COALESCE(SUM(CASE WHEN DATE(actual_end_time) = DATE_SUB(#{date}, INTERVAL 5 DAY) AND work_order_state = 5 THEN quantity ELSE 0 END), 0) `fiveDaysAgo`,
            COALESCE(SUM(CASE WHEN DATE(actual_end_time) = DATE_SUB(#{date}, INTERVAL 6 DAY) AND work_order_state = 5 THEN quantity ELSE 0 END), 0) `sixDaysAgo`
        FROM hv_pm_work_order
        WHERE route_name LIKE CONCAT('%',#{routeName},'%')
            AND DATE(actual_end_time) >= DATE_SUB(#{date}, INTERVAL 6 DAY);
    </select>

    <select id="getTodayDataByRouteName" resultType="java.lang.Integer">
        SELECT
            COALESCE(SUM(quantity),0) `actCount`
        FROM hv_pm_work_order
        WHERE DATE(actual_end_time) = #{date}
            AND route_name LIKE CONCAT('%',#{routeName},'%')
            AND work_order_state = 5
    </select>
    <select id="findCompletenessOrders" resultType="com.hvisions.pms.dto.WorkOrderDTO">
        select * from hv_pm_work_order where complete_set_check_status = 1 and work_order_state = 0
    </select>
    <select id="getTodayDataPlanByRouteName" resultType="java.lang.Integer">
        SELECT
            COALESCE(SUM(quantity),0) `actCount`
        FROM hv_pm_work_order
        WHERE DATE(plan_start_time) = #{date}
          AND route_name LIKE CONCAT('%',#{routeName},'%')
    </select>

    <select id="getWorkOrderExportList" resultType="com.hvisions.pms.exportdto.WorkOrderExportDTO">
            SELECT hv_pm_work_order.id orderId,
            hv_pm_work_order.ship_no shipNo,
            hv_pm_work_order.ship_model shipModel,
            hv_pm_work_order.segmentation_code segmentationCode,
            hv_pm_work_order.execute_sequence executeSequence,
            hv_pm_work_order.work_order_code workOrderCode,
            hv_pm_work_order.plan_code planCode,
            hv_pm_work_order.create_time createTime,
            hv_pm_work_order.work_order_state workOrderState,
            hv_pm_work_order.material_code materialCode,
            hv_pm_work_order.material_name materialName,
            hv_pm_work_order.plan_end_time planEndTime,
            hv_pm_work_order.quantity ,
            hv_pm_work_order.route_id routeId,
            hv_pm_work_order.route_name routeName,
            hv_pm_work_order.route_code routeCode,
            hv_pm_work_order.route_version routeVersion,
            hv_pm_work_order.bom_version bomVersion,
            hv_pm_work_order.issued_time issuedTime,
            hv_pm_work_order.actual_end_time acturalEndTime,
            hv_pm_work_order.eigenvalue ,
            hv_pm_work_order.plan_start_time planStartTime,
            hv_pm_work_order.serial_number serialNumber,
            hv_pm_work_order.material_id materialId,
            hv_pm_work_order.actual_start_time actualStartTime,
            hv_pm_work_order.actual_end_time actualEndTime,
            hv_pm_work_order.shift_id shiftId,
            hv_pm_work_order.crew_id crewId,
            hv_pm_work_order.shift_name shiftName,
            hv_pm_work_order.crew_name crewName,
            hv_pm_work_order.bom_id bomId,
            hv_pm_work_order.area_id areaId,
            hv_pm_work_order.cell_id cellId,
            hv_pm_work_order.update_time updateTime,
            hv_pm_work_order.creator_id creatorId,
            hv_pm_work_order.updater_id updaterId,
            hv_pm_work_order.actual_count actualCount,
            hv_pm_work_order.site_num siteNum,
            hv_pm_work_order.plan_or_new planOrNew,
            hv_pm_work_order.order_mode orderMode,
            hv_pm_work_order_extend.*,
            hv_pm_order_type.order_type_code as orderTypeCode,
            hv_pm_order_type.order_type_name as orderTypeName,
            hv_pm_order_type.id as orderTypeId,
            hv_pm_work_plan.plan_start_time as workPlanStartTime,
            hv_pm_work_plan.plan_end_time as workPlanEndTime,
            hv_pm_work_order.come_from as comeFrom,
            hv_pm_work_order.complete_set_check_status as completeSetCheckStatus,
            hv_pm_work_order.block_code as blockCode,
            hv_pm_work_order.out_flag as outFlag,
            hv_pm_work_order.special_purchase_type_code as specialPurchaseTypeCode,
            hv_pm_work_order.parent_work_order_code as parentWorkOrderCode,
            hv_pm_work_order.used_type as usedType,
            CASE
            WHEN hv_pm_work_order.complete_set_check_status >= 1 THEN
            (SELECT bom.complete_time FROM `hv_pm_plan_product_bom` bom WHERE bom.plan_code = hv_pm_work_order.work_order_code ORDER BY bom.complete_time DESC LIMIT 1 ) ELSE NULL
            END AS completeTime
            FROM hv_pm_work_order
            left join hv_pm_work_order_extend on hv_pm_work_order.id = hv_pm_work_order_extend.work_order_id
            left join hv_pm_order_type on hv_pm_work_order.order_type_id = hv_pm_order_type.id
            left join hv_pm_work_plan on hv_pm_work_order.plan_code = hv_pm_work_plan.plan_code
            <where>
                <if test="query.planCodeEqual != null and query.planCodeEqual != ''">
                    and hv_pm_work_order.plan_code = #{query.planCodeEqual}
                </if>
                <if test="query.planCodeLike != null and query.planCodeLike != ''">
                    and hv_pm_work_order.plan_code like concat ('%',#{query.planCodeLike},'%')
                </if>
                <if test="query.materialId != null">
                    and hv_pm_work_order.material_id = #{query.materialId}
                </if>
                <if test="query.planStartTimeStart != null and query.planStartTimeEnd != null">
                    and hv_pm_work_order.plan_start_time between #{query.planStartTimeStart} and #{query.planStartTimeEnd}
                </if>
                <if test="query.planEndTimeStart != null and query.planEndTimeEnd != null">
                    and hv_pm_work_order.plan_end_time between #{query.planEndTimeStart} and #{query.planEndTimeEnd}
                </if>
                <if test="query.timeAreaStart != null and query.timeAreaEnd != null">
                    and hv_pm_work_order.plan_start_time between #{query.timeAreaStart} and #{query.timeAreaEnd}
                </if>
                <if test="query.routeId != null">
                    and hv_pm_work_order.route_id = #{query.routeId}
                </if>
                <if test="query.routeCode != null and query.routeCode != ''">
                    and hv_pm_work_order.route_code = #{query.routeCode}
                </if>
                <if test="query.materialCodeLike != null and query.materialCodeLike != ''">
                    and hv_pm_work_order.material_code like concat('%',#{query.materialCodeLike},'%')
                </if>

                <if test="query.materialCodeEqual != null and query.materialCodeEqual != ''">
                    and hv_pm_work_order.material_code = #{query.materialCodeEqual}
                </if>
                <if test="query.actualStartTimeStart != null and query.actualStartTimeEnd != null">
                    and hv_pm_work_order.actual_start_time between #{query.actualStartTimeStart} and
                    #{query.actualStartTimeEnd}
                </if>
                <if test="query.actualEndTimeStart != null and query.actualEndTimeEnd != null">
                    and hv_pm_work_order.actual_end_time between #{query.actualEndTimeStart} and #{query.actualEndTimeEnd}
                </if>

                <if test="query.workOrderCodeEqual != null and query.workOrderCodeEqual != ''">
                    and hv_pm_work_order.work_order_code = #{query.workOrderCodeEqual}
                </if>

                <if test="query.workOrderCodeLike != null and query.workOrderCodeLike != ''">
                    and hv_pm_work_order.work_order_code like concat('%',#{query.workOrderCodeLike},'%')
                </if>

                <if test="query.issuedTimeStart != null and query.issuedTimeEnd != null">
                    and hv_pm_work_order.issued_time between #{query.issuedTimeStart} and #{query.issuedTimeEnd}
                </if>

                <if test="query.shiftId != null">
                    and hv_pm_work_order.shift_id = #{query.shiftId}
                </if>
                <if test="query.shiftName != null">
                    and hv_pm_work_order.shift_name = #{query.shiftName}
                </if>

                <if test="query.areaId != null">
                    and hv_pm_work_order.area_id = #{query.areaId}
                </if>
                <if test="query.cellId != null">
                    and hv_pm_work_order.cell_id = #{query.cellId}
                </if>
                <if test="query.crewId != null">
                    and hv_pm_work_order.crew_id = #{query.crewId}
                </if>
                <if test="query.crewName != null">
                    and hv_pm_work_order.crew_name = #{query.crewName}
                </if>

                <if test="query.bomId != null">
                    and hv_pm_work_order.bom_id = #{query.bomId}
                </if>
                <if test="query.planOrNew != null">
                    and hv_pm_work_order.plan_or_new = #{query.planOrNew}
                </if>
                <if test="query.orderMode != null">
                    and hv_pm_work_order.order_mode = #{query.orderMode}
                </if>
                <if test="query.orderTypeId != null">
                    and hv_pm_order_type.id = #{query.orderTypeId}
                </if>
                <if test="query.orderTypeCode != null">
                    and hv_pm_order_type.order_type_code like concat('%',#{query.orderTypeCode},'%')
                </if>
                <if test="query.orderTypeName != null">
                    and hv_pm_order_type.order_type_name like concat('%',#{query.orderTypeName},'%')
                </if>
                <if test="query.workOrderState != null">
                    and hv_pm_work_order.work_order_state = #{query.workOrderState}
                </if>
                <if test="query.comeFrom != null">
                    and hv_pm_work_order.come_from = #{query.comeFrom}
                </if>
                <if test="query.workOrderStates !=null and query.workOrderStates.size > 0">
                    <foreach collection="query.workOrderStates" index="index" item="item"
                             open="and hv_pm_work_order.work_order_state in("
                             separator=","
                             close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="query.extendQueries !=null and query.extendQueries.size > 0">
                    <foreach collection="query.extendQueries" index="index" item="item" open=""
                             separator=""
                             close="">
                        <if test="item.andOr == null or item.andOr == 1">
                            and
                        </if>
                        <if test="item.andOr != null and item.andOr == 2">
                            or
                        </if>
                        hv_pm_work_order_extend.${item.columnName}
                        <if test="item.symbol == null or item.symbol == 1">
                            =
                        </if>
                        <if test="item.symbol != null and item.symbol == 2">
                            &gt;
                        </if>
                        <if test="item.symbol != null and item.symbol == 3">
                            &gt;=
                        </if>
                        <if test="item.symbol != null and item.symbol == 4">
                            &lt;
                        </if>
                        <if test="item.symbol != null and item.symbol == 5">
                            &lt;=
                        </if>
                        <if test="item.symbol != null and item.symbol == 6">
                            is null
                        </if>
                        <if test="item.symbol != null and item.symbol == 7">
                            is not null
                        </if>
                        <if test="item.symbol != null and item.symbol == 8">
                            like
                        </if>
                        <if test="item.value != null ">
                            #{item.value}
                        </if>
                    </foreach>
                </if>
                <if test="query.usedType != null">
                    and hv_pm_work_order.used_type = #{query.usedType}
                </if>

                <if test="query.shipNo != null and query.shipNo != '' ">
                    and hv_pm_work_order.ship_no  like concat('%',#{query.shipNo} ,'%')
                </if>
                <if test="query.shipModel != null  and query.shipModel != ''">
                    and hv_pm_work_order.ship_model like concat('%',#{query.shipModel} ,'%')
                </if>
                <if test="query.segmentationCode != null  and query.segmentationCode != ''">
                    and hv_pm_work_order.segmentation_code  = #{query.segmentationCode}
                </if>
                <if test="query.routeName != null  and query.routeName != ''">
                    and hv_pm_work_order.route_name like concat('%',#{query.routeName},'%')
                </if>
                <if test="query.partMaterialCode !=null and query.partMaterialCode != ''">
                    and hv_pm_work_order.work_order_code in(
                    select plan_code from hv_pm_plan_product_bom
                    where material_code = #{query.partMaterialCode}
                    )
                </if>

            </where>
    </select>

</mapper>