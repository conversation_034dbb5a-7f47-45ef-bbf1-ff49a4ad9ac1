<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.HvPmXcMaterialCutPlanMapper">
    <select id="getPage" resultType="com.hvisions.pms.plan.HvPmXcMaterialCutPlanDTO">
        select p.*,u.user_name
        from hv_pm_xc_material_cut_plan p
        left join framework.sys_user u
        on p.send_user_id=u.id
        <where>
            <if test="query.orderNo != null and query.orderNo !=''">
                and order_no like concat('%',#{query.orderNo},'%')
            </if>
            <if test="query.lineId != null">
                and line_id = #{query.lineId}
            </if>
            <if test="query.shipMode != null and query.shipMode !='' ">
                and ship_mode = #{query.shipMode}
            </if>
            <if test="query.shipCode != null and query.shipCode !='' ">
                and ship_code = #{query.shipCode}
            </if>
            <if test="query.beginTime != null">
                and plan_end_time >= #{query.beginTime}
            </if>
            <if test="query.endTime != null">
                and plan_end_time &lt;= #{query.endTime}
            </if>
            <if test="query.sendBeginTime != null">
                and send_time >= #{query.sendBeginTime}
            </if>
            <if test="query.sendEndTime != null">
                and send_time &lt;= #{query.sendEndTime}
            </if>
            <if test="query.completionStartTime != null">
                and finish_time >= #{query.completionStartTime}
            </if>
            <if test="query.completionEndTime != null">
                and finish_time &lt;= #{query.completionEndTime}
            </if>
            <if test="query.segmentationCode != null and query.segmentationCode !=''">
                and segmentation_code like concat('%',#{query.segmentationCode},'%')
            </if>
            <if test="query.status != null">
                and status = #{query.status}
            </if>

            <if test="query.materialCode != null and query.materialCode != ''">
                and order_no in (
                  select order_no from hv_pm_xc_material_cut_plan_detail1
                  where material_code = #{query.materialCode}
                )
            </if>

            <if test="query.workOrderCode != null and query.workOrderCode != ''">
                and order_no in (
                select order_no from hv_pm_xc_material_cut_plan_detail1
                where work_order_code = #{query.workOrderCode}
                )
            </if>
        </where>
    </select>

     <select id="getCountToday" resultType="com.hvisions.pms.dto.TodayWorkOrderCountDTO">
        SELECT
            COALESCE((SELECT COUNT(*)  FROM hv_pm_xc_material_cut_plan WHERE DATE(send_time) = CURDATE()),0) `count`,
            COALESCE((SELECT COUNT(*)  FROM hv_pm_xc_material_cut_plan WHERE status = 6 AND DATE(finish_time) = CURDATE()),0) `complete`
    </select>
</mapper>