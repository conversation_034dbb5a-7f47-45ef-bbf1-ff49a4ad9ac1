<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.HvPmMaterialCutPlanDetail2Mapper">


    <select id="getOneByCutPlanCodeAndOperationType"
            resultType="com.hvisions.pms.entity.plan.HvPmMaterialCutPlanDetail2">
        SELECT
            d.*
        FROM
            `hv_pm_material_cut_plan_detail2` d
        WHERE
            d.cut_plan_code = #{cutPlanCode} and operation_type = #{operationType}
            limit 1
    </select>
</mapper>