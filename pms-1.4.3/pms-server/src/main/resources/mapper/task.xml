<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.pms.dao.TaskMapper">

    <resultMap id="orderTask" type="com.hvisions.pms.task.dto.OrderTaskDTO">
    </resultMap>

    <select id="getTaskDtoByQuery" resultMap="orderTask"
            parameterType="com.hvisions.pms.dto.OperationQueryDTO" databaseId="mysql">
        select t1.*,t2.user_id as taskUser,t3.user_name as taskUserName from hv_pm_order_task t1
        left join hv_pm_task_user t2 on t1.id = t2.task_id
        left join framework.sys_user t3 on t2.user_id = t3.id
        <where>
            <if test="dto.workCenterId !=null">
                and t1.work_center_id = #{dto.workCenterId}
            </if>
            <if test="dto.state !=null">
                and t1.state = #{dto.state}
            </if>
            <if test="dto.workOrderCode !=null">
                and t1.work_order_code like concat('%',#{dto.workOrderCode},'%')
            </if>
            <if test="dto.materialCode !=null">
                and t1.material_code like concat('%',#{dto.materialCode},'%')
            </if>
            <if test="dto.isAssign != null and dto.isAssign == true">
                and t2.user_id is not null
            </if>
            <if test="dto.isAssign != null and dto.isAssign == false">
                and t2.user_id is null
            </if>
            <if test="dto.userId != null">
                and (t2.user_id = #{dto.userId} or t2.user_id is null)
            </if>
        </where>
    </select>

    <select id="getTaskDtoByQuery" resultMap="orderTask"
            parameterType="com.hvisions.pms.dto.OperationQueryDTO" databaseId="sqlserver">
        select t1.*,t2.user_id as taskUser,t3.user_name as taskUserName from hv_pm_order_task t1
        left join hv_pm_task_user t2 on t1.id = t2.task_id
        left join framework.dbo.sys_user t3 on t2.user_id = t3.id
        <where>
            <if test="dto.workCenterId !=null">
                and t1.work_center_id = #{dto.workCenterId}
            </if>
            <if test="dto.state !=null">
                and t1.state = #{dto.state}
            </if>
            <if test="dto.workOrderCode !=null">
                and t1.work_order_code like concat('%',#{dto.workOrderCode},'%')
            </if>
            <if test="dto.materialCode !=null">
                and t1.material_code like concat('%',#{dto.materialCode},'%')
            </if>
            <if test="dto.isAssign != null and dto.isAssign == true">
                and t2.user_id is not null
            </if>
            <if test="dto.isAssign != null and dto.isAssign == false">
                and t2.user_id is null
            </if>
            <if test="dto.userId != null">
                and (t2.user_id = #{dto.userId} or t2.user_id is null )
            </if>
        </where>
    </select>

    <resultMap id="userName" type="com.hvisions.pms.dto.WorkCenterUserNameDTO"></resultMap>
    <select id="getCrewUserByWorkCenterId" resultMap="userName" parameterType="java.lang.Integer" databaseId="mysql">
        SELECT t1.id AS user_id, t1.user_name
        FROM framework.sys_user t1
        WHERE EXISTS(SELECT 1
                     FROM hv_pm_user_work_center t2
                     WHERE t2.work_center_id = #{id}
                       AND t2.user_id = t1.id)
           OR EXISTS(
                  SELECT 1
                  FROM hv_pm_crew_work_center t3
                           JOIN hiper_base.hv_bm_crew t4
                           JOIN hiper_base.hv_bm_crew_member t5
                  WHERE t3.crew_id = t4.id
                    AND t5.user_id = t1.id
                    AND t3.work_center_id = #{id}
                      )
        ORDER BY t1.user_name
    </select>

    <select id="getCrewUserByWorkCenterId" resultMap="userName" parameterType="java.lang.Integer"
            databaseId="sqlserver">
        SELECT t1.id AS user_id, t1.user_name
        FROM framework.dbo.sys_user t1
        WHERE EXISTS(SELECT 1
                     FROM hv_pm_user_work_center t2
                     WHERE t2.work_center_id = #{id}
                       AND t2.user_id = t1.id)
           OR EXISTS(
                  SELECT 1
                  FROM hv_pm_crew_work_center t3
                           JOIN hiper_base.dbo.hv_bm_crew t4
                           JOIN hiper_base.dbo.hv_bm_crew_member t5
                  WHERE t3.crew_id = t4.id
                    AND t5.user_id = t1.id
                    AND t3.work_center_id = #{id}
                      )
        ORDER BY t1.user_name
    </select>
</mapper>