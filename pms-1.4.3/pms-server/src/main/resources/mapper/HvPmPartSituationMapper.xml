<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.HvPmPartSituationMapper">

    <select id="getPage" resultType="com.hvisions.pms.dto.HvPmPartSituationDTO">
        select s.* from hv_pm_part_situation s
        <where>
            <if test="query.shipNo != null and query.shipNo != ''">
                and ship_no like concat('%',#{query.shipNo},'%')
            </if>

            <if test="query.shipModel != null and  query.shipModel != ''">
                and ship_model like concat('%',#{query.shipModel},'%')
            </if>

            <if test="query.segmentationCode != null">
                and segmentation_code like concat('%',#{query.segmentationCode},'%')
            </if>

            <if test="query.materialCode != null and query.materialCode != ''">
                and material_code = #{query.materialCode}
            </if>
        </where>
    </select>

</mapper>