<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.DeliveryMissionMapper">

    <select id="getPage" resultType="com.hvisions.pms.dto.deliveryMission.DeliveryMissionDTO">
        SELECT
            id,
        delivery_mission_code AS deliveryMissionCode,
        product_work_order_code AS productWorkOrderCode,
        requirement_code AS requirementCode,
        priority,
        pallet_no AS palletNo,
        start_point_code AS startPointCode,
        target_point_code AS targetPointCode,
        status,
        delivery_type AS deliveryType,
        operator,
        plan_delivery_time AS planDeliveryTime,
        actual_start_time AS actualStartTime,
        actual_end_time AS actualEndTime
        FROM
        hv_pm_delivery_mission
        <where>
            <if test="query.deliveryMissionCode != null and query.deliveryMissionCode != ''">
                AND delivery_mission_code LIKE CONCAT('%', #{query.deliveryMissionCode}, '%')
            </if>
            <if test="query.productWorkOrderCode != null and query.productWorkOrderCode != ''">
                AND product_work_order_code LIKE CONCAT('%', #{query.productWorkOrderCode}, '%')
            </if>
            <if test="query.requirementCode != null and query.requirementCode != ''">
                AND requirement_code LIKE CONCAT('%', #{query.requirementCode}, '%')
            </if>
            <if test="query.palletNo != null and query.palletNo != ''">
                AND pallet_no LIKE CONCAT('%', #{query.palletNo}, '%')
            </if>
            <if test="query.priority != null">
                AND priority = #{query.priority}
            </if>
            <if test="query.status != null">
                AND status = #{query.status}
            </if>
            <if test="query.deliveryType != null">
                AND delivery_type = #{query.deliveryType}
            </if>
            <if test="query.planDeliveryStartTime != null and query.planDeliveryEndTime != null">
                AND plan_delivery_time BETWEEN #{query.planDeliveryStartTime} AND #{query.planDeliveryEndTime}

            </if>
        </where>
    </select>


</mapper>