<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.HvPmPlanProductBomMapper">

    <update id="updateFrameCodeById">
        update hv_pm_plan_product_bom
        set frame_code    =#{frameCode},
            act_quantity  =#{actQuantity},
            complete_time =#{completeTime}
        where id = #{id}
    </update>
    <delete id="deleteByPlanCode">
        DELETE FROM hv_pm_plan_product_bom WHERE plan_code=#{planCode}
    </delete>
    <select id="getCompleteTimeByPlanCode" resultType="java.util.Date">
        SELECT bom.complete_time FROM `hv_pm_plan_product_bom` bom where  bom.plan_code =#{planCode} ORDER BY bom.complete_time DESC LIMIT 1
    </select>
    <select id="getListByPlanCodes" resultType="com.hvisions.pms.entity.HvPmPlanProductBom">
        select
        b.*,
        u.symbol +"/" + u.description as unitMeasures
        FROM
        hv_pm_plan_product_bom b
        LEFT JOIN  hiper_base.hv_bm_material m ON m.material_code = b.material_code AND m.eigenvalue =1
        LEFT JOIN  hiper_base.hv_bm_unit u ON u.id = m.uom
        where
        plan_code in
        <foreach collection="planCodes" item="planCode" index="index" open="(" separator="," close=")">
            #{planCode}
        </foreach>
    </select>

</mapper>