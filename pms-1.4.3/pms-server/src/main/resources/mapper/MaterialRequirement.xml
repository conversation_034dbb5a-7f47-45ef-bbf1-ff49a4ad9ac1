<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.MaterialRequirementMapper">

    <select id="getPage" resultType="com.hvisions.pms.dto.materialRequirement.MaterialRequirementDTO">
        SELECT
        id,
        requirement_code AS requirementCode,
        work_order_code AS workOrderCode,
        cell_id AS cellId,
        work_plan_start_time AS workPlanStartTime,
        priority,
        is_assembled AS isAssembled,
        is_close AS isClose
        FROM
        hv_pm_material_requirement
        <where>
            <if test="query.requirementCode != null and query.requirementCode != ''">
                AND requirement_code = #{query.requirementCode}
            </if>
            <if test="query.workOrderCode != null and query.workOrderCode != ''">
                AND work_order_code = #{query.workOrderCode}
            </if>
            <if test="query.cellId != null and query.cellId != ''">
                AND cell_id = #{query.cellId}
            </if>
            <if test="query.workPlanStartTime != null">
                AND work_plan_start_time = #{query.workPlanStartTime}
            </if>
            <if test="query.priority != null">
                AND priority = #{query.priority}
            </if>
            <if test="query.isAssembled != null">
                AND is_assembled = #{query.isAssembled}
            </if>
            <if test="query.isClose != null">
                AND is_close = #{query.isClose}
            </if>
        </where>
    </select>
    <select id="findListByCondition" resultType="com.hvisions.pms.entity.HvPmMaterialRequirement">
        SELECT
        id,
        requirement_code AS requirementCode,
        work_order_code AS workOrderCode,
        cell_id AS cellId,
        work_plan_start_time AS workPlanStartTime,
        priority,
        is_assembled AS isAssembled,
        is_close AS isClose,
        update_time AS updateTime,
        create_time AS createTime,
        creator_name AS creatorName,
        updater_name AS updaterName
        FROM
        hv_pm_material_requirement
        <where>
            <if test="query.requirementCode != null and query.requirementCode != ''">
                AND requirement_code = #{query.requirementCode}
            </if>
            <if test="query.workOrderCode != null and query.workOrderCode != ''">
                AND work_order_code = #{query.workOrderCode}
            </if>
            <if test="query.cellId != null and query.cellId != ''">
                AND cell_id = #{query.cellId}
            </if>
            <if test="query.workPlanStartTime != null">
                AND work_plan_start_time = #{query.workPlanStartTime}
            </if>
            <if test="query.priority != null">
                AND priority = #{query.priority}
            </if>
            <if test="query.isAssembled != null">
                AND is_assembled = #{query.isAssembled}
            </if>
            <if test="query.isClose != null">
                AND is_close = #{query.isClose}
            </if>
        </where>
    </select>

</mapper>