<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.HvPmMaterialCutPlanMapper">
    <select id="getPage" resultType="com.hvisions.pms.plan.HvPmMaterialCutPlanDTO">
        select p.*,u.user_name
        from hv_pm_material_cut_plan p
        left join framework.sys_user u
        on p.send_user_id=u.id
        <where>
            <if test="query.cutPlanCode != null">
                and cut_plan_code like concat('%',#{query.cutPlanCode},'%')
            </if>
            <if test="query.materialType != null">
                and material_type like #{query.materialType}
            </if>
            <if test="query.lineId != null">
                and line_id = #{query.lineId}
            </if>
            <if test="query.beginTime != null">
                and plan_end_time >= #{query.beginTime}
            </if>
            <if test="query.endTime != null">
                and plan_end_time &lt;= #{query.endTime}
            </if>
            <if test="query.status != null">
                and status = #{query.status}
            </if>

            <if test="query.materialCode != null and query.materialCode != ''">
                and cut_plan_code in (
                  select cut_plan_code from hv_pm_material_cut_plan_detail0
                  where material_code = #{query.materialCode}
                )
            </if>

            <if test="query.workOrder != null and query.workOrder != ''">
                and cut_plan_code in (
                select cut_plan_code from hv_pm_material_cut_plan_detail0
                where work_order = #{query.workOrder}
                )
            </if>
            <if test="query.operateStartTime != null and query.operateEndTime != null">
                and cut_plan_code in (
                select cut_plan_code from hv_pm_material_cut_plan_detail2 d2 where
                (d2.start_time >= #{query.operateStartTime} and  d2.start_time &lt;= #{query.operateEndTime} )  or
                (d2.end_time >= #{query.operateStartTime} and  d2.end_time &lt;= #{query.operateEndTime} )
                )
            </if>
            <if test="query.sendBeginTime != null">
                and send_time >= #{query.sendBeginTime}
            </if>
            <if test="query.segmentationCode != null and query.segmentationCode != ''">
                and segmentation_code  like concat('%',#{query.segmentationCode},'%')
            </if>
            <if test="query.shipNumber != null and query.shipNumber != ''">
                and ship_number = #{query.shipNumber}
            </if>
            <if test="query.completionStartTime != null">
                and finish_time >= #{query.completionStartTime}
            </if>
            <if test="query.completionEndTime != null">
                and finish_time &lt;= #{query.completionEndTime}
            </if>
        </where>
    </select>

     <select id="getCountToday" resultType="com.hvisions.pms.dto.TodayWorkOrderCountDTO">
        SELECT
            COALESCE((SELECT SUM(quality) FROM hv_pm_material_cut_plan WHERE DATE(send_time) = CURDATE()),0) `count`,
            COALESCE((SELECT SUM(quality) FROM hv_pm_material_cut_plan WHERE status = 10 AND DATE(finish_time) = CURDATE()),0) `complete`
    </select>
    <select id="getShipNumberList" resultType="java.lang.String">
        SELECT DISTINCT ship_number FROM `hv_pm_material_cut_plan`
    </select>
</mapper>