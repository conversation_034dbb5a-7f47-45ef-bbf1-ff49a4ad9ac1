<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.HvPmLineReportMapper">
    <select id="findByCondition" resultType="com.hvisions.pms.dto.HvPmLineReportDTO">
        select
        id,
        order_code orderCode,
        report_type reportType,
        material_code materialCode,
        material_type materialType,
        ship_number shipNumber,
        segmentation_code segmentationCode,
        line_code lineCode,
        station_code stationCode,
        station_name stationName,
        report_time reportTime,
        actual_start_time actualStartTime,
        actual_completion_time actualCompletionTime,
        deplete_time depleteTime,
        equipment_code equipmentCode,
        report_user_code reportUserCode,
        report_user_name reportUserName,
        report_qty reportQty,
        qualified_qty qualifiedQty,
        scrap_qty scrapQty,
        repair_qty repairQty,
        report_mes_flag reportMesFlag,
        report_mes_time reportMesTime
        from hv_pm_line_report
        <where>
            <if test="condition.orderCode != null and condition.orderCode != ''">
                and order_code = #{condition.orderCode}
            </if>
            <if test="condition.reportType != null and condition.reportType != ''">
                and report_type = #{condition.reportType}
            </if>
            <if test="condition.materialCode != null and condition.materialCode != ''">
                and material_code = #{condition.materialCode}
            </if>
            <if test="condition.materialType != null and condition.materialType != ''">
                and material_type = #{condition.materialType}
            </if>
            <if test="condition.shipNumber != null and condition.shipNumber != ''">
                and ship_number = #{condition.shipNumber}
            </if>
            <if test="condition.segmentationCode != null and condition.segmentationCode != ''">
                and segmentation_code = #{condition.segmentationCode}
            </if>
            <if test="condition.lineCode != null and condition.lineCode != ''">
                and line_code = #{condition.lineCode}
            </if>
            <if test="condition.stationCode != null and condition.stationCode != ''">
                and station_code = #{condition.stationCode}
            </if>
            <if test="condition.stationName != null and condition.stationName != ''">
                and station_name = #{condition.stationName}
            </if>
            <if test="condition.equipmentCode != null and condition.equipmentCode != ''">
                and equipment_code = #{condition.equipmentCode}
            </if>
            <if test="condition.reportMesFlag != null">
                and report_mes_flag = #{condition.reportMesFlag}
            </if>
            <if test="condition.startTimeUTC != null and condition.startTimeUTC != ''">
                and report_time &gt;= #{condition.startTimeUTC}
            </if>
            <if test="condition.endTimeUTC != null and condition.endTimeUTC != ''">
                and report_time &lt;= #{condition.endTimeUTC}
            </if>
        </where>
    </select>

    <select id="getOndLineReportByOrderCode" resultType="com.hvisions.pms.entity.HvPmLineReport">
        select * from  hv_pm_line_report where order_code =  #{orderCode}  ORDER BY id Desc LIMIT 1
    </select>
</mapper>