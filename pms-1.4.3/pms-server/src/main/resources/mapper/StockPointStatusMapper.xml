<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.StockPointStatusMapper">


    <insert id="savaPointStatus" parameterType="com.hvisions.thirdparty.common.dto.StockPointStatusDTO">
        INSERT INTO hv_pm_stock_point_status
        (pointCode,
         status,
         palletCode)
        VALUES (#{pointCode},
                #{status},
                #{palletCode})
    </insert>

    <select id="findPointStatus" resultType="com.hvisions.thirdparty.common.dto.StockPointStatusDTO">
        SELECT *
        FROM hv_pm_stock_point_status
    </select>

    <select id="findPointStatusByPointCode" resultType="com.hvisions.thirdparty.common.dto.StockPointStatusDTO">
        SELECT *
        FROM hv_pm_stock_point_status
        WHERE pointCode = #{pointCode};
    </select>

    <!-- 更新操作 -->
    <update id="updateStatusById" parameterType="com.hvisions.thirdparty.common.dto.StockPointStatusDTO">
        UPDATE hv_pm_stock_point_status
        SET status     = #{status},
            pointCode  = #{pointCode},
            palletCode = #{palletCode}
        WHERE id = #{id}
    </update>
</mapper>