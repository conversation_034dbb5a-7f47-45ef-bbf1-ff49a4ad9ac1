<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.StatisticsMapper">
    <resultMap id="rm1" type="com.hvisions.pms.statistics.MaterialStatisticsDTO">
    </resultMap>
    <resultMap id="rm2" type="com.hvisions.pms.statistics.ActualAndPlan">
    </resultMap>
    <select id="findAllMaterial" resultMap="rm1" databaseId="mysql">
        SELECT t3.name               cell_name,
               t2.crew_name,
               t1.material_name,
               MAX(t1.material_id)   material_id,
               SUM(t1.out_put_count) quantity,
               MAX(t1.create_time)   num_
        FROM hv_pm_operation_out_put_material t1
                 JOIN hv_pm_order_task t2 ON t1.operation_id = t2.operation_id
                 JOIN hiper_base.hv_bm_location t3 on t3.id = t2.cell_id
        WHERE t2.end_time BETWEEN #{beginTime} AND #{endTime}
        GROUP BY t3.name, t2.crew_name, t1.material_name
        ORDER BY num_ DESC
    </select>
    <select id="findAllMaterial" resultMap="rm1" databaseId="sqlserver">
        SELECT t3.name               cell_name,
               t2.crew_name,
               t1.material_name,
               MAX(t1.material_id)   material_id,
               SUM(t1.out_put_count) quantity,
               MAX(t1.create_time)   num_
        FROM hv_pm_operation_out_put_material t1
                 JOIN hv_pm_order_task t2 ON t1.operation_id = t2.operation_id
                 JOIN hiper_base.dbo.hv_bm_location t3 on t3.id = t2.cell_id
        WHERE t2.end_time BETWEEN #{beginTime} AND #{endTime}
        GROUP BY t3.name, t2.crew_name, t1.material_name
        ORDER BY num_ DESC
    </select>

    <select id="findActualAndPlan" resultMap="rm2">
        SELECT t.materialName, SUM(t.actual) actual, SUM(t.plan_count) plan
        FROM (SELECT material_name                                                    materialName,
                     SUM(CASE WHEN actual_count is NULL THEN 0 ELSE actual_count END) actual,
                     MAX(CASE WHEN plan_count IS NULL THEN 0 ELSE plan_count END)     plan_count
              FROM hv_pm_operation_material t
              WHERE t.create_time BETWEEN #{beginTime} AND #{endTime}
              GROUP BY operation_id, material_name) as t
        GROUP BY materialName
    </select>


    <resultMap id="bom" type="com.hvisions.pms.dto.OrderBomDTO">
        <result column="bom_code" property="bomCode"/>
        <result column="bom_name" property="bomName"/>
        <result column="bom_versions" property="bomVersion"/>
        <result column="bom_count" property="bomQuantity"/>
        <result column="bom_description" property="unit"/>
        <result column="nesting_file_path" property="nestingFilePath"/>
        <collection property="orderBomItemDTOS"
                    ofType="com.hvisions.pms.dto.OrderBomItemDTO">
            <result column="bom_item_name" property="materialName"/>
            <result column="bom_item_code" property="materialCode"/>
            <result column="eigenvalue" property="eigenvalue"/>
            <result column="quantity" property="quantity"/>
            <result column="bom_item_unit" property="unit"/>
            <result column="inventory_quantity" property="inventoryQuantity"/>
        </collection>
    </resultMap>
    <select id="getWorkPlanQuantity" resultMap="bom" parameterType="java.lang.Integer" databaseId="mysql">
        SELECT b.bom_code,
               b.bom_name,
               b.bom_count,
               b.bom_versions,
               u2.description                              AS bom_description,
               i.bom_item_name,
               i.bom_item_code,
               u.description                               AS bom_item_unit,
               m.eigenvalue                                AS eigenvalue,
               m.id                                        AS material_id,
               w.quantity * i.bom_item_count / b.bom_count AS quantity,
               e.nestingFilePath                           AS nesting_file_path,
               COALESCE((SELECT SUM(s.quantity) FROM wms.hv_wms_stocks s WHERE s.material_id = m.id), 0) + COALESCE(
                       (SELECT SUM(pfm.quantity)
                        FROM pms.hv_pm_call_frame_material pfm
                        WHERE pfm.material_code = m.material_code
                          AND NOT EXISTS (SELECT 1 FROM wms.hv_wms_stocks s WHERE s.material_id = m.id)),
                       0)                                  AS inventory_quantity
        FROM hv_pm_work_order w
                 LEFT JOIN hiper_base.hv_bm_bom b ON w.bom_id = b.id
                 LEFT JOIN hiper_base.hv_bm_bom_item i ON i.bom_id = b.id
                 LEFT JOIN hiper_base.hv_bm_material m ON i.materials_id = m.id
                 LEFT JOIN hiper_base.hv_bm_unit u ON u.id = m.uom
                 LEFT JOIN hiper_base.hv_bm_unit u2 ON u2.id = b.unit_id
                 LEFT JOIN hiper_base.hv_bm_bom_extend e ON e.bom_id = b.id
        WHERE w.id = #{id};
    </select>
    <select id="getWorkPlanQuantity" resultMap="bom" parameterType="java.lang.Integer" databaseId="sqlserver">
        select b.bom_code,
               b.bom_name,
               b.bom_count,
               b.bom_versions,
               u2.description                              as bom_description,
               i.bom_item_name,
               i.bom_item_code,
               u.description                               as bom_item_unit,
               m.eigenvalue                                as eigenvalue,
               m.id                                        as material_id,
               w.quantity * i.bom_item_count / b.bom_count as quantity,
               e.nestingFilePath                           as nesting_file_path,
               (
                   SELECT COALESCE(SUM(s.quantity), 0)
                   FROM wms.dbo.hv_wms_stocks s
                   WHERE s.material_id = m.id
                   GROUP BY
                       s.material_id
               ) as inventory_quantity

        from hv_pm_work_order w
                 left join hiper_base.dbo.hv_bm_bom b on w.bom_id = b.id
                 left join hiper_base.dbo.hv_bm_bom_item i on i.bom_id = b.id
                 left join hiper_base.dbo.hv_bm_material m on i.materials_id = m.id
                 left join hiper_base.dbo.hv_bm_unit u on u.id = m.uom
                 LEFT JOIN hiper_base.dbo.hv_bm_unit u2 on u2.id = b.unit_id
                 left join hiper_base.dbo.hv_bm_bom_extend e on e.bom_id = b.id
        where w.id = #{id}
    </select>


    <resultMap id="work" type="com.hvisions.pms.dto.WorkOutlineDTO">

    </resultMap>

    <select id="getWorkOutLineByNowDate" resultMap="work" databaseId="mysql">
        select *
        from (select count(*) as planIssued
              from hv_pm_work_plan t1
              where t1.actual_issued_time between #{startTime} and #{endTime} and t1.plan_status = 1
                 or t1.plan_status = 2) j1
                 join (select count(*) as planExecuting
                       from hv_pm_work_plan t2
                       where t2.plan_status = 1
                          or t2.plan_status = 2) j2
                 join (select count(*) as planFinish
                       from hv_pm_work_plan t3
                                left join hv_pm_work_plan_detail t4 on t3.id =
                                                                       t4.work_plan_id
                       where t3.plan_status = 3
                         and t4.finish_time between #{startTime} and #{endTime}) j3
                 join (select count(*) as workIssued
                       from hv_pm_work_order t5
                       where t5.issued_time between #{startTime} and
                                     #{endTime}) j4
                 join (select count(*) as workExecuting from hv_pm_work_order t6 where t6.work_order_state = 3) j5
                 join (select count(*) as workFinish
                       from hv_pm_work_order t7
                       where t7.actual_end_time between #{startTime} and
                                     #{endTime}) j6
                 join (select count(*) as taskWorkCenter from hiper_base.hv_bm_location t8 where t8.type = 50) j7
                 join (select count(*) as taskExecuting from hv_pm_order_task t9 where t9.state in (0, 1, 2, 3)) j8
                 join (select count(*) as taskFinish
                       from hv_pm_order_task t10
                       where t10.end_time between #{startTime} and
                                     #{endTime}) j9
                 join (select count(*) as product
                       from hiper_base.hv_bm_material t11
                                left join hiper_base.hv_bm_material_type t12 on t11.material_type = t12.id
                       where t12.material_type_code = 'Product') j10
                 join (select count(*) as semiProduct
                       from hiper_base.hv_bm_material t13
                                left join hiper_base.hv_bm_material_type t14 on t13.material_type = t14.id
                       where t14.material_type_code = 'Semi_Product') j11
                 join (select count(*) as rawMaterial
                       from hiper_base.hv_bm_material t15
                                left join hiper_base.hv_bm_material_type t16 on t15.material_type = t16.id
                       where t16.material_type_code = 'Raw_Material') j12
                 join (select count(*) as bomProduct
                       from hiper_base.hv_bm_bom t17
                                left join hiper_base.hv_bm_bom_material t18 on t17.id = t18.bom_id
                                left join hiper_base.hv_bm_material t19 on t18.material_id = t19.id
                                left join hiper_base.hv_bm_material_type t20 on t19.material_type = t20.id
                       where t20.material_type_code = 'Product')j13
                 join (select count(*) as bomSemiProduct
                       from hiper_base.hv_bm_bom t21
                                left join hiper_base.hv_bm_bom_material t22 on t21.id = t22.bom_id
                                left join hiper_base.hv_bm_material t23 on t22.material_id = t23.id
                                left join hiper_base.hv_bm_material_type t24 on t23.material_type = t24.id
                       where t24.material_type_code = 'Semi_Product')j14
                 join (select count(*) as productRoute
                       from hiper_base.hv_bm_route t25
                                left join hiper_base.hv_bm_material t26 on t25.product_id = t26.id
                                left join hiper_base.hv_bm_material_type t27 on t26.material_type = t27.id
                       where t27.material_type_code = 'Product') j15
                 join (select count(*) as semiProductRoute
                       from hiper_base.hv_bm_route t28
                                left join hiper_base.hv_bm_material t29 on t28.product_id = t29.id
                                left join hiper_base.hv_bm_material_type t30 on t29.material_type = t30.id
                       where t30.material_type_code = 'Semi_Product')j16
                 join (select count(*) as crew from hiper_base.hv_bm_crew) j17
                 join (select count(*) as schedule from hiper_base.hv_bm_shift)j18
                 join (select case count(1) when 0 then '未排班' else '排班完成' end as scheduling
                       from hiper_base.hv_bm_schedule t31
                       where date_format(t31.schedule_date, '%Y-%m') = date_format(now(), '%Y-%m')) j19
                 join (select count(distinct t32.user_id) as userCount from hv_pm_user_work_center t32) j20
                 join (select count(distinct t33.work_center_id) as workCenterCount from hv_pm_user_work_center t33) j21
                 join (select count(*) as changShiftCount  from hv_pm_inspection_item t34) j22
                 join (select count(*) as routeCount from hiper_base.hv_bm_route k1 where k1.route_type = 1)j24

                 join (select count(*) as workIssued0
                       from hv_pm_work_order t5
                       where t5.issued_time between #{startTime} and
                                 #{endTime} and used_type = 0) j23
                 join (select count(*) as workExecuting0 from hv_pm_work_order t6 where t6.work_order_state = 3 and used_type = 0) j32
                 join (select count(*) as workFinish0
                       from hv_pm_work_order t7
                       where t7.actual_end_time between #{startTime} and
                                 #{endTime} and  used_type = 0) j25

                 join (select count(*) as workIssued1
                       from hv_pm_work_order t5
                       where t5.issued_time between #{startTime} and
                           #{endTime} and used_type = 1) j26
                 join (select count(*) as workExecuting1 from hv_pm_work_order t6 where t6.work_order_state = 3 and used_type = 1) j27
                 join (select count(*) as workFinish1
                       from hv_pm_work_order t7
                       where t7.actual_end_time between #{startTime} and
                           #{endTime} and  used_type = 1) j28

                 join (select count(*) as workIssued2
                       from hv_pm_work_order t5
                       where t5.issued_time between #{startTime} and
                           #{endTime} and used_type = 2) j29
                 join (select count(*) as workExecuting2 from hv_pm_work_order t6 where t6.work_order_state = 3 and used_type = 2) j30
                 join (select count(*) as workFinish2
                       from hv_pm_work_order t7
                       where t7.actual_end_time between #{startTime} and
                           #{endTime} and  used_type = 2) j31
    </select>

    <select id="getWorkOutLineByNowDate" resultMap="work" databaseId="sqlserver">
        SELECT *
        FROM (SELECT COUNT(*) AS planIssued
              FROM hv_pm_work_plan t1
              WHERE t1.actual_issued_time BETWEEN #{ startTime }
                            AND #{ endTime }
                        AND t1.plan_status = 1
                 OR t1.plan_status = 2) j1
                 JOIN (SELECT COUNT(*) AS planExecuting
                       FROM hv_pm_work_plan t2
                       WHERE t2.plan_status = 1
                          OR t2.plan_status = 2) j2 ON 1 = 1
                 JOIN (SELECT COUNT(*) AS planFinish
                       FROM hv_pm_work_plan t3
                                LEFT JOIN hv_pm_work_plan_detail t4 ON t3.id = t4.work_plan_id
                       WHERE t3.plan_status = 3
                         AND t4.finish_time BETWEEN #{ startTime }
                                     AND #{ endTime }) j3 ON 1 = 1
                 JOIN (SELECT COUNT(*) AS workIssued
                       FROM hv_pm_work_order t5
                       WHERE t5.issued_time BETWEEN #{ startTime } AND #{ endTime }) j4 ON 1 = 1
                 JOIN (SELECT COUNT(*) AS workExecuting
                       FROM hv_pm_work_order t6
                       WHERE t6.work_order_state = 3) j5 ON 1 = 1
                 JOIN (SELECT COUNT(*) AS workFinish
                       FROM hv_pm_work_order t7
                       WHERE t7.actual_end_time BETWEEN #{ startTime } AND #{ endTime }) j6 ON 1 = 1
                 JOIN (SELECT COUNT(*) AS taskWorkCenter FROM hiper_base.dbo.hv_bm_location t8 WHERE t8.type = 50) j7
                     ON 1 = 1
                 JOIN (SELECT COUNT(*) AS taskExecuting
                       FROM hv_pm_order_task t9
                       WHERE t9.state IN (0, 1, 2, 3)) j8 ON 1 = 1
                 JOIN (SELECT COUNT(*) AS taskFinish
                       FROM hv_pm_order_task t10
                       WHERE t10.end_time BETWEEN #{ startTime } AND #{ endTime }) j9 ON 1 = 1
                 JOIN (SELECT COUNT(*) AS product
                       FROM hiper_base.dbo.hv_bm_material t11
                                LEFT JOIN hiper_base.dbo.hv_bm_material_type t12 ON t11.material_type = t12.id
                       WHERE t12.material_type_code = 'Product') j10 ON 1 = 1
                 JOIN (SELECT COUNT(*) AS semiProduct
                       FROM hiper_base.dbo.hv_bm_material t13
                                LEFT JOIN hiper_base.dbo.hv_bm_material_type t14 ON t13.material_type = t14.id
                       WHERE t14.material_type_code = 'Semi_Product') j11 ON 1 = 1
                 JOIN (SELECT COUNT(*) AS rawMaterial
                       FROM hiper_base.dbo.hv_bm_material t15
                                LEFT JOIN hiper_base.dbo.hv_bm_material_type t16 ON t15.material_type = t16.id
                       WHERE t16.material_type_code = 'Raw_Material') j12 ON 1 = 1
                 JOIN (SELECT COUNT(*) AS bomProduct
                       FROM hiper_base.dbo.hv_bm_bom t17
                                LEFT JOIN hiper_base.dbo.hv_bm_bom_material t18 ON t17.id = t18.bom_id
                                LEFT JOIN hiper_base.dbo.hv_bm_material t19 ON t18.material_id = t19.id
                                LEFT JOIN hiper_base.dbo.hv_bm_material_type t20 ON t19.material_type = t20.id
                       WHERE t20.material_type_code
                                 = 'Product') j13 ON 1 = 1
                 JOIN (SELECT COUNT(*) AS bomSemiProduct
                       FROM hiper_base.dbo.hv_bm_bom t21
                                LEFT JOIN hiper_base.dbo.hv_bm_bom_material t22 ON t21.id = t22.bom_id
                                LEFT JOIN hiper_base.dbo.hv_bm_material t23 ON t22.material_id = t23.id
                                LEFT JOIN hiper_base.dbo.hv_bm_material_type t24 ON t23.material_type = t24.id
                       WHERE t24.material_type_code
                                 = 'Semi_Product') j14 ON 1 = 1
                 JOIN (SELECT COUNT(*) AS routeCount FROM hiper_base.dbo.hv_bm_route k1 WHERE k1.route_type = 1) j24 ON 1 = 1
                 JOIN (SELECT COUNT(*) AS productRoute
                       FROM hiper_base.dbo.hv_bm_route t25
                                LEFT JOIN hiper_base.dbo.hv_bm_material t26 ON t25.product_id = t26.id
                                LEFT JOIN hiper_base.dbo.hv_bm_material_type t27 ON t26.material_type = t27.id
                       WHERE t27.material_type_code = 'Product') j15 ON 1 = 1
                 JOIN (SELECT COUNT(*) AS semiProductRoute
                       FROM hiper_base.dbo.hv_bm_route t28
                                LEFT JOIN hiper_base.dbo.hv_bm_material t29 ON t28.product_id = t29.id
                                LEFT JOIN hiper_base.dbo.hv_bm_material_type t30 ON t29.material_type = t30.id
                       WHERE t30.material_type_code = 'Semi_Product') j16 ON 1 = 1
                 JOIN (SELECT COUNT(*) AS crew FROM hiper_base.dbo.hv_bm_crew) j17 ON 1 = 1
                 JOIN (SELECT COUNT(*) AS schedule FROM hiper_base.dbo.hv_bm_shift) j18 ON 1 = 1
                 JOIN (SELECT CASE COUNT(1)
                                  WHEN 0 THEN '未排班'
                                  ELSE '排班完成'
                                      END AS scheduling
                       FROM hiper_base.dbo.hv_bm_schedule t31
                       WHERE datediff(MONTH, t31.schedule_date, getdate()) = 0) j19 ON 1 = 1
                 JOIN (SELECT COUNT(DISTINCT t32.user_id) AS userCount
                       FROM hv_pm_user_work_center t32) j20 ON 1 = 1
                 JOIN (SELECT COUNT(DISTINCT t33.work_center_id) AS workCenterCount
                       FROM hv_pm_user_work_center t33) j21 ON 1 = 1
                  join (select count(*) as changShiftCount  from hv_pm_inspection_item t34) j22 ON 1 = 1
    </select>

    <resultMap id="rm" type="com.hvisions.pms.dto.MaterialStatistics"/>
    <select id="findMaterial" resultMap="rm">
        SELECT t2.material_id,
               t2.material_name,
               SUM(t1.finish_quantity)    quantity,
               SUM(t2.quantity)           planQuantity,
               MAX(t1.actual_issued_time) finishTime
        FROM hv_pm_work_plan_detail t1
                 JOIN hv_pm_work_plan t2 ON t1.work_plan_id = t2.id
        WHERE t2.plan_end_time <![CDATA[ >= ]]> #{beginTime}
          AND t2.plan_start_time  <![CDATA[ <= ]]> #{endTime}
          AND t2.material_name IS NOT NULL
        GROUP BY t2.material_id, t2.material_name
        ORDER BY finishTime
    </select>

    <select id="getTodayPlanned" resultType="com.hvisions.pms.dto.DailyWorkPlanTaskDTO">
        select
            wo.work_order_code workOrderCode,
            wo.work_order_state workOrderState,
            wo.used_type usedType
        from
            hv_pm_work_order wo
        <where>
            DATE_FORMAT(wo.plan_start_time,'%Y-%m-%d') = #{todayString}
            <if test="workOrderState ==1">
                and wo.work_order_state = #{workOrderState}
            </if>
        </where>
    </select>

    <select id="getMonthPlanned" resultType="com.hvisions.pms.dto.DailyWorkPlanTaskDTO">
        select
        wo.work_order_code workOrderCode,
        wo.work_order_state workOrderState,
        wo.used_type usedType
        from
        hv_pm_work_order wo
        <where>
            DATE_FORMAT(wo.plan_start_time,'%Y-%m') = #{monthString}
            <if test="workOrderState ==5">
                and wo.work_order_state = #{workOrderState}
            </if>
        </where>
    </select>
    <select id="getSevenPlanDateList" resultType="java.lang.String">
        SELECT
            DATE( wo.plan_start_time ) date
        FROM
            hv_pm_work_order wo
        WHERE
            wo.plan_start_time BETWEEN #{sevenDaysAgo} AND #{tomorrowDate}
        GROUP BY
            DATE( wo.plan_start_time )
        ORDER BY DATE( wo.plan_start_time ) ASC
    </select>
    <select id="getsevenPlanQuantityList" resultType="java.lang.Integer">
        SELECT
            COUNT( wo.work_order_code ) quantity
        FROM
            hv_pm_work_order wo
        WHERE
            wo.plan_start_time BETWEEN #{sevenDaysAgo} AND #{tomorrowDate}
        GROUP BY
            DATE( wo.plan_start_time )
        ORDER BY DATE( wo.plan_start_time ) ASC
    </select>

    <select id="getSomeTimeTotal" resultType="java.lang.Integer">
        SELECT
            COUNT( wo.work_order_code ) num
        FROM
            hv_pm_work_order wo
        WHERE
            wo.plan_start_time BETWEEN #{startTime} AND #{endTime}
    </select>
    <select id="getWorkOrderBom" resultType="com.hvisions.pms.dto.OrderBomDTO">

          SELECT
               b.bom_code,
               b.bom_name,
               b.bom_count,
               b.bom_versions,
               u2.description                              AS bom_description,
               i.bom_item_name,
               i.bom_item_code,
               u.description                               AS bom_item_unit,
               m.eigenvalue                                AS eigenvalue,
               m.id                                        AS material_id,
               w.quantity * i.bom_item_count / b.bom_count AS quantity,
               e.nestingFilePath                           AS nesting_file_path,
               COALESCE((SELECT SUM(s.quantity) FROM wms.hv_wms_stocks s WHERE s.material_id = m.id), 0) + COALESCE(
                       (SELECT SUM(pfm.quantity)
                        FROM pms.hv_pm_call_frame_material pfm
                        WHERE pfm.material_code = m.material_code
                          AND NOT EXISTS (SELECT 1 FROM wms.hv_wms_stocks s WHERE s.material_id = m.id)),
                       0)                                  AS inventory_quantity
        FROM hv_pm_work_order w
                 LEFT JOIN hiper_base.hv_bm_bom b ON w.bom_id = b.id
                 LEFT JOIN hiper_base.hv_bm_bom_item i ON i.bom_id = b.id
                 LEFT JOIN hiper_base.hv_bm_material m ON i.materials_id = m.id
                 LEFT JOIN hiper_base.hv_bm_unit u ON u.id = m.uom
                 LEFT JOIN hiper_base.hv_bm_unit u2 ON u2.id = b.unit_id
                 LEFT JOIN hiper_base.hv_bm_bom_extend e ON e.bom_id = b.id
        WHERE w.id in
        <foreach collection="orderIds" item="orderId" index="index" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
    </select>


</mapper>
