<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.HvPmAgvTaskRecordMapper">

    <select id="getPage" resultType="com.hvisions.pms.entity.HvPmAgvTaskRecord">
        select r.*
        from hv_pm_agv_task_record r
        <where>
            <if test="query.taskCode != null and query.taskCode !=''">
                and task_code like concat('%',#{query.taskCode},'%')
            </if>

            <if test="query.requestCode != null and query.requestCode !=''">
                and request_code like concat('%',#{query.requestCode},'%')
            </if>

            <if test="query.startPoint != null and query.startPoint !='' ">
                and start_point = #{query.startPoint}
            </if>
            <if test="query.endPoint != null and query.endPoint !='' ">
                and end_point = #{query.endPoint}
            </if>
            <if test="query.frameCode != null and query.frameCode !='' ">
                and frame_code = #{query.frameCode}
            </if>
            <if test="query.taskType != null  and query.taskType !=''">
                and task_type = #{query.taskType}
            </if>

            <if test="query.schedulingState != null  and query.schedulingState !=''">
                and scheduling_state = #{query.schedulingState}
            </if>

            <if test="query.startTimeUTC != null and query.startTimeUTC !=''  and query.endTimeUTC != null  and query.endTimeUTC != '' ">
                and (
                (start_time >= #{query.startTimeUTC} and start_time &lt;= #{query.endTimeUTC}) or
                (end_time >= #{query.startTimeUTC} and end_time &lt;= #{query.endTimeUTC})
                )
            </if>

            <if test="query.materialCode != null and query.materialCode != ''">
                and id in (
                select rask_record_id from hv_pm_agv_task_record_material
                where material_code = #{query.materialCode}
                )
            </if>
        </where>
    </select>
    <select id="getAgvTaskRecordExport" resultType="com.hvisions.pms.exportdto.AgvTaskRecordExportDTO">
        select
        r.id,
        r.request_system,
        r.task_type,
        r.task_code,
        r.request_code,
        r.start_point,
        r.end_point,
        r.frame_code,
        r.frame_type_code,
        r.scheduling_state,
        r.create_time,
        r.start_time,
        r.end_time,
        rm.pn,
        rm.material_code,
        rm.material_name,
        rm.quality,
        rm.material_parent_code,
        rm.work_order_code,
        rm.parent_work_order_code,
        rm.block_code,
        rm.ship_no,
        bm.weight weight
        from hv_pm_agv_task_record r
        LEFT JOIN hv_pm_agv_task_record_material rm ON  r.id = rm.rask_record_id
        LEFT JOIN hiper_base.hv_bm_material bm ON bm.material_code = rm.material_code and bm.eigenvalue =1
        <where>
            <if test="query.taskCode != null and query.taskCode !=''">
                and task_code like concat('%',#{query.taskCode},'%')
            </if>

            <if test="query.requestCode != null and query.requestCode !=''">
                and request_code like concat('%',#{query.requestCode},'%')
            </if>

            <if test="query.startPoint != null and query.startPoint !='' ">
                and start_point = #{query.startPoint}
            </if>
            <if test="query.endPoint != null and query.endPoint !='' ">
                and end_point = #{query.endPoint}
            </if>
            <if test="query.frameCode != null and query.frameCode !='' ">
                and frame_code = #{query.frameCode}
            </if>
            <if test="query.taskType != null  and query.taskType !=''">
                and task_type = #{query.taskType}
            </if>

            <if test="query.schedulingState != null  and query.schedulingState !=''">
                and scheduling_state = #{query.schedulingState}
            </if>

            <if test="query.startTimeUTC != null and query.startTimeUTC !=''  and query.endTimeUTC != null  and query.endTimeUTC != '' ">
                and (
                (start_time >= #{query.startTimeUTC} and start_time &lt;= #{query.endTimeUTC}) or
                (end_time >= #{query.startTimeUTC} and end_time &lt;= #{query.endTimeUTC})
                )
            </if>

            <if test="query.materialCode != null and query.materialCode != ''">
                and id in (
                select rask_record_id from hv_pm_agv_task_record_material
                where material_code = #{query.materialCode}
                )
            </if>
        </where>

    </select>
</mapper>