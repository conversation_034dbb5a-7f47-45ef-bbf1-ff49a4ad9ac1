<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.HvPmMaterialPreparationDetailMapper">
    <select id="getGroupByPreparationId" resultType="com.hvisions.pms.dto.HvPmMaterialPreparationDetailGroupDTO">
        SELECT
            preparation_id,
            material_code,
            sepces,
            SUM(req_quantity) as reqTotalQuantity, -- 需求总数
            SUM(act_quantity)as actTotalQuantity -- 实际总数
        FROM
            `hv_pm_material_preparation_detail`
        WHERE
            preparation_id = #{id}
        GROUP BY material_code,sepces
    </select>
    <select id="getGroupDetailsByQuery" resultType="com.hvisions.pms.dto.HvPmMaterialPreparationDetailDTO">
        SELECT
            *
        FROM
            `hv_pm_material_preparation_detail`
        <where>
            preparation_id = #{query.preparationId}
            AND  material_code =#{query.materialCode}
            AND sepces = #{query.sepces}
            <if test="query.taskCode != null">
                AND task_code like concat('%',#{query.taskCode},'%')
            </if>
            <if test="query.status != null">
                AND status = #{query.status}
            </if>
        </where>

    </select>
    <select id="getDetailByMaterialCodeAndStatus"
            resultType="com.hvisions.pms.dto.HvPmMaterialPreparationDetailDTO">
        SELECT
            d.*,
            p.work_order_code as workOrderCode
        FROM
            `hv_pm_material_preparation_detail` d
             LEFT JOIN hv_pm_material_preparation p ON  p.id = d.preparation_id
        WHERE
            d.material_code = #{materialCode}
          and d.`status` = #{status}
        ORDER BY
            id ASC
    </select>

</mapper>