<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.HvPmCallMaterialRecordDetailMapper">

    <select id="getDetailListByRecordIds" resultType="com.hvisions.pms.entity.HvPmCallMaterialRecordDetail">
        select
            d.*,
            r.request_code,
            m.material_name
        from
            hv_pm_call_material_record_detail d
            LEFT JOIN hv_pm_call_material_record r ON r.id = d.record_id
            LEFT JOIN hiper_base.hv_bm_material m ON m.material_code = d.material_code and m.eigenvalue =1
        <where>
            d.record_id IN
            <foreach collection="recordIds" index="index" item="recordId" open="(" separator="," close=")">
                #{recordId}
            </foreach>
        </where>
    </select>
</mapper>