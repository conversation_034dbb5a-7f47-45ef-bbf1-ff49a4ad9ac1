<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.HvPmMaterialTaoPicMapper">
    <select id="getPage" resultType="com.hvisions.pms.dto.HvPmMaterialTaoPicDTO"
            parameterType="com.hvisions.pms.dto.HvPmXcMaterialCutPlanQueryDTO">
        select xp.id,xp.task_code,xp.line_code,xp.work_order,xp.model,xp.segmentation_code,xp.plan_end_time,xp.send_time,xp.send_user_id,u.user_name
        from hv_pm_material_tao_pic xp left join framework.sys_user u
        on xp.send_user_id=u.id
        <where>
            <if test="query.taskCode != null">
                and xp.task_code like concat('%',#{query.taskCode},'%')
            </if>
            <if test="query.lineCode != null">
                and xp.line_code = #{query.lineCode}
            </if>
            <if test="query.workOrder != null">
                and xp.work_order like concat('%',#{query.workOrder},'%')
            </if>
            <if test="query.beginTime != null">
                and xp.plan_end_time >= #{query.beginTime}
            </if>
            <if test="query.endTime != null">
                and xp.plan_end_time &lt;= #{query.endTime}
            </if>
        </where>
    </select>
</mapper>