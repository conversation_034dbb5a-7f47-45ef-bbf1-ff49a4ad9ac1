<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.MaterialCutPlanProcessRecordMapper">
    <select id="getAll" resultType="com.hvisions.pms.entity.HvPmMaterialCutPlanProcessRecord">
        select
        pr.id,
        pr.line_code lineCode,
        pr.work_order_no workOrderNo,
        ps.step_name stepName,
        ps.sequence sequence,
        pr.step_status stepStatus,
        pr.step_end_time stepEndTime
        from hv_pm_material_cut_plan_process_record pr
        left join hv_pm_material_cut_plan_process_step ps
        on pr.step_id = ps.id
        order by pr.line_code,ps.sequence
    </select>

    <select id="findByLineCode" resultType="com.hvisions.pms.entity.HvPmMaterialCutPlanProcessRecord">
        select
        pr.id,
        pr.line_code lineCode,
        pr.work_order_no workOrderNo,
        ps.step_name stepName,
        ps.sequence sequence,
        pr.step_status stepStatus,
        pr.step_end_time stepEndTime
        from hv_pm_material_cut_plan_process_record pr
        left join hv_pm_material_cut_plan_process_step ps
        on pr.step_id = ps.id
        where pr.line_code = #{lineCode}
        order by pr.line_code,ps.sequence
    </select>

    <update id="updateData">
        update hv_pm_material_cut_plan_process_record
        set step_status = #{hvPmMaterialCutPlanProcessRecord.stepStatus},step_end_time = #{hvPmMaterialCutPlanProcessRecord.stepEndTime},work_order_no = #{hvPmMaterialCutPlanProcessRecord.workOrderNo} where line_code = #{hvPmMaterialCutPlanProcessRecord.lineCode} and step_id = (
            select
            id
            from hv_pm_material_cut_plan_process_step
            where sequence = #{hvPmMaterialCutPlanProcessRecord.sequence}
              and process_id = #{hvPmMaterialCutPlanProcessRecord.processId}
        )
    </update>
</mapper>