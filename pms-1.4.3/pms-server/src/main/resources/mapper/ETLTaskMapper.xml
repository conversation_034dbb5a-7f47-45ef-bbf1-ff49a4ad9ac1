<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.ETLTaskMapper">


    <select id="materialCompleteness" resultType="java.lang.Double">
        SELECT
            CASE
                WHEN COUNT(*) = 0 THEN 0
                WHEN COUNT(*) > 0 THEN
                    ROUND( SUM( CASE WHEN complete_set_check_status = 1 THEN 1.0 ELSE 0 END ) / NULLIF( COUNT(*), 0 ), 2 )*100
                END AS rate
        FROM
            hv_pm_work_order
        WHERE
            used_type = 0
          AND DATE( create_time ) = #{date};
    </select>

    <select id="getPlanCountByUsedType" resultType="com.hvisions.thirdparty.common.dto.ETLPlanQualityDTO">
        SELECT
            cell_id AS lineCode,
            COALESCE(SUM(CASE WHEN DATE(plan_start_time) = #{date} THEN quantity ELSE 0 END), 0) AS `planQuality`,
            COALESCE(SUM(CASE WHEN DATE(actual_end_time) = #{date} AND work_order_state = 5 THEN quantity ELSE 0 END), 0) AS `finishQuality`
        FROM
            hv_pm_work_order
        WHERE
            used_type = #{usedType}
        GROUP BY
            cell_id
    </select>
    <select id="getSteelPlanCount" resultType="com.hvisions.thirdparty.common.dto.ETLPlanQualityDTO">
        SELECT
            line_id AS lineCode,
            COALESCE(SUM(CASE WHEN DATE(send_time) = #{date} THEN quality * cut_quality ELSE 0 END), 0) AS `planQuality`,
            COALESCE(SUM(CASE WHEN STATUS = 10 AND DATE(finish_time) = #{date} THEN quality * cut_quality ELSE 0 END), 0) AS `finishQuality`
        FROM
            hv_pm_material_cut_plan
        GROUP BY
            line_id;
    </select>

    <select id="getXCPlanCount" resultType="com.hvisions.thirdparty.common.dto.ETLPlanQualityDTO">
        SELECT
            line_id AS lineCode,
            COUNT(CASE WHEN DATE(send_time) = #{date} THEN 1 END) AS planQuality,
            COUNT(CASE WHEN STATUS = 6 AND DATE(finish_time) = #{date} THEN 1 END) AS finishQuality
        FROM
            hv_pm_xc_material_cut_plan
        GROUP BY
            line_id;
    </select>

    <select id="getSteelSchedule" resultType="com.hvisions.thirdparty.common.dto.ETLSteelScheduleDTO">
        SELECT
            subquery.lineCode,
            subquery.planQuality AS partNumber,
            subquery.planTotalPartWeight AS totalPartWeight,
            CASE
                WHEN subquery.planQuality > 0 THEN
                    CAST( COALESCE ( subquery2.finishQuantity, 0 ) AS FLOAT ) / subquery.planQuality ELSE 0
                END AS SCHEDULE
        FROM
            (
                SELECT
                    line_id AS lineCode,
                    COALESCE ( SUM( CASE WHEN DATE( send_time ) = #{date} THEN quality * cut_quality ELSE 0 END ), 0 ) AS planQuality,
                    COALESCE ( SUM( CASE WHEN DATE( send_time ) = #{date} THEN total_part_weight ELSE 0 END ), 0 ) AS planTotalPartWeight
                FROM
                    hv_pm_material_cut_plan
                GROUP BY
                    line_id
            ) AS subquery
                LEFT JOIN (
                SELECT
                    cp.line_id,
                    COALESCE ( SUM( lr.report_qty ), 0 ) AS finishQuantity
                FROM
                    hv_pm_material_cut_plan cp
                        LEFT JOIN hv_pm_material_cut_plan_detail0 cpd ON cpd.cut_plan_id = cp.id
                        LEFT JOIN hv_pm_line_report lr ON lr.order_code = cpd.work_order
                WHERE
                    DATE( cp.send_time ) = #{date}
        GROUP BY
            cp.line_id
            ) AS subquery2 ON subquery.lineCode = subquery2.line_id
    </select>
    <select id="getSteelCutTime" resultType="com.hvisions.thirdparty.common.dto.ETLSteelCutTimeDTO">
        SELECT
            cp.line_id AS lineCode,
            cp.cut_plan_code AS taskNumber,
            TIMESTAMPDIFF(MINUTE,
                    STR_TO_DATE(cpd2.start_time , '%Y-%m-%d %H:%i:%s'),
                    STR_TO_DATE(cpd2.end_time, '%Y-%m-%d %H:%i:%s')) AS actValue
        FROM
            hv_pm_material_cut_plan cp
            LEFT JOIN hv_pm_material_cut_plan_detail2 cpd2 ON cp.cut_plan_code = cpd2.cut_plan_code
        where
          cp.STATUS = 8
          AND cpd2.operation_type = '切割结束'
          AND cpd2.start_time IS NOT NULL
          AND cpd2.end_time IS NOT NULL
    </select>
    <select id="getProfileScheduleDTO" resultType="com.hvisions.thirdparty.common.dto.ETLProfileScheduleDTO">
        SELECT
            cell_id as lineCode,
            CASE
                WHEN COUNT(*) = 0 THEN 0
                WHEN COUNT(*) > 0 THEN
                    ROUND( SUM( CASE WHEN work_order_state = 10 THEN 1.0 ELSE 0 END ) / NULLIF( COUNT(*), 0 ), 2 )*100
                END AS schedule,
            SUM( CASE WHEN work_order_state = 10 THEN 1 ELSE 0 END ) AS finishQuantity,
            COUNT(*)  AS  planQuantity
        FROM
            hv_pm_work_order
        WHERE
            used_type = 2
          AND DATE( create_time ) = #{date}
        GROUP BY
            cell_id
    </select>

    <select id="getProfileGenDTO" resultType="com.hvisions.thirdparty.common.dto.ETLProfileGenDTO">
        SELECT
            line_id as lineCode,
            COUNT(*) as actValue
        FROM
            hv_pm_xc_material_cut_plan p
                LEFT JOIN hv_pm_xc_material_cut_plan_detail0 d0 ON p.order_no = d0.order_no
        WHERE
            DATE( create_time ) = #{date}
        GROUP BY
            line_id
    </select>



</mapper>