<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.HvPmLineReportMaterialMapper">
    <select id="findByCondition" resultType="com.hvisions.pms.dto.HvPmLineReportMaterialDTO">
        select
        id,
        report_id reportId,
        material_code materialCode,
        pn,frame_code,
        quality,
        qualified_qty qualifiedQty,
        loss_qty lossQty,
        scrap_qty scrapQty,
        repair_qty repairQty,
        cuttingLength,
        damagedLength
        from hv_pm_line_report_material
        <where>
            report_id = #{condition.reportId}
            <if test="condition.materialCode != null and condition.materialCode != ''">
                and material_code = #{condition.materialCode}
            </if>

            <if test="condition.pn != null and condition.pn != ''">
                and pn = #{condition.pn}
            </if>
        </where>
    </select>

    <select id="selectByMaterialAndFrameCode" resultType="com.hvisions.pms.entity.HvPmLineReportMaterial">
        select *
        from hv_pm_line_report_material
        <where>
            report_id = (select report_id
            from hv_pm_line_report_material
            <where>
                <if test="frameCode !=null and frameCode !=''">
                    and frame_code = #{frameCode}
                </if>
                <if test="materialCode !=null and materialCode !=''">
                    and material_code = #{materialCode}
                </if>
            </where>
            order by id desc
            limit 1 )
            <if test="materialCode !=null and materialCode !=''">
                and material_code = #{materialCode}
            </if>
        </where>
    </select>
    <select id="getlineReportMaterialListByReportId"
            resultType="com.hvisions.pms.exportdto.LineReportDetail0ExportDTO">
        SELECT m.*,r.order_code FROM
                     hv_pm_line_report_material m left join  hv_pm_line_report r ON r.id = m.report_id
        WHERE m.report_id IN
        <foreach  item="reportId" index="index" collection="reportIds" open="(" separator="," close=")">
            #{reportId}
        </foreach>
    </select>

</mapper>