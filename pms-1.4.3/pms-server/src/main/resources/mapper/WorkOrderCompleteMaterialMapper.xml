<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.WorkOrderCompleteMaterialMapper">
    <select id="getPage" resultType="com.hvisions.pms.dto.HvPmWorkOrderCompleteMaterialDTO">
        select
            *
        from hv_pm_work_order_complete_material m
        <where>
            <if test="query.orderCode != null">
                and order_code =#{query.orderCode}
            </if>
        </where>
    </select>

</mapper>