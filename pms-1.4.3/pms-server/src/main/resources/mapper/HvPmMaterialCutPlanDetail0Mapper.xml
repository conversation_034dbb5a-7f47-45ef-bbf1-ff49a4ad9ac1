<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.HvPmMaterialCutPlanDetail0Mapper">

    <select id="getDetail0DTOListByCutPlanId" resultType="com.hvisions.pms.plan.HvPmMaterialCutPlanDetail0DTO">
        SELECT
            d.* ,
            wo.block_code blockCode
        FROM
            `hv_pm_material_cut_plan_detail0` d
            LEFT JOIN hv_pm_work_order wo ON wo.work_order_code = d.work_order
        WHERE
            d.cut_plan_id =#{cutPlanId}
    </select>
    <select id="getWorkOrderByCutPlanCode" resultType="java.lang.String">
        SELECT
            d.work_order
        FROM
            hv_pm_material_cut_plan p
                LEFT JOIN  `hv_pm_material_cut_plan_detail0` d ON  d.cut_plan_id =  p.id
        WHERE
            d.cut_plan_code = #{cutPlanCode}
    </select>

    <select id="getAll" resultType="com.hvisions.pms.plan.HvPmMaterialCutPlanDetail0DTO">
        SELECT
            d.* ,
            wo.block_code blockCode
        FROM
            `hv_pm_material_cut_plan_detail0` d
                LEFT JOIN hv_pm_work_order wo ON wo.work_order_code = d.work_order
    </select>

</mapper>