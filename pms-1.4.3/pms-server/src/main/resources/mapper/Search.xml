<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.SearchMapper">
    <resultMap id="wo" type="com.hvisions.pms.rh.dto.RHWorkOrderDTO">
    </resultMap>

    <resultMap id="operationId" type="com.hvisions.pms.dto.OrderOperationDTO">
    </resultMap>

    <resultMap id="wo1" type="com.hvisions.pms.rh.dto.QueryOrderResultDTO">
    </resultMap>

    <resultMap id="orderState" type="com.hvisions.pms.dto.OrderStateCountDTO">

    </resultMap>

    <resultMap id="operationState" type="com.hvisions.pms.task.dto.TaskStateCountDTO">

    </resultMap>

    <select id="findOrderByPot" parameterType="java.util.Map" resultMap="wo" databaseId="mysql">
        select
        t1.work_order_code orderNo,
        t1.plan_code batchNo,
        t1.route_name craftRouteName,
        t5.plan_status planStatus,
        t1.work_order_state completedStatus,
        t3.bom_code bomNo,
        t1.material_name materialName,
        t4.material_code materialCode,
        t1.plan_start_time planStartDate,
        t1.plan_end_time planEndDate
        from
        hv_pm_work_order t1
        left join hiper_base.hv_bm_bom t3
        on t1.bom_id = t3.id
        left join hiper_base.hv_bm_material t4
        on t1.material_id = t4.id
        left join hv_pm_work_plan t5
        on t1.plan_code = t5.plan_code
        where exists (
        select 1
        from hv_pm_order_operation t2
        where t2.order_id = t1.id
        <if test="potNo != null and potNo !=''">
            and t2.equipment_code = #{potNo,jdbcType=VARCHAR}
        </if>
        <if test="now !=null   ">
            AND t2.create_time &gt; #{now,jdbcType=DATE}
        </if>
        ) group by t1.id
    </select>
    <select id="findOrderByPot" parameterType="java.util.Map" resultMap="wo" databaseId="sqlserver">
        select
        t1.work_order_code orderNo,
        t1.plan_code batchNo,
        t1.route_name craftRouteName,
        t5.plan_status planStatus,
        t1.work_order_state completedStatus,
        t3.bom_code bomNo,
        t1.material_name materialName,
        t4.material_code materialCode,
        t1.plan_start_time planStartDate,
        t1.plan_end_time planEndDate
        from
        hv_pm_work_order t1
        left join hiper_base.dbo.hv_bm_bom t3
        on t1.bom_id = t3.id
        left join hiper_base.dbo.hv_bm_material t4
        on t1.material_id = t4.id
        left join hv_pm_work_plan t5
        on t1.plan_code = t5.plan_code
        where exists (
        select 1
        from hv_pm_order_operation t2
        where t2.order_id = t1.id
        <if test="potNo != null and potNo !=''">
            and t2.equipment_code = #{potNo,jdbcType=VARCHAR}
        </if>
        <if test="now !=null   ">
            AND t2.create_time &gt; #{now,jdbcType=DATE}
        </if>
        ) group by t1.id
    </select>

    <select id="findOperationByOrderCode" parameterType="java.util.Map" resultMap="operationId">
        select *
        from hv_pm_order_operation h
        where h.work_order_code = #{orderNo,jdbcType=VARCHAR}
    </select>
    <select id="findOrderByInputMaterial" parameterType="java.util.Map" resultMap="wo1" databaseId="mysql">
        select
        t1.work_order_code batchCode,
        t1.bom_version bomRevision,
        t1.actual_start_time startTime,
        t1.actual_end_time endTime,
        t3.material_name materialName,
        t1.bom_id bomId,
        t4.name workLine,
        t3.actual_count quantity
        from hv_pm_operation_material t3
        left join hv_pm_order_operation t2
        on t2.id = t3.operation_id
        left join hv_pm_work_order t1
        on t1.id = t2.order_id
        left join hiper_base.hv_bm_location t4
        on t1.cell_id = t4.id
        <where>
            <if test="materialBatchCode!=null and materialBatchCode!=''">
                and t3.batch_number like concat ('%',#{materialBatchCode},'%')
            </if>
            <if test="materialCode!=null and materialCode!=''">
                and t3.material_code like concat ('%',#{materialCode},'%')
            </if>
            <if test="consumeStartTime!=null and consumeEndTime !=nul">
                and t1.actual_start_time &lt;= #{consumeStartTime}
                and (t1.actual_end_time is null or t1.actual_end_time &gt;= #{consumeEndTime})
            </if>
        </where>
    </select>
    <select id="findOrderByInputMaterial" parameterType="java.util.Map" resultMap="wo1" databaseId="sqlserver">
        select
        t1.work_order_code batchCode,
        t1.bom_version bomRevision,
        t1.actual_start_time startTime,
        t1.actual_end_time endTime,
        t3.material_name materialName,
        t1.bom_id bomId,
        t4.name workLine,
        t3.actual_count quantity
        from hv_pm_operation_material t3
        left join hv_pm_order_operation t2
        on t2.id = t3.operation_id
        left join hv_pm_work_order t1
        on t1.id = t2.order_id
        left join hiper_base.dbo.hv_bm_location t4
        on t1.cell_id = t4.id
        <where>
            <if test="materialBatchCode!=null and materialBatchCode!=''">
                and t3.batch_number like concat ('%',#{materialBatchCode},'%')
            </if>
            <if test="materialCode!=null and materialCode!=''">
                and t3.material_code like concat ('%',#{materialCode},'%')
            </if>
            <if test="consumeStartTime!=null and consumeEndTime !=nul">
                and t1.actual_start_time &lt;= #{consumeStartTime}
                and (t1.actual_end_time is null or t1.actual_end_time &gt;= #{consumeEndTime})
            </if>
        </where>
    </select>


    <select id="getOrderStateCount" resultMap="orderState">
        select
        count(o.work_order_state) as state_count,
        o.work_order_state
        FROM
        ( SELECT hv_pm_work_order.id orderId,
        hv_pm_work_order.work_order_code workOrderCode,
        hv_pm_work_order.plan_code planCode,
        hv_pm_work_order.create_time createTime,
        hv_pm_work_order.work_order_state,
        hv_pm_work_order.material_code materialCode,
        hv_pm_work_order.material_name materialName,
        hv_pm_work_order.plan_end_time planEndTime,
        hv_pm_work_order.quantity ,
        hv_pm_work_order.route_id routeId,
        hv_pm_work_order.route_name routeName,
        hv_pm_work_order.route_code routeCode,
        hv_pm_work_order.route_version routeVersion,
        hv_pm_work_order.bom_version bomVersion,
        hv_pm_work_order.issued_time issuedTime,
        hv_pm_work_order.actual_end_time acturalEndTime,
        hv_pm_work_order.eigenvalue ,
        hv_pm_work_order.plan_start_time planStartTime,
        hv_pm_work_order.serial_number serialNumber,
        hv_pm_work_order.material_id materialId,
        hv_pm_work_order.actual_start_time actualStartTime,
        hv_pm_work_order.shift_id shiftId,
        hv_pm_work_order.crew_id crewId,
        hv_pm_work_order.shift_name shiftName,
        hv_pm_work_order.crew_name crewName,
        hv_pm_work_order.bom_id bomId,
        hv_pm_work_order.area_id areaId,
        hv_pm_work_order.cell_id cellId,
        hv_pm_work_order.update_time updateTime,
        hv_pm_work_order.creator_id creatorId,
        hv_pm_work_order.updater_id updaterId,
        hv_pm_work_order.actual_count actualCount,
        hv_pm_work_order.site_num siteNum,
        hv_pm_work_order.plan_or_new planOrNew,
        hv_pm_work_order.order_mode orderMode,
        hv_pm_work_order_extend.*,
        hv_pm_order_type.order_type_code as orderTypeCode,
        hv_pm_order_type.order_type_name as orderTypeName,
        hv_pm_order_type.id as orderTypeId
        FROM hv_pm_work_order
        left join hv_pm_work_order_extend on hv_pm_work_order.id = hv_pm_work_order_extend.work_order_id
        left join hv_pm_order_type on hv_pm_work_order.order_type_id = hv_pm_order_type.id

        <where>
            <if test="query.planCodeEqual != null and query.planCodeEqual != ''">
                and hv_pm_work_order.plan_code = #{query.planCodeEqual}
            </if>
            <if test="query.planCodeLike != null and query.planCodeLike != ''">
                and hv_pm_work_order.plan_code like concat ('%',#{query.planCodeLike},'%')
            </if>
            <if test="query.materialId != null">
                and hv_pm_work_order.material_id = #{query.materialId}
            </if>
            <if test="query.planStartTimeStart != null and query.planStartTimeEnd != null">
                and hv_pm_work_order.plan_start_time between #{query.planStartTime} and #{query.planStartEnd}
            </if>
            <if test="query.planEndTimeStart != null and query.planEndTimeEnd != null">
                and hv_pm_work_order.plan_end_time between #{query.planEndTimeStart} and #{query.planEndTimeEnd}
            </if>
            <if test="query.routeId != null">
                and hv_pm_work_order.route_id = #{query.routeId}
            </if>
            <if test="query.routeCode != null and query.routeCode != ''">
                and hv_pm_work_order.route_code = #{query.routeCode}
            </if>
            <if test="query.materialCodeLike != null and query.materialCodeLike != ''">
                and hv_pm_work_order.material_code like concat('%',#{query.materialCodeLike},'%')
            </if>

            <if test="query.materialCodeEqual != null and query.materialCodeEqual != ''">
                and hv_pm_work_order.material_code = #{query.materialCodeEqual}
            </if>
            <if test="query.actualStartTimeStart != null and query.actualStartTimeEnd != null">
                and hv_pm_work_order.actual_start_time between #{query.actualStartTimeStart} and
                #{query.actualStartTimeEnd}
            </if>
            <if test="query.actualEndTimeStart != null and query.actualEndTimeEnd != null">
                and hv_pm_work_order.actual_end_time between #{query.actualEndTimeStart} and #{query.actualEndTimeEnd}
            </if>

            <if test="query.workOrderCodeEqual != null and query.workOrderCodeEqual != ''">
                and hv_pm_work_order.work_order_code = #{query.workOrderCodeEqual}
            </if>

            <if test="query.workOrderCodeLike != null and query.workOrderCodeLike != ''">
                and hv_pm_work_order.work_order_code like concat('%',#{query.workOrderCodeLike},'%')
            </if>

            <if test="query.issuedTimeStart != null and query.issuedTimeEnd != null">
                and hv_pm_work_order.issued_time between #{query.issuedTimeStart} and #{query.issuedTimeEnd}
            </if>

            <if test="query.shiftId != null">
                and hv_pm_work_order.shift_id = #{query.shiftId}
            </if>
            <if test="query.shiftName != null">
                and hv_pm_work_order.shift_name = #{query.shiftName}
            </if>

            <if test="query.areaId != null">
                and hv_pm_work_order.area_id = #{query.areaId}
            </if>
            <if test="query.cellId != null">
                and hv_pm_work_order.cell_id = #{query.cellId}
            </if>
            <if test="query.crewId != null">
                and hv_pm_work_order.crew_id = #{query.crewId}
            </if>
            <if test="query.crewName != null">
                and hv_pm_work_order.crew_name = #{query.crewName}
            </if>

            <if test="query.bomId != null">
                and hv_pm_work_order.bom_id = #{query.bomId}
            </if>
            <if test="query.planOrNew != null">
                and hv_pm_work_order.plan_or_new = #{query.planOrNew}
            </if>
            <if test="query.orderMode != null">
                and hv_pm_work_order.order_mode = #{query.orderMode}
            </if>
            <if test="query.orderTypeId != null">
                and hv_pm_order_type.id = #{query.orderTypeId}
            </if>
            <if test="query.orderTypeCode != null">
                and hv_pm_order_type.order_type_code like concat('%',#{query.orderTypeCode},'%')
            </if>
            <if test="query.orderTypeName != null">
                and hv_pm_order_type.order_type_name like concat('%',#{query.orderTypeName},'%')
            </if>
            <if test="query.workOrderState != null">
                and hv_pm_work_order.work_order_state = #{query.workOrderState}
            </if>
            <if test="query.usedType != null">
                and hv_pm_work_order.used_type = #{query.usedType}
            </if>
            <if test="query.workOrderStates !=null and query.workOrderStates.size > 0">
                <foreach collection="query.workOrderStates" index="index" item="item"
                         open="and hv_pm_work_order.work_order_state in("
                         separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.extendQueries !=null and query.extendQueries.size > 0">
                <foreach collection="query.extendQueries" index="index" item="item" open=""
                         separator=""
                         close="">
                    <if test="item.andOr == null or item.andOr == 1">
                        and
                    </if>
                    <if test="item.andOr != null and item.andOr == 2">
                        or
                    </if>
                    hv_pm_work_order_extend.${item.columnName}
                    <if test="item.symbol == null or item.symbol == 1">
                        =
                    </if>
                    <if test="item.symbol != null and item.symbol == 2">
                        &gt;
                    </if>
                    <if test="item.symbol != null and item.symbol == 3">
                        &gt;=
                    </if>
                    <if test="item.symbol != null and item.symbol == 4">
                        &lt;
                    </if>
                    <if test="item.symbol != null and item.symbol == 5">
                        &lt;=
                    </if>
                    <if test="item.symbol != null and item.symbol == 6">
                        is null
                    </if>
                    <if test="item.symbol != null and item.symbol == 7">
                        is not null
                    </if>
                    <if test="item.symbol != null and item.symbol == 8">
                        like
                    </if>
                    <if test="item.value != null ">
                        #{item.value}
                    </if>
                </foreach>
            </if>
        </where>
        ) o
        where
        o.work_order_state in (0,1,3)
        group by o.work_order_state
    </select>

    <select id="getOperationStateCount" resultMap="operationState"
            parameterType="com.hvisions.pms.task.dto.TaskStateCountQuery" databaseId="mysql">
        select count(o.state) as state_count, o.state
        FROM hv_pm_order_task o
        left join hv_pm_task_user t1 on o.id = t1.task_id
        <where>
            o.state in (1, 2, 3)
            <if test="dto.workCenterId != null">
                and o.work_center_id = #{dto.workCenterId}
            </if>
            <if test="dto.userId !=null">
                and (t1.user_id = #{dto.userId} or t1.user_id is null )
            </if>
        </where>
        group by o.state
    </select>

    <select id="getOperationStateCount" resultMap="operationState"
            parameterType="com.hvisions.pms.task.dto.TaskStateCountQuery"
            databaseId="sqlserver">
        select count(o.state) as state_count, o.state
        FROM hv_pm_order_task o
        left join hv_pm_task_user t1 on o.id = t1.task_id
        <where>
            o.state in (1, 2, 3)
            <if test="dto.workCenterId != null">
                and o.work_center_id = #{dto.workCenterId}
            </if>
            <if test="dto.userId !=null">
                and (t1.user_id = #{dto.userId} or t1.user_id is null )
            </if>
        </where>
        group by o.state
    </select>


    <resultMap id="trace" type="com.hvisions.pms.materialdto.MaterialTraceDTO"></resultMap>
    <select id="getTraceByBatchNum" resultMap="trace"
            parameterType="com.hvisions.pms.materialdto.TraceQuery" databaseId="mysql">
        SELECT
        t1.id,
        t3.id as orderId,
        t3.work_order_code as orderCode,
        t1.material_code,
        t1.material_name,
        t1.batch_number,
        t3.actual_start_time as startTime,
        t3.actual_end_time as endTime,
        t3.bom_version,
        t3.quantity as planCount,
        t3.actual_count
        FROM
        hv_pm_operation_out_put_material t1
        LEFT JOIN hv_pm_order_operation t2 ON t1.operation_id = t2.id
        LEFT JOIN hv_pm_work_order t3 ON t2.order_id = t3.id
        <where>
            <if test="dto.batchNum !=null">
                and t1.batch_number like concat('%',#{dto.batchNum},'%')
            </if>
            <if test="dto.materialCode !=null ">
                and t1.material_code like concat('%',#{dto.materialCode},'%')
            </if>
            <if test="dto.materialName !=null ">
                and t1.material_name like concat('%',#{dto.materialName},'%')
            </if>
            <if test="dto.startTime !=null and dto.endTime != null">
                and t3.actual_start_time between #{dto.startTime} and #{dto.endTime}
            </if>
        </where>
    </select>

    <select id="getTraceByBatchNum" resultMap="trace"
            parameterType="com.hvisions.pms.materialdto.TraceQuery" databaseId="sqlserver">
        SELECT
        t1.id,
        t3.id as orderId,
        t3.work_order_code as orderCode,
        t1.material_code,
        t1.material_name,
        t1.batch_number,
        t3.actual_start_time as startTime,
        t3.actual_end_time as endTime,
        t3.bom_version,
        t3.quantity as planCount,
        t3.actual_count
        FROM
        hv_pm_operation_out_put_material t1
        LEFT JOIN hv_pm_order_operation t2 ON t1.operation_id = t2.id
        LEFT JOIN hv_pm_work_order t3 ON t2.order_id = t3.id
        <where>
            <if test="dto.batchNum !=null">
                and t1.batch_number like concat('%',#{dto.batchNum},'%')
            </if>
            <if test="dto.materialCode !=null ">
                and t1.material_code like concat('%',#{dto.materialCode},'%')
            </if>
            <if test="dto.materialName !=null ">
                and t1.material_name like concat('%',#{dto.materialName},'%')
            </if>
            <if test="dto.startTime !=null and dto.endTime != null">
                and t3.actual_start_time between #{dto.startTime} and #{dto.endTime}
            </if>
        </where>
    </select>

</mapper>