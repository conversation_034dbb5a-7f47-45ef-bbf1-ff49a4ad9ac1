<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.HvPmMaterialBomItemMapper">


    <select id="getBomItemsByRequirementCode"
            resultType="com.hvisions.pms.dto.materialRequirement.HvPmMaterialBomItemDTO">

        SELECT
            id,
        material_code AS materialCode,
        material_name AS materialName,
        material_unit_name AS materialUnitName,
        material_required_quantity AS materialRequiredQuantity,
        material_requirement_code AS materialRequirementCode
        FROM
        hv_pm_material_bom_item
        WHERE
        material_requirement_code = #{requirementCode}
    </select>

    <select id="getPageMaterialByRequirementCode"
            resultType="com.hvisions.pms.dto.materialRequirement.HvPmMaterialBomItemDTO">
            SELECT
                id,
                material_code AS materialCode,
                material_name AS materialName,
                material_unit_name AS materialUnitName,
                material_required_quantity AS materialRequiredQuantity,
                material_requirement_code AS materialRequirementCode
            FROM
                hv_pm_material_bom_item
            WHERE
                material_requirement_code = #{query.materialRequirementCode}
    </select>
</mapper>