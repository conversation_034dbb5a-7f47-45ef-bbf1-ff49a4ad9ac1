<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.HvPmMaterialTaoPicDetailMapper">
    <select id="getPage" resultType="com.hvisions.pms.dto.HvPmMaterialTaoPicDetailDTO"
            parameterType="com.hvisions.pms.dto.HvPmMaterialTaoPicDetailQueryDTO">
        select tp.id,tp.tao_pic_id,tp.tao_file_code,tp.specifications,tp.length,tp.quantity,tp.weight
        from hv_pm_material_tao_pic_detail tp
        <where>
            <if test="query.taoPicId != null">
                and tp.tao_pic_id = #{query.taoPicId}
            </if>
        </where>
    </select>
</mapper>