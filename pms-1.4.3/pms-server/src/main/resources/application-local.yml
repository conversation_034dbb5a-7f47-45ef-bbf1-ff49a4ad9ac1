#dev配置
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: 123456
    url: *******************************************************************************************
  rabbitmq:
    host: localhost
    port: 5672
    username: admin
    password: admin
ribbon:
  #请求处理的超时时间
  ReadTimeout: 120000
  #请求连接的超时时间
  ConnectTimeout: 30000
h-visions:
  #当配置为true验证产出料是否与配置一致
  outPutMaterial: true
  #物料批次号
  materialBatchNum: "(?<code>{8})"
  service-name: 工单管理服务
  work-order:
    lock:
      task: true
  change:
    crew: false
  order-type: "default"
  order-type-name: "默认工单类型"
server:
  port: 9080