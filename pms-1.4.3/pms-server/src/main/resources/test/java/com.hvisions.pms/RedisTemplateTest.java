package com.hvisions.pms;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Set;

@SpringBootTest
public class RedisTemplateTest {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Test
    public void testRedisTemplate() {
        redisTemplate.opsForValue().set("testKey", "testValue");
        String value = redisTemplate.opsForValue().get("testKey");
        System.out.println("从 Redis 获取的值: " + value);
        long now = System.currentTimeMillis();
        redisTemplate.opsForZSet().add("delay:cut_plan", String.valueOf(534534), now);

        String delayQueueKey = "delay:cut_plan";
        Set<String> planIds = redisTemplate.opsForZSet().rangeByScore(delayQueueKey, 0, now);
        System.out.println("从 Redis 获取的值: " + planIds);
    }
}