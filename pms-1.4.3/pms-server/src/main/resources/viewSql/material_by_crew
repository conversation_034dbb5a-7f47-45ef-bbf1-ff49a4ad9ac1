SELECT
t1.crew_name AS crew_name,
t2.id AS operation_oid,
t3.out_put_count AS out_put_count,
t3.material_id AS material_id,
t1.actual_end_time AS actual_end_time,
t1.actual_start_time AS actual_start_time,
t1.cell_id AS cell_id,
t2.operation_code AS operation_code,
t2.start_time AS start_time,
t2.end_time AS end_time,
t3.material_name AS material_name,
t3.material_code AS material_code
FROM
	( ( hv_pm_work_order t1 LEFT JOIN hv_pm_order_task t2 ON t1.id = t2.order_id ) LEFT JOIN hv_pm_operation_out_put_material t3 ON t2.id = t3.operation_id )