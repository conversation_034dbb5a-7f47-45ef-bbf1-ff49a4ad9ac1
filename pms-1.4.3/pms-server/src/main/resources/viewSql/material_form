SELECT
	t1.material_code AS material_code,
	t1.material_name AS material_name,
	t1.material_eigenvalue AS material_eigenvalue,
	t1.material_id AS material_id,
	t1.actual_count AS actual_count,
	t1.plan_count AS plan_count,
	t1.operation_id AS operation_id,
	t2.operation_code AS operation_code,
	t3.plan_start_time AS plan_start_time,
	t3.plan_end_time AS plan_end_time,
	t3.work_order_code AS work_order_code,
	t3.crew_name AS crew_name,
	t3.actual_end_time AS actual_end_time,
	t3.actual_start_time AS actual_start_time,
	t3.cell_id AS cell_id
FROM
	( ( hv_pm_operation_material t1  JOIN hv_pm_order_operation t2 on t1.operation_id = t2.id ) JOIN hv_pm_work_order t3 on t2.order_id =t3.id )