package com.hvisions.pms.controller;


import com.hvisions.pms.dto.HvPmStockMovementDTO;
import com.hvisions.pms.entity.HvPmStockMovement;
import com.hvisions.pms.service.HvPmStockMovementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Api(tags = "HvPmStockMovementController")
@RestController
@RequestMapping("/stockMovement")
public class HvPmStockMovementController {

    @Autowired
    private HvPmStockMovementService hvPmStockMovementService;

    @ApiOperation("新增")
    @PostMapping("/save")
    public void save(@RequestBody HvPmStockMovement hvPmStockMovement) {
        hvPmStockMovementService.save(hvPmStockMovement);
    }

    @ApiOperation("根据任务号查询")
    @PostMapping("/findByTaskNo")
    public List<HvPmStockMovementDTO> findByTaskNo(@RequestParam String taskNo) {
        return hvPmStockMovementService.findByTaskNo(taskNo);
    }

    @ApiOperation("根据工单号和任务查询单个")
    @PostMapping("/findByWorkOrderCodeAndTaskNo")
    public HvPmStockMovementDTO findByWorkOrderCodeAndTaskNo(@RequestParam String workOrderCode, @PathVariable Long planProductBomId) {
        return hvPmStockMovementService.findByWorkOrderCodeAndTaskNo(workOrderCode, planProductBomId);
    }

    @ApiOperation("根据工单号和状态查询列表")
    @PostMapping("/findByWorkOrderCodeAndState")
    public List<HvPmStockMovementDTO> findByWorkOrderCodeAndState(@RequestParam String workOrderCode, Integer state) {
        return hvPmStockMovementService.findByWorkOrderCodeAndState(workOrderCode, state);
    }

    @ApiOperation("库存占用转移")
    @PostMapping("/modifyInventory")
    public void modifyInventory(@RequestBody List<HvPmStockMovementDTO> data) {
        hvPmStockMovementService.modifyInventory(data);
    }
}
