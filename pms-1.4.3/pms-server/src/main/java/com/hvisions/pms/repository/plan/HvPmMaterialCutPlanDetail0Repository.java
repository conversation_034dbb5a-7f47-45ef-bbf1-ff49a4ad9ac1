package com.hvisions.pms.repository.plan;

import com.hvisions.pms.entity.plan.HvPmMaterialCutPlanDetail0;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface HvPmMaterialCutPlanDetail0Repository extends JpaRepository<HvPmMaterialCutPlanDetail0,Long> {
    List<HvPmMaterialCutPlanDetail0> getAllByCutPlanId(long id);

    void deleteByCutPlanId(long cutPlanId);

    @Query("SELECT d FROM HvPmMaterialCutPlanDetail0 d WHERE d.cutPlanId IN :cutPlanIds")
    List<HvPmMaterialCutPlanDetail0> findByCutPlanIds(@Param("cutPlanIds") List<Long> cutPlanIds);

    List<HvPmMaterialCutPlanDetail0> findByMaterialCode(String materialCode);
}
