package com.hvisions.pms.repository.plan;

import com.hvisions.pms.entity.plan.HvPmMaterialCutPlanDetail1;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface HvPmMaterialCutPlanDetail1Repository extends JpaRepository<HvPmMaterialCutPlanDetail1,Long> {
    List<HvPmMaterialCutPlanDetail1> getAllByCutPlanId(long id);
    void deleteByCutPlanId(long cutPlanId);

    @Query("SELECT d FROM HvPmMaterialCutPlanDetail1 d WHERE d.cutPlanId IN :cutPlanIds")
    List<HvPmMaterialCutPlanDetail1> findByCutPlanIds(@Param("cutPlanIds") List<Long> cutPlanIds);
}
