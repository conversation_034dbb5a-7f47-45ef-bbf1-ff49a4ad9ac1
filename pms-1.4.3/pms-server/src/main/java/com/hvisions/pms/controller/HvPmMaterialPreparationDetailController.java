package com.hvisions.pms.controller;

import com.hvisions.pms.dto.HvPmMaterialPreparationDetailDTO;
import com.hvisions.pms.service.HvPmMaterialPreparationDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description HvPmMaterialPreparationDetailController
 * <AUTHOR>
 * @Date 2024-05-21
 */
@Slf4j
@Api(tags = "备料请求明细")
@RestController
@RequestMapping("/materialPreparationDetail")
public class HvPmMaterialPreparationDetailController {
    @Autowired
    private HvPmMaterialPreparationDetailService hvPmMaterialPreparationDetailService;

    @ApiOperation(value = "添加HvPmMaterialPreparationDetail信息")
    @PostMapping(value = "/add")
    public void addHvPmPreparationDetail(@Valid @RequestBody HvPmMaterialPreparationDetailDTO hvPmMaterialPreparationDetailDTO) {
        hvPmMaterialPreparationDetailService.addHvPmMaterialPreparationDetail(hvPmMaterialPreparationDetailDTO);
    }

    /**
     * 删除
     *
     * @param id 主键
     */
    @ApiOperation(value = "删除HvPmMaterialPreparationDetail信息")
    @DeleteMapping(value = "/delete/{id}")
    public void deleteHvPmPreparationDetail(@PathVariable Long id) {
        hvPmMaterialPreparationDetailService.deleteHvPmMaterialPreparationDetail(id);
    }

    /**
     * 修改
     *
     * @param hvPmMaterialPreparationDetailDTO HvPmMaterialPreparationDetail
     */
    @ApiOperation(value = "修改HvPmMaterialPreparationDetail")
    @PutMapping(value = "/update")
    public void updateHvPmMaterialPreparationDetail(@Valid @RequestBody HvPmMaterialPreparationDetailDTO hvPmMaterialPreparationDetailDTO) {
        hvPmMaterialPreparationDetailService.updateHvPmMaterialPreparationDetail(hvPmMaterialPreparationDetailDTO);
    }

    /**
     * 根据id获取
     *
     * @param id 主键
     * @return HvPmMaterialPreparationDetail hvPmMaterialPreparationDetailDTO
     */
    @ApiOperation(value = "根据id获取HvPmMaterialPreparationDetail")
    @GetMapping(value = "/get/{id}")
    public HvPmMaterialPreparationDetailDTO getList(@PathVariable Long id) {
        return hvPmMaterialPreparationDetailService.getHvPmMaterialPreparationDetailById(id);
    }

    /**
     * 查询全部
     * @return 列表
     */
    @ApiOperation(value = "获取HvPmMaterialPreparationDetail列表")
    @GetMapping(value = "/getAll")
    public List<HvPmMaterialPreparationDetailDTO> getAll(){
        return hvPmMaterialPreparationDetailService.getAll();
    }

}
