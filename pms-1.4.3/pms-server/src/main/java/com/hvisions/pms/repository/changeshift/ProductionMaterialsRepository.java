package com.hvisions.pms.repository.changeshift;

import com.hvisions.pms.entity.changeshift.HvPmProductionMaterials;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: HvPmProductionMaterialsRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-10-12</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Repository
public interface    ProductionMaterialsRepository extends JpaRepository<HvPmProductionMaterials, Integer> {

    /**
     * 根据交接班记录ID查询交班工作时间产出物料
     *
     * @param changeShiftsId 交接班记录ID
     * @return 工作时间产出物料记录列表
     */
    List<HvPmProductionMaterials> getAllByChangeShiftsId(int changeShiftsId);

}