package com.hvisions.pms.controller;

import com.hvisions.pms.changeshiftsdto.ChangeShiftsRecordDTO;
import com.hvisions.pms.changeshiftsdto.ChangeShiftsRecordQueryDTO;
import com.hvisions.pms.service.ChangeShiftsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.text.ParseException;

/**
 * <p>Title: ChangeShiftsController</p >
 * <p>Description: 交接班记录控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-10-14</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Api(description = "交接班记录控制器")
@RestController
@RequestMapping(value = "/changeShifts")
public class ChangeShiftsController {


    private final ChangeShiftsService changeShiftsService;

    @Autowired
    public ChangeShiftsController(ChangeShiftsService changeShiftsService) {
        this.changeShiftsService = changeShiftsService;
    }

    /**
     * 获取交接班表单信息
     *
     * @param workCenterId 工位ID
     * @return 交接班记录DTO
     * @throws ParseException 异常
     */
    @PostMapping(value = "/getRecord/{workCenterId}")
    @ApiOperation(value = "获取交接班表单信息")
    public ChangeShiftsRecordDTO getRecord(@PathVariable int workCenterId) throws ParseException {
        return changeShiftsService.getRecord(workCenterId);
    }


    /**
     * 交班
     *
     * @param changeShiftsRecordDTO 交接班记录Id
     */
    @PostMapping(value = "/changeRecordState")
    @ApiOperation(value = "交班")
    public synchronized void changeRecordState(@RequestBody @Valid ChangeShiftsRecordDTO changeShiftsRecordDTO) {
        changeShiftsService.changeRecordState(changeShiftsRecordDTO);
    }


    /**
     * 撤销交班申请
     *
     * @param id 交接班记录ID
     */
    @PutMapping(value = "/revoke/{id}")
    @ApiOperation(value = "撤销交班申请")
    public void revoke(@PathVariable int id) {
        changeShiftsService.revoke(id);
    }

    /**
     * 撤销交班申请
     *
     * @param overMan     交班人
     * @param equipmentId 工位ID
     */
    @DeleteMapping(value = "/revoke/{overMan}/{equipmentId}")
    @ApiOperation(value = "撤销交班申请(根据交班人员ID工位ID)")
    public void revokeChangeShift(@PathVariable int overMan, @PathVariable int equipmentId) {
        changeShiftsService.revokeChangeShift(overMan, equipmentId);
    }

    /**
     * 接班确认操作
     *
     * @param id        交接班记录ID
     * @param operation 操作状态
     */
    @PutMapping(value = "/returnChangeShift/{id}/{operation}")
    @ApiOperation(value = "接班确认操作")
    public synchronized void returnChangeShift(@PathVariable int id,
                                  @PathVariable Integer operation) {
        changeShiftsService.returnChangeShift(id, operation);
    }

    /**
     * 获取待接班记录
     *
     * @param overMan     接班人
     * @param workCenterId 工位ID
     * @return 接班记录
     */
    @GetMapping(value = "/getOverRecord/{overMan}/{workCenterId}")
    @ApiOperation(value = "获取待接班记录")
    public ChangeShiftsRecordDTO getOverRecord(@PathVariable int overMan, @PathVariable int workCenterId) {
        return changeShiftsService.getOverRecord(overMan, workCenterId);
    }

    /**
     * 分页查询 交接班记录
     *
     * @param changeShiftsRecordQueryDTO 查询条件
     * @return 交接班记录
     */
    @PostMapping(value = "getRecordByQuery")
    @ApiOperation(value = "分页查询 交接班记录")
    public Page<ChangeShiftsRecordDTO> getRecordByQuery(@RequestBody ChangeShiftsRecordQueryDTO changeShiftsRecordQueryDTO) {
        return changeShiftsService.getRecordByQuery(changeShiftsRecordQueryDTO);
    }
}