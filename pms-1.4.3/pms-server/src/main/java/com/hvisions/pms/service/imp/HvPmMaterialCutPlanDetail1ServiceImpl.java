package com.hvisions.pms.service.imp;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.pms.entity.plan.HvPmMaterialCutPlanDetail1;
import com.hvisions.pms.plan.HvPmMaterialCutPlanDetail1DTO;
import com.hvisions.pms.repository.plan.HvPmMaterialCutPlanDetail0Repository;
import com.hvisions.pms.repository.plan.HvPmMaterialCutPlanDetail1Repository;
import com.hvisions.pms.service.HvPmMaterialCutPlanDetail1Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class HvPmMaterialCutPlanDetail1ServiceImpl implements HvPmMaterialCutPlanDetail1Service {

    @Autowired
    private HvPmMaterialCutPlanDetail1Repository hvPmMaterialCutPlanDetail1Repository;
    @Autowired
    private HvPmMaterialCutPlanDetail0Repository hvPmMaterialCutPlanDetail0Repository;

    @Override
    public List<HvPmMaterialCutPlanDetail1> getAllDetail1ByCutPlanId(long id) {
        return hvPmMaterialCutPlanDetail1Repository.getAllByCutPlanId(id);
    }

    @Override
    public long createDetail1(HvPmMaterialCutPlanDetail1DTO hvPmMaterialCutPlanDetail1DTO) {
        HvPmMaterialCutPlanDetail1 hvPmMaterialCutPlanDetail1 = DtoMapper.convert(hvPmMaterialCutPlanDetail1DTO, HvPmMaterialCutPlanDetail1.class);
        HvPmMaterialCutPlanDetail1 detail1 = hvPmMaterialCutPlanDetail1Repository.saveAndFlush(hvPmMaterialCutPlanDetail1);
        return detail1.getId();
    }

    @Override
    public long updateDetail1(HvPmMaterialCutPlanDetail1DTO hvPmMaterialCutPlanDetail1DTO) {
        HvPmMaterialCutPlanDetail1 hvPmMaterialCutPlanDetail1 = DtoMapper.convert(hvPmMaterialCutPlanDetail1DTO, HvPmMaterialCutPlanDetail1.class);
        HvPmMaterialCutPlanDetail1 detail1 = hvPmMaterialCutPlanDetail1Repository.save(hvPmMaterialCutPlanDetail1);
        return detail1.getId();
    }

    @Override
    public void deleteDetail1ById(long id) {
        hvPmMaterialCutPlanDetail1Repository.deleteById(id);
    }

    @Override
    public List<HvPmMaterialCutPlanDetail1> getDetail1ListByCutPlanIds(List<Long> cutPlanIds) {
        return hvPmMaterialCutPlanDetail1Repository.findByCutPlanIds(cutPlanIds);
    }

    @Override
    public void createDetail1Batch(List<HvPmMaterialCutPlanDetail1> entities) {
        hvPmMaterialCutPlanDetail1Repository.saveAll(entities);
    }

}
