package com.hvisions.pms.repository.plan;

import com.hvisions.pms.entity.plan.HvPmXcMaterialCutPlanDetail0;
import com.hvisions.pms.plan.HvPmXcMaterialCutPlanDetail0DTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/8/26
 */
@Repository
public interface HvPmXcMaterialCutPlanDetail0Repository extends JpaRepository<HvPmXcMaterialCutPlanDetail0,Long> {

    List<HvPmXcMaterialCutPlanDetail0> getAllByOrderId(Long OrderId);

    void deleteByOrderId(long id);

    /**
     * 是否存在 SubPlanNo
     * @param subPlanNo
     * @return
     */
    HvPmXcMaterialCutPlanDetail0DTO getBySubPlanNoEquals(String subPlanNo);


   @Query("SELECT d FROM HvPmXcMaterialCutPlanDetail0 d WHERE d.orderId IN :orderIds")
    List<HvPmXcMaterialCutPlanDetail0> findByOrderIds(@Param("orderIds") List<Long> orderIds);

}
