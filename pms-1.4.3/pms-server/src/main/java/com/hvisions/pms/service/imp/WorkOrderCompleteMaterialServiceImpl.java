package com.hvisions.pms.service.imp;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.client.MaterialClient;
import com.hvisions.hiperbase.materials.dto.MaterialDTO;
import com.hvisions.pms.dao.WorkOrderCompleteMaterialMapper;
import com.hvisions.pms.dto.HvPmWorkOrderCompleteMaterialDTO;
import com.hvisions.pms.dto.WorkOrderCompleteMaterialQueryDTO;
import com.hvisions.pms.entity.HvPmWorkOrderCompleteMaterial;
import com.hvisions.pms.repository.WorkOrderCompleteMaterialRepository;
import com.hvisions.pms.service.WorkOrderCompleteMaterialService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

/**
 * <P>    <P>
 *
 * <AUTHOR>
 * @date 2024/12/6
 */
@Service
@Slf4j
public class WorkOrderCompleteMaterialServiceImpl  implements WorkOrderCompleteMaterialService {

    @Autowired
    private WorkOrderCompleteMaterialMapper workOrderCompleteMaterialMapper;

    @Autowired
    private WorkOrderCompleteMaterialRepository workOrderCompleteMaterialRepository;

    @Autowired
    private MaterialClient materialClient;

    @Override
    public long addWorkOrderCompleteMaterial(HvPmWorkOrderCompleteMaterialDTO workOrderCompleteMaterialDTO) {
        HvPmWorkOrderCompleteMaterial workOrderCompleteMaterial = DtoMapper.convert(workOrderCompleteMaterialDTO, HvPmWorkOrderCompleteMaterial.class);

        //根据物料编码查询物料
        ResultVO<MaterialDTO> materialVo = materialClient.getMaterialByMaterialCodeAndEigenvalue(workOrderCompleteMaterialDTO.getMaterialCode(), "1");
        if (materialVo.getData() == null) {
            throw new BaseKnownException("物料编码：" + workOrderCompleteMaterialDTO.getMaterialCode() + "不存在");
        }
        workOrderCompleteMaterial.setMaterialName(materialVo.getData().getMaterialName());
        HvPmWorkOrderCompleteMaterial material = workOrderCompleteMaterialRepository.saveAndFlush(workOrderCompleteMaterial);
        return material.getId();
    }

    @Override
    public Page<HvPmWorkOrderCompleteMaterialDTO> getAllCompleteMaterial(WorkOrderCompleteMaterialQueryDTO workOrderCompleteMaterialQueryDTO) {
        Page<HvPmWorkOrderCompleteMaterialDTO> page = PageHelperUtil.getPage(workOrderCompleteMaterialMapper::getPage, workOrderCompleteMaterialQueryDTO);
        return page;
    }

    @Override
    public void deleteCompleteMaterialById(long id) {
        workOrderCompleteMaterialRepository.deleteById(id);
    }

    @Override
    public long upWorkOrderCompleteMaterial(HvPmWorkOrderCompleteMaterialDTO workOrderCompleteMaterialDTO) {
        HvPmWorkOrderCompleteMaterial workOrderCompleteMaterial = DtoMapper.convert(workOrderCompleteMaterialDTO, HvPmWorkOrderCompleteMaterial.class);
        HvPmWorkOrderCompleteMaterial material = workOrderCompleteMaterialRepository.saveAndFlush(workOrderCompleteMaterial);
        return material.getId();
    }

}
