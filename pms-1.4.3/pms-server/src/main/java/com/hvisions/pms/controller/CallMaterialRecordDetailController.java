package com.hvisions.pms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.pms.entity.HvPmCallMaterialRecordDetail;
import com.hvisions.pms.service.HvPmCallMaterialRecordDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024-05-27 16:02
 */

@RestController
@RequestMapping(value = "/callMaterialRecordDetail")
@Api(description = "叫料记录物料详情")
public class CallMaterialRecordDetailController {

    @Autowired
    private HvPmCallMaterialRecordDetailService hvPmCallMaterialRecordDetailService;

    /**
     * 分页查询
     */
    @ApiOperation("分页查询")
    @PostMapping("/list")
    public Page<HvPmCallMaterialRecordDetail> list(@RequestBody HvPmCallMaterialRecordDetail condition,
                                                   @RequestParam(name = "pageNum", defaultValue = "1") Integer pageNo,
                                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        QueryWrapper<HvPmCallMaterialRecordDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("record_id",condition.getRecordId())
                .eq(!StringUtils.isEmpty(condition.getMaterialCode()), "material_code", condition.getMaterialCode());
        Page<HvPmCallMaterialRecordDetail> page = new Page<>(pageNo,pageSize);
        return hvPmCallMaterialRecordDetailService.pageList(page,queryWrapper);
    }



    /**
     * 添加
     *
     * @param HvPmCallMaterialRecordDetail hvPmCallMaterialRecordDetail
     */
    @ApiOperation(value = "添加")
    @PostMapping(value = "/add")
    public boolean add(@RequestBody HvPmCallMaterialRecordDetail hvPmCallMaterialRecordDetail) {
        return hvPmCallMaterialRecordDetailService.save(hvPmCallMaterialRecordDetail);
    }

    /**
     * 删除
     *
     * @param id 主键
     */
    @ApiOperation(value = "删除")
    @DeleteMapping(value = "/delete/{id}")
    public boolean delete(@PathVariable long id) {
        return hvPmCallMaterialRecordDetailService.removeById(id);
    }

    /**
     * 修改
     *
     * @param HvPmCallMaterialRecordDetail hvPmCallMaterialRecordDetail
     */
    @ApiOperation(value = "修改")
    @PutMapping(value = "/update")
    public boolean updateHvPmAgvTaskRecord(@RequestBody HvPmCallMaterialRecordDetail hvPmCallMaterialRecordDetail) {
        return hvPmCallMaterialRecordDetailService.updateById(hvPmCallMaterialRecordDetail);
    }
}
