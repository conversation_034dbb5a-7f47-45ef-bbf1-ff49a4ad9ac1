package com.hvisions.pms.service;

import java.util.Map;

/**
 * <p>Title: WorkOrderParameterService</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/3/8</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
public interface WorkOrderParameterService {


    /**
     * 创建工单参数
     *
     * @param map     参数列表
     * @param orderId 工单ID
     */
    void createParameter(Map<String, Object> map, int orderId);


    /**
     * 根据工单获取参数值
     *
     * @param orderId 工单Id
     * @return 参数值map
     */
    Map<String, Object> getValueByOrder(Integer orderId);

    /**
     * 根据工单ID 参数编码查询 参数值
     *
     * @param code    参数编码
     * @param orderId 工单ID
     * @return 参数值
     */
    Object getValueByCodeAndOrderId(String code, Integer orderId);


}