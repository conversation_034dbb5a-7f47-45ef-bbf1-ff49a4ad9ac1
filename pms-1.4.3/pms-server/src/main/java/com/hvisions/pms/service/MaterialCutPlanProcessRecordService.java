package com.hvisions.pms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.pms.entity.HvPmMaterialCutPlanProcessRecord;
import com.hvisions.thirdparty.common.dto.AGVStatusDTO;
import com.hvisions.thirdparty.common.dto.DetailReportDTO;
import com.hvisions.thirdparty.common.dto.IWMSCallMaterialDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-06-27 15:06
 */
public interface MaterialCutPlanProcessRecordService extends IService<HvPmMaterialCutPlanProcessRecord> {
    List<HvPmMaterialCutPlanProcessRecord> getAll();

    List<HvPmMaterialCutPlanProcessRecord> findByLineCode(String lineCode);

    boolean addData();

    boolean updateDataForQgTwoToSixStep(DetailReportDTO detailReportDTO);

    boolean updateData(HvPmMaterialCutPlanProcessRecord hvPmMaterialCutPlanProcessRecord);

    void updateDataForAGVStatusDTO(AGVStatusDTO agvStatusDTO);

    void updateDataForIWMSCallMaterialDTO(IWMSCallMaterialDTO callMaterialDTO);

}
