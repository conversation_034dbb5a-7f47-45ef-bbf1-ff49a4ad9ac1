package com.hvisions.pms.controller;

import com.hvisions.pms.dto.HvPmXcMaterialOutStockResultDetail1DTO;
import com.hvisions.pms.service.HvPmXcMaterialOutStockResultDetail1Service;
import com.hvisions.pms.service.HvPmXcMaterialOutStockResultService;
import com.hvisions.thirdparty.common.dto.ProfileOutboundInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/5/8
 */
@Slf4j
@Api(tags = "型材出库计划-结果")
@RestController
@RequestMapping("xcMaterialOutStockResult")
public class HvPmXcMaterialOutStockResultController {

    @Autowired
    private HvPmXcMaterialOutStockResultService resultService;

    @Autowired
    private HvPmXcMaterialOutStockResultDetail1Service detail1Service;

    /**
     * 根据taskNo获取列表
     * @param taskNo
     * @return
     */
    @GetMapping("/getByTaskNo/{taskNo}")
    @ApiOperation("根据taskNo获取出库结果列表")
    public List<HvPmXcMaterialOutStockResultDetail1DTO> getByTaskNo(@PathVariable String taskNo){
        return detail1Service.getByTaskNo(taskNo);
    }

    /**
     * 添加出库结果
     * @param profileOutboundInfoDTO
     * @return
     */
    @PostMapping("/addOutStockResultAndDetail")
    @ApiOperation("添加出库结果")
    public void addOutStockResultAndDetail(@RequestBody ProfileOutboundInfoDTO profileOutboundInfoDTO) throws ParseException {
        resultService.addOutStockResultAndDetail(profileOutboundInfoDTO);
    }

    /**
     * 添加
     * @param detail1DTO
     * @return
     */
    @PostMapping("/addDetail")
    @ApiOperation("添加")
    public long addDetail(@RequestBody HvPmXcMaterialOutStockResultDetail1DTO detail1DTO){
        return detail1Service.createDetail1(detail1DTO);
    }

    /**
     * 修改
     * @param detail1DTO
     * @return
     */
    @PutMapping("/updateDetail")
    @ApiOperation("修改")
    public long updateDetail(@RequestBody HvPmXcMaterialOutStockResultDetail1DTO detail1DTO){
        return detail1Service.updateDetail1(detail1DTO);
    }

    /**
     * 删除
     * @param id
     */
    @DeleteMapping("/deleteDetailById/{id}/{taskNo}")
    @ApiOperation("删除")
    public void deleteDetailById(@PathVariable long id,@PathVariable String taskNo){
        detail1Service.deleteDetail1(id,taskNo);
    }

    /**
     * 根据任务号修改型材切割计划的状态
     * @param taskCode
     * @return
     */
    @PutMapping("/upXcCutPlanStatusByTaskCode/{taskCode}")
    @ApiOperation("根据任务号修改型材切割计划的状态")
    public void upXcCutPlanStatusByTaskCode(@PathVariable String taskCode){
        resultService.upXcCutPlanStatusByTaskCode(taskCode);
    }
}
