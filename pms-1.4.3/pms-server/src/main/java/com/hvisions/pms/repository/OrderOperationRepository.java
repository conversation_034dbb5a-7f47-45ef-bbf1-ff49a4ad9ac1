package com.hvisions.pms.repository;

import com.hvisions.pms.entity.HvPmOrderOperation;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: OrderOperationRepository</p>
 * <p>Description: 工序仓储层</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/1/17</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface OrderOperationRepository extends JpaRepository<HvPmOrderOperation, Integer> {
    /**
     * 根据工单id获取所有的工序列表
     *
     * @param orderId 工单id
     * @return 工序列表
     */
    List<HvPmOrderOperation> findAllByOrderId(int orderId);

    /**
     * 根据ID列表删除工序
     *
     * @param idIn Id列表
     */
    void deleteByIdIn(List<Integer> idIn);

    /**
     * 根据ID列表查询工序
     *
     * @param idIn id列表
     * @return 工序列表
     */
    List<HvPmOrderOperation> getAllByIdIn(List<Integer> idIn);


    /**
     * 根据设备ID查询工序
     *
     * @param workCenterId 设备ID
     * @return 工序列表
     */
    List<HvPmOrderOperation> getAllByWorkCenterId(int workCenterId);

    /**
     * 根据工单ID和工序顺寻号查询
     *
     * @param orderId        工单ID
     * @param operationOrder 工序顺序号
     * @return 工序列表
     */
    List<HvPmOrderOperation> getByOrderIdAndOperationOrder(int orderId, int operationOrder);

    /**
     * 根据工艺操作Id查询工序列表
     *
     * @param routeOperationId 工艺操作Id
     * @return 工艺操作列表
     */
    List<HvPmOrderOperation> getAllByRouteOperationId(int routeOperationId);

    //根据工单号删除
    @Modifying
    @Query("DELETE FROM HvPmOrderOperation o WHERE o.workOrderCode = :workOrderCode")
    int deleteByWorkOrderCode(@Param("workOrderCode") String workOrderCode);

}









