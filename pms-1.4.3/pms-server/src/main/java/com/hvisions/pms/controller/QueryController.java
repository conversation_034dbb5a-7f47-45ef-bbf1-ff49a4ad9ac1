package com.hvisions.pms.controller;

import com.hvisions.pms.dto.LocationWithTaskCount;
import com.hvisions.pms.service.QueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>Title: QueryController</p>
 * <p>Description: 综合类接口</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2022/7/5</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RequestMapping(value = "query")
@Api(description = "综合查询接口")
@RestController
public class QueryController {
    private final QueryService queryService;

    @Autowired
    public QueryController(QueryService queryService) {
        this.queryService = queryService;
    }

    /**
     * 获取人员的工位信息
     *
     * @param userId 用户id
     * @return 人员工位信息
     */
    @GetMapping(value = "/getWorkCenterWithTaskCount/{userId}")
    @ApiOperation(value = "获取人员的工位信息，附带工单任务数量信息")
    public List<LocationWithTaskCount> getWorkCenterWithTaskCount(@PathVariable Integer userId) {
        return queryService.getWorkCenterWithTaskCount(userId);
    }
}









