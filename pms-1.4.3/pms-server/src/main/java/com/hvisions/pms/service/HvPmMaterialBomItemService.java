package com.hvisions.pms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.pms.dto.materialRequirement.HvPmMaterialBomItemDTO;
import com.hvisions.pms.dto.materialRequirement.HvPmMaterialBomItemQueryDTO;
import com.hvisions.pms.entity.HvPmMaterialBomItem;
import org.springframework.data.domain.Page;

public interface HvPmMaterialBomItemService extends IService<HvPmMaterialBomItem> {
    Page<HvPmMaterialBomItemDTO> getPageMaterialByRequirementCode(HvPmMaterialBomItemQueryDTO queryDTO);
}

