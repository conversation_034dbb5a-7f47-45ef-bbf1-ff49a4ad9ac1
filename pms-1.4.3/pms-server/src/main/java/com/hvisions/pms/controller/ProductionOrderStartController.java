/*
package com.hvisions.pms.controller;

import com.hvisions.pms.service.ProductionOrderStartService;
import com.hvisions.thirdparty.common.dto.DetailReportDTO;
import com.hvisions.thirdparty.common.dto.ProfileOutboundInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

*/
/**
 *  生产订单开工
 * <AUTHOR>
 * @date 2024-06-25 14:35
 *//*

@RestController
@RequestMapping(value = "/productionOrderStart")
@Api(description = "生产订单开工")
public class ProductionOrderStartController {
    @Autowired
    private ProductionOrderStartService productionOrderStartService;

    @ApiOperation("型材计划开工")
    @PostMapping("/XcMaterialPlanStart")
    public void materialRequests(@RequestBody ProfileOutboundInfoDTO profileOutboundInfoDTO) {
        productionOrderStartService.XcMaterialPlanStart(profileOutboundInfoDTO);
    }

    @ApiOperation("钢板计划开工")
    @PostMapping("/materialCutPlanStart")
    public void partOrderReporting(@RequestBody DetailReportDTO detailReportDTO) {
        productionOrderStartService.partOrderReporting(detailReportDTO);
    }
}
*/
