package com.hvisions.pms.service;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.pms.dto.HvPmPartSituationDTO;
import com.hvisions.pms.dto.HvPmPartSituationQueryDTO;
import org.springframework.data.domain.Page;

import java.io.IOException;

/**
 * <P>    <P>
 *
 * <AUTHOR>
 * @date 2025/1/8
 */
public interface HvPmPartSituationService {

    /**
     * 分页条件查询
     * @param hvPmPartSituationQueryDTO
     * @return
     */
    Page<HvPmPartSituationDTO> getPage(HvPmPartSituationQueryDTO hvPmPartSituationQueryDTO);

    /**
     * 导出
     * @param hvPmPartSituationQueryDTO
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    ResultVO<ExcelExportDto> exportPartSituation(HvPmPartSituationQueryDTO hvPmPartSituationQueryDTO) throws IOException, IllegalAccessException;

    String getRefreshTime();

    void refreshPartSituation();
}
