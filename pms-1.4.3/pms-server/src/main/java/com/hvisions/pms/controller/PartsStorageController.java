package com.hvisions.pms.controller;

import com.hvisions.pms.service.PartsStorageService;
import com.hvisions.thirdparty.common.dto.EmptyPodAlertDTO;
import com.hvisions.thirdparty.common.dto.StockPointStatusDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/partsStorage")
@Api(description = "立库业务控制层")
@Slf4j
public class PartsStorageController {
    @Autowired
    private PartsStorageService partsStorageService;

    /**
     * 保存上料点状态信息
     *
     * @param stockPointStatusDTO 上料点状态
     */
    @ApiOperation("保存上料点状态信息")
    @PostMapping("/saveStockPointStatus")
    public void saveStockPointStatus(@RequestBody StockPointStatusDTO stockPointStatusDTO) {
        partsStorageService.saveStockPointStatus(stockPointStatusDTO);
    }

    /**
     * 更新上料点状态信息
     *
     * @param stockPointStatusDTO 上料点状态
     */
    @ApiOperation("根据id更新上料点状态信息")
    @PostMapping("/updateStatusById")
    public void updateStatusById(@RequestBody StockPointStatusDTO stockPointStatusDTO) {
        partsStorageService.updateStatusById(stockPointStatusDTO);
    }

    /**
     * 根据料点查询上料点
     *
     * @param pointCode 点位
     */
    @ApiOperation("根据id更新上料点状态信息")
    @PostMapping("/findPointStatusByPointCode")
    public StockPointStatusDTO findPointStatusByPointCode(@RequestParam("pointCode") String pointCode) {
        return partsStorageService.findPointStatusByPointCode(pointCode);
    }


    /**
     * 空框入立库
     *
     * @param emptyPodAlertDTO 海康空料框阈值警告信号
     */
    @ApiOperation("处理海康空料框阈值警告信号")
    @PostMapping("/handleEmptyPodAlert")
    public void handleEmptyPodAlert(@RequestBody EmptyPodAlertDTO emptyPodAlertDTO) {
        partsStorageService.handleEmptyPodAlert(emptyPodAlertDTO);
    }

    @PostMapping("/handleHvPmPartsOut")
    public void handleHvPmPartsOut(@RequestParam String taskNo) {
        partsStorageService.handleHvPmPartsOut(taskNo);
    }
}
