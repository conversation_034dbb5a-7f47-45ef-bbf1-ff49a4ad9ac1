package com.hvisions.pms.service;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.pms.entity.plan.HvPmXcMaterialCutPlan;
import com.hvisions.pms.plan.HvPmXcMaterialCutPlanDTO;
import com.hvisions.pms.plan.HvPmXcMaterialCutPlanTabQueryDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/8/26
 */
public interface HvPmXcMaterialCutPlanService {
    /**
     * 分页查询
     * @param hvPmXcMaterialCutPlanTabQueryDTO
     * @return
     */
    Page<HvPmXcMaterialCutPlanDTO> getPage(HvPmXcMaterialCutPlanTabQueryDTO hvPmXcMaterialCutPlanTabQueryDTO);

    /**
     * 查询工作号是否存在
     * @param orderNo
     * @return
     */
    boolean isExistsOrderNo(String orderNo);

    HvPmXcMaterialCutPlan getByOrderNoEquals(String orderNo);

    /**
     * 新增型材切割计划
     * @param hvPmXcMaterialCutPlanDTO
     * @return
     */
    long createXcCutPlan(HvPmXcMaterialCutPlanDTO hvPmXcMaterialCutPlanDTO);

    /**
     * 修改型材切割计划
     * @param hvPmXcMaterialCutPlanDTO
     * @return
     */
    long updateXcCutPlan(HvPmXcMaterialCutPlanDTO hvPmXcMaterialCutPlanDTO);

    /**
     * 根据套料系统下发添加型材切割任务
     * @param cutPlan
     */
    void addXcMaterialCutPlan(List<HvPmXcMaterialCutPlanDTO> cutPlan);


    /**
     * 型材切割计划下发
     * @param hvPmXcMaterialCutPlanDTO
     * @param userInfo
     * @return
     */
    ResultVO<?> sendXcOrder(HvPmXcMaterialCutPlanDTO hvPmXcMaterialCutPlanDTO, UserInfoDTO userInfo);

    /**
     * 删除型材切割计划和从表信息
     * @param id
     */
    void deleteXcCutPlanById(long id);


    /**
     * 获取导入模板
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    ResultVO<ExcelExportDto> getImportTemplate() throws IOException, IllegalAccessException;

    /**
     * 导入切割计划信息
     *
     * @param file bom信息文档
     * @return 返回信息
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    ImportResult importXcMaterialCutPlan(MultipartFile file) throws IllegalAccessException, ParseException, IOException;


    /**
     * 导出型材切割计划
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    ResultVO<ExcelExportDto> exportXcMaterialCutPlan(HvPmXcMaterialCutPlanTabQueryDTO hvPmXcMaterialCutPlanTabQueryDTO) throws IOException, IllegalAccessException;
}
