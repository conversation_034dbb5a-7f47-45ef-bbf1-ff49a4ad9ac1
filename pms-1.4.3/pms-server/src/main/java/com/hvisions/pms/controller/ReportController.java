package com.hvisions.pms.controller;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.pms.dao.SearchMapper;
import com.hvisions.pms.dto.OrderOperationDTO;
import com.hvisions.pms.rh.dto.*;
import com.hvisions.pms.service.WorkOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/rh-search")
@Api(description = "查询控制器，用于日化项目查询所用")
@Slf4j
public class ReportController {

    @Autowired
    SearchMapper mapper;

    @Autowired
    WorkOrderService workOrderService;

    @PostMapping(value = "/findOrderByPot")
    @ApiOperation(value = "查询工单信息，根据锅号，时间查询")
    public List<RHWorkOrderDTO> findOrderByPot(@RequestBody SearchOrderQueryDTO queryDTO) {
        List<RHWorkOrderDTO> rhWorkOrderDTOS = DtoMapper.convertList(mapper.findOrderByPot(queryDTO), RHWorkOrderDTO.class);
        for (RHWorkOrderDTO rhWorkOrderDTO : rhWorkOrderDTOS) {
            log.info(rhWorkOrderDTO.getOrderNo());
            List<OrderOperationDTO> operationByOrderCode = mapper.findOperationByOrderCode(rhWorkOrderDTO.getOrderNo());
            rhWorkOrderDTO.setOperationIds(operationByOrderCode);
        }

        return rhWorkOrderDTOS;
    }

    @PostMapping(value = "/findOrderByInputMaterial")
    @ApiOperation(value = "根据物料批次查询投入其他批情况")
    public Page<QueryOrderResultDTO> findOrderByInputMaterial(@RequestBody QueryOrderDTO queryDTO) {
        return PageHelperUtil.getPage(mapper::findOrderByInputMaterial, queryDTO, QueryOrderResultDTO.class);
    }

    /**
     * 获取当天的物料需求量
     *
     * @param workMaterialDTO 工单物料
     * @return 物料需求量
     * @throws ParseException ParseException
     */
    @PostMapping(value = "/getMaterial")
    @ApiOperation(value = "获取当天的物料需求量")
    public List<MaterialDemandDTO> getMaterial(@RequestBody WorkMaterialDTO workMaterialDTO) throws ParseException {
        return workOrderService.getMaterial(workMaterialDTO);
    }

}
