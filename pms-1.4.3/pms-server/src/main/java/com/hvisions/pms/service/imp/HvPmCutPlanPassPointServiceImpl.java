package com.hvisions.pms.service.imp;

import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.EasyExcelUtil;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.pms.dao.HvPmCutPlanPassPointMapper;
import com.hvisions.pms.dto.HvPmCutPlanPassPointDTO;
import com.hvisions.pms.entity.HvPmCutPlanPassPoint;
import com.hvisions.pms.enums.CutPlanStatusEnum;
import com.hvisions.pms.importTemplate.HvPmCutPlanPassPointTemplate;
import com.hvisions.pms.repository.HvPmCutPlanPassPointRepository;
import com.hvisions.pms.service.HvPmCutPlanPassPointService;
import com.hvisions.thirdparty.common.dto.DetailReportDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.function.Consumer;


@Slf4j
@Service
public class HvPmCutPlanPassPointServiceImpl implements HvPmCutPlanPassPointService {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    @Autowired
    private HvPmCutPlanPassPointRepository hvPmCutPlanPassPointRepository;
    @Autowired
    private HvPmCutPlanPassPointMapper hvPmCutPlanPassPointMapper;

    /**
     * 保存
     *
     * @param hvPmCutPlanPassPointDTO HvPmCutPlanPassPoint
     */
    @Override
    public void addHvPmCutPlanPassPoint(HvPmCutPlanPassPointDTO hvPmCutPlanPassPointDTO) {
        hvPmCutPlanPassPointRepository.save(DtoMapper.convert(hvPmCutPlanPassPointDTO, HvPmCutPlanPassPoint.class));
    }

    /**
     * 通过id删除
     *
     * @param id 主键
     */
    @Override
    public void deleteHvPmCutPlanPassPoint(Long id) {
        hvPmCutPlanPassPointRepository.deleteById(id);
    }

    /**
     * 修改
     *
     * @param hvPmCutPlanPassPointDTO HvPmCutPlanPassPoint
     */
    @Override
    public void updateHvPmCutPlanPassPoint(HvPmCutPlanPassPointDTO hvPmCutPlanPassPointDTO) {
        hvPmCutPlanPassPointRepository.save(DtoMapper.convert(hvPmCutPlanPassPointDTO, HvPmCutPlanPassPoint.class));
    }

    /**
     * 获取
     *
     * @param id 主键
     * @return HvPmCutPlanPassPoint hvPmCutPlanPassPointDTO HvPmCutPlanPassPoint
     */
    @Override
    public HvPmCutPlanPassPointDTO getHvPmCutPlanPassPointById(Long id) {
        Optional<HvPmCutPlanPassPoint> optional = hvPmCutPlanPassPointRepository.findById(id);
        return optional.map(hvPmCutPlanPassPoint -> DtoMapper.convert(hvPmCutPlanPassPoint, HvPmCutPlanPassPointDTO.class)).orElse(null);
    }

    /**
     * 获取列表
     *
     * @return hvPmCutPlanPassPointDTO 列表
     */
    @Override
    public List<HvPmCutPlanPassPointDTO> getAll() {
        return DtoMapper.convertList(hvPmCutPlanPassPointRepository.findAll(), HvPmCutPlanPassPointDTO.class);
    }

    @Override
    public Page<HvPmCutPlanPassPointDTO> getPage(HvPmCutPlanPassPointDTO hvPmCutPlanPassPointDTO) {
        return PageHelperUtil.getPage(hvPmCutPlanPassPointMapper::getPage, hvPmCutPlanPassPointDTO);

    }

    @Override
    public List<HvPmCutPlanPassPoint> exportList() {
        return hvPmCutPlanPassPointRepository.findAll();
    }

    @Override
    public void save(DetailReportDTO detailReportDTO, Long planId) {
        String status = detailReportDTO.getStatus();
        String lineCode = detailReportDTO.getLineCode();

        Map<String, Consumer<DetailReportDTO>> statusHandlers = new HashMap<>();
        statusHandlers.put(CutPlanStatusEnum.DC_OUT_STOCK.getHgCode(), dto -> handleStatus(dto, planId, dto.getOutStartTime(), CutPlanStatusEnum.DC_OUT_STOCK));
        statusHandlers.put(CutPlanStatusEnum.LLJ_IN_STOCK.getHgCode(), dto -> handleStatus(dto, planId, dto.getInStockStartTime1(), CutPlanStatusEnum.LLJ_IN_STOCK));
        statusHandlers.put(CutPlanStatusEnum.LLJ_OUT_STOCK.getHgCode(), dto -> handleStatus(dto, planId, dto.getOutStockStartTime1(), CutPlanStatusEnum.LLJ_OUT_STOCK));
        statusHandlers.put(CutPlanStatusEnum.CUT_START.getHgCode(), dto -> handleCutStatus(dto, planId, lineCode, dto.getCuttingStartTime(), CutPlanStatusEnum.CUT_START));
        statusHandlers.put(CutPlanStatusEnum.CUT_END.getHgCode(), dto -> handleCutStatus(dto, planId, lineCode, dto.getCuttingEndTime(), CutPlanStatusEnum.CUT_END));
        statusHandlers.put(CutPlanStatusEnum.START_SORTING.getHgCode(), dto -> handleCutStatus(dto, planId, lineCode, dto.getPickStartTime(), CutPlanStatusEnum.START_SORTING));
        statusHandlers.put(CutPlanStatusEnum.FINISH.getHgCode(), dto -> handleCutStatus(dto, planId, lineCode, dto.getPickEndTime(), CutPlanStatusEnum.FINISH));

        Consumer<DetailReportDTO> handler = statusHandlers.get(status);
        if (handler != null) {
            handler.accept(detailReportDTO);
        } else {
            log.error("未知的状态: {}", status);
            throw new IllegalArgumentException("未知的状态: " + status);
        }
    }

    private void handleStatus(DetailReportDTO detailReportDTO, Long planId, String startTime, CutPlanStatusEnum statusEnum) {
        String[] stationCodeAndName = statusEnum.getDefaultStation().split("_");
        saveSingleCutPlanPassPoint(detailReportDTO, planId, stationCodeAndName[0], stationCodeAndName[1], startTime);
    }

    private void handleCutStatus(DetailReportDTO detailReportDTO, Long planId, String lineCode, String startTime, CutPlanStatusEnum baseStatusEnum) {
        CutPlanStatusEnum statusEnum = getLineCodeStatusEnum(lineCode, baseStatusEnum);
        String[] stationCodeAndName = statusEnum.getDefaultStation().split("_");
        saveSingleCutPlanPassPoint(detailReportDTO, planId, stationCodeAndName[0], stationCodeAndName[1], startTime);
    }

    private CutPlanStatusEnum getLineCodeStatusEnum(String lineCode, CutPlanStatusEnum baseStatusEnum) {
        Map<String, CutPlanStatusEnum> lineCodeToStatusEnumMap = getLineCodeToStatusEnumMap(baseStatusEnum);
        CutPlanStatusEnum statusEnum = lineCodeToStatusEnumMap.get(lineCode);
        if (statusEnum == null) {
            log.error("线体编码不存在: {}", lineCode);
            throw new IllegalArgumentException("线体编码不存在: " + lineCode);
        }
        return statusEnum;
    }

    private Map<String, CutPlanStatusEnum> getLineCodeToStatusEnumMap(CutPlanStatusEnum baseStatusEnum) {
        switch (baseStatusEnum) {
            case CUT_START:
                Map<String, CutPlanStatusEnum> startMap = new HashMap<>();
                startMap.put("ZNDBQGX", CutPlanStatusEnum.CUT_START_LINE_1);
                startMap.put("ZNBJQGAX", CutPlanStatusEnum.CUT_START_LINE_2);
                startMap.put("ZNBJQGBX", CutPlanStatusEnum.CUT_START_LINE_3);
                return startMap;
            case CUT_END:
                Map<String, CutPlanStatusEnum> endMap = new HashMap<>();
                endMap.put("ZNDBQGX", CutPlanStatusEnum.CUT_END_1);
                endMap.put("ZNBJQGAX", CutPlanStatusEnum.CUT_END_2);
                endMap.put("ZNBJQGBX", CutPlanStatusEnum.CUT_END_3);
                return endMap;
            case START_SORTING:
                Map<String, CutPlanStatusEnum> sortingMap = new HashMap<>();
                sortingMap.put("ZNDBQGX", CutPlanStatusEnum.START_SORTING_1);
                sortingMap.put("ZNBJQGAX", CutPlanStatusEnum.START_SORTING_2);
                sortingMap.put("ZNBJQGBX", CutPlanStatusEnum.START_SORTING_3);
                return sortingMap;
            case FINISH:
                Map<String, CutPlanStatusEnum> finishMap = new HashMap<>();
                finishMap.put("ZNDBQGX", CutPlanStatusEnum.FINISH_1);
                finishMap.put("ZNBJQGAX", CutPlanStatusEnum.FINISH_2);
                finishMap.put("ZNBJQGBX", CutPlanStatusEnum.FINISH_3);
                return finishMap;
            default:
                log.error("未知的状态枚举: {}", baseStatusEnum);
                throw new IllegalArgumentException("未知的状态枚举: " + baseStatusEnum);
        }
    }

    private void saveSingleCutPlanPassPoint(DetailReportDTO detailReportDTO, Long planId,
                                            String stationCode, String stationName,
                                            String startTime) {
        HvPmCutPlanPassPoint cutPlanPassPoint = new HvPmCutPlanPassPoint();
        cutPlanPassPoint.setCutPlanId(planId);
        cutPlanPassPoint.setCutPlanNo(detailReportDTO.getCode());
        Optional<Date> date = timeToDate(startTime);
        date.ifPresent(cutPlanPassPoint::setPassTime);
        cutPlanPassPoint.setLineCode(detailReportDTO.getLineCode());
        cutPlanPassPoint.setStationCode(stationCode);
        cutPlanPassPoint.setStationName(stationName);
        hvPmCutPlanPassPointRepository.save(cutPlanPassPoint);
    }

    public Optional<Date> timeToDate(String time) {
        try {
            LocalDateTime localDateTime = LocalDateTime.parse(time, DATE_TIME_FORMATTER);
            return Optional.of(Date.from(localDateTime.atZone(java.time.ZoneId.systemDefault()).toInstant()));
        } catch (DateTimeParseException e) {
            log.error("时间格式化失败，输入时间：{}，错误信息：{}", time, e.getMessage());
            return Optional.empty();
        }
    }

    @Override
    public ImportResult importExcel(MultipartFile file) {
        List<HvPmCutPlanPassPointTemplate> data = EasyExcelUtil.getImport(file, HvPmCutPlanPassPointTemplate.class);
        if (data != null && data.size() > 0) {
            for (HvPmCutPlanPassPointTemplate hvPmCutPlanPassPointTemplate : data) {
                addHvPmCutPlanPassPoint(DtoMapper.convert(hvPmCutPlanPassPointTemplate, HvPmCutPlanPassPointDTO.class));
            }
        }
        return new ImportResult();
    }


}