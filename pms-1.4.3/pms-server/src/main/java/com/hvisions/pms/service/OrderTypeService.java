package com.hvisions.pms.service;

import com.hvisions.common.dto.ExtendColumnInfo;
import com.hvisions.pms.dto.OrderTypeDTO;
import com.hvisions.pms.dto.OrderTypeQueryDTO;
import com.hvisions.pms.dto.SetTypeExtendDTO;
import com.hvisions.pms.type.OrderTypeMaterialDTO;
import com.hvisions.pms.type.ReturnOrderTypeMaterialDTO;
import com.hvisions.pms.type.TypeMaterialQuery;
import com.hvisions.pms.type.WorkOrderMaterialQuery;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <p>Title: OrderTypeService</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/11/6</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
public interface OrderTypeService {

    /**
     * 新增工单类型
     *
     * @param orderTypeDTO 工单类型对象
     * @return 工单类型对象
     */
    OrderTypeDTO createOrderType(OrderTypeDTO orderTypeDTO);


    /**
     * 修改工单类型
     *
     * @param orderTypeDTO 工单类型对象
     * @return 工单类型对象
     */
    OrderTypeDTO updateOrderType(OrderTypeDTO orderTypeDTO);


    /**
     * 根据物料id查询扩展字段
     *
     * @param materialId 物料id
     * @return 工单绑定的扩展字段
     */
    List<ExtendColumnInfo> getExtendByMaterialId(Integer materialId);
    /**
     * 根据类型id查询扩展字段
     *
     * @param typeCode 类型id
     * @return 工单绑定的扩展字段
     */
    List<ExtendColumnInfo> getExtendByTypeCode(String typeCode);

    /**
     * 设置工单类型的扩展属性
     *
     * @param setTypeExtendDTO 关联对象
     */
    void setTypeExtend(SetTypeExtendDTO setTypeExtendDTO);

    /**
     * 删除工单类型扩展字段绑定关系
     *
     * @param orderTypeId 工单类型id
     * @param extendCode  扩展字段code
     */
    void deleteTypeExtend(Integer orderTypeId, String extendCode);


    /**
     * 根据id删除工单类型
     *
     * @param id 工单类型Id
     */
    void deleteById(Integer id);

    /**
     * 分页查询
     *
     * @param orderTypeQueryDTO 查询条件
     * @return 工单类型信息
     */
    Page<OrderTypeDTO> getOrderType(OrderTypeQueryDTO orderTypeQueryDTO);

    /**
     * 根据工单类型编码查询工单类型
     *
     * @param orderTypeCode 工单类型编码
     * @return 工单类型
     */
    OrderTypeDTO getOrderTypeByCode(@RequestParam String orderTypeCode);

    /**
     * 工单类型 物料绑定
     *
     * @param orderTypeMaterialDTO 工单类型物料对象
     * @return 已有绑定关系的返回
     */
    ReturnOrderTypeMaterialDTO bingOrderType(OrderTypeMaterialDTO orderTypeMaterialDTO);

    /**
     * 分页查询工单类型绑定的物料
     *
     * @param typeMaterialQuery 分页查询条件
     * @return 绑定结果
     */
    Page<OrderTypeMaterialDTO> getOrderTypeMaterial(TypeMaterialQuery typeMaterialQuery);

    /**
     * 删除绑定关系
     *
     * @param orderTypeId 类型id
     * @param materialId  物料id
     */
    void deleteTypeMaterial(Integer orderTypeId, Integer materialId);

    /**
     * 根据工单类型查询物料
     *
     * @param workOrderMaterialQuery 工单类型id
     * @return 物料
     */
    Page<OrderTypeMaterialDTO> getMaterialByOrderTypeCode(WorkOrderMaterialQuery workOrderMaterialQuery);
}