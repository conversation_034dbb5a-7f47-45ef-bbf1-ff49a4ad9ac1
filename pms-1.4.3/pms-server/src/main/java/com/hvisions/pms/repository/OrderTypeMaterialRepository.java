package com.hvisions.pms.repository;

import com.hvisions.pms.entity.HvPmOrderTypeMaterial;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>Title: OrderTypeMaterialRepository</p >
 * <p>Description: 类型物料仓储层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2022/1/12</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Repository
public interface OrderTypeMaterialRepository extends JpaRepository<HvPmOrderTypeMaterial, Integer> {

    HvPmOrderTypeMaterial getAllByMaterialId(Integer materialId);

    List<HvPmOrderTypeMaterial> getAllByOrderTypeId(Integer orderTypeId);

    @Transactional(rollbackFor = Exception.class)
    void deleteByOrderTypeIdAndMaterialId(Integer orderTypeId, Integer materialId);

    @Transactional(rollbackFor = Exception.class)
    void deleteByIdIn(List<Integer> idList);
}