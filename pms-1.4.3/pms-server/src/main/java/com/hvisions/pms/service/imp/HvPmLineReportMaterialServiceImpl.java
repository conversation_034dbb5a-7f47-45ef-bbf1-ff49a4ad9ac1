package com.hvisions.pms.service.imp;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.pms.dao.HvPmLineReportMaterialMapper;
import com.hvisions.pms.dto.HvPmLineReportMaterialDTO;
import com.hvisions.pms.entity.HvPmLineReportMaterial;
import com.hvisions.pms.exportdto.LineReportDetail0ExportDTO;
import com.hvisions.pms.repository.HvPmLineReportMaterialRepository;
import com.hvisions.pms.service.HvPmLineReportMaterialService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024-04-11 10:15
 */
@Service
public class HvPmLineReportMaterialServiceImpl implements HvPmLineReportMaterialService {
    @Autowired
    private HvPmLineReportMaterialRepository hvPmLineReportMaterialRepository;
    @Autowired
    private HvPmLineReportMaterialMapper hvPmLineReportMaterialMapper;

    /**
     * 保存
     *
     * @param hvPmLineReportMaterialDTO HvPmLineReportMaterial
     *
     */
    @Override
    public void addHvPmLineReportMaterial(HvPmLineReportMaterialDTO hvPmLineReportMaterialDTO){
        hvPmLineReportMaterialRepository.save(DtoMapper.convert(hvPmLineReportMaterialDTO, HvPmLineReportMaterial.class));
    }

    /**
     * 通过id删除
     *
     * @param id 主键
     *
     */
    @Override
    public void deleteHvPmLineReportMaterial(long id){
        hvPmLineReportMaterialRepository.deleteById(id);
    }

    /**
     * 修改
     *
     * @param hvPmLineReportMaterialDTO HvPmLineReportMaterial
     *
     */
    @Override
    public void updateHvPmLineReportMaterial(HvPmLineReportMaterialDTO hvPmLineReportMaterialDTO){
        hvPmLineReportMaterialRepository.save(DtoMapper.convert(hvPmLineReportMaterialDTO, HvPmLineReportMaterial.class));
    }

    /**
     * 获取
     *
     * @param id 主键
     * @return HvPmLineReportMaterial hvPmLineReportMaterialDTO HvPmLineReportMaterial
     */
    @Override
    public HvPmLineReportMaterialDTO getHvPmLineReportMaterialById(long id){
        Optional<HvPmLineReportMaterial> optional = hvPmLineReportMaterialRepository.findById(id);
        return optional.map(hvPmLineReportMaterial -> DtoMapper.convert(hvPmLineReportMaterial, HvPmLineReportMaterialDTO.class)).orElse(null);
    }

    /**
     * 获取列表
     *
     * @return  hvPmLineReportMaterialDTO 列表
     */
    @Override
    public List<HvPmLineReportMaterialDTO> getAll(){
        return DtoMapper.convertList(hvPmLineReportMaterialRepository.findAll(),HvPmLineReportMaterialDTO.class);
    }

    @Override
    public Page<HvPmLineReportMaterialDTO> findPageByCondition(HvPmLineReportMaterialDTO condition) {
        return PageHelperUtil.getPage(hvPmLineReportMaterialMapper::findByCondition,condition);
    }

    @Override
    public void saveBatch(List<HvPmLineReportMaterialDTO> hvPmLineReportMaterialDTOS) {
        hvPmLineReportMaterialRepository.saveAll(DtoMapper.convertList(hvPmLineReportMaterialDTOS,HvPmLineReportMaterial.class));
    }

    @Override
    public List<HvPmLineReportMaterial> selectByMaterialAndFrameCode(String materialCode, String frameCode) {
        return hvPmLineReportMaterialMapper.selectByMaterialAndFrameCode(materialCode,frameCode);
    }

    @Override
    public List<LineReportDetail0ExportDTO> getlineReportMaterialListByReportId(List<Long> reportIds) {
        return hvPmLineReportMaterialMapper.getlineReportMaterialListByReportId(reportIds);
    }
}
