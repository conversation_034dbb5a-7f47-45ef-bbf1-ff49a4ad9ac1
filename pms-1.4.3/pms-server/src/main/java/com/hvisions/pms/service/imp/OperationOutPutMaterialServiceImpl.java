package com.hvisions.pms.service.imp;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.SerialUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.client.MaterialClient;
import com.hvisions.hiperbase.client.RouteClient;
import com.hvisions.hiperbase.materials.dto.MaterialDTO;
import com.hvisions.hiperbase.route.dto.Node;
import com.hvisions.hiperbase.route.dto.NodeData;
import com.hvisions.hiperbase.route.dto.NodeProductionData;
import com.hvisions.hiperbase.route.dto.RouteDTO;
import com.hvisions.pms.dto.OperationOutPutMaterialDTO;
import com.hvisions.pms.dto.OutPutIDListDTO;
import com.hvisions.pms.entity.HvPmOperationOutPutMaterial;
import com.hvisions.pms.entity.HvPmOrderOperation;
import com.hvisions.pms.entity.HvPmOrderTask;
import com.hvisions.pms.entity.HvPmWorkOrder;
import com.hvisions.pms.enums.WorkOrderExceptionEnum;
import com.hvisions.pms.enums.WorkOrderStateEnum;
import com.hvisions.pms.repository.OperationOutPutMaterialRepository;
import com.hvisions.pms.repository.OrderOperationRepository;
import com.hvisions.pms.repository.OrderTaskRepository;
import com.hvisions.pms.repository.WorkOrderRepository;
import com.hvisions.pms.service.OperationOutPutMaterialService;
import com.hvisions.pms.util.ChangeShifts;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>Title: OperationOutPutMaterialImpl</p >
 * <p>Description: 工序产出物料服务层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/14</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Service
public class OperationOutPutMaterialServiceImpl implements OperationOutPutMaterialService {

    @Value("${h-visions.outPutMaterial:true}")
    private Boolean outPutMaterial;

    private final OperationOutPutMaterialRepository operationOutPutMaterialRepository;
    private final OrderOperationRepository orderOperationRepository;
    private final WorkOrderRepository workOrderRepository;
    private final ChangeShifts changeShifts;
    private final RouteClient routeClient;
    private final MaterialClient materialClient;
    private final OrderTaskRepository orderTaskRepository;
    private SerialUtil serialUtil;

    @Autowired
    public OperationOutPutMaterialServiceImpl(OperationOutPutMaterialRepository operationOutPutMaterialRepository, OrderOperationRepository orderOperationRepository, WorkOrderRepository workOrderRepository, ChangeShifts changeShifts, RouteClient routeClient, MaterialClient materialClient, OrderTaskRepository orderTaskRepository, SerialUtil serialUtil) {
        this.operationOutPutMaterialRepository = operationOutPutMaterialRepository;
        this.orderOperationRepository = orderOperationRepository;
        this.workOrderRepository = workOrderRepository;
        this.changeShifts = changeShifts;
        this.routeClient = routeClient;
        this.materialClient = materialClient;
        this.orderTaskRepository = orderTaskRepository;
        this.serialUtil = serialUtil;
    }


    /**
     * 添加产出物料
     *
     * @param operationOutPutMaterialDTOS 产出物料DTO
     */
    @Override
    public void createOutMaterial(List<OperationOutPutMaterialDTO> operationOutPutMaterialDTOS) {
        Optional<Integer> first = operationOutPutMaterialDTOS.stream().map(OperationOutPutMaterialDTO::getOperationId).findFirst();
        HvPmOrderOperation one = orderOperationRepository.getOne(first.get());
        HvPmWorkOrder hvPmWorkOrder = workOrderRepository.getOne(one.getOrderId());
        validProduction(operationOutPutMaterialDTOS, one, hvPmWorkOrder);
        if (hvPmWorkOrder.getWorkOrderState().equals(WorkOrderStateEnum.FINISH.getCode())) {
            throw new BaseKnownException(WorkOrderExceptionEnum.WORK_ORDER_FINISH);
        }
        for (OperationOutPutMaterialDTO operationOutPutMaterialDTO : operationOutPutMaterialDTOS) {
            changeShifts.lockTask(operationOutPutMaterialDTO.getTaskId());
            String serialNumber = serialUtil.getSerialNumber("order-manage");
            operationOutPutMaterialDTO.setBatchNumber(operationOutPutMaterialDTO.getMaterialCode() + serialNumber);
        }
        operationOutPutMaterialRepository.saveAll(
            DtoMapper.convertList(operationOutPutMaterialDTOS, HvPmOperationOutPutMaterial.class));
    }

    /**
     * 验证该工序产出料是否与配置一致
     *
     * @param operationOutPutMaterialDTOS 产出料新增
     * @param orderOperation              工序信息
     * @param hvPmWorkOrder               工单信息
     */
    private void validProduction(List<OperationOutPutMaterialDTO> operationOutPutMaterialDTOS, HvPmOrderOperation orderOperation, HvPmWorkOrder hvPmWorkOrder) {
        if (outPutMaterial) {
            ResultVO<RouteDTO> routeById = routeClient.getEffectRouteByProductIdAndRouteId(hvPmWorkOrder.getMaterialId(),
                hvPmWorkOrder.getRouteId());
            if (routeById.isSuccess()) {
                for (Node node : routeById.getData().getNodes()) {
                    if (node.getData().getCode().equals(orderOperation.getNodeCode())) {
                        for (OperationOutPutMaterialDTO operationOutPutMaterialDTO : operationOutPutMaterialDTOS) {
                            boolean outPut = false;
                            if (node.getData().getNodeProductionData() != null) {
                                for (NodeProductionData nodeProductionDatum : node.getData().getNodeProductionData()) {
                                    if (operationOutPutMaterialDTO.getMaterialId().equals(nodeProductionDatum.getMaterialId())) {
                                        outPut = true;
                                    }
                                }
                                if (!outPut) {
                                    throw new BaseKnownException("无法新增未配置产出料", 10000);
                                }
                            } else {
                                throw new BaseKnownException("该工艺未配置产出料", 10000);
                            }
                        }
                    }
                }
            } else {
                throw new BaseKnownException(routeById);
            }
        }
    }

    /**
     * 根据工序ID查询产出物料
     *
     * @param operationId 工序ID
     * @return 产出物料列表
     */
    @Override
    public List<OperationOutPutMaterialDTO> getAllByOperationId(int operationId) {
        return DtoMapper.convertList(operationOutPutMaterialRepository.
            getAllByOperationId(operationId), OperationOutPutMaterialDTO.class);
    }

    /**
     * 工序产出物料删除
     *
     * @param outPutIDListDTO id列表DTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIdList(OutPutIDListDTO outPutIDListDTO) {
        List<HvPmOperationOutPutMaterial> allByIdIn = operationOutPutMaterialRepository.getAllByIdIn(outPutIDListDTO.getIsList());
        for (HvPmOperationOutPutMaterial hvPmOperationOutPutMaterial : allByIdIn) {
            changeShifts.lockTask(hvPmOperationOutPutMaterial.getTaskId());
        }
        List<Integer> collect = allByIdIn.stream().map(t -> t.getOperationId()).collect(Collectors.toList());
        List<HvPmOrderOperation> allByIdIn1 = orderOperationRepository.getAllByIdIn(collect);
        List<Integer> orderIdList = allByIdIn1.stream().map(t -> t.getOrderId()).collect(Collectors.toList());
        List<HvPmWorkOrder> hvPmOrderManageByIdIn = workOrderRepository.getHvPmOrderManageByIdIn(orderIdList);
        List<HvPmWorkOrder> hvPmWorkOrders =
            hvPmOrderManageByIdIn.stream().filter(t -> t.getWorkOrderState().equals(WorkOrderStateEnum.FINISH)).collect(Collectors.toList());
        if (hvPmWorkOrders.size() > 0) {
            throw new BaseKnownException(WorkOrderExceptionEnum.WORK_ORDER_FINISH);
        }
        operationOutPutMaterialRepository.deleteByIdIn(outPutIDListDTO.getIsList());
    }

    /**
     * 获取可以产出的物料信息列表
     *
     * @param taskId 操作id
     * @return 物料列表
     */
    @Override
    public List<MaterialDTO> getOutMaterial(Integer taskId) {
        HvPmOrderTask task = orderTaskRepository.findById(taskId)
            .orElseThrow(() -> new BaseKnownException("找不到对应的工单任务，请检查输入"));
        HvPmWorkOrder order = workOrderRepository.findById(task.getOrderId())
            .orElseThrow(() -> new BaseKnownException("任务没有对应的工单信息，请联系管理员"));
        ResultVO<RouteDTO> route = routeClient.getRoute(order.getRouteId());
        if (!route.isSuccess()) {
            throw new BaseKnownException("工艺信息查询失败:请求失败，请重试");
        }
        if (route.getData() == null) {
            throw new BaseKnownException("工艺信息查询失败:数据为空，请重试");
        }
        Node node = route.getData().findAllOperationNode()
            .stream()
            .filter(t -> t.getCode().equals(task.getNodeCode()))
            .findFirst()
            .orElseThrow(() -> new BaseKnownException("获取对应的工艺步骤出错：找不到对应的节点，请检查任务的节点编码和工艺对应的节点编码"));
        List<NodeProductionData> productionData = Optional.of(node)
            .map(Node::getData)
            .map(NodeData::getNodeProductionData)
            .orElse(Collections.emptyList());
        if (CollectionUtils.isEmpty(productionData)) {
            return Collections.emptyList();
        }
        List<Integer> materialId = productionData.stream()
            .map(NodeProductionData::getMaterialId)
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(materialId)) {
            return Collections.emptyList();
        }
        ResultVO<List<MaterialDTO>> materials = materialClient.getMaterialsByIdList(materialId);
        if (materials.isSuccess()) {
            return materials.getData();
        }
        return Collections.emptyList();
    }


}
