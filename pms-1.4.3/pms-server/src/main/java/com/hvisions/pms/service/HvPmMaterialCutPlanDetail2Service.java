package com.hvisions.pms.service;

import com.hvisions.pms.entity.plan.HvPmMaterialCutPlanDetail2;
import com.hvisions.pms.plan.HvPmMaterialCutPlanDetail2DTO;

import java.util.List;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/8/2
 */
public interface HvPmMaterialCutPlanDetail2Service {

    /**
     * 添加
     * @param HvPmMaterialCutPlanDetail2DTO
     * @return
     */
    long createDetail2(HvPmMaterialCutPlanDetail2DTO HvPmMaterialCutPlanDetail2DTO);


    /**
     * 根据切割计划id获取列表
     * @param id
     * @return
     */
    List<HvPmMaterialCutPlanDetail2> getAllDetail2ByCutPlanId(long id);

    /**
     * 批量添加
     * @param dtoList
     */
    void createDetail2List(List<HvPmMaterialCutPlanDetail2DTO> dtoList);

    /**
     * 根据物料编号和物料类型查询
     * @param cutPlanCode
     * @param operationType
     * @return
     */
    HvPmMaterialCutPlanDetail2 getOneByCutPlanCodeAndOperationType(String cutPlanCode,String operationType);

    /**
     * 批量更新或保存
     * @param dtoList
     */
    void createOrUpdateDetail2List(List<HvPmMaterialCutPlanDetail2DTO> dtoList);


    List<HvPmMaterialCutPlanDetail2> getDetail2ListByCutPlanIds(List<Long> cutPlanIds);
}
