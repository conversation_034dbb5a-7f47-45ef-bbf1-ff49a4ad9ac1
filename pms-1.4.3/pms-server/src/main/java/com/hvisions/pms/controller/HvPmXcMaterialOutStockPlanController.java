package com.hvisions.pms.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.pms.plan.XcMaterialOutStockPlanDTO;
import com.hvisions.pms.plan.XcMaterialOutStockPlanQueryDTO;
import com.hvisions.pms.service.XcMaterialOutStockPlanService;
import com.hvisions.thirdparty.common.dto.MaterialRequestsDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/5/7
 */
@Slf4j
@Api(tags = "型材出库计划")
@RestController
@RequestMapping("/xcMaterialOutStockPlan")
public class HvPmXcMaterialOutStockPlanController {

    @Autowired
    private XcMaterialOutStockPlanService xcMaterialOutStockPlanService;

    /**
     * 分页模糊查询
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/getPage")
    @ApiOperation("分页模糊查询")
    public Page<XcMaterialOutStockPlanDTO> getPage(@RequestBody XcMaterialOutStockPlanQueryDTO queryDTO) {
        return xcMaterialOutStockPlanService.getPage(queryDTO);
    }

    /**
     * 新增型材出库计划
     *
     * @param xcMaterialOutStockPlanDTO
     * @return
     */
    @PostMapping("/createOutStockPlan")
    @ApiOperation("新增型材出库计划")
    public int createOutStockPlan(@RequestBody XcMaterialOutStockPlanDTO xcMaterialOutStockPlanDTO) {
        return xcMaterialOutStockPlanService.createOutStockPlan(xcMaterialOutStockPlanDTO);
    }

    /**
     * 修改型材出库计划
     *
     * @param xcMaterialOutStockPlanDTO
     * @return
     */
    @PutMapping("/updateOutStockPlan")
    @ApiOperation("修改型材出库计划")
    public int updateOutStockPlan(@RequestBody XcMaterialOutStockPlanDTO xcMaterialOutStockPlanDTO) {
        return xcMaterialOutStockPlanService.updateOutStockPlan(xcMaterialOutStockPlanDTO);
    }

    /**
     * 删除型材出库计划
     *
     * @param id
     */
    @DeleteMapping("/deleteOutStockPlanById/{id}/{taskNo}")
    @ApiOperation("删除型材出库计划")
    public void deleteOutStockPlan(@PathVariable Integer id,@PathVariable String taskNo) {
        xcMaterialOutStockPlanService.deleteOutStockPlanById(id,taskNo);
    }

    /**
     * 判断型材出库计划是否存在
     *
     * @param taskNo
     * @return
     */
    @GetMapping("/isExistsOutStockPlan/{taskNo}")
    @ApiOperation("判断型材出库计划是否存在")
    public boolean isExistsOutStockPlan(@PathVariable String taskNo) {
        return xcMaterialOutStockPlanService.isExistsOutStockPlan(taskNo);
    }

    /**
     * 获取模板
     *
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    @ApiResultIgnore
    @GetMapping("/getImportTemplate")
    @ApiOperation("获取模板")
    public ResultVO<ExcelExportDto> getImportTemplate() throws IOException, IllegalAccessException {
        return xcMaterialOutStockPlanService.getImportTemplate();
    }

    /**
     * 导入型材出库计划
     * @param file
     * @return
     * @throws IllegalAccessException
     * @throws ParseException
     * @throws IOException
     */
    @PostMapping("/importPlan")
    @ApiOperation("导入型材出库计划")
    public ImportResult importPlan(@RequestParam("file") MultipartFile file) throws IllegalAccessException, IOException {
        return xcMaterialOutStockPlanService.importPlan(file);
    }

    @ApiResultIgnore
    @PostMapping(value = "/exportOutStockPlan")
    @ApiOperation(value = "导出型材出库计划")
    public ResultVO<ExcelExportDto> exportOutStockPlan(@RequestBody XcMaterialOutStockPlanQueryDTO xcMaterialOutStockPlanQueryDTO) throws IOException, IllegalAccessException {
        return xcMaterialOutStockPlanService.exportOutStockPlan(xcMaterialOutStockPlanQueryDTO);
    }

    /**
     * 根据型材切割下发型材出库计划
     *
     * @param materialRequestsDTO
     * @return
     */
    @PostMapping("/createOutStockPlanByRequests")
    @ApiOperation("新增型材出库计划")
    public int createOutStockPlanByRequests(@RequestBody MaterialRequestsDTO materialRequestsDTO) {
        return xcMaterialOutStockPlanService.createOutStockPlanByRequests(materialRequestsDTO);
    }

    /**
     * 根据工单号获取型材出库计划
     *
     * @param workOrderCode
     * @return
     */
    @PostMapping("/getListByWorkOrderCode/{workOrderCode}")
    @ApiOperation("根据工单号获取型材出库计划和详情列表")
    public List<XcMaterialOutStockPlanDTO> getListByWorkOrderCode(@PathVariable String workOrderCode) {
        return xcMaterialOutStockPlanService.getListByWorkOrderCode(workOrderCode);
    }

    /**
     * 手动下发型材出库计划
     * @param xcMaterialOutStockPlanDTO
     */
    @PostMapping("/handSendOrder")
    @ApiOperation("手动下发型材出库计划")
    public ResultVO<?> handSendOrder(@RequestBody XcMaterialOutStockPlanDTO xcMaterialOutStockPlanDTO, @UserInfo @ApiIgnore UserInfoDTO userInfo) {
        return xcMaterialOutStockPlanService.handSendOrder(xcMaterialOutStockPlanDTO, userInfo);

    }

}
