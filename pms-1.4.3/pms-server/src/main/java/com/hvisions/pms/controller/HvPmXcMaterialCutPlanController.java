package com.hvisions.pms.controller;


import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.pms.entity.plan.HvPmXcMaterialCutPlanDetail0;
import com.hvisions.pms.entity.plan.HvPmXcMaterialCutPlanDetail1;
import com.hvisions.pms.plan.HvPmXcMaterialCutPlanDTO;
import com.hvisions.pms.plan.HvPmXcMaterialCutPlanDetail0DTO;
import com.hvisions.pms.plan.HvPmXcMaterialCutPlanDetail1DTO;
import com.hvisions.pms.plan.HvPmXcMaterialCutPlanTabQueryDTO;
import com.hvisions.pms.repository.plan.HvPmXcMaterialCutPlanRepository;
import com.hvisions.pms.service.HvPmXcMaterialCutPlanDetail0Service;
import com.hvisions.pms.service.HvPmXcMaterialCutPlanDetail1Service;
import com.hvisions.pms.service.HvPmXcMaterialCutPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import java.io.IOException;
import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/8/26
 */
@Slf4j
@Api(tags = "型材切割计划")
@RestController
@RequestMapping("/xcMaterialCutPlan")
public class HvPmXcMaterialCutPlanController {

    @Autowired
    private HvPmXcMaterialCutPlanService hvPmXcMaterialCutPlanService;
    @Autowired
    private HvPmXcMaterialCutPlanDetail0Service hvPmXcMaterialCutPlanDetail0Service;
    @Autowired
    private HvPmXcMaterialCutPlanDetail1Service hvPmXcMaterialCutPlanDetail1Service;
    @Autowired
    private HvPmXcMaterialCutPlanRepository hvPmXcMaterialCutPlanRepository;


    /**
     * 根据套料系统下发添加型材切割任务
     *
     * @param CutPlan 接收的切割任务
     */
    @ApiOperation("根据套料系统下发添加型材切割任务")
    @PostMapping("/addXcMaterialCutPlan")
    public void addXcMaterialCutPlan(@RequestBody List<HvPmXcMaterialCutPlanDTO> CutPlan) {
        hvPmXcMaterialCutPlanService.addXcMaterialCutPlan(CutPlan);
    }

    /**
     * 分页查询
     *
     * @param hvPmXcMaterialCutPlanTabQueryDTO
     * @return
     */
    @PostMapping("/getPage")
    @ApiOperation("分页模糊查询")
    public Page<HvPmXcMaterialCutPlanDTO> getPage(@RequestBody HvPmXcMaterialCutPlanTabQueryDTO hvPmXcMaterialCutPlanTabQueryDTO) {
        return hvPmXcMaterialCutPlanService.getPage(hvPmXcMaterialCutPlanTabQueryDTO);
    }

    @GetMapping("/isExistsOrderNo/{orderNo}")
    @ApiOperation("查询工作号是否存在")
    public boolean isExistsOrderNo(@PathVariable String orderNo) {
        return hvPmXcMaterialCutPlanService.isExistsOrderNo(orderNo);
    }

    @ApiOperation("新增型材切割计划")
    @PostMapping("/createXcCutPlan")
    public long createXcCutPlan(@RequestBody HvPmXcMaterialCutPlanDTO hvPmXcMaterialCutPlanDTO){
        return hvPmXcMaterialCutPlanService.createXcCutPlan(hvPmXcMaterialCutPlanDTO);
    }

    @ApiOperation("修改型材切割计划")
    @PostMapping("/updateXcCutPlan")
    public long updateXcCutPlan(@RequestBody HvPmXcMaterialCutPlanDTO hvPmXcMaterialCutPlanDTO){
        return hvPmXcMaterialCutPlanService.updateXcCutPlan(hvPmXcMaterialCutPlanDTO);
    }


    @ApiOperation("删除型材切割计划")
    @DeleteMapping("/deleteXcCutPlanById/{id}")
    public void deleteXcCutPlanById(@PathVariable long id){
        hvPmXcMaterialCutPlanService.deleteXcCutPlanById(id);
    }

    /**
     * 获取导入模板
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    @ApiResultIgnore
    @GetMapping("/getImportTemplate")
    @ApiOperation("获取导入模板")
    public ResultVO<ExcelExportDto> getImportTemplate() throws IOException, IllegalAccessException {
        return hvPmXcMaterialCutPlanService.getImportTemplate();
    }

    /**
     * 导入切割计划
     * @param file
     * @return
     * @throws IllegalAccessException
     * @throws ParseException
     * @throws IOException
     */
    @PostMapping("/importXcMaterialCutPlan")
    @ApiOperation("导入切割计划")
    public ImportResult importXcMaterialCutPlan(@RequestParam("file") MultipartFile file) throws IllegalAccessException, ParseException, IOException {
        return hvPmXcMaterialCutPlanService.importXcMaterialCutPlan(file);
    }


    @ApiResultIgnore
    @PostMapping(value = "/exportXcMaterialCutPlan")
    @ApiOperation(value = "导出型材切割计划")
    public ResultVO<ExcelExportDto> exportXcMaterialCutPlan(@RequestBody HvPmXcMaterialCutPlanTabQueryDTO hvPmXcMaterialCutPlanTabQueryDTO) throws IOException, IllegalAccessException {
        return hvPmXcMaterialCutPlanService.exportXcMaterialCutPlan(hvPmXcMaterialCutPlanTabQueryDTO);
    }


    /**
     * 根据切割计划id获取gen集合
     *
     * @param id
     * @return
     */
    @GetMapping("/getByOrderId/{id}")
    @ApiOperation("根据切割计划id获取gen列表")
    public Map<String, Object> getByOrderId(@PathVariable long id) {
        Map<String, Object> detailMap = new HashMap<>();
        detailMap.put("detail0", hvPmXcMaterialCutPlanDetail0Service.getAllDetail0ByOrderId(id));
        return detailMap;
    }

    /**
     * 根据切割计划id获取gen列表
     *
     * @param id
     * @return
     */
    @GetMapping("/getDetail0List/{id}")
    @ApiOperation("根据切割计划id获取gen列表")
    public List<HvPmXcMaterialCutPlanDetail0> getDetail0List(@PathVariable long id) {
        return hvPmXcMaterialCutPlanDetail0Service.getAllDetail0ByOrderId(id);
    }

    /**
     * 根据gen的id获取零件集合
     *
     * @param id
     * @return
     */
    @GetMapping("/getDetail1Map/{id}")
    @ApiOperation("根据gen的id获取零件集合")
    public Map<String, Object> getDetail1Map(@PathVariable long id) {
        Map<String, Object> detailMap = new HashMap<>();
        detailMap.put("detail1", hvPmXcMaterialCutPlanDetail1Service.getDetail1List(id));
        return detailMap;
    }

    /**
     * 根据gen的id获取零件列表
     *
     * @param id
     * @return
     */
    @GetMapping("/getDetail1List/{id}")
    @ApiOperation("根据gen的id获取零件列表")
    public List<HvPmXcMaterialCutPlanDetail1> getDetail1List(@PathVariable long id) {
        return hvPmXcMaterialCutPlanDetail1Service.getDetail1List(id);
    }


    /**
     * 添加gen
     *
     * @param hvPmXcMaterialCutPlanDetail0DTO
     * @return
     */
    @PostMapping("/createDetail0")
    @ApiOperation("添加gen")
    public long createDetail0(@RequestBody HvPmXcMaterialCutPlanDetail0DTO hvPmXcMaterialCutPlanDetail0DTO) {
        return hvPmXcMaterialCutPlanDetail0Service.createDetail0(hvPmXcMaterialCutPlanDetail0DTO);
    }

    /**
     * 添加零件
     *
     * @param hvPmXcMaterialCutPlanDetail1DTO
     * @return
     */
    @PostMapping("/createDetail1")
    @ApiOperation("添加零件")
    public long createDetail1(@RequestBody HvPmXcMaterialCutPlanDetail1DTO hvPmXcMaterialCutPlanDetail1DTO) {
        return hvPmXcMaterialCutPlanDetail1Service.createDetail1(hvPmXcMaterialCutPlanDetail1DTO);
    }

    /**
     * 修改gen
     *
     * @param hvPmXcMaterialCutPlanDetail0DTO
     * @return
     */
    @PutMapping("/updateDetail0")
    @ApiOperation("修改gen")
    public long updateDetail0(@RequestBody HvPmXcMaterialCutPlanDetail0DTO hvPmXcMaterialCutPlanDetail0DTO) {
        return hvPmXcMaterialCutPlanDetail0Service.updateDetail0(hvPmXcMaterialCutPlanDetail0DTO);
    }

    /**
     * 修改零件
     *
     * @param hvPmXcMaterialCutPlanDetail1DTO
     * @return
     */
    @PutMapping("/updateDetail1")
    @ApiOperation("修改零件")
    public long updateDetail1(@RequestBody HvPmXcMaterialCutPlanDetail1DTO hvPmXcMaterialCutPlanDetail1DTO) {
        return hvPmXcMaterialCutPlanDetail1Service.updateDetail1(hvPmXcMaterialCutPlanDetail1DTO);
    }

    /**
     * 删除gen
     *
     * @param id
     */
    @DeleteMapping("/deleteDetail0ById/{id}")
    @ApiOperation("删除gen")
    public void deleteDetail0ById(@PathVariable long id) {
        hvPmXcMaterialCutPlanDetail0Service.deleteDetail0ById(id);
    }

    /**
     * 删除零件
     *
     * @param id
     */
    @DeleteMapping("/deleteDetail1ById/{id}")
    @ApiOperation("删除零件")
    public void deleteDetail1ById(@PathVariable long id) {
        hvPmXcMaterialCutPlanDetail1Service.deleteDetail1ById(id);
    }


    /**
     * 根据orderId获取gen和零件的集合
     *
     * @param id
     * @return
     */
    @GetMapping("/getDetailDTOMapByOrderId/{id}")
    @ApiOperation("根据orderId获取gen和零件的集合")
    public Map<String, Object> getDetailDTOMapByOrderId(@PathVariable long id) {
        Map<String, Object> detailMap = new HashMap<>();
        detailMap.put("detail0", hvPmXcMaterialCutPlanDetail0Service.getDetailDTOListByOrderId(id));
        return detailMap;
    }

    /**
     * 型材切割计划下发
     * @param hvPmXcMaterialCutPlanDTO
     */
    @PostMapping("/sendXcOrder")
    @ApiOperation("型材切割计划下发")
    public ResultVO<?> sendXcOrder(@RequestBody HvPmXcMaterialCutPlanDTO hvPmXcMaterialCutPlanDTO, @UserInfo @ApiIgnore UserInfoDTO userInfo) {
       return hvPmXcMaterialCutPlanService.sendXcOrder(hvPmXcMaterialCutPlanDTO,userInfo);
    }
}
