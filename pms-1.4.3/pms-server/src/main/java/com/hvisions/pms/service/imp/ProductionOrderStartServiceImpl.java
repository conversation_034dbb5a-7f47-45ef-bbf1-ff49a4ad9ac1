/*
package com.hvisions.pms.service.imp;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.pms.dto.WorkOrderDTO;
import com.hvisions.pms.entity.HvPmPlanRouteInfo;
import com.hvisions.pms.service.HvPmMaterialCutPlanDetail0Service;
import com.hvisions.pms.service.HvPmPlanRouteInfoService;
import com.hvisions.pms.service.ProductionOrderStartService;
import com.hvisions.pms.service.WorkOrderService;
import com.hvisions.thirdparty.common.dto.*;
import com.hvisions.thridparty.client.MesClient;
import com.hvisions.wms.client.WaresLocationClient;
import com.hvisions.wms.client.WaresLocationMaterialPointClient;
import com.hvisions.wms.dto.location.WaresLocationDTO;
import com.hvisions.wms.dto.location.WaresLocationMaterialPointDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

*/
/**
 * <AUTHOR>
 * @date 2024-06-25 14:36
 *//*

@Service
public class ProductionOrderStartServiceImpl implements ProductionOrderStartService {
    @Autowired
    private WorkOrderService workOrderService;
    @Autowired
    private HvPmPlanRouteInfoService planRouteInfoService;
    @Autowired
    private HvPmMaterialCutPlanDetail0Service hvPmMaterialCutPlanDetail0Service;
    @Autowired
    private MesClient mesClient;
    @Autowired
    private WaresLocationClient waresLocationClient;
    @Autowired
    private WaresLocationMaterialPointClient waresLocationMaterialPointClient;

    */
/**
     * 型材计划开工
     * @param profileOutboundInfoDTO
 *//*

    @Override
    public void XcMaterialPlanStart(ProfileOutboundInfoDTO profileOutboundInfoDTO) {
        //截取任务号后面的工单号
        String[] split = profileOutboundInfoDTO.getTaskCode().split("-");
        if(split.length <= 1){
            throw new BaseKnownException("任务号未解析出工单号");
        }
        HvPmPlanRouteInfo routeInfo = planRouteInfoService.getStepIdByWorkOrderCode(split[1]);
        if(routeInfo == null){
            throw new BaseKnownException("工单号" + split[1] +"对应的工艺路线未找到下属工序");
        }
        List<WaresLocationDTO> waresLocationDTOS = waresLocationClient.getAllNotInZero().getData();
        Map<String, WaresLocationDTO> waresLocationMap = new HashMap<>();
        for (WaresLocationDTO waresLocationDTO : waresLocationDTOS) {
            waresLocationMap.put(waresLocationDTO.getCode(),waresLocationDTO);
        }
        List<WaresLocationMaterialPointDTO> materialPointDTOS = waresLocationMaterialPointClient.getAll().getData();
        Map<String, WaresLocationMaterialPointDTO> materialPointMap = new HashMap<>();
        for (WaresLocationMaterialPointDTO materialPointDTO : materialPointDTOS) {
            materialPointMap.put(materialPointDTO.getMaterialPointCode(),materialPointDTO);
        }

        List<OutboundMaterialDTO> materialList = profileOutboundInfoDTO.getMaterialList();
        ProductionOrderStartDTO productionOrderStartDTO = new ProductionOrderStartDTO();
        //productionOrderStartDTO.setProcessOrderId(routeInfo.getStepId());
        List<PickingOrder> orderArrayList = new ArrayList<>();
        for (OutboundMaterialDTO outboundMaterialDTO : materialList) {
            checkData(outboundMaterialDTO);
            PickingOrder pickingOrder = new PickingOrder();
            pickingOrder.setMaterialCode(outboundMaterialDTO.getMaterialCode());
            pickingOrder.setPickingQty(outboundMaterialDTO.getQuality().intValue());
            pickingOrder.setPalletCode(outboundMaterialDTO.getPalletCode());
            pickingOrder.setStockCode(outboundMaterialDTO.getWarehouseCode());
            pickingOrder.setStockName(waresLocationMap.get(outboundMaterialDTO.getWarehouseCode()).getName());
            //pickingOrder.setPickingPositionCode(outboundMaterialDTO.getLocationCode());
            //pickingOrder.setPickingPositionName(materialPointMap.get(outboundMaterialDTO.getLocationCode()).getMaterialPointName());
            orderArrayList.add(pickingOrder);
        }



        productionOrderStartDTO.setPickingOrderList(orderArrayList);
        mesClient.sendProductionOrderStart(productionOrderStartDTO);
    }

    */
/**
     * 校验数据是否存在
     * @param outboundMaterialDTO
 *//*

    private void checkData(OutboundMaterialDTO outboundMaterialDTO) {
        //库区编号非空校验
        if(StringUtils.isBlank(outboundMaterialDTO.getWarehouseCode())){
            throw new BaseKnownException("库区编号不能为空");
        }

        //库位/料点编号非空校验
        if(StringUtils.isBlank(outboundMaterialDTO.getLocationCode())){
            throw new BaseKnownException("库位/料点编号不能为空");
        }

        //物料编号非空校验
        if(StringUtils.isBlank(outboundMaterialDTO.getMaterialCode())){
            throw new BaseKnownException("物料编号不能为空");
        }

        //检测库区是否存在
        Integer locationId = waresLocationClient.getLocationId(outboundMaterialDTO.getWarehouseCode()).getData();
        if(locationId == null){
            throw new BaseKnownException("库区编号" + outboundMaterialDTO.getWarehouseCode() + "在系统库区主数据中不存在");
        }

        //检测库区与库位对应关系
        WaresLocationMaterialPointDTO condition = new WaresLocationMaterialPointDTO();
        condition.setLocationId(locationId);
        condition.setMaterialPointCode(outboundMaterialDTO.getLocationCode());
        WaresLocationMaterialPointDTO locationMaterialPointDTO = waresLocationMaterialPointClient.getMaterialPointByLocationIdAndMaterialPointCode(condition).getData();
        if(locationMaterialPointDTO == null){
            throw new BaseKnownException("库区编号" + outboundMaterialDTO.getWarehouseCode() + "与库位编号" + outboundMaterialDTO.getLocationCode() +"在系统库区主数据中不存在");
        }

        //检测物料数量
        if(outboundMaterialDTO.getQuality() <= 0){
            throw new BaseKnownException("物料数量必须大于0");
        }
    }

    */
/**
     * 钢板计划开工
     * @param detailReportDTO
 *//*

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void partOrderReporting(DetailReportDTO detailReportDTO) {
       */
/* //未实现
        if(StringUtils.isBlank(detailReportDTO.getOutEndTime())){
            return;
        }
        List<String> workOrderList = hvPmMaterialCutPlanDetail0Service.getWorkOrderByCutPlanCode(detailReportDTO.getCode());
        WorkOrderDTO workOrderDTO;
        ProductionOrderStartDTO productionOrderStartDTO;
        for (String workOrder : workOrderList) {
            HvPmPlanRouteInfo routeInfo =  planRouteInfoService.getStepIdByWorkOrderCode(workOrder);
            if(routeInfo == null){
                throw new BaseKnownException("工单号" + workOrder +"对应的工艺路线未找到下属工序");
            }
            workOrderDTO = workOrderService.findByWorkOrderCode(workOrder);
            productionOrderStartDTO = new ProductionOrderStartDTO();
            //productionOrderStartDTO.setProcessOrderId(routeInfo.getStepId());
            List<PickingOrder> orderArrayList = new ArrayList<>();
            PickingOrder pickingOrder = new PickingOrder();
            pickingOrder.setMaterialCode(workOrderDTO.getMaterialCode());
            pickingOrder.setPickingQty(workOrderDTO.getQuantity().intValue());

            orderArrayList.add(pickingOrder);
            productionOrderStartDTO.setPickingOrderList(orderArrayList);
            mesClient.sendProductionOrderStart(productionOrderStartDTO);*//*

        }
    }
}
*/
