package com.hvisions.pms.service.imp;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.pms.dao.HvPmMaterialBomItemMapper;
import com.hvisions.pms.dto.materialRequirement.HvPmMaterialBomItemDTO;
import com.hvisions.pms.dto.materialRequirement.HvPmMaterialBomItemQueryDTO;
import com.hvisions.pms.entity.HvPmMaterialBomItem;
import com.hvisions.pms.service.HvPmMaterialBomItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

@Service
public class HvPmMaterialBomItemServiceImpl extends ServiceImpl<HvPmMaterialBomItemMapper, HvPmMaterialBomItem> implements HvPmMaterialBomItemService {
    @Autowired
    private HvPmMaterialBomItemMapper hvPmMaterialBomItemMapper;

    @Override
    public Page<HvPmMaterialBomItemDTO> getPageMaterialByRequirementCode(HvPmMaterialBomItemQueryDTO queryDTO) {
        return PageHelperUtil.getPage(hvPmMaterialBomItemMapper::getPageMaterialByRequirementCode, queryDTO);
    }
}
