package com.hvisions.pms.entity;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

@Data
@Entity
public class HvPmStockPointStatus {
    /**
     * id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String pointCode; // 料点编号
    private Integer status; // 状态  0:闲置，1：占用
    private String palletCode;// 托盘/料框编号
}
