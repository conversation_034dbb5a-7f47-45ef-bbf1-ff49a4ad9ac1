package com.hvisions.pms.service;

import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.pms.dto.*;
import com.hvisions.pms.entity.HvPmOrderTask;
import com.hvisions.pms.viewdto.OrderOperationNumDTO;
import org.springframework.data.domain.Page;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: OrderOperationService</p>
 * <p>Description: 工单工序服务</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/2/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface OrderOperationService {


    /**
     * 工序开始
     *
     * @param operationOperatorDTO 工序操作传输对象
     * @param userInfo
     */
    void startOperation(OperationOperatorDTO operationOperatorDTO, UserInfoDTO userInfo);

    /**
     * 工序批量开始
     *
     * @param operationOperatorDTOS 工序操作传输对象列表
     * @param userInfo              用户信息
     */
    void startOperationBatch(List<OperationOperatorDTO> operationOperatorDTOS, UserInfoDTO userInfo);

    /**
     * 工序中断
     *
     * @param operationOperatorDTO 工序操作传输对象
     */
    void breakOperation(OperationOperatorDTO operationOperatorDTO);


    /**
     * 工序批量中断
     *
     * @param operationOperatorDTOS 工序操作传输对象列表
     */
    void breakOperationBatch(List<OperationOperatorDTO> operationOperatorDTOS);

    /**
     * 工序继续
     *
     * @param operationOperatorDTO 工序操作传输对象
     */
    void resumeOperation(OperationOperatorDTO operationOperatorDTO);

    /**
     * 工序批量继续
     *
     * @param operationOperatorDTOS 工序操作传输对象列表
     */
    void resumeOperationBatch(List<OperationOperatorDTO> operationOperatorDTOS);

    /**
     * 工序结束
     *
     * @param operationOperatorDTO 工序操作传输对象
     * @return 结束信息
     */
    Map<Object, String> endOperation(OperationOperatorDTO operationOperatorDTO);

    /**
     * 工序批量结束
     *
     * @param operationOperatorDTOS 工序操作传输对象列表
     * @return 结束信息
     */
    List<Map<Object, String>> endOperationBatch(List<OperationOperatorDTO> operationOperatorDTOS);

    /**
     * 根据工序ID查询下一道工序
     *
     * @param id 工序ID
     * @return 工序列表¬
     */
    Map<String, Object> getOrderOperationByOpeartionId(int id);

    /**
     * 工序终止
     *
     * @param operationOperatorDTO 工序操作传输对象
     */
    void cancelOperation(OperationOperatorDTO operationOperatorDTO);

    /**
     * 工序批量终止
     *
     * @param operationOperatorDTOS 工序操作传输对象列表
     */
    void cancelOperationBatch(List<OperationOperatorDTO> operationOperatorDTOS);

    /***
     * 切换加工设备
     * @param taskId 工序任务ID
     * @param workCenterId  设备ID
     */
    void changeWorkCenter(int taskId, int workCenterId);

    /**
     * 批量切换加工设备
     *
     * @param changeWorkCenterDTO 切换条件DTO
     */
    void changeManyWorkCenter(ChangeWorkCenterDTO changeWorkCenterDTO);


    /**
     * 拆分工序
     *
     * @param operationId 工序Id
     * @param count       拆分数量
     * @param equipmentId 设备Id
     */
    void splitOrderOperation(int operationId, BigDecimal count, Integer equipmentId);

    /**
     * 根据工单id获取工序信息列表
     *
     * @param orderId 工单id
     * @return 工序信息列表
     */
    List<OrderOperationDTO> getOperationByOrderId(int orderId);

    /**
     * 根据ID列表查询工序
     *
     * @param idIn 工序ID列表
     * @return 工序列表
     */
    List<OrderOperationDTO> getOperationByIdIn(List<Integer> idIn);

    /**
     * 根据设备ID查找工序列表
     *
     * @param workCenterId 设备ID
     * @return 工序列表
     */
    List<OrderOperationDTO> getOrderOperationByWorkCenterId(int workCenterId);

    /**
     * 根据ID查询工序
     *
     * @param id     工序ID
     * @param taskId 任务ID
     * @return 工序信息
     */
    OrderOperationDTO getOperationById(int id, int taskId);

    /**
     * 根据设备id，工序状态查询工单信息
     *
     * @param operationQueryDTO 分页查询对象
     * @return 工序的分页列表
     */
    Page<OrderOperationDTO> getOperationPage(OperationQueryDTO operationQueryDTO);

    /**
     * 根据工序ID查询 工序操作记录
     *
     * @param operationId 工序ID
     * @return 工序操作记录
     */
    List<ProcedureRecordDTO> getProcedureRecordByOperationId(int operationId);

    /**
     * 进入工序进度查询
     *
     * @param workCenterId 设备ID
     * @return 工序进度列表
     * @throws ParseException 解析异常
     */
    OrderOperationNumDTO getOperationIdByWorkCenterId(int workCenterId) throws ParseException;


    /**
     * 根据工序id和设备id查询设备采集参数
     *
     * @param query 工艺参数查询对象
     * @return 设备采集参数
     */
    OperationParamDto getEquipCollectData(RouteParamQuery query);

    /**
     * 根据工序id和设备id查询设备下发参数
     *
     * @param query 工艺参数查询对象
     * @return 设备下发参数列表
     */
    OperationParamDto getEquipSendData(RouteParamQuery query);

    /**
     * 记录采集参数
     *
     * @param recordParamDto 保存记录
     */
    void recordCollectParam(RecordParamDto recordParamDto);

    /**
     * 记录并下发参数
     *
     * @param recordParamDto 保存记录
     */
    void recordSendParam(RecordParamDto recordParamDto);

    /**
     * 根据记录id查询工序参数历史记录
     *
     * @param recordId 记录id
     * @return 工序参数历史记录
     */
    ParamHistoryDataDto findByRecordId(Integer recordId);

    /**
     * 自动采集或者自动发送参数信息
     *
     * @param task      任务信息
     * @param eventType 任务事件
     */
    void autoHandleIotParameterInfo(HvPmOrderTask task, String eventType);

    void test(Integer taskId, Integer event);

    int deleteByWorkOrderCode(String workOrderCode);
}

    
    
    
    