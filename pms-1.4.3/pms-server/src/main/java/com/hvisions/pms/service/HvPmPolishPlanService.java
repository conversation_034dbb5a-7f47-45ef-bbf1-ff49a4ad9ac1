package com.hvisions.pms.service;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.pms.entity.plan.HvPmPolishPlan;
import com.hvisions.pms.plan.HvPmPolishPlanDTO;
import com.hvisions.pms.plan.HvPmPolishPlanTabQueryDTO;
import com.hvisions.thirdparty.common.dto.AssemblyWorkOrderDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/9/21
 */
public interface HvPmPolishPlanService {
    /**
     * 分页查询
     * @param hvPmPolishPlanTabQueryDTO
     * @return
     */
    Page<HvPmPolishPlanDTO> getPage(HvPmPolishPlanTabQueryDTO hvPmPolishPlanTabQueryDTO);

    /**
     * 查询工作号是否存在
     * @param code
     * @return
     */
    boolean isExistsCode(String code);

    HvPmPolishPlan getByCodeEquals(String orderNo);

    /**
     * 新增打磨计划
     * @param hvPmPolishPlanDTO
     * @return
     */
    long createPolishPlan(HvPmPolishPlanDTO hvPmPolishPlanDTO);

    /**
     * 修改打磨计划
     * @param hvPmPolishPlanDTO
     * @return
     */
    long updatePolishPlan(HvPmPolishPlanDTO hvPmPolishPlanDTO);


    /**
     * 删除打磨计划和从表信息
     * @param id
     */
    void deletePolishPlanById(long id);


    /**
     * 获取导入模板
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    ResultVO<ExcelExportDto> getImportTemplate() throws IOException, IllegalAccessException;

    /**
     * 导入打磨计划
     *
     * @param file bom信息文档
     * @return 返回信息
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    ImportResult importPolishPlan(MultipartFile file) throws IllegalAccessException, ParseException, IOException;


    /**
     *
     * 导出打磨计划
     * @param hvPmPolishPlanTabQueryDTO
     * @return
     */
    ResultVO<ExcelExportDto> exportPolishPlan(HvPmPolishPlanTabQueryDTO hvPmPolishPlanTabQueryDTO) throws IOException, IllegalAccessException;


    /**
     * 根据组立系统下发添加打磨计划
     * @param assemblyWorkOrderDTO
     */
    void addPolishPlan(AssemblyWorkOrderDTO assemblyWorkOrderDTO);

    /**
     * 手动下发打磨计划
     * @param userInfo
     * @return
     */
    ResultVO<?> sendPolishPlan(HvPmPolishPlanDTO hvPmPolishPlanDTO,UserInfoDTO userInfo);

    /**
     * 根据任务编号获取打磨计划
     * @param code
     * @return
     */
    HvPmPolishPlanDTO getHvPmPolishPlanDTOByCode(String code);

    /**
     * 根据任务编号修改打磨计划
     * @param code
     * @return
     */
    int upPolishPlanStatusByCode(String code,Integer status);


    /**
     * 根据工单号获取打磨计划
     * @param workOrderCode
     * @return
     */
    HvPmPolishPlanDTO getByWorkOrderCode(String workOrderCode);


}
