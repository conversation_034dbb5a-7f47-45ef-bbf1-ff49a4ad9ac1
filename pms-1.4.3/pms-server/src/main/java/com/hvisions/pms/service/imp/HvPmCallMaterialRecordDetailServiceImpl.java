package com.hvisions.pms.service.imp;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.hiperbase.client.MaterialClient;
import com.hvisions.hiperbase.materials.dto.MaterialDTO;
import com.hvisions.pms.dao.HvPmCallMaterialRecordDetailMapper;
import com.hvisions.pms.entity.HvPmCallMaterialRecordDetail;
import com.hvisions.pms.service.HvPmCallMaterialRecordDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-05-27 16:00
 */
@Service
public class HvPmCallMaterialRecordDetailServiceImpl extends ServiceImpl<HvPmCallMaterialRecordDetailMapper, HvPmCallMaterialRecordDetail> implements HvPmCallMaterialRecordDetailService {
    @Autowired
    private MaterialClient materialClient;

    @Autowired
    private HvPmCallMaterialRecordDetailMapper hvPmCallMaterialRecordDetailMapper;

    @Override
    public Page<HvPmCallMaterialRecordDetail> pageList(Page<HvPmCallMaterialRecordDetail> page, QueryWrapper<HvPmCallMaterialRecordDetail> queryWrapper) {
        Page<HvPmCallMaterialRecordDetail> pageList = this.page(page, queryWrapper);
        List<HvPmCallMaterialRecordDetail> listRecords = pageList.getRecords();
        List<String> materialCodes = new ArrayList<>();
        Map<String, String> materialMap = new HashMap<>();
        for (HvPmCallMaterialRecordDetail hvPmCallMaterialRecordDetail : listRecords) {
            materialCodes.add(hvPmCallMaterialRecordDetail.getMaterialCode());
        }
        //根据物料号查询物料信息
        if(!materialCodes.isEmpty()){
            List<MaterialDTO> materialDTOS = materialClient.getMaterialListByMaterialCodes(materialCodes).getData();
            for (MaterialDTO materialDTO : materialDTOS) {
                materialMap.put(materialDTO.getMaterialCode(), materialDTO.getMaterialName());
            }
        }
        List<HvPmCallMaterialRecordDetail> collect = listRecords.stream().peek(hvPmCallMaterialRecordDetail -> hvPmCallMaterialRecordDetail.setMaterialName(materialMap.get(hvPmCallMaterialRecordDetail.getMaterialCode()))).collect(Collectors.toList());
        pageList.setRecords(collect);
        return pageList;
    }

    /**
     * 根据主表id列表获取详细信息
     * @param recordIds
     * @return
     */
    @Override
    public List<HvPmCallMaterialRecordDetail> getDetailListByRecordIds(List<Long> recordIds) {
        return hvPmCallMaterialRecordDetailMapper.getDetailListByRecordIds(recordIds);
    }

}
