package com.hvisions.pms.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.pms.dto.ShipRawMaterialDTO;
import com.hvisions.pms.dto.ShipRawMaterialDetailDTO;
import com.hvisions.pms.dto.ShipRawMaterialQueryDTO;
import com.hvisions.pms.service.ShipRawMaterialDetailService;
import com.hvisions.pms.service.ShipRawMaterialService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/5/22
 */
@Slf4j
@Api(tags = "船型分段原料")
@RestController
@RequestMapping("/shipRawMaterial")
public class ShipRawMaterialController {

    @Autowired
    private ShipRawMaterialService shipRawMaterialService;

    @Autowired
    private ShipRawMaterialDetailService detailService;

    /**
     * 分页查询
     * @param query
     * @return
     */
    @ApiOperation("分页查询")
    @PostMapping("/getPage")
    public Page<ShipRawMaterialDTO> getPage(@RequestBody ShipRawMaterialQueryDTO query){
        return shipRawMaterialService.getPage(query);
    }

    /**
     * 增加
     * @param shipRawMaterialDTO
     * @return
     */
    @ApiOperation("增加")
    @PostMapping("/addShipRawMaterial")
    public Long addShipRawMaterial(@RequestBody ShipRawMaterialDTO shipRawMaterialDTO){
        return shipRawMaterialService.createShipRawMaterial(shipRawMaterialDTO);
    }

    /**
     * 修改
     * @param shipRawMaterialDTO
     * @return
     */
    @ApiOperation("修改")
    @PutMapping("/updateShipRawMaterial")
    public Long updateShipRawMaterial(@RequestBody ShipRawMaterialDTO shipRawMaterialDTO){
        return shipRawMaterialService.updateShipRawMaterial(shipRawMaterialDTO);
    }

    /**
     * 删除
     * @param id
     */
    @ApiOperation("删除")
    @DeleteMapping("/deleteById/{id}")
    public void deleteById(@PathVariable Long id){
        shipRawMaterialService.deleteShipRawMaterial(id);
    }

    /**
     * 根据shipRawMaterialId获取明细
     * @param shipRawMaterialId
     * @return
     */
    @ApiOperation("根据shipRawMaterialId获取明细")
    @GetMapping("/getByShipRawMaterialId/{shipRawMaterialId}")
    public List<ShipRawMaterialDetailDTO> getByShipRawMaterialId(@PathVariable Long shipRawMaterialId){
        return detailService.getByRawMaterialId(shipRawMaterialId);
    }

    /**
     * 添加明细
     * @param shipRawMaterialDetailDTO
     * @return
     */
    @ApiOperation("添加明细")
    @PostMapping("/addDetail")
    public Long addDetail(@RequestBody ShipRawMaterialDetailDTO shipRawMaterialDetailDTO){
        return detailService.createShipRawMaterialDetail(shipRawMaterialDetailDTO);
    }

    /**
     * 修改明细
     * @param shipRawMaterialDetailDTO
     * @return
     */
    @ApiOperation("修改明细")
    @PutMapping("/updateDetail")
    public Long updateDetail(@RequestBody ShipRawMaterialDetailDTO shipRawMaterialDetailDTO){
        return detailService.updateShipRawMaterialDetail(shipRawMaterialDetailDTO);
    }

    /**
     * 删除明细
     * @param id
     */
    @ApiOperation("删除明细")
    @DeleteMapping("/deleteDetail/{id}")
    public void deleteDetail(@PathVariable Long id){
        detailService.deleteShipRawMaterialDetail(id);
    }

    /**
     * 获取模板
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    @ApiOperation("获取模板")
    @GetMapping("/getImportTemplate")
    @ApiResultIgnore
    public ResultVO<ExcelExportDto> getImportTemplate() throws IOException, IllegalAccessException {
        return shipRawMaterialService.getImportTemplate();
    }

    /**
     * 导入
     * @param file
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    @PostMapping("/importShipRawMaterial")
    @ApiOperation("导入")
    public ImportResult importShipRawMaterial(@RequestParam("file")MultipartFile file) throws IOException, IllegalAccessException {
        return shipRawMaterialService.importShipRawMaterial(file);
    }

}
