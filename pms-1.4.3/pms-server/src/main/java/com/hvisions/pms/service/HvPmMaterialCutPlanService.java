package com.hvisions.pms.service;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.pms.entity.plan.HvPmMaterialCutPlan;
import com.hvisions.pms.plan.HvPmMaterialCutPlanDTO;
import com.hvisions.pms.plan.HvPmMaterialCutPlanQueryDTO;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

public interface HvPmMaterialCutPlanService {

    /**
     * 分页条件查询
     * @param hvPmMaterialCutPlanQueryDTO
     * @return
     */
    Page<HvPmMaterialCutPlanDTO> getPage(HvPmMaterialCutPlanQueryDTO hvPmMaterialCutPlanQueryDTO);

    /**
     * 添加切割计划
     * @param hvPmMaterialCutPlanDTO
     * @return
     */
    long createCutPlan(HvPmMaterialCutPlanDTO hvPmMaterialCutPlanDTO);

    /**
     * 修改切割计划
     * @param hvPmMaterialCutPlanDTO
     * @return
     */
    long updateCutPlan(HvPmMaterialCutPlanDTO hvPmMaterialCutPlanDTO);

    /**
     * 修改切割计划并通知产线取消
     * @param hvPmMaterialCutPlanDTO
     * @return
     */
    ResultVO<?> updateCutPlanAndCancel(HvPmMaterialCutPlanDTO hvPmMaterialCutPlanDTO);

    /**
     * 删除切割计划
     * @param id
     */
    ResultVO<?>  deleteCutPlanById(long id);

    /**
     * 是否存在cutPlanCode
     * @param cutPlanCode
     * @return
     */
    boolean isExistsCutPlanCode(String cutPlanCode);

    /**
     * 钢板零件订单下发
     *
     * @param hvPmMaterialCutPlanDTO
     * @return
     */
    ResultVO<?> sendOrder(HvPmMaterialCutPlanDTO hvPmMaterialCutPlanDTO, UserInfoDTO userInfo);

    /**
     * 获取导入模板
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    ResultVO<ExcelExportDto> getImportTemplate() throws IOException, IllegalAccessException;

    /**
     * 导入切割计划信息
     *
     * @param file bom信息文档
     * @return 返回信息
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    ImportResult importMaterialCutPlan(MultipartFile file) throws IllegalAccessException, ParseException, IOException;


    /**
     * 导出所有钢板切割计划
     *
     * @return Excel表
     * @throws IOException            io异   常
     * @throws IllegalAccessException field访问异常
     */
    ResultVO<ExcelExportDto> exportMaterialCutPlan(HvPmMaterialCutPlanQueryDTO hvPmMaterialCutPlanQueryDTO) throws IOException, IllegalAccessException;

    /**
     *  获取需要导出的数据内容
     * @return excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    ResponseEntity<byte[]> geteExportInfo(HvPmMaterialCutPlanQueryDTO hvPmMaterialCutPlanQueryDTO) throws IOException, IllegalAccessException;

    void addMaterialCutPlan(List<HvPmMaterialCutPlanDTO> cutPlan);

    HvPmMaterialCutPlan getByPlanCode(String code);

    ResultVO<?> cutTaskCancel(int lineId,String type,String code);

    List<String> getShipNumberList();

    HvPmMaterialCutPlan getById(Long planId);

    HvPmMaterialCutPlanDTO buildCutPlanDTO(HvPmMaterialCutPlan plan, Long planId);
}
