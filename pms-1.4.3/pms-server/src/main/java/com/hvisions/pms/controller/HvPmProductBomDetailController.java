package com.hvisions.pms.controller;

import com.hvisions.pms.entity.HvPmProductBomDetail;
import com.hvisions.pms.service.HvPmProductBomDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@Api(tags = "工单物料料框绑定")
@RestController
@RequestMapping("/bomDetail")
public class HvPmProductBomDetailController {
    @Autowired
    private HvPmProductBomDetailService bomDetailService;

    @ApiOperation("根据料框编号查询")
    @PostMapping("/findByPalletCode")
    public List<HvPmProductBomDetail> findByPalletCode(@RequestParam String PalletCode) {
        return bomDetailService.findByPalletCode(PalletCode);
    }
}
