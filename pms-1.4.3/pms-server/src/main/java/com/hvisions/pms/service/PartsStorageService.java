package com.hvisions.pms.service;

import com.hvisions.thirdparty.common.dto.EmptyPodAlertDTO;
import com.hvisions.thirdparty.common.dto.StockPointStatusDTO;

public interface PartsStorageService {
    void saveStockPointStatus(StockPointStatusDTO stockPointStatusDTO);

    void handleEmptyPodAlert(EmptyPodAlertDTO emptyPodAlertDTO);

    Integer updateStatusById(StockPointStatusDTO stockPointStatusDTO);

    StockPointStatusDTO findPointStatusByPointCode(String pointCode);

    void handleHvPmPartsOut(String taskNo);
}
