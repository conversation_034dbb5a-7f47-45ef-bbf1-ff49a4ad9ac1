package com.hvisions.pms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.pms.entity.HvPmCallMaterialRecordDetail;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-05-27 15:59
 */
public interface HvPmCallMaterialRecordDetailService extends IService<HvPmCallMaterialRecordDetail> {
    Page<HvPmCallMaterialRecordDetail> pageList(Page<HvPmCallMaterialRecordDetail> page, QueryWrapper<HvPmCallMaterialRecordDetail> queryWrapper);

    List<HvPmCallMaterialRecordDetail> getDetailListByRecordIds(List<Long> recordIds);
}
