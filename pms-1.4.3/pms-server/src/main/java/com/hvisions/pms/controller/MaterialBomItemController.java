package com.hvisions.pms.controller;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.pms.dto.materialRequirement.HvPmMaterialBomItemDTO;
import com.hvisions.pms.dto.materialRequirement.HvPmMaterialBomItemQueryDTO;
import com.hvisions.pms.entity.HvPmMaterialBomItem;
import com.hvisions.pms.service.HvPmMaterialBomItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping(value = "/materialBomItem")
@Api(description = "物料需求子项物料")
public class MaterialBomItemController {
    @Autowired
    private HvPmMaterialBomItemService materialBomItemService;

    @PostMapping(value = "/getMaterialByRequirementCode")
    @ApiOperation("根据物料需求编号获取子项物料分页列表")
    public Page<HvPmMaterialBomItemDTO> getPageMaterialByRequirementCode(@RequestBody HvPmMaterialBomItemQueryDTO queryDTO) {
        return materialBomItemService.getPageMaterialByRequirementCode(queryDTO);
    }

    @GetMapping(value = "/getListMaterialByRequirementCode/{code}")
    @ApiOperation("根据物料需求编号获取子项物料列表")
    public List<HvPmMaterialBomItemDTO> getListMaterialByRequirementCode(@PathVariable String code) {
        Wrapper<HvPmMaterialBomItem> eq = new LambdaQueryWrapper<HvPmMaterialBomItem>()
                .eq(HvPmMaterialBomItem::getMaterialRequirementCode, code);
        return DtoMapper.convertList(materialBomItemService.list(eq), HvPmMaterialBomItemDTO.class);
    }


    @PostMapping(value = "/addMaterialBomItem")
    @ApiOperation("新增子项物料")
    public ResultVO addMaterialBomItem(@RequestBody HvPmMaterialBomItemDTO hvPmMaterialBomItemDTO) {
        return ResultVO.success(materialBomItemService.save(DtoMapper.convert(hvPmMaterialBomItemDTO, HvPmMaterialBomItem.class)));
    }

}
