package com.hvisions.pms.controller;

import com.hvisions.common.annotation.EnableFilter;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.pms.dto.HvPmLineReportMaterialDTO;
import com.hvisions.pms.service.HvPmLineReportMaterialService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-11 10:21
 */
@RestController
@RequestMapping(value = "/hvPmLineReportMaterial")
@Api(description = "产线报工记录详情")
public class LineReportMaterialController {
    @Autowired
    private HvPmLineReportMaterialService hvPmLineReportMaterialService;


    /**
     * 添加
     *
     * @param hvPmLineReportMaterialDTO HvPmLineReportMaterial
     */
    @ApiOperation(value = "添加HvPmLineReportMaterial信息")
    @PostMapping(value = "/add")
    public void addHvPmLineReportMaterial(@Valid @RequestBody HvPmLineReportMaterialDTO hvPmLineReportMaterialDTO) {
        hvPmLineReportMaterialService.addHvPmLineReportMaterial(hvPmLineReportMaterialDTO);
    }

    /**
     * 删除
     *
     * @param id 主键
     */
    @ApiOperation(value = "删除HvPmLineReportMaterial信息")
    @DeleteMapping(value = "/delete/{id}")
    public void deleteHvPmLineReportMaterial(@PathVariable Integer id) {
        hvPmLineReportMaterialService.deleteHvPmLineReportMaterial(id);
    }

    /**
     * 修改
     *
     * @param hvPmLineReportMaterialDTO HvPmLineReportMaterial
     */
    @ApiOperation(value = "修改HvPmLineReportMaterial")
    @PutMapping(value = "/update")
    public void updateHvPmLineReportMaterial(@Valid @RequestBody HvPmLineReportMaterialDTO hvPmLineReportMaterialDTO) {
        hvPmLineReportMaterialService.updateHvPmLineReportMaterial(hvPmLineReportMaterialDTO);
    }

    /**
     * 根据id获取
     *
     * @param id 主键
     * @return HvPmLineReportMaterial hvPmLineReportMaterialDTO
     */
    @ApiOperation(value = "根据id获取HvPmLineReportMaterial")
    @GetMapping(value = "/get/{id}")
    public HvPmLineReportMaterialDTO getList(@PathVariable long id) {
        return hvPmLineReportMaterialService.getHvPmLineReportMaterialById(id);
    }

    /**
     * 查询全部
     * @return 列表
     */
    @ApiOperation(value = "获取HvPmLineReportMaterial列表")
    @GetMapping(value = "/getAll")
    public List<HvPmLineReportMaterialDTO> getAll(){
        return hvPmLineReportMaterialService.getAll();
    }

    /**
     * 根据 条件分页查询产线报工记录物料详情
     *
     * @param  condition 查询条件
     * @return 检查项目分页信息
     */
    @EnableFilter
    @PostMapping(value = "/findPageByCondition")
    @ApiOperation(value = "根据 条件分页查询产线报工记录物料详情")
    public Page<HvPmLineReportMaterialDTO> findPageByCondition(@RequestBody HvPmLineReportMaterialDTO condition) {
        return hvPmLineReportMaterialService.findPageByCondition(condition);
    }
}
