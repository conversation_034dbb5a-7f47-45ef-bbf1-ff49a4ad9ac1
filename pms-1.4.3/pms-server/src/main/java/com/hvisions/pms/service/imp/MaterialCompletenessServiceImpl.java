package com.hvisions.pms.service.imp;

import com.hvisions.pms.service.MaterialCompletenessInterface;
import com.hvisions.pms.service.MaterialCompletenessService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.stereotype.Service;

import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class MaterialCompletenessServiceImpl implements MaterialCompletenessService {
    private static final String TASK_LOCK_KEY = "materialCompletenessTaskLock"; // 分布式锁的键
    private static final String TASK_PAUSED_KEY = "materialCompletenessTaskPaused"; // Redis 中存储任务暂停状态的键
    private static final String TASK_RUNNING_KEY = "materialCompletenessTaskRunning"; // Redis 中存储任务运行状态的键

    @Autowired
    private RedissonClient redissonClient; // 注入 Redisson 客户端
    @Autowired
    private TaskScheduler taskScheduler; // 注入 TaskScheduler
    @Autowired
    private MaterialCompletenessInterface materialCompletenessInterface;

    private ScheduledFuture<?> scheduledFuture; // 用于保存调度任务的 Future
    private boolean isScheduled = false; // 标记任务是否已调度

    @Value("${scheduled.materialCompleteness.interval}")
    private long taskInterval; // 从配置文件中读取任务执行间隔（单位：毫秒）

    @Override
    public void startScheduledTask() {
        RLock lock = redissonClient.getLock(TASK_LOCK_KEY);
        try {
            if (lock.tryLock(0, TimeUnit.MINUTES)) {
                try {
                    if (!isScheduled) {
                        // 使用 TaskScheduler 调度任务
                        scheduledFuture = taskScheduler.scheduleAtFixedRate(
                                this::performTasks,
                                taskInterval // 执行间隔
                        );
                        isScheduled = true;
                        // 初始化任务状态为未暂停
                        redissonClient.getBucket(TASK_PAUSED_KEY).set(false);
                        log.info("定时任务已启动，执行间隔：{} 毫秒", taskInterval);
                    } else {
                        log.warn("定时任务已处于启动状态，无需重复启动");
                    }
                } finally {
                    lock.unlock();
                }
            } else {
                log.info("未获取到分布式锁，跳过本次任务启动");
            }
        } catch (InterruptedException e) {
            log.error("获取分布式锁时被中断", e);
            Thread.currentThread().interrupt(); // 恢复中断状态
        }
    }

    @Override
    public void pauseScheduledTask() {
        // 1. 原子设置暂停状态
        redissonClient.getBucket(TASK_PAUSED_KEY).set(true);
        log.info("定时任务已标记为暂停，等待当前任务执行完成");

        // 2. 等待当前任务完成（通过轮询 TASK_RUNNING_KEY）
        long maxWaitTime = 60_000; // 最大等待时间 60 秒
        long startTime = System.currentTimeMillis();

        try {
            while (Boolean.TRUE.equals(redissonClient.getBucket(TASK_RUNNING_KEY).get())) {
                log.info("当前任务正在执行，等待完成...");
                if (System.currentTimeMillis() - startTime > maxWaitTime) {
                    log.warn("等待超时，强制终止等待");
                    break;
                }
                Thread.sleep(1000); // 每秒检查一次
            }
            log.info("任务已暂停");
        } catch (InterruptedException e) {
            log.error("等待任务完成时被中断", e);
            Thread.currentThread().interrupt();
        }
    }

    @Override
    public void resumeScheduledTask() {
        // 直接原子恢复任务状态
        redissonClient.getBucket(TASK_PAUSED_KEY).set(false);
        log.info("定时任务已恢复");
    }

    public void performTasks() {
        // 检查任务是否暂停
        boolean isPaused = Boolean.TRUE.equals(redissonClient.getBucket(TASK_PAUSED_KEY).get());
        if (isPaused) {
            log.info("任务处于暂停状态，跳过本次执行");
            return;
        }

        RLock executionLock = redissonClient.getLock(TASK_LOCK_KEY); // 独立的执行锁
        try {
            // 标记任务为运行中
            redissonClient.getBucket(TASK_RUNNING_KEY).set(true);

            // 尝试获取执行锁（仅用于保证单实例执行）
            if (executionLock.tryLock(5, TimeUnit.SECONDS)) {
                log.info("成功获取执行锁，开始执行物料齐套校验");
                try {
                    materialCompletenessInterface.checkStockAndSend(null);
                } catch (Exception e) {
                    log.error("物料齐套校验失败", e);
                } finally {
                    executionLock.unlock();
                    log.info("释放执行锁，任务执行完成");
                }
            } else {
                log.info("未获取到执行锁，跳过本次任务执行");
            }
        } catch (InterruptedException e) {
            log.error("获取执行锁时被中断", e);
            Thread.currentThread().interrupt();
        } finally {
            // 标记任务为未运行
            redissonClient.getBucket(TASK_RUNNING_KEY).set(false);
        }
    }
}