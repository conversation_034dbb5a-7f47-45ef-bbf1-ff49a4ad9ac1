package com.hvisions.pms.service;

import com.hvisions.pms.dto.HvPmLineReportMaterialDTO;
import com.hvisions.pms.entity.HvPmLineReportMaterial;
import com.hvisions.pms.exportdto.LineReportDetail0ExportDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-11 10:09
 */
public interface HvPmLineReportMaterialService {
    /**
     * 保存
     *
     * @param hvPmLineReportMaterialDTO HvPmLineReportMaterial
     *
     */
    void addHvPmLineReportMaterial(HvPmLineReportMaterialDTO hvPmLineReportMaterialDTO);

    /**
     * 通过id删除
     *
     * @param id 主键
     *
     */
    void deleteHvPmLineReportMaterial(long id);

    /**
     * 修改
     *
     * @param hvPmLineReportMaterialDTO HvPmLineReportMaterial
     *
     */
    void updateHvPmLineReportMaterial(HvPmLineReportMaterialDTO hvPmLineReportMaterialDTO);

    /**
     * 获取
     *
     * @param id 主键
     * @return HvPmLineReportMaterial hvPmLineReportMaterialDTO HvPmLineReportMaterial
     */
    HvPmLineReportMaterialDTO getHvPmLineReportMaterialById(long id);

    /**
     * 获取列表
     *
     * @return List<HvPmLineReportMaterialDTO> HvPmLineReportMaterial列表
     */
    List<HvPmLineReportMaterialDTO> getAll();

    Page<HvPmLineReportMaterialDTO> findPageByCondition(HvPmLineReportMaterialDTO condition);

    void saveBatch(List<HvPmLineReportMaterialDTO> hvPmLineReportMaterialDTOS);

    List<HvPmLineReportMaterial> selectByMaterialAndFrameCode(String materialCode, String frameCode);

    List<LineReportDetail0ExportDTO> getlineReportMaterialListByReportId(List<Long> reportIds);

}
