package com.hvisions.pms.controller;


import com.hvisions.pms.dto.HomeSevenDataDTO;
import com.hvisions.pms.entity.CutPartsQueryDTO;
import com.hvisions.pms.entity.CuttingResultDTO;
import com.hvisions.pms.service.CutPlanReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <P> 切割计划报表  <P>
 *
 * <AUTHOR>
 * @date 2024/11/16
 */
@Slf4j
@Api("切割计划报表")
@RestController
@RequestMapping("/cutPlanReport")
public class CutPlanReportController {

    @Autowired
    private CutPlanReportService cutPlanReportService;

    @ApiOperation("获取某天钢板切割完成数量")
    @RequestMapping("/getOneDataSteelCutByLineCode/{lineCode}/{date}")
    public Integer getOneDataSteelCutByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
       return cutPlanReportService.getOneDataSteelCutByLineCode(lineCode,date);
    }

    @ApiOperation("获取某天钢板切割计划数量")
    @RequestMapping("/getOneDataSteelPlanCutByLineCode/{lineCode}/{date}")
    public Integer getOneDataSteelPlanCutByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
        return cutPlanReportService.getOneDataSteelPlanCutByLineCode(lineCode,date);
    }



    @ApiOperation("获取钢板切割计划的七天完成数量")
    @RequestMapping("/getCutSevenDataByLineCode/{lineCode}/{date}")
    public HomeSevenDataDTO getCutSevenDataByLineCode(@PathVariable("lineCode") String  lineCode, @PathVariable("date") String date){
        return cutPlanReportService.getCutSevenDataByLineCode(lineCode,date);
    }



    @ApiOperation("获取钢板切割计划的一周完成数量")
    @RequestMapping("/getOneWeekSteelCutByLineCode/{lineCode}/{date}")
    public Integer getOneWeekSteelCutByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
        return cutPlanReportService.getOneWeekSteelCutByLineCode(lineCode,date);
    }

    @ApiOperation("获取钢板切割计划的一个月完成数量")
    @RequestMapping("/getOneMonthSteelCutByLineCode/{lineCode}/{date}")
    public Integer getOneMonthSteelCutByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
        return cutPlanReportService.getOneMonthSteelCutByLineCode(lineCode,date);
    }

    @ApiOperation("获取钢板切割计划的一个年完成数量")
    @RequestMapping("/getOneYearSteelCutByLineCode/{lineCode}/{date}")
    public Integer getOneYearSteelCutByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
        return cutPlanReportService.getOneYearSteelCutByLineCode(lineCode,date);
    }

    @ApiOperation("获取钢板切割计划的一周计划数量")
    @RequestMapping("/getOneWeekSteelPlanCutByLineCode/{lineCode}/{date}")
    public Integer getOneWeekSteelPlanCutByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
        return cutPlanReportService.getOneWeekSteelPlanCutByLineCode(lineCode,date);
    }


    @ApiOperation("获取钢板切割计划的一个月计划数量")
    @RequestMapping("/getOneMonthSteelPlanCutByLineCode/{lineCode}/{date}")
    public Integer getOneMonthSteelPlanCutByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
        return cutPlanReportService.getOneMonthSteelPlanCutByLineCode(lineCode,date);
    }

    @ApiOperation("获取钢板切割计划的一年计划数量")
    @RequestMapping("/getOneYearSteelPlanCutByLineCode/{lineCode}/{date}")
    public Integer getOneYearSteelPlanCutByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
        return cutPlanReportService.getOneYearSteelPlanCutByLineCode(lineCode,date);
    }


    @ApiOperation("钢板零件切割某天的完成数量")
    @RequestMapping("/getOneDataSteelPartCutByLineCode/{lineCode}/{date}")
    public Integer getOneDataSteelPartCutByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
        return cutPlanReportService.getOneDataSteelPartCutByLineCode(lineCode,date);
    }

    @ApiOperation("钢板零件切割某天的完成数量")
    @RequestMapping("/getOneDataSteelPartPlanCutByLineCode/{lineCode}/{date}")
    public Integer getOneDataSteelPartPlanCutByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
        return cutPlanReportService.getOneDataSteelPartPlanCutByLineCode(lineCode,date);
    }

    @ApiOperation("钢板零件切割七天的完成数量")
    @RequestMapping("/getPartCutSevenDataByLineCode/{lineCode}/{date}")
    public HomeSevenDataDTO getPartCutSevenDataByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
        return cutPlanReportService.getPartCutSevenDataByLineCode(lineCode,date);
    }

    @ApiOperation("钢板零件切割本周的完成数量")
    @RequestMapping("/getOneWeekSteelPartCutByLineCode/{lineCode}/{date}")
    public Integer getOneWeekSteelPartCutByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
        return cutPlanReportService.getOneWeekSteelPartCutByLineCode(lineCode,date);
    }

    @ApiOperation("钢板零件切割本月的完成数量")
    @RequestMapping("/getOneMonthSteelPartCutByLineCode/{lineCode}/{date}")
    public Integer getOneMonthSteelPartCutByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
        return cutPlanReportService.getOneMonthSteelPartCutByLineCode(lineCode,date);
    }

    @ApiOperation("钢板零件切割本年的完成数量")
    @RequestMapping("/getOneYearSteelPartCutByLineCode/{lineCode}/{date}")
    public Integer getOneYearSteelPartCutByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
        return cutPlanReportService.getOneYearSteelPartCutByLineCode(lineCode,date);
    }

    @ApiOperation("钢板零件切割本周的计划数量")
    @RequestMapping("/getOneWeekSteelPartPlanCutByLineCode/{lineCode}/{date}")
    public Integer getOneWeekSteelPartPlanCutByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
        return cutPlanReportService.getOneWeekSteelPartPlanCutByLineCode(lineCode,date);
    }


    @ApiOperation("钢板零件切割本月的计划数量")
    @RequestMapping("/getOneMonthSteelPartPlanCutByLineCode/{lineCode}/{date}")
    public Integer getOneMonthXcPlagetOneMonthSteelPartPlanCutByLineCodenCutByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
        return cutPlanReportService.getOneMonthSteelPartPlanCutByLineCode(lineCode,date);
    }

    @ApiOperation("钢板零件切割本年的计划数量")
    @RequestMapping("/getOneYearSteelPartPlanCutByLineCode/{lineCode}/{date}")
    public Integer getOneYearSteelPartPlanCutByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
        return cutPlanReportService.getOneYearSteelPartPlanCutByLineCode(lineCode,date);
    }

    @ApiOperation("钢板切割米数")
    @PostMapping("/cut-parts/count")
    public CuttingResultDTO getCuttingLengthByDateRange(@RequestBody CutPartsQueryDTO cutPartsQueryDTO) {
        return cutPlanReportService.getCuttingLengthByDateRange(cutPartsQueryDTO);
    }

    @ApiOperation("每日钢板切割米数")
    @PostMapping("/cut-parts/dayCount")
    public List<CuttingResultDTO> getCuttingLengthByDate(@RequestBody CutPartsQueryDTO cutPartsQueryDTO) {
        return cutPlanReportService.getCuttingLengthByDate(cutPartsQueryDTO);
    }

}
