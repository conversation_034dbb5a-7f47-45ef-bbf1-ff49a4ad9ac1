package com.hvisions.pms.repository;

import com.hvisions.pms.entity.HvPmProcedureRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: ProcedureRecordRepository</p >
 * <p>Description: 工序操作记录仓储层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/15</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface ProcedureRecordRepository extends JpaRepository<HvPmProcedureRecord, Integer> {


    /**
     * 根据工序ID 查询 工序操作记录
     *
     * @param operationId 工序ID
     * @return 工序操作记录列表
     */
    List<HvPmProcedureRecord> getAllByOperationId(int operationId);
}
