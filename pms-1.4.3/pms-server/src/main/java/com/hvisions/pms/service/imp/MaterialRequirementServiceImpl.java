package com.hvisions.pms.service.imp;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.pms.dao.HvPmMaterialBomItemMapper;
import com.hvisions.pms.dao.MaterialRequirementMapper;
import com.hvisions.pms.dto.materialRequirement.MaterialRequirementDTO;
import com.hvisions.pms.dto.materialRequirement.MaterialRequirementQueryDTO;
import com.hvisions.pms.entity.HvPmMaterialRequirement;
import com.hvisions.pms.service.MaterialRequirementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MaterialRequirementServiceImpl extends ServiceImpl<MaterialRequirementMapper, HvPmMaterialRequirement> implements MaterialRequirementService {

    @Autowired
    private MaterialRequirementMapper materialRequirementMapper;
    @Autowired
    private HvPmMaterialBomItemMapper materialBomItemMapper;

    @Override
    public Page<MaterialRequirementDTO> getPage(MaterialRequirementQueryDTO queryDTO) {
        Page<MaterialRequirementDTO> page = PageHelperUtil.getPage(materialRequirementMapper::getPage, queryDTO);
        Page<MaterialRequirementDTO> modifiedPage = page.map(materialRequirementDTO -> {
            materialRequirementDTO.setBomItems(materialBomItemMapper.getBomItemsByRequirementCode(materialRequirementDTO.getRequirementCode()));
            return materialRequirementDTO;
        });
        return modifiedPage;
    }

    @Override
    public List<HvPmMaterialRequirement> findListByCondition(MaterialRequirementQueryDTO materialRequirementQueryDTO) {
        return materialRequirementMapper.findListByCondition(materialRequirementQueryDTO);
    }
}
