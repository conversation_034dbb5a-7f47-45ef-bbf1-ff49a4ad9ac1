package com.hvisions.pms.service;

import com.hvisions.pms.entity.plan.HvPmMaterialCutPlanDetail0;
import com.hvisions.pms.plan.HvPmMaterialCutPlanDetail0DTO;

import java.util.List;

public interface HvPmMaterialCutPlanDetail0Service {

    /**
     * 根据切割计划id获取细节列表
     * @param id
     * @return
     */
    List<HvPmMaterialCutPlanDetail0> getAllDetail0ByCutPlanId(long id);

    /**
     * 添加
     * @param hvPmMaterialCutPlanDetail0DTO
     * @return
     */
    long createDetail0(HvPmMaterialCutPlanDetail0DTO hvPmMaterialCutPlanDetail0DTO);

    /**
     * 修改
     * @param hvPmMaterialCutPlanDetail0DTO
     * @return
     */
    long updateDetail0(HvPmMaterialCutPlanDetail0DTO hvPmMaterialCutPlanDetail0DTO);

    /**
     * 删除
     * @param id
     */
    void deleteDetail0ById(long id);

    List<HvPmMaterialCutPlanDetail0DTO> getAllDetail0DTOByCutPlanId(long id);

    List<HvPmMaterialCutPlanDetail0DTO> getAll();

    List<HvPmMaterialCutPlanDetail0> getDetail0ListByCutPlanIds(List<Long> cutPlanIds);

    /**
     * 根据物料编码查询切割零件表
     *
     * @param materialCode 物料编码
     * @return 切割零件表
     */
    List<HvPmMaterialCutPlanDetail0> getDetail0ListByMaterialCode(String materialCode);

    /**
     * 批量添加切割零件明细数据
     *
     * @param detail0Entities 零件明细数据
     * @return
     */
    List<HvPmMaterialCutPlanDetail0> createDetail0Batch(List<HvPmMaterialCutPlanDetail0> detail0Entities);
}
