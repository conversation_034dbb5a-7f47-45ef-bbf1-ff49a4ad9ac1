package com.hvisions.pms.repository.plan;

import com.hvisions.pms.entity.plan.HvPmXcMaterialCutPlanDetail1;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/8/26
 */
@Repository
public interface HvPmXcMaterialCutPlanDetail1Repository extends JpaRepository<HvPmXcMaterialCutPlanDetail1,Long> {

    List<HvPmXcMaterialCutPlanDetail1> getAllBySubPlanId(Long subPlanId);

    void deleteBySubPlanId(Long id);

    //根据切割计划id列表获取 零件信息
    @Query("SELECT d FROM HvPmXcMaterialCutPlanDetail1 d WHERE d.orderId IN :orderIds")
    List<HvPmXcMaterialCutPlanDetail1> findByOrderIds(@Param("orderIds") List<Long> orderIds);

}
