package com.hvisions.pms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.pms.dto.deliveryMission.DeliveryMissionDTO;
import com.hvisions.pms.dto.deliveryMission.DeliveryMissionQueryDTO;
import com.hvisions.pms.entity.deliveryMission.DeliveryMission;
import org.springframework.data.domain.Page;

public interface DeliveryMissionService extends IService<DeliveryMission> {
    Page<DeliveryMissionDTO> getPage(DeliveryMissionQueryDTO queryDTO);
}

