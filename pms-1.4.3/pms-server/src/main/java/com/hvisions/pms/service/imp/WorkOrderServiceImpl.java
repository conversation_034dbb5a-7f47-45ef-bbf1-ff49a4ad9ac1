package com.hvisions.pms.service.imp;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hvisions.common.config.coderule.utils.SerialCodeUtils;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.ExtendColumnInfo;
import com.hvisions.common.dto.ExtendInfo;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.service.BaseExtendService;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.common.utils.SerialUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.framework.client.DictionaryItemClient;
import com.hvisions.framework.dto.dictionary.DictionaryItemDTO;
import com.hvisions.hiperbase.SysBaseDTO;
import com.hvisions.hiperbase.bom.dto.*;
import com.hvisions.hiperbase.client.*;
import com.hvisions.hiperbase.equipment.location.LocationDTO;
import com.hvisions.hiperbase.materials.dto.MaterialDTO;
import com.hvisions.hiperbase.route.dto.*;
import com.hvisions.hiperbase.schedule.dto.CrewWithMemberDTO;
import com.hvisions.hiperbase.schedule.dto.ShiftDTO;
import com.hvisions.pms.OrderMqConst;
import com.hvisions.pms.consts.WorkOrderConst;
import com.hvisions.pms.dao.HvPmPlanProductBomMapper;
import com.hvisions.pms.dao.OrderMapper;
import com.hvisions.pms.dao.StatisticsMapper;
import com.hvisions.pms.dto.*;
import com.hvisions.pms.entity.*;
import com.hvisions.pms.entity.plan.HvPmCallFrameMaterial;
import com.hvisions.pms.entity.plan.HvPmMaterialCutPlan;
import com.hvisions.pms.entity.plan.HvPmMaterialCutPlanDetail0;
import com.hvisions.pms.enums.*;
import com.hvisions.pms.event.OrderEvent;
import com.hvisions.pms.exportdto.BcWorkOrderExportDTO;
import com.hvisions.pms.exportdto.PlanProductBomExportDTO;
import com.hvisions.pms.exportdto.WorkOrderExportDTO;
import com.hvisions.pms.exportdto.XcWorkOrderExportDTO;
import com.hvisions.pms.importTemplate.WorkOrderBomInfoTemplate;
import com.hvisions.pms.importTemplate.WorkOrderRouteInfoTemplate;
import com.hvisions.pms.importTemplate.WorkOrderTemplate;
import com.hvisions.pms.materialdto.OperationMaterialsDTO;
import com.hvisions.pms.query.OrderQuery;
import com.hvisions.pms.repository.*;
import com.hvisions.pms.rh.dto.MaterialDemandDTO;
import com.hvisions.pms.rh.dto.WorkMaterialDTO;
import com.hvisions.pms.service.*;
import com.hvisions.pms.task.dto.OrderTaskDTO;
import com.hvisions.pms.type.OrderTypeMaterialDTO;
import com.hvisions.pms.type.WorkOrderMaterialQuery;
import com.hvisions.pms.utils.SerialCodeUtilsV2;
import com.hvisions.pms.viewdto.WorkOrderNumDTO;
import com.hvisions.thirdparty.common.dto.*;
import com.hvisions.thridparty.client.MaterialCuttingLineClient;
import com.hvisions.thridparty.client.MesClient;
import com.hvisions.thridparty.client.WeldLineClient;
import com.hvisions.wms.client.StockClient;
import com.hvisions.wms.client.WaresLocationClient;
import com.hvisions.wms.dto.location.WaresLocationDTO;
import com.hvisions.wms.dto.stock.StockMaterialDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.hibernate.exception.ConstraintViolationException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <p>Title: DemoEntityServiceImp</p>
 * <p>Description: 工单创建服务实现层</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/01/17</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
@Slf4j
@Component
public class WorkOrderServiceImpl implements WorkOrderService {
    /**
     * 工单已下发状态为1 未下发状态为0
     */
    private static final int ZERO = 0;

    private final ApplicationEventPublisher applicationEventPublisher;
    private final WorkOrderRepository workOrderRepository;
    private final MaterialsClient materialClient;
    private final RouteClient routeClient;
    private final ShiftClient shiftClient;
    private final CrewClient crewClient;
    private final OrderOperationRepository orderOperationRepository;
    private final LocationExtendClient locationExtendClient;
    private final OperationMaterialRepository materialRepository;
    private final OperationParameterRepository operationParameterRepository;
    private final OperationFileRepository operationFileRepository;
    private final OperationOutPutMaterialRepository operationOutPutMaterialRepository;
    private final OrderOperationService orderOperationService;
    private final OperationMaterialService operationMaterialService;
    private final OperationOutPutMaterialService operationOutPutMaterialService;
    private final OperationParameterService parameterService;
    private final SerialUtil serialUtil;
    private final WorkOrderParameterService workOrderParameterService;
    private final OrderTaskRepository orderTaskRepository;
    private final OrderTaskService orderTaskService;
    private final StatisticsMapper statisticsMapper;
    private final RouteTemplateClient routeTemplateClient;
    private final HvPmWorkPlanDetailService workPlanDetailService;
    private final OrderTypeRepository orderTypeRepository;
    private final SerialCodeUtils serialCodeUtils;
    private final ExecutorService executor = Executors.newCachedThreadPool();
    @Resource
    private WorkOrderService workOrderService;
    @Resource(name = "hv_pm_work_order_extend")
    private BaseExtendService workOrderExtend;
    @Resource
    private ProductRouteClient productRouteClient;
    @Resource
    private BomClient bomClient;
    @Resource
    private StockClient stockClient;
    @Resource
    private WaresLocationClient waresLocationClient;
    @Resource
    private HvPmCallFrameMaterialService hvPmCallFrameMaterialService;
    @Autowired
    private OrderMapper orderMapper;
    @Resource
    private WeldLineClient weldLineClient;
    @Autowired
    private HvPmPlanProductBomService hvPmPlanProductBomService;
    @Autowired
    private HvPmPlanRouteInfoService hvPmPlanRouteInfoService;
    @Resource
    private MaterialClient materialsClient;
    @Autowired
    private MaterialCuttingLineClient materialCuttingLineClient;
    @Resource
    private DictionaryItemClient dictionaryItemClient;
    @Resource
    private HvPmLineReportMaterialService lineReportMaterialService;
    @Resource
    private NestMaterialFileClient nestMaterialFileClient;
    @Autowired
    private MesClient mesClient;
    @Autowired
    private SerialCodeUtilsV2 serialCodeUtilsV2;
    @Autowired
    private HvPmProductBomDetailRepository bomDetailRepository;
    @Autowired
    private HvPmMaterialCutPlanDetail0Service cutPlanDetail0Service;
    @Autowired
    private HvPmMaterialCutPlanService cutPlanService;
    @Autowired
    private HvPmPlanProductBomServiceImpl productBomService;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private HvPmPlanProductBomMapper productBomMapper;

    /**
     * 根据mes下发的工单创建零件或组立工单
     *
     * @param orderDTO mes下发的工单信息
     */

    @Value("${my.variable}")
    private Integer variable;
    @Value("${my.XCFlag}")
    private Boolean flag;


    @Autowired
    @Lazy
    public WorkOrderServiceImpl(SerialCodeUtils serialCodeUtils, ApplicationEventPublisher applicationEventPublisher, WorkOrderRepository workOrderRepository, MaterialsClient materialClient, RouteClient routeClient, ShiftClient shiftClient, CrewClient crewClient, OrderOperationRepository orderOperationRepository, OperationMaterialRepository materialRepository, OperationParameterRepository operationParameterRepository, OperationFileRepository operationFileRepository, OperationOutPutMaterialRepository operationOutPutMaterialRepository, LocationExtendClient locationExtendClient, OrderOperationService orderOperationService, OperationMaterialService operationMaterialService, OperationOutPutMaterialService operationOutPutMaterialService, OperationParameterService parameterService, SerialUtil serialUtil, WorkOrderParameterService workOrderParameterService, OrderTaskRepository orderTaskRepository, OrderTaskService orderTaskService, StatisticsMapper statisticsMapper, RouteTemplateClient routeTemplateClient, HvPmWorkPlanDetailService workPlanDetailService, OrderTypeRepository orderTypeRepository) {
        this.applicationEventPublisher = applicationEventPublisher;
        this.workOrderRepository = workOrderRepository;
        this.materialClient = materialClient;
        this.routeClient = routeClient;
        this.shiftClient = shiftClient;
        this.crewClient = crewClient;
        this.orderOperationRepository = orderOperationRepository;
        this.materialRepository = materialRepository;
        this.operationParameterRepository = operationParameterRepository;
        this.operationFileRepository = operationFileRepository;
        this.operationOutPutMaterialRepository = operationOutPutMaterialRepository;
        this.locationExtendClient = locationExtendClient;
        this.orderOperationService = orderOperationService;
        this.operationMaterialService = operationMaterialService;
        this.operationOutPutMaterialService = operationOutPutMaterialService;
        this.parameterService = parameterService;
        this.serialUtil = serialUtil;
        this.workOrderParameterService = workOrderParameterService;
        this.orderTaskRepository = orderTaskRepository;
        this.orderTaskService = orderTaskService;
        this.statisticsMapper = statisticsMapper;
        this.routeTemplateClient = routeTemplateClient;
        this.workPlanDetailService = workPlanDetailService;
        this.orderTypeRepository = orderTypeRepository;
        this.serialCodeUtils = serialCodeUtils;
    }

    public static <T> List<List<T>> splitList(List<T> list, int size) {
        List<List<T>> lists = new ArrayList<>();
        int listSize = list.size();
        int startIndex = 0;

        for (int i = 0; i < listSize; i += size) {
            int endIndex = Math.min(i + size, listSize);
            lists.add(list.subList(startIndex, endIndex));
            startIndex = endIndex;
        }

        return lists;
    }

    static Date getDate() throws ParseException {
        Calendar cal = Calendar.getInstance();
        cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH), cal.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
        Date beginOfDate = cal.getTime();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
        String dataStr = formatter.format(beginOfDate);
        return formatter.parse(dataStr);
    }

    /**
     * @return
     */
    private static Date getEDate() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_YEAR, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public WorkOrderDTO createOrderManage(WorkOrderDTO workOrderDTO) {
        if (workOrderDTO.getPlanOrNew() == null) {
            workOrderDTO.setPlanOrNew(0);
        }
        if (workOrderDTO.getUsedType() == null) {
            throw new BaseKnownException("未区分零件/组立工单");
        }
        // 请输入工单编码
        if (workOrderDTO.getWorkOrderCode() == null) {
            throw new BaseKnownException(WorkOrderExceptionEnum.WORK_ORDER_CODE_NOT_NULL);
        }
        if (workOrderDTO.getMaterialId() > ZERO) {
            ResultVO<MaterialDTO> materialDTO = materialClient.getMaterialById(workOrderDTO.getMaterialId());
            getMaterialDate(workOrderDTO, materialDTO);
        } else {
            throw new BaseKnownException(WorkOrderExceptionEnum.MATERIAL_NOT_NULL);
        }
        if (workOrderDTO.getSerialNumber() != null) {
            HvPmWorkOrder byPlanCodeAndSerialNumber = workOrderRepository.getByPlanCodeAndSerialNumber(workOrderDTO.getPlanCode(), workOrderDTO.getSerialNumber());
            if (byPlanCodeAndSerialNumber != null) {
                return DtoMapper.convert(byPlanCodeAndSerialNumber, WorkOrderDTO.class);
            }
        }
        if (workOrderDTO.getOrderTypeId() == null) {
            HvPmOrderType aDefault = orderTypeRepository.getAllByOrderTypeCode("default");
            workOrderDTO.setOrderTypeId(aDefault.getId());
        }

        ResultVO<RouteDTO> route = routeClient.getEffectRouteByProductIdAndRouteId(workOrderDTO.getMaterialId(), workOrderDTO.getRouteId());
        // 获取工艺路线信息
        getRouteDate(workOrderDTO, route);
        // 新增工单的状态为0
        workOrderDTO.setWorkOrderState(ZERO);
        // 新增工单完成数量为0
        workOrderDTO.setActualCount(new BigDecimal(0));
        // 获取班次班组信息
        if (workOrderDTO.getCrewId() != null) {
            ResultVO<CrewWithMemberDTO> crewById = crewClient.getCrewById(workOrderDTO.getCrewId());
            if (crewById.isSuccess()) {
                workOrderDTO.setCrewName(crewById.getData().getCrewName());
            } else {
                throw new BaseKnownException(crewById);
            }
        }
        if (workOrderDTO.getShiftId() != null) {
            ResultVO<ShiftDTO> shiftById = shiftClient.getShiftById(workOrderDTO.getShiftId());
            if (shiftById.isSuccess()) {
                workOrderDTO.setShiftName(shiftById.getData().getShiftName());
            } else {
                throw new BaseKnownException(shiftById);
            }
        }
        workOrderDTO.setCompleteSetCheckStatus(0);
        HvPmWorkOrder hvPmWorkOrder;
        try {
            hvPmWorkOrder = workOrderRepository.save(DtoMapper.convert(workOrderDTO, HvPmWorkOrder.class));
        } catch (DataIntegrityViolationException e) {
            if (e.getCause() instanceof ConstraintViolationException) { // 进一步检查是否是因为违反了约束（如唯一性约束）
                throw new BaseKnownException("工单:" + workOrderDTO.getWorkOrderCode() + "已存在！");
            } else {
                throw new BaseKnownException("工单:" + workOrderDTO.getWorkOrderCode() + "入库失败！");
            }
        }

        if (workOrderDTO.getExtend() != null && !workOrderDTO.getExtend().isEmpty()) {
            ExtendInfo extendInfo = new ExtendInfo();
            extendInfo.setEntityId(hvPmWorkOrder.getId());
            extendInfo.setValues(workOrderDTO.getExtend());
            workOrderExtend.addExtendInfo(extendInfo);
        }
        applicationEventPublisher.publishEvent(new OrderEvent(this, OrderMqConst.CRATE, hvPmWorkOrder));
        return DtoMapper.convert(hvPmWorkOrder, WorkOrderDTO.class);

    }

    /**
     * 创建子工单群
     *
     * @param orderProjectDTO 创建工单对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createSubOrderManage(OrderProjectDTO orderProjectDTO) {
        BomAllDTO bom = isBom(orderProjectDTO.getMaterialId());

        executor.submit(new Runnable() {
            @Override
            public void run() {
                try {
                    for (BomItemsAllDTO bomItemDTO : bom.getBomItemDTOS()) {
                        WorkOrderDTO workOrderDTO = DtoMapper.convert(orderProjectDTO, WorkOrderDTO.class);
                        workOrderDTO.setPlanCode(orderProjectDTO.getProjectCode());
                        workOrderDTO.setMaterialId(bomItemDTO.getMaterialsId());
                        workOrderDTO.setWorkOrderCode(orderProjectDTO.getProjectCode() + bomItemDTO.getBomItemCode());
                        workOrderDTO.setQuantity(bomItemDTO.getBomItemCount().multiply(orderProjectDTO.getQuantity()));
                        createOrderManage(workOrderDTO);
                    }
                } catch (Exception ex) {
                    throw new RuntimeException(ex.toString());
                }
            }
        });


    }

    private BomAllDTO isBom(int materialId) {
        ResultVO<Integer> bomID = materialClient.getBomIdByMaterialId(materialId);
        if (bomID.isSuccess()) {
            ResultVO<BomAllDTO> bomAllDTOResultVO = materialClient.getAllBomInfoById(bomID.getData());
            if (bomAllDTOResultVO.isSuccess()) {
                return bomAllDTOResultVO.getData();
            } else {
                throw new BaseKnownException(WorkOrderExceptionEnum.MATERIAL_IS_NOT_BOM);
            }
        } else {
            throw new BaseKnownException(bomID);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateOrderManage(WorkOrderDTO workOrderDTO) {
        if (workOrderDTO.getId() == null) {
            throw new BaseKnownException(WorkOrderExceptionEnum.NEED_ID);
        }
        boolean existId = workOrderRepository.existsById(workOrderDTO.getId());
        if (!existId) {
            throw new BaseKnownException(WorkOrderExceptionEnum.ID_NOT_EXIST);
        }
        HvPmWorkOrder orderManageState = workOrderRepository.getOne(workOrderDTO.getId());
        // 判断状态是否为未下发  如果不是未下发禁止修改。
        if (!orderManageState.getWorkOrderState().equals(WorkOrderStateEnum.NOT_ISSUED.getCode())) {
            throw new BaseKnownException(WorkOrderExceptionEnum.OPERATION_STATE_NOT_MATCH);
        }
        if (workOrderDTO.getQuantity() != null) {
            if ((workOrderDTO.getQuantity()).compareTo((orderManageState.getQuantity())) != 0) {
                if (orderManageState.getPlanOrNew() != null && orderManageState.getPlanOrNew() == 1) {
                    throw new BaseKnownException(WorkOrderExceptionEnum.PLAN_ORDER_NOT_OPERATION);
                }
            }
        }
        if (workOrderDTO.getPlanCode() != null) {
            if (!workOrderDTO.getPlanCode().equals(orderManageState.getPlanCode())) {
                if (orderManageState.getPlanOrNew() != null && orderManageState.getPlanOrNew() == 1) {
                    throw new BaseKnownException(WorkOrderExceptionEnum.PLAN_ORDER_NOT_OPERATION);
                }
            }
        }
        if (workOrderDTO.getWorkOrderCode() != null) {
            if (!workOrderDTO.getWorkOrderCode().equals(orderManageState.getWorkOrderCode())) {
                if (orderManageState.getPlanOrNew() != null && orderManageState.getPlanOrNew() == 1) {
                    throw new BaseKnownException(WorkOrderExceptionEnum.PLAN_ORDER_NOT_OPERATION);
                }
            }
        }
        if (workOrderDTO.getMaterialId() != null) {
            if (!workOrderDTO.getMaterialId().equals(orderManageState.getMaterialId())) {
                if (orderManageState.getPlanOrNew() != null && orderManageState.getPlanOrNew() == 1) {
                    throw new BaseKnownException(WorkOrderExceptionEnum.PLAN_ORDER_NOT_OPERATION);
                }
            }
        }
        if (workOrderDTO.getRouteId() != null) {
            if (!(workOrderDTO.getRouteId()).equals(orderManageState.getRouteId())) {
                if (orderManageState.getPlanOrNew() != null && orderManageState.getPlanOrNew() == 1) {
                    throw new BaseKnownException(WorkOrderExceptionEnum.PLAN_ORDER_NOT_OPERATION);
                }
            }
        }

        ResultVO<MaterialDTO> materialDTO = materialClient.getMaterialById(workOrderDTO.getMaterialId());
        getMaterialDate(workOrderDTO, materialDTO);
        ResultVO<RouteDTO> route = routeClient.getEffectRouteByProductIdAndRouteId(workOrderDTO.getMaterialId(), workOrderDTO.getRouteId());
        getRouteDate(workOrderDTO, route);
        if (workOrderDTO.getShiftId() != null) {
            ResultVO<ShiftDTO> shifById = shiftClient.getShiftById(workOrderDTO.getShiftId());
            if (shifById.isSuccess()) {
                workOrderDTO.setShiftName(shifById.getData().getShiftName());
            } else {
                throw new BaseKnownException(shifById);
            }
        }
        // 插入班次班组的名称
        if (workOrderDTO.getCrewId() != null) {
            ResultVO<CrewWithMemberDTO> crewById = crewClient.getCrewById(workOrderDTO.getCrewId());
            if (crewById.isSuccess()) {
                workOrderDTO.setCrewName(crewById.getData().getCrewName());
            } else {
                throw new BaseKnownException(crewById);
            }
        }
        // 修改扩展属性
        if (workOrderDTO.getExtend() != null) {
            ExtendInfo extendInfo = new ExtendInfo();
            extendInfo.setEntityId(workOrderDTO.getId());
            extendInfo.setValues(workOrderDTO.getExtend());
            workOrderExtend.updateExtendInfo(extendInfo);
        }
        return workOrderRepository.save(DtoMapper.convert(workOrderDTO, HvPmWorkOrder.class)).getId();
    }

    /**
     * 获取工艺 信息
     *
     * @param workOrderDTO          工单DTO
     * @param routeWithRouteStepDTO 工艺DTO
     */
    private void getRouteDate(WorkOrderDTO workOrderDTO, ResultVO<RouteDTO> routeWithRouteStepDTO) {
        if (routeWithRouteStepDTO.isSuccess()) {
            workOrderDTO.setRouteCode(routeWithRouteStepDTO.getData().getRouteCode());
            workOrderDTO.setRouteName(routeWithRouteStepDTO.getData().getRouteName());
            workOrderDTO.setRouteVersion(routeWithRouteStepDTO.getData().getRouteVersion());
        } else {
            throw new BaseKnownException(routeWithRouteStepDTO);
        }
    }

    /**
     * 获取物料 信息
     *
     * @param workOrderDTO 工单DTO
     * @param materialDTO  物料DTO
     */
    private void getMaterialDate(WorkOrderDTO workOrderDTO, ResultVO<MaterialDTO> materialDTO) {
        if (materialDTO.isSuccess()) {
            workOrderDTO.setMaterialCode(materialDTO.getData().getMaterialCode());
            workOrderDTO.setMaterialName(materialDTO.getData().getMaterialName());
            workOrderDTO.setEigenvalue(materialDTO.getData().getEigenvalue());
            ResultVO<Integer> bomID = materialClient.getBomIdByMaterialId(workOrderDTO.getMaterialId());
            if (bomID.isSuccess()) {
                workOrderDTO.setBomId(bomID.getData());
                ResultVO<BomAllDTO> bomAllDTOResultVO = materialClient.getAllBomInfoById(bomID.getData());
                if (bomAllDTOResultVO.isSuccess()) {
                    workOrderDTO.setBomVersion(bomAllDTOResultVO.getData().getBomVersions());
                }
            }
        } else {
            throw new BaseKnownException(materialDTO);
        }
    }

    /**
     * 计划工单复制
     *
     * @param id            计划工单ID
     * @param workOrderCode 工单计划编号
     * @return 新工单ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int copyWorkOrder(Integer id, String workOrderCode) {
        HvPmWorkOrder hvPmWorkOrder = workOrderRepository.getOne(id);
        HvPmWorkOrder workOrderNew = new HvPmWorkOrder();
        BeanUtils.copyProperties(hvPmWorkOrder, workOrderNew);
        workOrderNew.setId(0);
        workOrderNew.setWorkOrderState(WorkOrderStateEnum.NOT_ISSUED.getCode());
        workOrderNew.setWorkOrderCode(workOrderCode);
        String serialNumber = serialUtil.getSerialNumber("h-visions-order-manage");
        workOrderNew.setPlanCode(serialNumber);
        Random r = new Random(10);
        workOrderNew.setSerialNumber(r.nextInt());
        workOrderNew.setPlanOrNew(0);
        workOrderNew.setActualEndTime(null);
        workOrderNew.setActualCount(new BigDecimal(0));
        // 复制扩展属性
        HvPmWorkOrder save = workOrderRepository.save(workOrderNew);
        Map<String, Object> extend = workOrderExtend.getExtend(id);
        ExtendInfo extendInfo = new ExtendInfo();
        extendInfo.setEntityId(save.getId());
        extendInfo.setValues(extend);
        workOrderExtend.addExtendInfo(extendInfo);
        applicationEventPublisher.publishEvent(new OrderEvent(this, OrderMqConst.CRATE, save));
        return save.getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public BomAllDTO getBomAllDtoByOrderManageId(Integer workOrderId) {
        HvPmWorkOrder one = workOrderRepository.getOne(workOrderId);

        int materialId = one.getMaterialId();
        ResultVO<Integer> bomId = materialClient.getBomIdByMaterialId(materialId);
        BomAllDTO bomAllDTO = new BomAllDTO();
        if (one.getBomId() != null) {
            ResultVO<BomAllDTO> bomAllDTOResultVO = materialClient.getAllBomInfoById(bomId.getData());
            if (!bomAllDTOResultVO.isSuccess()) {
                throw new BaseKnownException(bomAllDTOResultVO);
            }
            bomAllDTO = bomAllDTOResultVO.getData();
        }
        return bomAllDTO;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<WorkOrderDTO> getHvPmWorkOrderListByIdIn(List<Integer> idIn) {
        List<WorkOrderDTO> hvPmOrderManageByIdIn = DtoMapper.convertList(workOrderRepository.getHvPmOrderManageByIdIn(idIn), WorkOrderDTO.class);
        for (WorkOrderDTO hvPmWorkOrder : hvPmOrderManageByIdIn) {
            if (hvPmWorkOrder.getBomId() != null) {
                ResultVO<BomAllDTO> allBomInfoById = materialClient.getAllBomInfoById(hvPmWorkOrder.getBomId());
                if (allBomInfoById.isSuccess()) {
                    hvPmWorkOrder.setBomVersion(allBomInfoById.getData().getBomVersions());
                    hvPmWorkOrder.setBomCode(allBomInfoById.getData().getBomCode());
                    hvPmWorkOrder.setBomName(allBomInfoById.getData().getBomName());
                }
            }
        }
        return hvPmOrderManageByIdIn;
    }

    /**
     * 分页查询
     *
     * @param workOrderQueryDTO 查询条件DTO
     * @return 分页信息
     */
    @Override
    public Page<WorkOrderDTO> findAllByPlanStartTimeAndMaterialCodeAndWorkOrderCode(WorkOrderQueryDTO workOrderQueryDTO) {
        if (workOrderQueryDTO.getPlanEndTime() != null && workOrderQueryDTO.getPlanStartTime() != null) {
            // 把排序字段驼峰的改为下划线
            String order = StrUtil.toUnderlineCase(workOrderQueryDTO.getSortCol());
            workOrderQueryDTO.setSortCol(order);
            Page<HvPmWorkOrder> dto = workOrderRepository.findAllByMaterialCodeAndWorkOrderCodeAndDate(workOrderQueryDTO.getMaterialCode(), workOrderQueryDTO.getWorkOrderCode(), workOrderQueryDTO.getPlanStartTime(), workOrderQueryDTO.getPlanEndTime(), workOrderQueryDTO.getWorkOrderState(), workOrderQueryDTO.getPlanOrNew(), workOrderQueryDTO.getRequest());
            List<ExtendInfo> extendInfos = workOrderExtend.getExtend(dto.stream().map(HvPmWorkOrder::getId).collect(Collectors.toList()));
            Page<WorkOrderDTO> workOrderDTOS = DtoMapper.convertPage(dto, WorkOrderDTO.class);
            joinOrderManageDTOithExtend(workOrderDTOS, extendInfos);
            return workOrderDTOS;
        } else {
            Page<HvPmWorkOrder> hvPmWorkOrders;
            HvPmWorkOrder hvPmWorkOrder = DtoMapper.convert(workOrderQueryDTO, HvPmWorkOrder.class);
            ExampleMatcher exampleMatcher = ExampleMatcher.matching().withMatcher("materialCode", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase()).withMatcher("workOrderCode", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase()).withMatcher("planCode", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase()).withMatcher("routeName", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase()).withMatcher("planOrNew", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase()).withMatcher("workOrderState", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase());
            Example<HvPmWorkOrder> example = Example.of(hvPmWorkOrder, exampleMatcher);
            hvPmWorkOrders = workOrderRepository.findAll(example, workOrderQueryDTO.getRequest());
            Page<WorkOrderDTO> dto = DtoMapper.convertPage(hvPmWorkOrders, WorkOrderDTO.class);
            List<ExtendInfo> extendInfos = workOrderExtend.getExtend(hvPmWorkOrders.stream().map(HvPmWorkOrder::getId).collect(Collectors.toList()));
            joinOrderManageDTOithExtend(dto, extendInfos);
            return dto;
        }
    }

    /**
     * 根据产线ID查询工单
     *
     * @param id 产线ID
     * @return 工单列表
     */
    @Override
    public List<WorkOrderDTO> getWorkOrderByCellId(Integer id) {

        List<HvPmWorkOrder> allByCellId = workOrderRepository.getAllByCellId(id);
        return DtoMapper.convertList(allByCellId, WorkOrderDTO.class);
    }

    /**
     * 根据工单状态查询工单
     *
     * @param state 工单状态
     * @return 工单列表
     */
    @Override
    public List<WorkOrderDTO> getAllByWorkOrderState(Integer state) {
        List<HvPmWorkOrder> allByWorkOrderState = workOrderRepository.getAllByWorkOrderState(state);
        return DtoMapper.convertList(allByWorkOrderState, WorkOrderDTO.class);
    }

    /**
     * dto拼接到
     *
     * @param orderManageDTOs dto列表
     * @param extendInfos     扩展信息列表
     */
    private void joinOrderManageDTOithExtend(Page<WorkOrderDTO> orderManageDTOs, Iterable<ExtendInfo> extendInfos) {
        for (WorkOrderDTO workOrderDTO : orderManageDTOs) {
            for (ExtendInfo extendInfo : extendInfos) {
                if (workOrderDTO.getId() == extendInfo.getEntityId()) {
                    workOrderDTO.setExtend(extendInfo.getValues());
                    break;
                }
            }
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteOrderManageById(Integer id) {
        HvPmWorkOrder hvPmWorkOrder = workOrderRepository.getOne(id);
        // 只有新建的工单才能删除
        if (hvPmWorkOrder.getPlanOrNew() != null && hvPmWorkOrder.getPlanOrNew() == 1) {
            throw new BaseKnownException(WorkOrderExceptionEnum.PLAN_ORDER_NOT_OPERATION);
        }
        if (hvPmWorkOrder.getWorkOrderState().equals(WorkOrderStateEnum.NOT_ISSUED.getCode()) || hvPmWorkOrder.getWorkOrderState().equals(WorkOrderStateEnum.REVOKE.getCode())) {
            workOrderRepository.deleteById(id);
            workOrderExtend.deleteExtendInfo(id);
            //删除工序id表
            hvPmPlanRouteInfoService.deleteByPlanCode(hvPmWorkOrder.getWorkOrderCode());
        } else {
            throw new BaseKnownException(WorkOrderExceptionEnum.ORDER_MANAGE_DELETE_NOT_ALLOWED);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteOrderMangeByIdList(List<Integer> id) {
        // 查询传入ID列表是否有状态不为新建的工单 如果有
        List<HvPmWorkOrder> hvPmOrderManageByIdIn = workOrderRepository.getHvPmOrderManageByIdIn(id);
        Optional<HvPmWorkOrder> orderManage = hvPmOrderManageByIdIn.stream().filter(t -> t.getWorkOrderState() != ZERO).findFirst();
        List<HvPmWorkOrder> collect = hvPmOrderManageByIdIn.stream().filter(t -> t.getPlanOrNew().equals(1)).collect(Collectors.toList());

        if (orderManage.isPresent()) {
            throw new BaseKnownException(WorkOrderExceptionEnum.ORDER_MANAGE_DELETE_NOT_ALLOWED);
        } else {
            if (collect.size() > 0) {
                throw new BaseKnownException(WorkOrderExceptionEnum.PLAN_ORDER_NOT_OPERATION);
            } else {
                List<HvPmWorkOrder> hvPmWorkOrderList = workOrderRepository.getHvPmOrderManageByIdIn(id);
                List<String> workOrderCodeList = hvPmWorkOrderList.stream().map(HvPmWorkOrder::getWorkOrderCode).collect(Collectors.toList());
                for (String workOrderCode : workOrderCodeList) {
                    LambdaQueryWrapper<HvPmPlanProductBom> lqw = new LambdaQueryWrapper<>();
                    lqw.eq(HvPmPlanProductBom::getPlanCode, workOrderCode);
                    hvPmPlanProductBomService.remove(lqw);
                    LambdaQueryWrapper<HvPmPlanRouteInfo> lwq2 = new LambdaQueryWrapper<>();
                    lwq2.eq(HvPmPlanRouteInfo::getPlanCode, workOrderCode);
                    hvPmPlanRouteInfoService.remove(lwq2);
                    //删除 hv_pm_order_operation
                    orderOperationRepository.deleteByWorkOrderCode(workOrderCode);
                    //删除 hv_pm_order_task
                    orderTaskRepository.deleteByWorkOrderCode(workOrderCode);
                    //删除 hv_pm_plan_product_bom_detail
                    bomDetailRepository.deleteByWorkOrderCode(workOrderCode);
                }
                workOrderRepository.deleteHvPmOrderManageByIdIn(id);
            }
        }

    }

    /**
     * {@inheritDoc}
     */
    @Transactional(rollbackFor = Exception.class)
    public void issueTaskOneByOne(HvPmWorkOrder workOrder, String nodeCode, Map<String, Object> map) {

        // 参数非空判断
        workOrderNotNull(workOrder);
        // 判断工单状态是否为新建或者是撤销（撤销的工单可以再次下发）
        if (workOrder.getWorkOrderState().equals(WorkOrderStateEnum.NOT_ISSUED.getCode()) || workOrder.getWorkOrderState().equals(WorkOrderStateEnum.REVOKE.getCode())) {
            // 根据CellId获取对应的工位列表
            ResultVO<List<LocationDTO>> workCenter = locationExtendClient.getLocationListByParentId(workOrder.getCellId());
            // 循环遍历插入工序数据
            if (!workCenter.isSuccess()) {
                throw new BaseKnownException(workCenter);
            }
            BigDecimal divide = new BigDecimal(0);
            if (workOrder.getBomId() != null) {
                ResultVO<BomAllDTO> allBomInfoById = materialClient.getAllBomInfoById(workOrder.getBomId());
                if (!allBomInfoById.isSuccess()) {
                    throw new BaseKnownException(allBomInfoById);
                }

                BigDecimal bomCount = allBomInfoById.getData().getBomCount();
                if (bomCount.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal quantity = workOrder.getQuantity();
                    if (quantity == null) {
                        quantity = new BigDecimal(0);
                    }
                    divide = quantity.divide(bomCount, 4, RoundingMode.HALF_UP);
                } else {
                    divide = new BigDecimal(0);
                }
            }

            // 根据工艺路线ID查询工艺路线以及工艺步骤
            ResultVO<RouteDTO> route = routeClient.getEffectRouteByProductIdAndRouteId(workOrder.getMaterialId(), workOrder.getRouteId());

            if (!route.isSuccess()) {
                throw new BaseKnownException("工艺信息获取失败:" + route.getMessage());
            }
            workOrder.setIssuedTime(new Date());
            route.getData().flat();
            // 创建步骤
            List<HvPmOrderOperation> orderOperations = createOperationByRouteStep(workOrder, route.getData(), workCenter, divide);
            // 判断排他还是并行。如果是排他返回俩个routeStepId，如果是并行，俩道工序变为就绪状态如果是排他返回数据
            Node firstNode = route.getData().findStartNode();
            NextStepDTO data = route.getData().findNextStep(firstNode);
            Integer type = data.getType();
            switch (type) {
                // 正常
                case 1:
                    List<String> nextSteps = data.getNextSteps();

                    for (String nextStep : nextSteps) {
                        Optional<HvPmOrderOperation> first = orderOperations.stream().filter(t -> t.getNodeCode().equals(nextStep)).findFirst();
                        first.ifPresent(operation -> createTask(operation, OperationStateEnum.READY.getCode()));
                    }
                    break;
                // 排他网关
                case 2:
                    if (nodeCode == null) {
                        throw new BaseKnownException(WorkOrderExceptionEnum.CHOOSE_OPERATION);
                    } else if (!nodeCode.equals("0")) {
                        Optional<HvPmOrderOperation> first = orderOperations.stream().filter(t -> t.getNodeCode().equals(nodeCode)).findFirst();
                        if (!first.isPresent()) {
                            throw new BaseKnownException(WorkOrderExceptionEnum.NOT_START_NODE);
                        }
                        first.ifPresent(operation -> createTask(operation, OperationStateEnum.READY.getCode()));
                    }
                    break;
                // 并行
                case 4:
                    List<String> nextSteps1 = data.getNextSteps();
                    for (String nextStep : nextSteps1) {
                        Optional<HvPmOrderOperation> first = orderOperations.stream().filter(t -> t.getNodeCode().equals(nextStep)).findFirst();
                        if (!first.isPresent()) {
                            throw new BaseKnownException(WorkOrderExceptionEnum.NOT_START_NODE);
                        }
                        first.ifPresent(operation -> createTask(operation, OperationStateEnum.READY.getCode()));
                    }
                    break;

            }
            workOrder.setWorkOrderState(WorkOrderStateEnum.ALREADY_ISSUED.getCode());
            workOrderRepository.save(workOrder);
        } else {
            throw new BaseKnownException(WorkOrderExceptionEnum.OPERATION_STATE_NOT_MATCH);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Transactional(rollbackFor = Exception.class)
    public void issueTaskAll(HvPmWorkOrder workOrder, String nodeCode, Map<String, Object> map) {

        // 参数非空判断
        workOrderNotNull(workOrder);
        // 判断工单状态是否为新建或者是撤销（撤销的工单可以再次下发）
        if (workOrder.getWorkOrderState().equals(WorkOrderStateEnum.NOT_ISSUED.getCode()) || workOrder.getWorkOrderState().equals(WorkOrderStateEnum.REVOKE.getCode())) {
            // 根据CellId获取对应的工位列表
            ResultVO<List<LocationDTO>> workCenter = locationExtendClient.getLocationListByParentId(workOrder.getCellId());
            // 循环遍历插入工序数据
            if (!workCenter.isSuccess()) {
                throw new BaseKnownException(workCenter);
            }
            BigDecimal divide = new BigDecimal(0);
            if (workOrder.getBomId() != null) {
                ResultVO<BomAllDTO> allBomInfoById = materialClient.getAllBomInfoById(workOrder.getBomId());
                if (!allBomInfoById.isSuccess()) {
                    throw new BaseKnownException(allBomInfoById);
                }

                BigDecimal bomCount = allBomInfoById.getData().getBomCount();
                if (bomCount.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal quantity = workOrder.getQuantity();
                    if (quantity == null) {
                        quantity = new BigDecimal(0);
                    }
                    divide = quantity.divide(bomCount, 4, RoundingMode.HALF_UP);
                } else {
                    divide = new BigDecimal(0);
                }
            }

            // 根据工艺路线ID查询工艺路线以及工艺步骤
            ResultVO<RouteDTO> routeById = routeClient.getEffectRouteByProductIdAndRouteId(workOrder.getMaterialId(), workOrder.getRouteId());

            if (!routeById.isSuccess()) {
                throw new BaseKnownException("工艺信息获取失败" + routeById.getMessage());
            } else {
                workOrder.setIssuedTime(new Date());
                routeById.getData().flat();
                List<HvPmOrderOperation> orderOperations = createOperationAndTaskByRouteStep(workOrder, routeById.getData(), workCenter, divide);
                Node firstNode = routeById.getData().findStartNode();
                NextStepDTO data = routeById.getData().findNextStep(firstNode);
                Integer type = data.getType();
                List<OrderTaskDTO> orderTaskDTOS = orderTaskService.getAllByOrderId(workOrder.getId());
                switch (type) {
                    // 正常
                    case 1:
                        List<String> nextSteps = data.getNextSteps();
                        for (String nextStep : nextSteps) {
                            Optional<HvPmOrderOperation> first = orderOperations.stream().filter(t -> t.getNodeCode().equals(nextStep)).findFirst();
                            List<OrderTaskDTO> collect = orderTaskDTOS.stream().filter(t -> t.getOperationId().equals(first.get().getId())).collect(Collectors.toList());

                            collect.forEach(t -> t.setState(OperationStateEnum.READY.getCode()));
                            orderTaskRepository.saveAll(DtoMapper.convertList(collect, HvPmOrderTask.class));
                        }
                        break;
                    // 排他网关
                    case 2:
                        if (nodeCode == null) {
                            throw new BaseKnownException(WorkOrderExceptionEnum.CHOOSE_OPERATION);
                        } else if (!nodeCode.equals("0")) {

                            Optional<HvPmOrderOperation> first = orderOperations.stream().filter(t -> t.getNodeCode().equals(nodeCode)).findFirst();
                            if (!first.isPresent()) {
                                throw new BaseKnownException(WorkOrderExceptionEnum.NOT_START_NODE);
                            }

                            List<OrderTaskDTO> collect = orderTaskDTOS.stream().filter(t -> t.getOperationId().equals(first.get().getId())).collect(Collectors.toList());

                            collect.forEach(t -> t.setState(OperationStateEnum.READY.getCode()));
                            orderTaskRepository.saveAll(DtoMapper.convertList(collect, HvPmOrderTask.class));
                        }
                        break;
                    // 并行
                    case 4:
                        List<String> nextSteps1 = data.getNextSteps();
                        for (String nextStep : nextSteps1) {
                            Optional<HvPmOrderOperation> first = orderOperations.stream().filter(t -> t.getNodeCode().equals(nextStep)).findFirst();
//                            if (!first.isPresent()) {
//                                throw new BaseKnownException(WorkOrderExceptionEnum.NOT_START_NODE);
//                            }
                            if (first.isPresent()) {
                                List<OrderTaskDTO> collect = orderTaskDTOS.stream().filter(t -> t.getOperationId().equals(first.get().getId())).collect(Collectors.toList());
                                collect.forEach(t -> t.setState(OperationStateEnum.READY.getCode()));
                                orderTaskRepository.saveAll(DtoMapper.convertList(collect, HvPmOrderTask.class));
                            }
                        }
                        break;

                }
                workOrder.setWorkOrderState(WorkOrderStateEnum.ALREADY_ISSUED.getCode());
                workOrderRepository.save(workOrder);

            }
        } else {
            throw new BaseKnownException(WorkOrderExceptionEnum.OPERATION_STATE_NOT_MATCH);
        }
    }

    /**
     * 工单下发 根据工单下发方式下发工单
     *
     * @param orderId  工单Id
     * @param nodeCode 工艺步骤编码
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void workOrderIssued(int orderId, String nodeCode, Map<String, Object> map) {

        Map<String, Object> valueByOrder = workOrderParameterService.getValueByOrder(orderId);
        HashMap<String, Object> mergeMap = new HashMap<>();
        if (map == null) {
            map = new HashMap<>();
        }
        // 合并map
        // 如果key值相同 取map的value值
        if (valueByOrder.size() > 0) {
            for (Map.Entry<String, Object> stringObjectEntry : valueByOrder.entrySet()) {
                map.merge(stringObjectEntry.getKey(), valueByOrder.get(stringObjectEntry.getKey()), (t1, t2) -> t1);
            }
            mergeMap = new HashMap<>(map);
        } else if (map.size() > 0) {
            for (Map.Entry<String, Object> stringObjectEntry : map.entrySet()) {
                valueByOrder.merge(stringObjectEntry.getKey(), map.get(stringObjectEntry.getKey()), (t1, t2) -> t2);
            }
            mergeMap = new HashMap<>(valueByOrder);
        }

        HvPmWorkOrder order = workOrderRepository.findById(orderId).orElseThrow(() -> new BaseKnownException("工单找不到，请检查id:" + orderId));

        //组立订单 未齐套
        if (WorkOrderUsedTypeEnum.PRODUCT.getCode().equals(order.getUsedType()) && order.getCompleteSetCheckStatus() == 0) {
            throw new BaseKnownException("组立工单：" + order.getWorkOrderCode() + "未齐套！");
        }
        //组立订单 齐套
        if (WorkOrderUsedTypeEnum.PRODUCT.getCode().equals(order.getUsedType()) && order.getCompleteSetCheckStatus() == 1) {
            AssemblyWorkOrderDTO assemblyWorkOrder = getAssemblyWorkOrder(order);

            boolean hasEmptyQuantity = assemblyWorkOrder.getMaterial_list().stream()
                    .anyMatch(material -> material.getQuantity() == null || material.getQuantity() == 0);
            if (hasEmptyQuantity) {
                // 批量删除操作放在循环外
                bomDetailRepository.deleteByWorkOrderCode(order.getWorkOrderCode());

                // 查询并更新相关记录
                LambdaQueryWrapper<HvPmPlanProductBom> lqw = new LambdaQueryWrapper<>();
                lqw.eq(HvPmPlanProductBom::getPlanCode, order.getWorkOrderCode());
                List<HvPmPlanProductBom> hvPmPlanProductBoms = productBomService.list(lqw);

                // 批量更新而不是逐个更新
                hvPmPlanProductBoms.forEach(bom -> {
                    bom.setFrameCode(null);
                    bom.setActQuantity(null);
                    bom.setCompleteTime(null);
                });
                for (HvPmPlanProductBom hvPmPlanProductBom : hvPmPlanProductBoms) {
                    productBomMapper.updateFrameCodeById(hvPmPlanProductBom);
                }
                // 状态设置和保存操作放在循环外，避免重复执行
                order.setCompleteSetCheckStatus(0);
                workOrderRepository.save(order);
            } else {
                // 发送组立订单
                ResultVO r = weldLineClient.assemblyWorkOrderSend(assemblyWorkOrder);
                if (!r.isSuccess()) {
                    order.setWorkOrderState(WorkOrderStateEnum.NOT_ISSUED.getCode());
                    workOrderRepository.save(order);
                    log.error("组立工单下发失败！{}", r.getMessage());
                } else {
                    extracted(orderId, nodeCode, map, order, mergeMap);
                }
                try {
                    ProductionOrderStartDTO orderStart = new ProductionOrderStartDTO();
                    ResultVO<LocationDTO> location = locationExtendClient.getLocationById(order.getCellId());
                    if (location.getData() == null) {
                        throw new BaseKnownException("产线ID：" + order.getCellId() + "不存在~" + location.getMessage());
                    }
                    orderStart.setPickingPeople(location.getData().getName());
                    // 获取当前时间
                    LocalDateTime now = LocalDateTime.now();
                    // 定义日期时间格式化器
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    // 将当前时间格式化为字符串
                    String formattedDateTime = now.format(formatter);

                    orderStart.setActualStartTime(formattedDateTime);
                    List<PickingOrder> pickingOrderList = new ArrayList<>();
                    List<HvPmPlanProductBom> productBomList = hvPmPlanProductBomService.getListByWorkOrder(order.getWorkOrderCode());
                    if (productBomList.isEmpty()) {
                        throw new BaseKnownException("组立工单：" + order.getWorkOrderCode() + "Bom数据异常！");
                    }
                    for (HvPmPlanProductBom hvPmPlanProductBom : productBomList) {
                        PickingOrder pickingOrder = new PickingOrder();
                        pickingOrder.setMaterialCode(hvPmPlanProductBom.getMaterialCode());
                        pickingOrder.setPickingQty(Math.toIntExact(hvPmPlanProductBom.getQuantity()));
                        String frameCodes = hvPmPlanProductBom.getFrameCode();
                        if (frameCodes.contains(",")) {
                            String[] codeArray = frameCodes.split(","); // 使用逗号来分割字符串
                            for (String code : codeArray) {
                                HvPmProductBomDetail productBom = bomDetailRepository.findByPalletCodeAndPlanProductBomId(code, hvPmPlanProductBom.getId());
                                ResultVO<StockMaterialDTO> stocks = stockClient.getStocksById(productBom.getStockId());
                                if (stocks.getData() != null) {
                                    ResultVO<WaresLocationDTO> locationDTO = waresLocationClient.getById(stocks.getData().getLocationId());
                                    pickingOrder.setStockCode(locationDTO.getData().getCode());
                                    pickingOrder.setStockName(locationDTO.getData().getName());
                                    pickingOrderList.add(pickingOrder);
                                } else {
                                    HvPmCallFrameMaterial mCode = hvPmCallFrameMaterialService.findOneByFrameCodeAndMCode(code, hvPmPlanProductBom.getMaterialCode());
                                    pickingOrder.setStockCode(mCode.getWarehouseCode());
                                    ResultVO<LocationDTO> locationDto = locationExtendClient.getLocationByCode(mCode.getWarehouseCode());
                                    if (locationDto.getData() != null) {
                                        pickingOrder.setStockName(locationDto.getData().getName());
                                    }
                                }
                                pickingOrder.setPalletCode(code);
                            }
                        } else {
                            HvPmProductBomDetail productBom = bomDetailRepository.findByPalletCodeAndPlanProductBomId(hvPmPlanProductBom.getFrameCode(), hvPmPlanProductBom.getId());
                            ResultVO<StockMaterialDTO> stocks = stockClient.getStocksById(productBom.getStockId());
                            if (stocks.getData() != null) {
                                ResultVO<WaresLocationDTO> locationDTO = waresLocationClient.getById(stocks.getData().getLocationId());
                                pickingOrder.setStockCode(locationDTO.getData().getCode());
                                pickingOrder.setStockName(locationDTO.getData().getName());
                                pickingOrderList.add(pickingOrder);
                            } else {
                                HvPmCallFrameMaterial mCode = hvPmCallFrameMaterialService.findOneByFrameCodeAndMCode(hvPmPlanProductBom.getFrameCode(), hvPmPlanProductBom.getMaterialCode());
                                if (mCode.getWarehouseCode() == null) {
                                    pickingOrder.setStockCode("A01");
                                } else {
                                    pickingOrder.setStockCode(mCode.getWarehouseCode());
                                }
                                ResultVO<LocationDTO> locationDto = locationExtendClient.getLocationByCode(mCode.getWarehouseCode());
                                if (locationDto.getData() != null) {
                                    pickingOrder.setStockName(locationDto.getData().getName());
                                }
                            }
                            pickingOrder.setPalletCode(hvPmPlanProductBom.getFrameCode());
                        }

                    }
                    orderStart.setPickingOrderList(pickingOrderList);

                    HvPmPlanRouteInfo stepId = hvPmPlanRouteInfoService.getStepIdByCodeAndSequence(order.getWorkOrderCode());
                    orderStart.setProcessOrderIdList(Collections.singletonList(stepId.getStepId()));
                    ResultVO resultVO = mesClient.sendProductionOrderStart(orderStart);
                    if (!resultVO.isSuccess()) {
                        throw new BaseKnownException("Mes组立开工失败! " + resultVO.getMessage());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("Mes组立开工失败!{}", e.getMessage());
                }
            }
        }
        // 型材工单
        else if (WorkOrderUsedTypeEnum.XC.getCode().equals(order.getUsedType())) {
            ResultVO vo = materialCuttingLineClient.issuedPlan(getMaterialCuttingLinePlanDTO(order));
            if (!vo.isSuccess()) {
                throw new BaseKnownException("型材切割计划下发失败！" + vo.getMessage());
            } else {
                extracted(orderId, nodeCode, map, order, mergeMap);
            }
        }


    }

    private void extracted(int orderId, String nodeCode, Map<String, Object> map, HvPmWorkOrder order, HashMap<String, Object> mergeMap) {
        // 原工单下发（不涉及第三方），可以批量全部创建任务，也可以逐步创建
        if (order.getOrderMode() != null && order.getOrderMode() == 1) {
            // 全部创建
            issueTaskAll(order, nodeCode, mergeMap);
        } else {
            // 逐步创建
            issueTaskOneByOne(order, nodeCode, mergeMap);
        }
        // 添加工单参数
        workOrderParameterService.createParameter(map, orderId);

        applicationEventPublisher.publishEvent(new OrderEvent(this, OrderMqConst.ISSUED, order));
    }

    private MaterialCuttingLinePlanDTO getMaterialCuttingLinePlanDTO(HvPmWorkOrder order) {
        ResultVO<LocationDTO> locationVo = locationExtendClient.getLocationById(order.getCellId());
        LocationDTO locationDTO = locationVo.getData();

        //型材的切割计划 下发 型材切割线
        MaterialCuttingLinePlanDTO materialCuttingLinePlanDTO = new MaterialCuttingLinePlanDTO();
        materialCuttingLinePlanDTO.setTimestamp(System.currentTimeMillis());
        materialCuttingLinePlanDTO.setName("型材切割计划下发");
        materialCuttingLinePlanDTO.setId(UUID.randomUUID().toString().replace("-", ""));
        //车间编号作为字段KEY
        ResultVO<List<DictionaryItemDTO>> d = dictionaryItemClient.findAll(locationDTO.getCode());
        String station = null;
        if (d.isSuccess()) {
            for (DictionaryItemDTO datum : d.getData()) {
                station = datum.getItemKey();
            }
        }
        if (station == null) {
            throw new BaseKnownException("字典未配置 型材切割线：" + locationDTO.getCode() + "的上线点位");
        }
        materialCuttingLinePlanDTO.setStation_id(station);
        materialCuttingLinePlanDTO.setOrder_no(order.getWorkOrderCode());
        materialCuttingLinePlanDTO.setOrderType(String.valueOf(order.getOrderTypeId()));
        ResultVO<ShiftDTO> shiftVo = shiftClient.getShiftById(order.getShiftId());
        materialCuttingLinePlanDTO.setBc(shiftVo.getData().getShiftCode());
        materialCuttingLinePlanDTO.setProd_date(order.getPlanEndTime());
        materialCuttingLinePlanDTO.setQty(String.valueOf(order.getQuantity().intValue()));
        materialCuttingLinePlanDTO.setSegmentationCode(order.getSegmentationCode());
        materialCuttingLinePlanDTO.setShipcode(order.getShipNo());
        materialCuttingLinePlanDTO.setShipmode(order.getShipModel());
        return materialCuttingLinePlanDTO;
    }

    private AssemblyWorkOrderDTO getAssemblyWorkOrder(HvPmWorkOrder order) {
        AssemblyWorkOrderDTO assemblyWorkOrderDTO = new AssemblyWorkOrderDTO();
        String zlCode = serialCodeUtilsV2.generateCode("ZLCode");
        //任务编号
        assemblyWorkOrderDTO.setCode(zlCode);
        //任务名称
        assemblyWorkOrderDTO.setName("组立工单下发");
        //任务类型
        assemblyWorkOrderDTO.setType("ZL");
        //工单编码
        assemblyWorkOrderDTO.setWork_order_code(order.getWorkOrderCode());
        //物料类型
        assemblyWorkOrderDTO.setMaterial_type("2");
        //船号
        assemblyWorkOrderDTO.setShip_number(order.getShipNo());
        //分段号
        assemblyWorkOrderDTO.setSegmentation_code(order.getSegmentationCode());

        ResultVO<LocationDTO> lineVo = locationExtendClient.getLocationById(order.getCellId());
        LocationDTO locationDTO = lineVo.getData();
        if (locationDTO == null) throw new BaseKnownException("工单：" + order.getWorkOrderCode() + "配置的产线异常！");
        ResultVO<List<DictionaryItemDTO>> itemClientAll = dictionaryItemClient.findAll(locationDTO.getCode());
        List<DictionaryItemDTO> itemDTOS = itemClientAll.getData();
        for (DictionaryItemDTO itemDTO : itemDTOS) {
            //工位编号
            assemblyWorkOrderDTO.setStation_type(itemDTO.getItemKey());
            //工位名称
            assemblyWorkOrderDTO.setStation_name(itemDTO.getItemValue());
        }
        //加工数量
        assemblyWorkOrderDTO.setQty(order.getQuantity().intValue());
        //班次
        assemblyWorkOrderDTO.setBatch_no(order.getShiftName());
        //组立套料文件
        List<BomDTO> bomResult = null;
        String blockCode = order.getBlockCode();
        if (("A1".equals(blockCode) || "A2".equals(blockCode))) {
            ResultVO<List<BomDTO>> allByMaterialsId = bomClient.getAllByMaterialsId(order.getMaterialId());
            bomResult = allByMaterialsId.getData();

        }
        if (bomResult != null) {
            BomDTO bomDTO = bomResult.get(0);
            // 假设我们只需要第一个BOM
            String filePath = (String) bomDTO.getExtend().get("nesting_file_path");
            if (filePath != null) {
                assemblyWorkOrderDTO.setNesting_file_path(filePath);
            }
        }
        ResultVO<LocationDTO> location = locationExtendClient.getLocationById(order.getCellId());
        if (location.getData() == null) {
            throw new BaseKnownException("工单：" + order.getWorkOrderCode() + "对应的产线数据异常！");
        }
        //线体编号
        assemblyWorkOrderDTO.setLine_code(location.getData().getCode());
        assemblyWorkOrderDTO.setLineId(order.getCellId());

        ResultVO<MaterialDTO> materialData = materialClient.getMaterialByMaterialCodeAndEigenvalue(order.getMaterialCode(), "1");
        MaterialDTO materialDTO = materialData.getData();
        if (materialDTO == null) {
            throw new BaseKnownException("工单：" + order.getWorkOrderCode() + "组立物料不存在");
        }

        ResultVO<BaseBomDTO> baseBomDTOByCodeAndEffect = bomClient.getBaseBomDTOByCodeAndEffect(order.getMaterialCode(), 1);
        if (baseBomDTOByCodeAndEffect.getData() == null) {
            throw new BaseKnownException("组立物料：" + order.getMaterialCode() + "bom信息不存在或未生效");
        }
        BomAllDTO data = bomClient.getBomBomItemSubstituteItemByBomId(baseBomDTOByCodeAndEffect.getData().getId()).getData();
        if (data == null) {
            throw new BaseKnownException("组立物料：" + order.getMaterialCode() + "bom信息不存在或未生效");
        }
        //List<AssemblyMaterial> materialList = new ArrayList<>();
        assemblyWorkOrderDTO.setMaterial_code(order.getMaterialCode());
        assemblyWorkOrderDTO.setMaterial_name(order.getMaterialName());
        assemblyWorkOrderDTO.setLength(materialDTO.getMLength());
        assemblyWorkOrderDTO.setWidth(materialDTO.getMWidth());
        assemblyWorkOrderDTO.setThick((String) materialDTO.getExtend().get("height"));
        assemblyWorkOrderDTO.setWeight(materialDTO.getWeight());
        assemblyWorkOrderDTO.setDraw((String) data.getExtend().get("nestingFilePath"));
        assemblyWorkOrderDTO.setTexture_of_material(materialDTO.getQuality());
        List<AssemblyMaterial> matList = new ArrayList<>();
        //组立零件
        LambdaQueryWrapper<HvPmPlanProductBom> lqw = new LambdaQueryWrapper<>();
        lqw.eq(HvPmPlanProductBom::getPlanCode, order.getWorkOrderCode());
        lqw.isNotNull(HvPmPlanProductBom::getFrameCode);
        List<HvPmPlanProductBom> hvPmPlanProductBoms = hvPmPlanProductBomService.list(lqw);
        Map<String, HvPmProductBomDetail> bomDetailMap = new HashMap<>();
        List<HvPmProductBomDetail> bomDetailList = bomDetailRepository.findByWorkOrderCode(order.getWorkOrderCode());
        for (HvPmProductBomDetail hvPmProductBomDetail : bomDetailList) {
            bomDetailMap.put(hvPmProductBomDetail.getPlanProductBomId() + hvPmProductBomDetail.getPalletCode(), hvPmProductBomDetail);
        }


        for (HvPmPlanProductBom hvPmPlanProductBom : hvPmPlanProductBoms) {
            ResultVO<MaterialDTO> materialList2 = materialClient.getMaterialByMaterialCodeAndEigenvalue(hvPmPlanProductBom.getMaterialCode(), "1");
            MaterialDTO materialDTO2 = materialList2.getData();
            if (materialDTO2 == null) {
                throw new BaseKnownException("工单：" + order.getWorkOrderCode() + "零件物料不存在");
            }
            NestingMaterialFileDTO materialFileDTO2;
            WorkOrderDTO orderCode = workOrderService.findByWorkOrderCode(hvPmPlanProductBom.getPlanCode());
            if (orderCode != null) {
                materialFileDTO2 = nestMaterialFileClient.getByMaterialCodeAndShipNo(hvPmPlanProductBom.getMaterialCode(), orderCode.getShipNo()).getData();
            } else {
                materialFileDTO2 = nestMaterialFileClient.getByMaterialCode(hvPmPlanProductBom.getMaterialCode()).getData();
            }

            String markingFilePath = null;
            List<HvPmMaterialCutPlanDetail0> detail0List = cutPlanDetail0Service.getDetail0ListByMaterialCode(hvPmPlanProductBom.getMaterialCode());
            for (HvPmMaterialCutPlanDetail0 cutPlanDetail0 : detail0List) {
                HvPmMaterialCutPlan cutPlan = cutPlanService.getByPlanCode(cutPlanDetail0.getCutPlanCode());
                if (cutPlan != null && cutPlan.getShipNumber().equals(order.getShipNo())) {
                    markingFilePath = cutPlan.getMarkingFilePath();
                    break;
                }
            }

           /* if (materialFileDTO2 == null) {
                throw new BaseKnownException("物料：" + hvPmPlanProductBom.getMaterialCode() + "零件图不存在");
            }*/
            //齐套验证后，料框可能会有多个，目前是拼接到一块
            String[] palletCodes = hvPmPlanProductBom.getFrameCode().split(",");

            for (String palletCode : palletCodes) {
                AssemblyMaterial material = new AssemblyMaterial();
                material.setMaterial_code(materialDTO2.getMaterialCode());
                material.setMaterial_name(materialDTO2.getMaterialName());
                material.setLength(materialDTO2.getMLength());
                material.setWidth(materialDTO2.getMWidth());
                material.setThick((String) materialDTO2.getExtend().get("height"));
                material.setWeight(materialDTO2.getWeight());
                HvPmProductBomDetail hvPmProductBomDetail = bomDetailMap.get(hvPmPlanProductBom.getId() + palletCode);
                if (hvPmProductBomDetail != null) {
                    material.setQuantity(Math.toIntExact(hvPmProductBomDetail.getQuality()));
                }
                //  TODO料框为000000的默认齐套
                if ("000000".equals(palletCode)) {
                    material.setQuantity(Math.toIntExact(hvPmPlanProductBom.getQuantity()));
                }
                //  TODO料框为000000的默认齐套
                material.setMarking_file_path(markingFilePath);
                if (materialFileDTO2 != null) {
                    material.setDraw(materialFileDTO2.getFilePath());
                    material.setMarking_file_path(materialFileDTO2.getMarkingFilePath());
                }
                material.setTexture_of_material(materialDTO2.getQuality());
                material.setSn(materialDTO2.getMaterialCode());
                material.setI_order("0000");
                material.setPallet_code(palletCode);
                material.setGps4((String) materialDTO2.getExtend().get("gps4"));


                /*//1.优先：料框找的报工记录、找到对应的零件和sn
                //2.备选：没有料框，用物料编号对应的报工记录、找到对应的零件和sn
                List<HvPmLineReportMaterial> lineReportMaterials = lineReportMaterialService.selectByMaterialAndFrameCode(hvPmPlanProductBom.getMaterialCode(), hvPmPlanProductBom.getFrameCode());
                //多个一样的物料，对应多个pn码
                List<AssemblyMaterial> snList = new ArrayList<>();
                for (HvPmLineReportMaterial lineReportMaterial : lineReportMaterials) {
                    AssemblyMaterial material2 = new AssemblyMaterial();
                    material2.setMATERIAL_CODE(lineReportMaterial.getMaterialCode());
                    material2.setSN(lineReportMaterial.getSn());
                    snList.add(material2);
                }*/
                matList.add(material);
            }
        }
        assemblyWorkOrderDTO.setMaterial_list(matList);
        return assemblyWorkOrderDTO;
    }

    /**
     * 根据工单ID 查询工艺步骤
     *
     * @param orderId 工单Id
     * @return 工艺步骤列表
     */
    @Override
    public Map<String, Object> getRoutStepByOrderId(int orderId) {
        HvPmWorkOrder one = workOrderRepository.getOne(orderId);
        ResultVO<RouteDTO> route = routeClient.getEffectRouteByProductIdAndRouteId(one.getMaterialId(), one.getRouteId());
        if (!route.isSuccess()) {
            throw new BaseKnownException(route);
        }
        route.getData().flat();
        Node firstNode = route.getData().findStartNode();
        NextStepDTO data = route.getData().findNextStep(firstNode);
        Integer type = data.getType();
        Map<String, Object> map = new HashMap<>();
        List<Object> objects = new ArrayList<>();
        if (data.getNextSteps().size() > 0) {
            for (String nextStep : data.getNextSteps()) {
                if (nextStep != null) {
                    Optional<Node> first = route.getData().getNodes().stream().filter(t -> t.getData().getCode().equals(nextStep)).findFirst();
                    first.ifPresent(objects::add);
                } else {
                    map.put("结束工序", "0");
                }
            }
        } else {
            throw new BaseKnownException(WorkOrderExceptionEnum.NOT_STEP);
        }
        map.put("工序选择", objects);

        return map;
    }

    /**
     * 批量下发
     *
     * @param idList Id列表
     */
    @Override
    public Map<Integer, String> workOrderIssuedByIdList(List<Integer> idList, Map<String, Object> map) {
        Map<Integer, String> errorMap = new HashMap<>();
        for (Integer id : idList) {
            try {
                workOrderIssued(id, null, map);
            } catch (Exception e) {
                errorMap.put(id, getMessage(e.getMessage(), null));
                break;
            }
        }
        return errorMap;
    }

    public String getMessage(String result, Object[] params) {
        ReloadableResourceBundleMessageSource messageSource = new ReloadableResourceBundleMessageSource();
        messageSource.setCacheSeconds(-1);
        messageSource.setDefaultEncoding(StandardCharsets.UTF_8.name());
        messageSource.setBasenames("/i18n/messages");
        String message = "";
        try {
            Locale locale = LocaleContextHolder.getLocale();
            message = messageSource.getMessage(result, params, locale);
        } catch (Exception e) {
            log.error("parse message error! ", e);
        }
        return message;
    }

    /**
     * 批量下发工单
     *
     * @param workIssuedDTO 下发信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchIssued(WorkIssuedDTO workIssuedDTO) {
        List<HvPmWorkOrder> hvPmOrderManageByIdIn = workOrderRepository.getHvPmOrderManageByIdIn(workIssuedDTO.getOrderIds());
        if (hvPmOrderManageByIdIn.isEmpty()) {
            throw new BaseKnownException(WorkOrderExceptionEnum.CHOOSE_ORDER);
        }
        // 判断工单中工艺路线是否不同
        List<@NotNull(message = "工艺路线ID不能为空") Integer> collect = hvPmOrderManageByIdIn.stream().map(HvPmWorkOrder::getRouteId).collect(Collectors.toList());
        if (1 != collect.stream().distinct().count()) {
            // 相同工艺的工单才能一起批量下发
            throw new BaseKnownException(WorkOrderExceptionEnum.ORDER_ROUTE_DIFFERENT);
        }
        // 更新工单信息 并下发
        for (HvPmWorkOrder workOrder : hvPmOrderManageByIdIn) {
            workIssuedDTO.getWorkOrderDTO().setId(workOrder.getId());
            HvPmWorkOrder one = workOrderRepository.getOne(workOrder.getId());

            WorkOrderDTO convert = DtoMapper.convert(one, WorkOrderDTO.class);
            convert.setAreaId(workIssuedDTO.getWorkOrderDTO().getAreaId());
            convert.setCellId(workIssuedDTO.getWorkOrderDTO().getCellId());
            convert.setShiftId(workIssuedDTO.getWorkOrderDTO().getShiftId());
            convert.setCrewId(workIssuedDTO.getWorkOrderDTO().getCrewId());
            convert.setOrderMode(workIssuedDTO.getWorkOrderDTO().getOrderMode());
            updateOrderManage(convert);
            workOrderIssued(workOrder.getId(), workIssuedDTO.getNodeCode(), workIssuedDTO.getMap());
        }
    }

    /**
     * 撤销工单
     *
     * @param id 工单ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelOrderById(int id) {
        Integer state = workOrderRepository.findById(id).orElseThrow(() -> new BaseKnownException("工单id异常：" + id)).getWorkOrderState();
        // 只有已下发的工单可以撤销 撤销删除工单下所有工序  工序下物料 文件 参数 关系
        if (state.equals(WorkOrderStateEnum.ALREADY_ISSUED.getCode())) {
            List<HvPmOrderOperation> operations = orderOperationRepository.findAllByOrderId(id);
            List<Integer> collect = operations.stream().map(SysBase::getId).collect(Collectors.toList());
            orderOperationRepository.deleteByIdIn(collect);
            List<HvPmOrderTask> allByOrderId = orderTaskRepository.getAllByOrderId(id);
            orderTaskRepository.deleteInBatch(allByOrderId);
            for (int operationId : collect) {
                List<HvPmOperationFile> files = operationFileRepository.getByOperationId(operationId);
                List<HvPmOperationParameter> parameters = operationParameterRepository.getByOperationId(operationId);
                List<HvPmOperationMaterial> materials = materialRepository.findAllByOperationId(operationId);
                List<Integer> fileIds = files.stream().map(SysBase::getId).collect(Collectors.toList());
                List<Integer> parametersIds = parameters.stream().map(SysBase::getId).collect(Collectors.toList());
                List<Integer> materialsIds = materials.stream().map(SysBase::getId).collect(Collectors.toList());

                operationParameterRepository.deleteByIdIn(parametersIds);
                operationFileRepository.deleteByIdIn(fileIds);
                materialRepository.deleteAllByIdIn(materialsIds);
            }
            // 修改工单状态为新增
            workOrderRepository.updateWorkOrderState(WorkOrderStateEnum.NOT_ISSUED.getCode(), id);
            HvPmWorkOrder order = workOrderRepository.findById(id).orElseThrow(() -> new BaseKnownException("工单id异常:" + id));
            applicationEventPublisher.publishEvent(new OrderEvent(this, OrderMqConst.ON_ISSUED, order));
        } else {
            throw new BaseKnownException(WorkOrderExceptionEnum.OPERATION_STATE_NOT_MATCH);
        }

    }

    /**
     * 批量撤销
     *
     * @param idList Id列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelOrderByIdList(List<Integer> idList) {
        for (Integer id : idList) {
            cancelOrderById(id);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void scarpWorkOrder(int id) {
        HvPmWorkOrder one = workOrderRepository.getOne(id);
        // 已报工的工单不能报废
        if (one.getWorkOrderState().equals(WorkOrderStateEnum.FINISH.getCode())) {
            throw new BaseKnownException(WorkOrderExceptionEnum.FINISH_WORK_ORDER_NO_SCARP);
        }
        workOrderRepository.updateWorkOrderState(WorkOrderStateEnum.SCARP.getCode(), id);
        List<HvPmOrderTask> hvPmOrderTaskList = orderTaskRepository.getAllByOrderId(id);
        for (HvPmOrderTask hvPmOrderTask : hvPmOrderTaskList) {
            hvPmOrderTask.setState(OperationStateEnum.SCARP.getCode());
        }
        orderTaskRepository.saveAll(hvPmOrderTaskList);
        HvPmWorkOrder order = workOrderRepository.findById(id).orElseThrow(() -> new BaseKnownException("工单id异常：" + id));
        applicationEventPublisher.publishEvent(new OrderEvent(this, OrderMqConst.SCRAP, order));
    }

    /**
     * 批量报废
     *
     * @param idList ID列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void scarpWorkOrderByIdList(List<Integer> idList) {
        for (Integer id : idList) {
            scarpWorkOrder(id);
        }
    }

    /**
     * 工单报工
     *
     * @param id 工单ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void finishWorkOrder(int id) {
        HvPmWorkOrder hvPmWorkOrder = workOrderRepository.getOne(id);
        if (hvPmWorkOrder.getWorkOrderState().equals(WorkOrderStateEnum.FINISH.getCode())) {
            throw new BaseKnownException(WorkOrderExceptionEnum.WORK_ORDER_FINISH);
        }
        if (hvPmWorkOrder.getPlanOrNew() == 0) {
            throw new BaseKnownException(WorkOrderExceptionEnum.NOT_PLAN_FINISH);
        }
        if (!hvPmWorkOrder.getWorkOrderState().equals(WorkOrderStateEnum.END.getCode())) {
            throw new BaseKnownException(WorkOrderExceptionEnum.NOT_END_WORK_ORDER_NOT_FINISH);
        }
        // 1.查询工单下最后一道工序 录入的物料
        List<HvPmOrderOperation> operationList = orderOperationRepository.findAllByOrderId(id);
        // 查出所有工序的顺序号 排序
        // 找出最后一道工序
        BigDecimal bigDecimal = new BigDecimal(0);
        // 查出所有工序产出物料信息
        for (HvPmOrderOperation hvPmOrderOperation : operationList) {
            List<HvPmOperationOutPutMaterial> getOutPutByOperationId = operationOutPutMaterialRepository.getAllByOperationId(hvPmOrderOperation.getId());
            // 把跟工单相同的物料产出数量相加 进行统计
            for (HvPmOperationOutPutMaterial hvPmOperationOutPutMaterial : getOutPutByOperationId) {
                if (hvPmOperationOutPutMaterial.getMaterialCode().equals(hvPmWorkOrder.getMaterialCode()) || hvPmOperationOutPutMaterial.getEigenvalue().equals(hvPmWorkOrder.getEigenvalue())) {
                    bigDecimal = bigDecimal.add(hvPmOperationOutPutMaterial.getOutPutCount());
                }
            }
        }
        // 把统计后的数量录入工单实际产出数量中
        hvPmWorkOrder.setActualCount(bigDecimal);
        // 修改工单状态
        OrderFinishDTO orderFinishDTO = new OrderFinishDTO();
        orderFinishDTO.setId(id);
        orderFinishDTO.setActualCount(bigDecimal);
        orderFinishDTO.setOrderCode(hvPmWorkOrder.getWorkOrderCode());
        orderFinishDTO.setSerialNum(hvPmWorkOrder.getSerialNumber());
        boolean b = workPlanDetailService.orderFinish(hvPmWorkOrder.getWorkOrderCode(), hvPmWorkOrder.getSerialNumber(), bigDecimal);
        if (b) {
            hvPmWorkOrder.setWorkOrderState(WorkOrderStateEnum.FINISH.getCode());
            workOrderRepository.save(hvPmWorkOrder);
        } else {
            throw new BaseKnownException(WorkPlanExceptionEnum.ORDER_FINISH_ERROR);
        }

        HvPmWorkOrder workOrder = workOrderRepository.findById(id).orElseThrow(() -> new BaseKnownException("工单id异常：" + id));
        applicationEventPublisher.publishEvent(new OrderEvent(this, OrderMqConst.COMPLETE, workOrder));
    }

    /**
     * 批量报工
     *
     * @param idList id列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void finishWorkOrderAll(List<Integer> idList) {
        for (Integer id : idList) {
            finishWorkOrder(id);
        }
    }

    /**
     * 展示所有未完成工单数
     *
     * @return 所有未完成工单总数数值
     */
    @Override
    public int workOrderTodayNotFinish(Integer orderTypeId, Integer usedType) {
        int newWOrkOrder;
        int issued;
        int running;
        int suspend;
        if (usedType == null) {
            if (orderTypeId != null && orderTypeId != 0) {
                newWOrkOrder = workOrderRepository.getWorkOrderNumByStateAndTypeId(WorkOrderStateEnum.NOT_ISSUED.getCode(), orderTypeId);
                issued = workOrderRepository.getWorkOrderNumByStateAndTypeId(WorkOrderStateEnum.ALREADY_ISSUED.getCode(), orderTypeId);
                running = workOrderRepository.getWorkOrderNumByStateAndTypeId(WorkOrderStateEnum.RUNNTING.getCode(), orderTypeId);
                suspend = workOrderRepository.getWorkOrderNumByStateAndTypeId(WorkOrderStateEnum.SUSPEND.getCode(), orderTypeId);
            } else {
                newWOrkOrder = workOrderRepository.getWorkOrderNumByState(WorkOrderStateEnum.NOT_ISSUED.getCode());
                issued = workOrderRepository.getWorkOrderNumByState(WorkOrderStateEnum.ALREADY_ISSUED.getCode());
                running = workOrderRepository.getWorkOrderNumByState(WorkOrderStateEnum.RUNNTING.getCode());
                suspend = workOrderRepository.getWorkOrderNumByState(WorkOrderStateEnum.SUSPEND.getCode());
            }
        } else {
            if (orderTypeId != null && orderTypeId != 0) {
                newWOrkOrder = workOrderRepository.getWorkOrderNumByStateAndTypeIdAndUsedType(WorkOrderStateEnum.NOT_ISSUED.getCode(), orderTypeId, usedType);
                issued = workOrderRepository.getWorkOrderNumByStateAndTypeId(WorkOrderStateEnum.ALREADY_ISSUED.getCode(), orderTypeId, usedType);
                running = workOrderRepository.getWorkOrderNumByStateAndTypeId(WorkOrderStateEnum.RUNNTING.getCode(), orderTypeId, usedType);
                suspend = workOrderRepository.getWorkOrderNumByStateAndTypeId(WorkOrderStateEnum.SUSPEND.getCode(), orderTypeId, usedType);
            } else {
                newWOrkOrder = workOrderRepository.getWorkOrderNumByState(WorkOrderStateEnum.NOT_ISSUED.getCode(), usedType);
                issued = workOrderRepository.getWorkOrderNumByState(WorkOrderStateEnum.ALREADY_ISSUED.getCode(), usedType);
                running = workOrderRepository.getWorkOrderNumByState(WorkOrderStateEnum.RUNNTING.getCode(), usedType);
                suspend = workOrderRepository.getWorkOrderNumByState(WorkOrderStateEnum.SUSPEND.getCode(), usedType);
            }
        }
        return newWOrkOrder + issued + running + suspend;
    }

    /**
     * 获取今日完成工单数
     *
     * @return 今日完成工单数值
     * @throws ParseException 解析异常
     */
    @Override
    public int workEndOrder(Integer orderTypeId, Integer usedType) throws ParseException {
        List<WorkOrderDTO> todayWorkOrder = getTodayWorkOrder(orderTypeId, usedType);
        // 在实际完成时间在今天的工单中找状态为完成 的工单
        Long endOrder = todayWorkOrder.stream().filter(t -> t.getWorkOrderState().equals(WorkOrderStateEnum.END.getCode())).count();
        Long finish = todayWorkOrder.stream().filter(t -> t.getWorkOrderState().equals(WorkOrderStateEnum.FINISH.getCode())).count();
        return endOrder.intValue() + finish.intValue();
    }

    /**
     * 今日工单进度信息
     *
     * @return WorkOrderNumDTO 工单进度信息
     * @throws ParseException 异常信息
     */
    @Override
    public WorkOrderNumDTO workOrderNum(String orderTypeCode, Integer usedType) throws ParseException {
        HvPmOrderType allByOrderTypeCode = orderTypeRepository.getAllByOrderTypeCode(orderTypeCode);
        Integer orderTypeId = 0;
        if (allByOrderTypeCode != null) {
            orderTypeId = allByOrderTypeCode.getId();
        }
        WorkOrderNumDTO workOrderNumDTO = new WorkOrderNumDTO();
        int workEndOrderNum = workEndOrder(orderTypeId, usedType);
        int workNotFinish = workOrderTodayNotFinish(orderTypeId, usedType);
        workOrderNumDTO.setWorkOrderFinishToday(workEndOrderNum);
        workOrderNumDTO.setWorkOrderRunToday(workNotFinish);
        return workOrderNumDTO;
    }

    /**
     * 根据工单ID查询工单下工序的产出和投入料
     *
     * @param workOrderId 工单ID
     * @return 工单工序下投入和产出料列表
     */
    @Override
    public List<OperationMaterialsDTO> getOperationMaterialByWorkOrderId(int workOrderId) {
        // 查询工单下所有工序
        List<OrderOperationDTO> operationByOrderId = orderOperationService.getOperationByOrderId(workOrderId);
        List<OperationMaterialsDTO> dtos = new ArrayList<>();
        // 遍历工序找出所有产出料和投入料
        for (OrderOperationDTO orderOperationDTO : operationByOrderId) {
            OperationMaterialsDTO operationMaterialsDTO = new OperationMaterialsDTO();

            // 加入工序ID
            operationMaterialsDTO.setOperationId(orderOperationDTO.getId());

            // 加入工序编码
            operationMaterialsDTO.setOperationCode(orderOperationDTO.getOperationCode());

            // 查询工序下的投入料
            List<OperationMaterialDTO> operationMaterialListByOperationId = operationMaterialService.getOperationMaterialListByOperationId(orderOperationDTO.getId());

            for (OperationMaterialDTO operationMaterialDTO : operationMaterialListByOperationId) {
                List<OperationMaterialBatchNumDTO> collect = operationMaterialDTO.getBatchNumDTOList().stream().filter(t -> t.getBatchNumber() != null).collect(Collectors.toList());
                operationMaterialDTO.setBatchNumDTOList(collect);
            }
            List<OperationMaterialDTO> inputMaterial = operationMaterialListByOperationId.stream().filter(t -> t.getBatchNumDTOList().size() > 0).collect(Collectors.toList());

            operationMaterialsDTO.setOperationMaterialDTOS(inputMaterial);

            // 查询工序下的产出料
            List<OperationOutPutMaterialDTO> allByOperationId = operationOutPutMaterialService.getAllByOperationId(orderOperationDTO.getId());

            List<OperationOutPutMaterialDTO> collect = (allByOperationId.stream().filter(t -> t.getBatchNumber() != null).collect(Collectors.toList())).stream().filter(t -> t.getOutPutCount() != null).collect(Collectors.toList());

            operationMaterialsDTO.setOperationOutPutMaterialDTO(collect);

            dtos.add(operationMaterialsDTO);
        }
        return dtos;
    }

    /**
     * 根据工单id查询工单所有信息
     *
     * @param workOrderId 工单id
     * @return 工单正向追溯信息
     */
    @Override
    public WorkAllDTO getWorkOrderAll(int workOrderId) {
        HvPmWorkOrder hvPmWorkOrder = workOrderRepository.getOne(workOrderId);


        WorkAllDTO workAllDTO = DtoMapper.convert(hvPmWorkOrder, WorkAllDTO.class);
        if (hvPmWorkOrder.getWorkOrderState().equals(WorkOrderStateEnum.NOT_ISSUED.getCode())) {
            try {
                if (hvPmWorkOrder.getCellId() == null) {
                    throw new BaseKnownException(WorkOrderExceptionEnum.ORDER_NOT_CELL);
                }
                // 根据CellId获取对应的工位列表
                ResultVO<List<LocationDTO>> workCenter = locationExtendClient.getLocationListByParentId(hvPmWorkOrder.getCellId());
                // 定义一个工序顺序号 在每次创建工序循环中加10 最小代表工序越靠前 最小是10
                Integer operationNum = 10;
                // 循环遍历插入工序数据
                if (!workCenter.isSuccess()) {
                    throw new BaseKnownException(WorkOrderExceptionEnum.EQUIPMENT_SERVER_ERROR);
                }
                BigDecimal divide = new BigDecimal(0);
                if (hvPmWorkOrder.getBomId() != null) {
                    ResultVO<BomAllDTO> allBomInfoById = materialClient.getAllBomInfoById(hvPmWorkOrder.getBomId());
                    if (!allBomInfoById.isSuccess()) {
                        throw new BaseKnownException(WorkOrderExceptionEnum.MATERIAL_SERVER_ERROR);
                    }

                    if (allBomInfoById.getData() != null) {
                        BigDecimal bomCount = allBomInfoById.getData().getBomCount();
                        if (bomCount.compareTo(BigDecimal.ZERO) > 0) {
                            BigDecimal quantity = hvPmWorkOrder.getQuantity();
                            if (quantity == null) {
                                quantity = new BigDecimal(0);
                            }
                            divide = quantity.divide(bomCount, 4, RoundingMode.HALF_UP);
                        } else {
                            divide = new BigDecimal(0);
                        }
                        workAllDTO.setBomName(allBomInfoById.getData().getBomName());
                        workAllDTO.setBomCode(allBomInfoById.getData().getBomCode());
                    }
                }
                // 根据产品ID获取对应的RouteWithRouteStepDTO  根据routeStep 创建对应的工序
                ResultVO<RouteDTO> routeByMaterialId = routeClient.getEffectRouteByProductIdAndRouteId(hvPmWorkOrder.getMaterialId(), hvPmWorkOrder.getRouteId());

                if (!routeByMaterialId.isSuccess()) {
                    throw new BaseKnownException(WorkOrderExceptionEnum.ROUTE_SERVER_ERROR);

                } else {
                    routeByMaterialId.getData().flat();
                    hvPmWorkOrder.setIssuedTime(new Date());
                    List<OperationAllDTO> list = new ArrayList<>();
                    createFictitiousOperationByRouteStep(hvPmWorkOrder, list, workCenter, routeByMaterialId.getData(), divide);
                    workAllDTO.setOperationAllDTOS(list);
                    if (workAllDTO.getOperationAllDTOS().size() > 0) {
                        List<OperationAllDTO> collect = workAllDTO.getOperationAllDTOS().stream().sorted(Comparator.comparing(OrderTaskDTO::getOperationOrder)).collect(Collectors.toList());
                        workAllDTO.setOperationAllDTOS(collect);
                    }
                    return workAllDTO;
                }
            } catch (Exception e) {
                log.error("查询详细信息出错", e);
                List<OperationAllDTO> list = new ArrayList<>();
                workAllDTO.setOperationAllDTOS(list);
                return workAllDTO;
            }

        }
        List<HvPmOrderTask> operationByOrderId = orderTaskRepository.getAllByOrderId(workOrderId);

        if (operationByOrderId != null && operationByOrderId.size() > 0) {
            List<OperationAllDTO> operationAllDTOS = DtoMapper.convertList(operationByOrderId, OperationAllDTO.class);

            for (OperationAllDTO operationAllDTO : operationAllDTOS) {

                List<OperationMaterialDTO> operationMaterialDTOS = operationMaterialService.getOperationMaterialListByOperationId(operationAllDTO.getOperationId());
                operationAllDTO.setOperationMaterialDTOS(operationMaterialDTOS);

                List<OperationOutPutMaterialDTO> outPutMaterialDTOS = operationOutPutMaterialService.getAllByOperationId(operationAllDTO.getOperationId());

                operationAllDTO.setOperationOutPutMaterialDTOS(outPutMaterialDTOS);

                List<OperationParameterDTO> parameterByOperationId = parameterService.getParameterByOperationId(operationAllDTO.getOperationId());

                operationAllDTO.setOperationParameterDTOS(parameterByOperationId);
            }
            if (hvPmWorkOrder.getBomId() != null) {
                ResultVO<BomAllDTO> allBomInfoById = materialClient.getAllBomInfoById(hvPmWorkOrder.getBomId());
                if (!allBomInfoById.isSuccess()) {
                    throw new BaseKnownException(WorkOrderExceptionEnum.MATERIAL_SERVER_ERROR);
                }
                workAllDTO.setBomName(allBomInfoById.getData().getBomName());
                workAllDTO.setBomCode(allBomInfoById.getData().getBomCode());
            }
            workAllDTO.setOperationAllDTOS(operationAllDTOS);

        } else {
            List<OperationAllDTO> list = new ArrayList<>();
            workAllDTO.setOperationAllDTOS(list);
        }
        if (workAllDTO.getOperationAllDTOS().size() > 0) {
            List<OperationAllDTO> collect = workAllDTO.getOperationAllDTOS().stream().sorted(Comparator.comparing(OrderTaskDTO::getCreateTime)).collect(Collectors.toList());
            workAllDTO.setOperationAllDTOS(collect);
        }
        return workAllDTO;
    }

    /**
     * 获取当天的物料需求量
     *
     * @param workMaterialDTO 工单物料
     * @return 物料需求量
     */
    @Override
    public List<MaterialDemandDTO> getMaterial(WorkMaterialDTO workMaterialDTO) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date parse = sdf.parse(workMaterialDTO.getWorkDate());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date start = simpleDateFormat.parse(DateFormatUtils.format(parse, "yyyy-MM-dd 00:00:00"));
        Date end = simpleDateFormat.parse(DateFormatUtils.format(parse, "yyyy-MM-dd 23:59:59"));
        start = getDate(start);
        end = getDate(end);
        log.info(start.toString());
        log.info(end.toString());
        List<HvPmWorkOrder> hvPmWorkOrders = workOrderRepository.getAllByPlanStartTime(start, end);
        List<OperationMaterialDTO> dtos = new ArrayList<>();
        for (Integer integer : workMaterialDTO.getWorkState()) {
            for (HvPmWorkOrder hvPmWorkOrder : hvPmWorkOrders) {
                if (hvPmWorkOrder.getWorkOrderState().equals(integer)) {
                    WorkAllDTO workOrderAll = getWorkOrderAll(hvPmWorkOrder.getId());
                    for (OperationAllDTO operationAllDTO : workOrderAll.getOperationAllDTOS()) {
                        for (OperationMaterialDTO operationMaterialDTO : operationAllDTO.getOperationMaterialDTOS()) {
                            Optional<OperationMaterialDTO> first = dtos.stream().filter(t -> t.getMaterialId().equals(operationMaterialDTO.getMaterialId())).findFirst();
                            if (first.isPresent()) {
                                OperationMaterialDTO dto = first.get();
                                BigDecimal add = dto.getPlanCount().add(operationMaterialDTO.getPlanCount());
                                dtos.remove(dto);
                                dto.setPlanCount(add);
                                dtos.add(dto);
                            } else {
                                dtos.add(operationMaterialDTO);
                            }
                        }
                    }
                }
            }
        }
        List<MaterialDemandDTO> materialDemandDTOS = DtoMapper.convertList(dtos, MaterialDemandDTO.class);
        for (MaterialDemandDTO materialDemandDTO : materialDemandDTOS) {
            ResultVO<MaterialDTO> materialById = materialClient.getMaterialById(materialDemandDTO.getMaterialId());
            if (materialById.isSuccess()) {
                materialDemandDTO.setMaterialUom(materialById.getData().getUomName());
            }
        }
        return materialDemandDTOS;
    }

    @Override
    public List<WorkOrderDTO> findOrderByOperation(int operationId) {
        List<HvPmOrderOperation> allByRouteOperationId = orderOperationRepository.getAllByRouteOperationId(operationId);
        List<Integer> collect = allByRouteOperationId.stream().map(t -> t.getOrderId()).collect(Collectors.toList());

        List<Integer> orderIdList = collect.stream().distinct().collect(Collectors.toList());
        List<HvPmWorkOrder> hvPmOrderManageByIdIn = workOrderRepository.getHvPmOrderManageByIdIn(orderIdList);


        return DtoMapper.convertList(hvPmOrderManageByIdIn, WorkOrderDTO.class);
    }

    /**
     * 获取工单计划用料单信息
     *
     * @param workOrderId 工单Id
     * @return 工单用料单信息
     */
    @Override
    public OrderBomDTO getWorkPlanQuantity(Integer workOrderId) {
        return statisticsMapper.getWorkPlanQuantity(workOrderId);
    }

    /**
     * 获取工单属性
     *
     * @return 工单属性列表
     */
    @Override
    public Map<String, String> getOrderParameters() {
        HashMap<String, String> result = new HashMap<>();
        result.put("orderCode", "工单编码");
        result.put("materialCode", "物料编码");
        result.put("materialName", "物料名称");
        result.put("orderNum", "工单数量");
        result.put("routeCode", "产品工艺编码");
        result.put("routeName", "产品工艺名称");
        result.put("operationCode", "工艺操作编码");
        result.put("operationName", "工艺操作名称");
        List<ExtendColumnInfo> columns = workOrderExtend.getExtendColumnInfo();
        for (ExtendColumnInfo column : columns) {
            result.put(column.getColumnName(), column.getChName());
        }
        return result;
    }

    /**
     * 批量插入数据
     *
     * @param workOrderTemplates
     * @return
     */
    @Override
    public ResultVO saveBatch(List<WorkOrderTemplate> workOrderTemplates) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // List<HvPmWorkOrder> hvPmWorkOrders = DtoMapper.convertList(workOrderDTOS, HvPmWorkOrder.class);
        // 校验数据 ....
        // 校验数量
        try {
            for (WorkOrderTemplate workOrderTemplate : workOrderTemplates) {
                // 校验工厂
                if (StringUtils.isEmpty(workOrderTemplate.getAreaCode())) {
                    return ResultVO.error(500, "车间编号不能为空");
                }
                LocationDTO locationDTO = locationExtendClient.getHvBmLocationByCode(workOrderTemplate.getAreaCode()).getData();
                if (locationDTO == null) {
                    return ResultVO.error(500, "该车间编号不存在");
                }

                // 校验计划编号
                if (StringUtils.isEmpty(workOrderTemplate.getPlanCode())) {
                    return ResultVO.error(500, "批次号不能为空");
                }

                // 校验物料编号
                if (StringUtils.isEmpty(workOrderTemplate.getMaterialCode())) {
                    return ResultVO.error(500, "物料编号不能为空");
                }

                // 校验计划开始时间 和 计划结束时间
                if (StringUtils.isEmpty(workOrderTemplate.getPlanStartTime()) || StringUtils.isEmpty(workOrderTemplate.getPlanEndTime())) {
                    return ResultVO.error(500, "计划开始时间和结束时间不能为空");
                }
                // 校验开始时间是否大于结束时间
                if (simpleDateFormat.parse(workOrderTemplate.getPlanStartTime()).getTime() - simpleDateFormat.parse(workOrderTemplate.getPlanEndTime()).getTime() > 0) {
                    return ResultVO.error(500, "计划开始时间不能大于结束时间");
                }


                // 校验物料数量
                int quantityInt = Integer.parseInt(workOrderTemplate.getQuantity());
                int executeSequenceInt = Integer.parseInt(workOrderTemplate.getExecuteSequence());
                if (quantityInt < 1 || executeSequenceInt < 1) {
                    throw new NumberFormatException();
                }
                WorkOrderMaterialQuery workOrderMaterialQuery = new WorkOrderMaterialQuery();
                workOrderMaterialQuery.setOrderTypeCode("default");
                // 校验物料是否存在
                List<OrderTypeMaterialDTO> materialDTOS = orderMapper.getMaterial(workOrderMaterialQuery);
                boolean checkMaterialCode = false;
                HvPmWorkOrder hvPmWorkOrder = null;
                for (OrderTypeMaterialDTO materialDTO : materialDTOS) {
                    hvPmWorkOrder = new HvPmWorkOrder();
                    if (materialDTO.getMaterialCode().equals(workOrderTemplate.getMaterialCode())) {
                        checkMaterialCode = true;
                        hvPmWorkOrder.setMaterialId(materialDTO.getMaterialId());
                        break;
                    }
                }
                if (!checkMaterialCode) {
                    return ResultVO.error(500, "导入失败，物料编号" + workOrderTemplate.getMaterialCode() + "不存在");
                }

                // 班组非空校验
                if (StringUtils.isEmpty(workOrderTemplate.getCrew())) {
                    return ResultVO.error(500, "班组编号不能为空");
                }


                // 班次非空校验
                if (StringUtils.isEmpty(workOrderTemplate.getShift())) {
                    return ResultVO.error(500, "班次编号不能为空");
                }


                // 校验工艺路线是否为空
                if (StringUtils.isEmpty(workOrderTemplate.getRouteCode()) || StringUtils.isEmpty(workOrderTemplate.getRouteVersion())) {
                    return ResultVO.error(500, "工单路线编号和工艺路线版本不能为空");
                }

                // 校验工艺路线和物料的对应关系是否存在
                RouteDTO productRouteDTOByProductId = routeClient.getProductRouteDTOByProductId(hvPmWorkOrder.getMaterialId(), workOrderTemplate.getRouteCode(), workOrderTemplate.getRouteVersion()).getData();
                if (productRouteDTOByProductId == null) {
                    return ResultVO.error(500, "导入失败，物料编号" + workOrderTemplate.getMaterialCode() + "与工艺路线编号为" + workOrderTemplate.getRouteCode() + "未绑定工艺路线版本为" + workOrderTemplate.getRouteVersion() + "的工艺路线");
                }
                hvPmWorkOrder.setRouteId(productRouteDTOByProductId.getId());
                // 校验班次
                ShiftDTO shiftDTO = shiftClient.getInfoShiftByShiftCode(workOrderTemplate.getShift()).getData();
                if (shiftDTO == null) {
                    return ResultVO.error(500, "导入的班次编号" + workOrderTemplate.getShift() + "不存在");
                }

                // 校验班组
                CrewWithMemberDTO crewWithMemberDTO = crewClient.getCrewByCrewCode(workOrderTemplate.getCrew()).getData();
                if (crewWithMemberDTO == null) {
                    return ResultVO.error(500, "导入的班组编号" + workOrderTemplate.getShift() + "不存在");
                }

                hvPmWorkOrder.setQuantity(BigDecimal.valueOf(quantityInt));
                hvPmWorkOrder.setExecuteSequence(executeSequenceInt);
                hvPmWorkOrder.setPlanCode(workOrderTemplate.getPlanCode());
                hvPmWorkOrder.setWorkOrderCode(workOrderTemplate.getWorkOrderCode());
                hvPmWorkOrder.setPlanStartTime(simpleDateFormat.parse(workOrderTemplate.getPlanStartTime()));
                hvPmWorkOrder.setPlanEndTime(simpleDateFormat.parse(workOrderTemplate.getPlanEndTime()));
                hvPmWorkOrder.setShiftId(shiftDTO.getId());
                hvPmWorkOrder.setCrewId(crewWithMemberDTO.getId());
                hvPmWorkOrder.setAreaId(locationDTO.getId());
                hvPmWorkOrder.setShipNo(workOrderTemplate.getShipNo());
                hvPmWorkOrder.setShipModel(workOrderTemplate.getShipModel());
                hvPmWorkOrder.setSegmentationCode(workOrderTemplate.getSegmentationCode());
                hvPmWorkOrder.setUsedType(workOrderTemplate.getUsedType());
                // 插入数据库
                WorkOrderDTO orderManage = createOrderManage(DtoMapper.convert(hvPmWorkOrder, WorkOrderDTO.class));
            }
            /*List<HvPmFrameMaterial> userInfoData = data.stream().map(hvPmFrameMaterial -> {
            return hvPmFrameMaterial;
            }).collect(Collectors.toList());
            workOrderRepository.saveAll(hvPmWorkOrders);*/

        } catch (NumberFormatException e) {
            return ResultVO.error(500, "导入失败，物料数量和执行顺序必须为大于0的整数");
        } catch (ParseException e) {
            return ResultVO.error(500, "请输入正确格式的开始时间和结束时间，格式为yyyy-MM-dd HH:mm:ss");
        }

        return ResultVO.success("导入成功");
    }

    @Override
    public WorkOrderDTO findByWorkOrderCode(String workOrderCode) {
        return DtoMapper.convert(workOrderRepository.getByWorkOrderCode(workOrderCode), WorkOrderDTO.class);
    }

    @Override
    public WorkOrderDTO checkWorkOrderCodeBindRouteUnique(ArrayList<String> workOrderCodes) {
        List<HvPmWorkOrder> workOrders = orderMapper.findByWorkOrders(workOrderCodes);
        if (workOrders.size() != 1) {
            return null;
        }
        return DtoMapper.convert(workOrders.get(0), WorkOrderDTO.class);
    }

    @Override
    public List<String> getBatchNoListByMaterialGroupId(Integer materialGroupId) {
        // 查询该分组ID下的物料
        List<Integer> materialIdList = materialClient.getMaterialIdByMaterialGroupId(materialGroupId).getData();
        if (materialIdList.isEmpty()) {
            return new ArrayList<String>();
        }
        return orderMapper.getPlanCodeByMaterialIds(materialIdList);
    }

    @Override
    public Integer getWorkOrderCountByBatchNo(String batchNo) {
        return orderMapper.getWorkOrderCountByBatchNo(batchNo);
    }

    @Override
    public void updateCompleteSetCheckStatusByWorkOrderCode(WorkOrderDTO workOrderDTO) {
        orderMapper.updateCompleteSetCheckStatusByWorkOrderCode(workOrderDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendWorkOrder(SendWorkerOrderDTO senderOrderDTO) {
        // 根据物料分组查询批次号
        // 找到通批次同物料分组的工单
        List<HvPmWorkOrder> workOrderList = orderMapper.getWorkOrderByPlanCode(senderOrderDTO.getPlanCode());
        List<WorkOrderDTO> workOrderDTOs = workOrderList.stream().map(hvPmWorkOrder -> DtoMapper.convert(hvPmWorkOrder, WorkOrderDTO.class)).collect(Collectors.toList());
        List<Integer> ids = new ArrayList<>();

        WorkIssuedDTO workIssuedDTO = new WorkIssuedDTO();
        for (WorkOrderDTO workOrderDTO : workOrderDTOs) {
            workIssuedDTO.setWorkOrderDTO(workOrderDTO);
            ids.add(workOrderDTO.getId());
        }
        workIssuedDTO.setOrderIds(ids);
        batchIssued(workIssuedDTO);
//        // 添加到【型材切割计划】 取消了
//        Integer lineId = workIssuedDTO.getWorkOrderDTO().getCellId();
//        Date endTime = workIssuedDTO.getWorkOrderDTO().getPlanEndTime();
//        saveHvPmMaterialTaoPic(senderOrderDTO, lineId, endTime);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveWorkOrder(List<MesOrderDTO> orderDTO) {
        orderPoolTask(orderDTO);
    }

    private ResultVO<?> saveWithTask(List<MesOrderDTO> orderDTO) {
        WorkOrderDTO workOrder;
        //订单计划
        List<WorkOrderDTO> sendList = new ArrayList<>();
        for (MesOrderDTO mesOrderDTO : orderDTO) {

            //判断是否删除
            if (mesOrderDTO.getDeleteFlag() != null && mesOrderDTO.getDeleteFlag() == 1) {
                //判断是否能删除,需要是组立工单，并且是新建状态，而且是未齐套的才能删除
                HvPmWorkOrder hvPmWorkOrder = workOrderRepository.getHvPmWorkOrderByWorkOrderCode(mesOrderDTO.getWorkOrderCode());
                if (hvPmWorkOrder == null) {
                    throw new BaseKnownException("未找到工单编号为" + mesOrderDTO.getWorkOrderCode() + "的工单，删除失败");
                }
                if (hvPmWorkOrder.getUsedType() != 0) {
                    throw new BaseKnownException("工单编号为" + mesOrderDTO.getWorkOrderCode() + "的工单不是组立工单，无法删除");
                }
                if (hvPmWorkOrder.getWorkOrderState() != 0) {
                    throw new BaseKnownException("工单编号为" + mesOrderDTO.getWorkOrderCode() + "的工单不是新建工单，无法删除");
                }
                if (hvPmWorkOrder.getCompleteSetCheckStatus() == 1) {
                    throw new BaseKnownException("工单编号为" + mesOrderDTO.getWorkOrderCode() + "的工单已经齐套，无法删除");
                }
                //满足条件删除组立工单和相关的表
                //删除 hv_pm_plan_route_info表
                hvPmPlanRouteInfoService.deleteByPlanCode(mesOrderDTO.getWorkOrderCode());

                //删除 hv_pm_plan_product_bom表
                hvPmPlanProductBomService.deleteByPlanCode(mesOrderDTO.getWorkOrderCode());

                //删除工单表和关联表
                workOrderRepository.deleteById(hvPmWorkOrder.getId());
                workOrderExtend.deleteExtendInfo(hvPmWorkOrder.getId());
                //跳过下面的操作，继续循环
                continue;
            }
            //根据物料编码和特征值查询物料
            ResultVO<MaterialDTO> material = materialClient.getMaterialByMaterialCodeAndEigenvalue(mesOrderDTO.getMaterialCode(), "1");
            MaterialDTO materialDTO = material.getData();
            if (materialDTO == null) {
                throw new BaseKnownException("物料：" + mesOrderDTO.getMaterialCode() + "不存在！");
            }
            //工艺流向
            if (mesOrderDTO.getBlockCode() == null) {
                throw new BaseKnownException("工单" + mesOrderDTO.getWorkOrderCode() + "没有流向代码！");
            }
            //创建工单实例
            WorkOrderDTO workOrderDTO = new WorkOrderDTO();
            BeanUtils.copyProperties(mesOrderDTO, workOrderDTO);
            //默认全部下发
            workOrderDTO.setOrderMode(1);
            //来源 MES
            workOrderDTO.setComeFrom(0);
            //设置物料id
            workOrderDTO.setMaterialId(material.getData().getId());
            String blockCode = mesOrderDTO.getBlockCode();
            if (mesOrderDTO.getXc() == 1) {
                blockCode = "XC";
            } else {
                if ("1".equals(materialDTO.getMaterialTypeCode())) {
                    blockCode = "BC";
                } else {
                    if (!StringUtils.isBlank(blockCode)) {
                        if (blockCode.contains("P1") || blockCode.contains("P2")) {
                            blockCode = "P1/P2"; // 如果包含P1或P2，则设置blockCode为P1/P2
                        } else if (blockCode.contains("C2") || blockCode.contains("C4")) {
                            blockCode = "C2/C4"; // 如果包含C2或C4，则设置blockCode为C2/C4
                        } else if (blockCode.contains("C1") || blockCode.contains("C3")) {
                            blockCode = "C1/C3"; // 如果包含C1或C3，则设置blockCode为C1/C3
                        } else if (blockCode.contains("A1") || blockCode.contains("A2")) {
                            blockCode = "A1/A2"; // 如果包含A1或A2，则设置blockCode为A1/A2
                        } else {
                            blockCode = "TC";
                        }
                    }
                }
            }

            //根据物料id和流向获取生效版本的工艺路线
            ResultVO<RouteDTO> routeDTO = productRouteClient.getActiveProductRoute(materialDTO.getId(), blockCode);
            RouteDTO route = routeDTO.getData();
            if (route == null) {
                //根据工艺流向查询工艺路线
                ResultVO<RouteDTO> resultVO = productRouteClient.getRouteByDestination(blockCode);
                if (resultVO.getData() == null) {
                    throw new BaseKnownException("工艺流向：" + blockCode + "没有对应的工艺路线！");
                }
                route = resultVO.getData();
                ProductRouteDTO productRouteDTO = getProductRouteDTO(materialDTO, route);
                //新增产品和工艺路线关系
                ResultVO<Integer> vo = productRouteClient.addProductRoute(productRouteDTO);
                if (vo.getData() == null || vo.getData() == 0) {
                    throw new BaseKnownException("产品：" + materialDTO.getId() + "创建失败！" + vo.getMessage());
                }
                //把物料对应的工艺路线进行生效
                ResultVO<?> vo2 = productRouteClient.activeRoute(vo.getData());
                if (!vo2.isSuccess()) {
                    throw new BaseKnownException("工艺编号：" + route.getDestination() + "生效失败！");
                }
                workOrderDTO.setRouteId(vo.getData());
                workOrderDTO.setRouteCode(route.getDestination());
                workOrderDTO.setRouteName(route.getRouteName());
                workOrderDTO.setRouteVersion(route.getRouteVersion());

            }
            //存在工艺路线，直接设置工艺路线id
            workOrderDTO.setRouteId(route.getId());
            //船号
            workOrderDTO.setShipNo(mesOrderDTO.getShipNumber());
            //船型
            workOrderDTO.setShipModel(mesOrderDTO.getModel());
            //产品与bom
            String stage = (String) materialDTO.getExtend().get("stage");
            if ("2".equals(materialDTO.getMaterialTypeCode()) && mesOrderDTO.getBomCode() != null && "G".equals(stage)) {
                ResultVO<BaseBomDTO> bomDTO = bomClient.getBaseBomDTOByCodeAndEffect(mesOrderDTO.getBomCode(), 1);
                if (bomDTO.getData() == null) {
                    throw new BaseKnownException("组立：" + materialDTO.getMaterialCode() + "没有对应的BOM数据！");
                }
                ResultVO resultVO = bomClient.existsByMaterialId(materialDTO.getId());
                if (resultVO.getData() != null) {
                    BomMaterialDTO bomMaterialDTO = new BomMaterialDTO();
                    bomMaterialDTO.setBomCode(bomDTO.getData().getBomCode());
                    bomMaterialDTO.setMaterialId(materialDTO.getId());
                    bomMaterialDTO.setBomId(bomDTO.getData().getId());
                    bomMaterialDTO.setBomVersions(bomDTO.getData().getBomVersions());
                    materialsClient.createBomMaterial(bomMaterialDTO);
                } else {
                    log.error("接收mes工单中，根据物料id查询物料与bom关系异常");
                }
            }
            //计划开始时间
            if (mesOrderDTO.getPlanStartTime() != null) {
                workOrderDTO.setPlanStartTime(mesOrderDTO.getPlanStartTime());
            } else {
                // 获取今天的日期
                LocalDate today = LocalDate.now();
                // 获取明天的日期
                LocalDate tomorrow = today.plusDays(1);
                workOrderDTO.setPlanStartTime(Date.from(tomorrow.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            }
            //数量
            workOrderDTO.setQuantity(BigDecimal.valueOf(mesOrderDTO.getQuantity()));
            //工单类型
            workOrderDTO.setOrderTypeCode(mesOrderDTO.getOrderType());
            ResultVO<LocationDTO> hvBmLocation;
            if (route.getCells() == null || route.getCells().isEmpty()) {
                throw new BaseKnownException("工艺" + route.getRouteName() + "未配置产线！");
            }
            // 1是B线，0是A线
            if (flag) {
                route.setLineUsedFlag(variable);
            }
            Integer lineId = null;
            //多条线均分工单
            if (route.getCells().size() == 1) {
                lineId = route.getCells().get(0);
            } else if (route.getCells().size() > 1) {
                lineId = route.getLineUsedFlag() == 0 ? route.getCells().get(0) : route.getCells().get(1);
                route.setLineUsedFlag(route.getLineUsedFlag() == 0 ? 1 : 0);
            }
            if (lineId == null) {
                throw new BaseKnownException("工艺路线" + route.getRouteName() + "未配置产线！");
            }
            hvBmLocation = locationExtendClient.getLocationById(lineId);
            LocationDTO line = hvBmLocation.getData();
            if (line == null) {
                throw new BaseKnownException("工艺" + route.getRouteName() + "配置的产线已过时！请更新工艺路线！" + hvBmLocation.getMessage());
            }
            workOrderDTO.setCellId(lineId);
            //根据车间编号查询工厂建模获取车间id
            hvBmLocation = locationExtendClient.getHvBmLocationByCode(mesOrderDTO.getAreaCode());
            LocationDTO shop = hvBmLocation.getData();
            if (shop == null) {
                throw new BaseKnownException("车间AreaCode:" + mesOrderDTO.getAreaCode() + "不存在！" + "创建失败！" + hvBmLocation.getMessage());
            }
            //车间ID
            workOrderDTO.setAreaId(shop.getId());
            //获取组立bom
            List<MesBomInfoDTO> bomInfoList = mesOrderDTO.getBomInfoList();
            List<HvPmPlanProductBom> hvPmPlanProductBomList = new ArrayList<>();
            if (bomInfoList != null) {

                for (MesBomInfoDTO bomInfoDTO : bomInfoList) {
                    LambdaQueryWrapper<HvPmPlanProductBom> lqw = new LambdaQueryWrapper<>();
                    lqw.eq(HvPmPlanProductBom::getPlanCode, mesOrderDTO.getWorkOrderCode());
                    HvPmPlanProductBom one = hvPmPlanProductBomService.getOne(lqw);
                    if (one != null) {
                        continue;
                    }

                    HvPmPlanProductBom hvPmPlanProductBom = new HvPmPlanProductBom();
                    BeanUtils.copyProperties(bomInfoDTO, hvPmPlanProductBom);
                    hvPmPlanProductBom.setPlanCode(mesOrderDTO.getWorkOrderCode());
                    hvPmPlanProductBomList.add(hvPmPlanProductBom);
                }
                //批量添加工艺bom
                Integer i = hvPmPlanProductBomService.InsertHvPmPlanProductBomList(hvPmPlanProductBomList);
                if (i <= 0) {
                    throw new BaseKnownException("工艺BOM:" + hvPmPlanProductBomList + "创建失败！");
                }
            }
            //获取组立工艺路线
            List<MesRouteInfoDTO> routeInfoList = mesOrderDTO.getRouteInfoList();
            List<HvPmPlanRouteInfo> hvPmPlanRouteInfoList = new ArrayList<>();
            for (MesRouteInfoDTO routeInfoDTO : routeInfoList) {
                LambdaQueryWrapper<HvPmPlanRouteInfo> lqw = new LambdaQueryWrapper<>();
                lqw.eq(HvPmPlanRouteInfo::getPlanCode, mesOrderDTO.getWorkOrderCode());
                HvPmPlanRouteInfo one = hvPmPlanRouteInfoService.getOne(lqw);
                if (one != null) {
                    continue;
                }

                HvPmPlanRouteInfo hvPmPlanRouteInfo = new HvPmPlanRouteInfo();
                BeanUtils.copyProperties(routeInfoDTO, hvPmPlanRouteInfo);
                hvPmPlanRouteInfo.setSequence(routeInfoDTO.getIndex());
                hvPmPlanRouteInfo.setStepId(routeInfoDTO.getId());
                hvPmPlanRouteInfo.setPlanCode(workOrderDTO.getWorkOrderCode());
                hvPmPlanRouteInfoList.add(hvPmPlanRouteInfo);
            }
            //批量添加工艺
            Integer i = hvPmPlanRouteInfoService.InsertHvPmPlanRouteInfoList(hvPmPlanRouteInfoList);
            if (i <= 0) {
                throw new BaseKnownException("工艺:" + hvPmPlanRouteInfoList + "创建失败！");
            }

            //型材的切割计划 优先判断
            if (1 == mesOrderDTO.getXc()) {
                workOrderDTO.setUsedType(WorkOrderUsedTypeEnum.XC.getCode());
            }
            //判断零件工单还是组立工单   2组立1零件  UsedType组立工单是0 零件工单是1
            else if ("2".equals(materialDTO.getMaterialTypeCode())) {
                workOrderDTO.setUsedType(WorkOrderUsedTypeEnum.PRODUCT.getCode());
            } else if ("1".equals(materialDTO.getMaterialTypeCode())) {
                workOrderDTO.setUsedType(WorkOrderUsedTypeEnum.STEEL.getCode());
                //零件工单必填字段校验
                checkLJOrderMandatoryField(mesOrderDTO);
            }
            workOrderDTO.setWorkOrderState(WorkOrderStateEnum.NOT_ISSUED.getCode());

            //班次和班组
            ResultVO<List<ShiftDTO>> shiftVo = shiftClient.getShiftListByAreaIdAndCellId(workOrderDTO.getAreaId(), workOrderDTO.getCellId());
            ShiftDTO shiftDTO = null;
            for (ShiftDTO shift : shiftVo.getData()) {
                shiftDTO = shift;
            }
            if (shiftDTO == null) {
                throw new BaseKnownException("产线：" + line.getName() + "未配置班次信息！");
            }
            ResultVO<List<CrewWithMemberDTO>> crewListVo = crewClient.getCrewListByAreaIdAndCellId(workOrderDTO.getAreaId(), workOrderDTO.getCellId());
            if (crewListVo.isSuccess() && !crewListVo.getData().isEmpty()) {
                workOrderDTO.setCrewName(crewListVo.getData().get(0).getCrewName());
                workOrderDTO.setCrewId(crewListVo.getData().get(0).getId());
            } else {
                throw new BaseKnownException("班次/班组未配置");
            }
            workOrderDTO.setShiftId(shiftDTO.getId());
            workOrderDTO.setShiftName(shiftDTO.getShiftName());
            //更新
            productRouteClient.updateByRoute(route);
            workOrder = this.createOrderManage(workOrderDTO);
            if (workOrder == null) {
                throw new BaseKnownException("工单:" + workOrderDTO.getWorkOrderCode() + "创建失败！");
            }

            //型材工单下发
            //if (WorkOrderUsedTypeEnum.XC.getCode().equals(workOrderDTO.getUsedType())) {
            //    sendList.add(workOrder);
            //}
        }


        //if (!sendList.isEmpty()) {
        //    for (WorkOrderDTO workOrderDTO : sendList) {
        //        CommonPoolExecutor.execute(() -> {
        //            WorkIssuedDTO workIssuedDTO = new WorkIssuedDTO();
        //            workIssuedDTO.setOrderIds(Collections.singletonList(workOrderDTO.getId()));
        //            workIssuedDTO.setWorkOrderDTO(workOrderDTO);
        //            try {
        //                batchIssued(workIssuedDTO);//批量下发，下发失败与工单入库逻辑分开，即使失败也不回滚
        //            } catch (Exception e) {
        //                log.error("下发工单失败！{}", e.getMessage());
        //            }
        //        });
        //    }
        //}

        return ResultVO.success("");
    }

    //零件工单必填字段校验
    private void checkLJOrderMandatoryField(MesOrderDTO mesOrderDTO) {
//        if (mesOrderDTO.getParentMaterialCode() ==null || mesOrderDTO.getParentMaterialCode().isEmpty() ) {
//            throw new RuntimeException("工单号:"+mesOrderDTO.getWorkOrderCode()+"的父物料号（parentMaterialCode）不能为空!");
//        }
//        if (mesOrderDTO.getParentWorkOrderCode() ==null || mesOrderDTO.getParentWorkOrderCode().isEmpty() ) {
//            throw new RuntimeException("工单号:"+mesOrderDTO.getWorkOrderCode()+"的父工单号（parentWorkOrderCode）不能为空!");
//        }
        if (mesOrderDTO.getOutFlag() == null || mesOrderDTO.getOutFlag().isEmpty()) {
            throw new RuntimeException("工单号:" + mesOrderDTO.getWorkOrderCode() + "的是否外发（outFlag）不能为空!");
        }
        if (mesOrderDTO.getSpecialPurchaseTypeCode() == null || mesOrderDTO.getSpecialPurchaseTypeCode().isEmpty()) {
            throw new RuntimeException("工单号:" + mesOrderDTO.getWorkOrderCode() + "的自制外协（specialPurchaseTypeCode）不能为空!");
        }
    }

    private ProductRouteDTO getProductRouteDTO(MaterialDTO materialDTO, RouteDTO route) {
        ProductRouteDTO productRouteDTO = new ProductRouteDTO();
        productRouteDTO.setProductId(materialDTO.getId());
        productRouteDTO.setRouteId(route.getId());
        //工艺流向代码
        productRouteDTO.setRouteCode(route.getDestination());
        productRouteDTO.setRouteName(route.getRouteName());
        productRouteDTO.setRouteVersion(route.getRouteVersion());
        //设置为0，标识第一条产线
        productRouteDTO.setLineUsedFlag(0);
        return productRouteDTO;
    }

    private void orderPoolTask(List<MesOrderDTO> orderDTOs) {
        // 拆分、每个集合最多30条数据
        List<List<MesOrderDTO>> parts = splitList(orderDTOs, 30);
        String error = null;
        ExecutorService executor = Executors.newFixedThreadPool(20); // 创建线程池
        List<Future<ResultVO>> results = new ArrayList<>(); // 用来保存异步执行的结果

        try {
            // 提交任务
            for (List<MesOrderDTO> orders : parts) {
                Future<ResultVO> future = executor.submit(() -> {
                    try {
                        return saveWithTask(orders);
                    } catch (Exception e) {
                        log.error("保存失败{}", e.getMessage());
                        return ResultVO.error(e.getMessage());
                    }
                });
                results.add(future); // 保存结果
            }

            // 等待所有任务完成
            for (Future<ResultVO> result : results) {
                try {
                    ResultVO res = result.get(); // 获取任务结果
                    if (!res.isSuccess()) {
                        error = res.getMessage();
                    }
                } catch (InterruptedException e) {
                    // 处理异常，可能包括记录日志、重置中断状态等
                    Thread.currentThread().interrupt(); // 如果需要，重置中断状态
                    throw new BaseKnownException("处理订单时发生异常：" + e.getMessage());
                }
            }

            // 关闭线程池（可选地等待所有任务完成）
            executor.shutdown();
            // 如果需要，可以添加executor.awaitTermination(...)来等待所有任务完成

        } catch (Exception e) {
            // 这个catch块现在主要是为了捕获除了InterruptedException和ExecutionException之外的异常
            // 但由于上面的内部try-catch块，这里实际上不太可能被触发
            throw new BaseKnownException("程序执行异常！" + e.getMessage());
        }

        if (!StringUtils.isBlank(error)) {
            throw new BaseKnownException(error);
        }
    }

    /**
     * 工单非空判断
     *
     * @param hvPmWorkOrder 工单对象
     */
    private void workOrderNotNull(HvPmWorkOrder hvPmWorkOrder) {

        if (hvPmWorkOrder.getAreaId() == null) {
            throw new BaseKnownException(WorkOrderExceptionEnum.ORDER_NOT_AREA);
        }
        if (hvPmWorkOrder.getCellId() == null) {
            throw new BaseKnownException(WorkOrderExceptionEnum.ORDER_NOT_CELL);
        }
        if (hvPmWorkOrder.getCrewId() == null) {
            throw new BaseKnownException(WorkOrderExceptionEnum.ORDER_NOT_CREW);
        }
        if (hvPmWorkOrder.getShiftId() == null) {
            throw new BaseKnownException(WorkOrderExceptionEnum.ORDER_NOT_SHIFT);
        }
        if (hvPmWorkOrder.getRouteId() == null) {
            throw new BaseKnownException(WorkOrderExceptionEnum.ORDER_NOT_ROUTE);
        }
    }

    /**
     * 工单下发的工序创建
     *
     * @param hvPmWorkOrder 工单信息
     * @param routeDTO      获取参数信息对象
     * @param workCenter    产线下的工位列表
     */
    List<HvPmOrderOperation> createOperationByRouteStep(HvPmWorkOrder hvPmWorkOrder, RouteDTO routeDTO, ResultVO<List<LocationDTO>> workCenter, BigDecimal divide) {

        List<HvPmOrderOperation> operationList = new ArrayList<>();
        routeDTO.indexBreadthFirst();
        List<Node> nodes = routeDTO.findAllOperationNode();
        for (Node node : nodes) {
            // 如果是工艺操作  创建工序 工序物料 工序文件关系 工序参数
            HvPmOrderOperation orderOperationDTO = new HvPmOrderOperation();
            orderOperationDTO.setCellId(hvPmWorkOrder.getCellId());
            orderOperationDTO.setAreaId(hvPmWorkOrder.getAreaId());
            orderOperationDTO.setCrewId(hvPmWorkOrder.getCrewId());
            orderOperationDTO.setCrewName(hvPmWorkOrder.getCrewName());
            orderOperationDTO.setWorkOrderQuantity(hvPmWorkOrder.getQuantity());
            orderOperationDTO.setWorkOrderCode(hvPmWorkOrder.getWorkOrderCode());
            orderOperationDTO.setShiftId(hvPmWorkOrder.getShiftId());
            orderOperationDTO.setShiftName(hvPmWorkOrder.getShiftName());
            orderOperationDTO.setNodeCode(node.getCode());
            orderOperationDTO.setRouteOperationId(node.getData().getOperationId());
            orderOperationDTO.setOrderId(hvPmWorkOrder.getId());
            orderOperationDTO.setCreateTime(hvPmWorkOrder.getIssuedTime());
            orderOperationDTO.setOperationCode(node.getData().getOperationCode());
            orderOperationDTO.setOperationName(node.getData().getOperationName());
            orderOperationDTO.setMaterialCode(hvPmWorkOrder.getMaterialCode());
            orderOperationDTO.setMaterialName(hvPmWorkOrder.getMaterialName());
            orderOperationDTO.setSiteNum(hvPmWorkOrder.getSiteNum());
            orderOperationDTO.setSerialNum(hvPmWorkOrder.getWorkOrderCode() + "-" + node.getData().getCode());
            orderOperationDTO.setOperationOrder(node.getIndex());
            ResultVO<TemplateDTO> operationTemplate1 = routeTemplateClient.getOperationTemplate(node.getData().getOperationId(), 1);
            if (operationTemplate1.isSuccess()) {
                if (operationTemplate1.getData() != null) {
                    orderOperationDTO.setTemplateUrl(operationTemplate1.getData().getTemplateUrl());
                }
            }
            ResultVO<TemplateDTO> operationTemplate = routeTemplateClient.getOperationTemplate(node.getData().getOperationId(), 2);
            if (operationTemplate.isSuccess()) {
                if (operationTemplate.getData() != null) {
                    orderOperationDTO.setWebTemplateUrl(operationTemplate.getData().getTemplateUrl());
                }
            }
            // 获取产线下的工位数据
            List<LocationDTO> data = workCenter.getData();
            // 获取工艺下绑定的工位数据
            List<NodeWorkCenterData> nodeWorkCenterDataList = node.getData().getNodeWorkCenterData();

            if (nodeWorkCenterDataList == null) {
                throw new BaseKnownException(WorkOrderExceptionEnum.ROUTE_WORK_CENTER_NOT_FOUND);
            }
            if (nodeWorkCenterDataList.size() < 1) {
                throw new BaseKnownException(WorkOrderExceptionEnum.ROUTE_WORK_CENTER_NOT_FOUND);
            }
            if (data == null) {
                throw new BaseKnownException(WorkOrderExceptionEnum.WORK_CENTER_NOT_FOUND);
            }
            if (data.size() < 1) {
                throw new BaseKnownException(WorkOrderExceptionEnum.WORK_CENTER_NOT_FOUND);
            }

            // 获取 产线 工艺 下工位的ID
            List<Integer> collect = data.stream().map(SysBaseDTO::getId).collect(Collectors.toList());
            List<Integer> collect1 = nodeWorkCenterDataList.stream().map(NodeWorkCenterData::getWorkCenterId).collect(Collectors.toList());
            // 获取产线与工艺路线下工位ID交集
            collect.retainAll(collect1);
            // 判断是否找到对应工位，如果没有 抛出异常
            if (collect.size() == 0) {
                throw new BaseKnownException(WorkOrderExceptionEnum.WORK_CENTER_NOT_FOUND);
            } else if (collect.size() == 1) {
                // 如果找到唯一工位  直接赋值
                LocationDTO dto = data.stream().filter(t -> t.getId().equals(collect.get(0))).findFirst().get();
                orderOperationDTO.setWorkCenterId(dto.getId());
                orderOperationDTO.setWorkCenterName(dto.getName());
            } else {
                // 多台工位时 判断优先级
                List<NodeWorkCenterData> equipments = nodeWorkCenterDataList.stream().filter(t -> collect.stream().anyMatch(a -> a.equals(t.getWorkCenterId()))).collect(Collectors.toList());
                // 找出 最小优先级
                Integer priorty = Collections.min(equipments.stream().map(t -> t.getPriority()).collect(Collectors.toList()));
                // 根据最小优先级找工位
                List<NodeWorkCenterData> minPriEquip = equipments.stream().filter(t -> t.getPriority().equals(priorty)).collect(Collectors.toList());
                NodeWorkCenterData e;
                // 如果只有一台工位 直接赋值 ，多台工位 找ID最小的
                if (minPriEquip.size() == 1) {
                    e = minPriEquip.get(0);
                } else {
                    Integer id = Collections.min(equipments.stream().map(t -> t.getWorkCenterId()).collect(Collectors.toList()));
                    e = equipments.stream().filter(t -> t.getWorkCenterId().equals(id)).findFirst().get();
                }
                orderOperationDTO.setWorkCenterId(e.getWorkCenterId());
                orderOperationDTO.setWorkCenterName(e.getWorkCenterName());
            }
            // 加入父级工序ID  用于拆分工序区别使用  工单下发的工序初始值为0
            orderOperationDTO.setParentOrderId(0);
            HvPmOrderOperation orderOperation = orderOperationRepository.save(DtoMapper.convert(orderOperationDTO, HvPmOrderOperation.class));
            operationList.add(orderOperation);
            if (hvPmWorkOrder.getBomId() != null) {
                // 获取route服务的 step与operation合并后的物料数据
                List<NodeMaterialData> routeMaterial = node.getData().getNodeMaterialData();
                // 插入数据 与 工序ID
                if (routeMaterial != null) {
                    List<HvPmOperationMaterial> entityList = new ArrayList<>();
                    for (NodeMaterialData materialDTO : routeMaterial) {
                        HvPmOperationMaterial entity = new HvPmOperationMaterial();
                        entity.setMaterialId(materialDTO.getMaterialId());
                        entity.setMaterialCode(materialDTO.getMaterialCode());
                        entity.setMaterialEigenvalue(materialDTO.getEigenvalue());
                        entity.setMaterialName(materialDTO.getMaterialName());
                        entity.setOperationId(orderOperation.getId());
                        if (materialDTO.getQuantity() != null) {
                            entity.setPlanCount(BigDecimal.valueOf(materialDTO.getQuantity().multiply(divide).doubleValue()));
                        }
                        entityList.add(entity);
                        materialRepository.saveAll(entityList);
                    }
                }
            }
            // 工艺参数存储  创建工序参数
            List<NodeParameterData> nodeParameterData = node.getData().getNodeParameterData();

            if (nodeParameterData != null) {
                List<HvPmOperationParameter> list = new ArrayList<>();
                for (NodeParameterData parameterDTO : nodeParameterData) {
                    if (parameterDTO.getCollectionParameter() && "手动".equals(parameterDTO.getCollectionMethod())) {
                        HvPmOperationParameter entity = new HvPmOperationParameter();
                        entity.setDataType(parameterDTO.getParameterDataType());
                        entity.setOperationId(orderOperation.getId());
                        entity.setParameterCode(parameterDTO.getParameterCode());
                        entity.setParameterName(parameterDTO.getParameterName());
                        if (parameterDTO.getMaxValue() != null) {
                            entity.setMaxValue(parameterDTO.getMaxValue());
                        }
                        if (parameterDTO.getMinValue() != null) {
                            entity.setMinValue(parameterDTO.getMinValue());
                        }
                        if (parameterDTO.getDefaultValue() != null) {
                            entity.setDefaultValue(parameterDTO.getDefaultValue());
                        }
                        entity.setParameterVersion(1);
                        list.add(entity);
                    }
                }
                operationParameterRepository.saveAll(list);
            }

            // 添加工序与文件关联关系
            List<NodeFileData> fileDTOS = node.getData().getNodeFileData();
            if (fileDTOS != null) {
                List<Integer> fileIdList = fileDTOS.stream().map(NodeFileData::getFileId).collect(Collectors.toList());
                List<HvPmOperationFile> list = new ArrayList<>();
                for (int a : fileIdList) {
                    HvPmOperationFile fileEntity = new HvPmOperationFile();
                    fileEntity.setOperationId(orderOperation.getId());
                    fileEntity.setFileId(a);
                    list.add(fileEntity);
                }
                operationFileRepository.saveAll(list);
            }

        }
        return operationList;

    }

    /**
     * 工单下发的工序创建
     *
     * @param hvPmWorkOrder 工单信息
     * @param routeDTO      获取参数信息对象
     * @param workCenter    产线下的工位列表
     */
    List<HvPmOrderOperation> createOperationAndTaskByRouteStep(HvPmWorkOrder hvPmWorkOrder, RouteDTO routeDTO, ResultVO<List<LocationDTO>> workCenter, BigDecimal divide) {
        // 获取产线下的工位数据
        List<LocationDTO> data = workCenter.getData();
        if (data == null || data.size() < 1) {
            throw new BaseKnownException("产线下的工位未配置");
        }
        List<HvPmOrderOperation> operationList = new ArrayList<>();
        routeDTO.indexBreadthFirst();
        List<Node> nodes = routeDTO.findAllOperationNode();
        //增加线体工位结点过滤
        List<Node> nodes2 = new ArrayList<>();
        Set<String> stationCodeSet = new HashSet<>();
        for (LocationDTO station : data) {
            stationCodeSet.add(station.getCode());
        }
        //相同产线的工位下的结点
        for (Node node : nodes) {
            List<NodeWorkCenterData> nodeWorkCenterDataList = node.getData().getNodeWorkCenterData();
            for (NodeWorkCenterData nodeWorkCenterData : nodeWorkCenterDataList) {
                if (StringUtils.isBlank(nodeWorkCenterData.getWorkCenterCode())) {
                    throw new BaseKnownException("工序：" + node.getData().getOperationCode() + "未配置工位！");
                }
                if (stationCodeSet.contains(nodeWorkCenterData.getWorkCenterCode())) {
                    nodes2.add(node);
                }
                break;
            }
        }
        for (Node node : nodes2) {
            // 如果是工艺操作  创建工序 工序物料 工序文件关系 工序参数
            HvPmOrderOperation orderOperationDTO = new HvPmOrderOperation();
            orderOperationDTO.setCellId(hvPmWorkOrder.getCellId());
            orderOperationDTO.setAreaId(hvPmWorkOrder.getAreaId());
            orderOperationDTO.setCrewId(hvPmWorkOrder.getCrewId());
            orderOperationDTO.setCrewName(hvPmWorkOrder.getCrewName());
            orderOperationDTO.setWorkOrderQuantity(hvPmWorkOrder.getQuantity());
            orderOperationDTO.setWorkOrderCode(hvPmWorkOrder.getWorkOrderCode());
            orderOperationDTO.setShiftId(hvPmWorkOrder.getShiftId());
            orderOperationDTO.setShiftName(hvPmWorkOrder.getShiftName());
            orderOperationDTO.setNodeCode(node.getCode());
            orderOperationDTO.setRouteOperationId(node.getData().getOperationId());
            orderOperationDTO.setOrderId(hvPmWorkOrder.getId());
            orderOperationDTO.setCreateTime(hvPmWorkOrder.getIssuedTime());
            orderOperationDTO.setOperationCode(node.getData().getOperationCode());
            orderOperationDTO.setOperationName(node.getData().getOperationName());

            orderOperationDTO.setMaterialCode(hvPmWorkOrder.getMaterialCode());
            orderOperationDTO.setMaterialName(hvPmWorkOrder.getMaterialName());
            orderOperationDTO.setSiteNum(hvPmWorkOrder.getSiteNum());
            orderOperationDTO.setSerialNum(hvPmWorkOrder.getWorkOrderCode() + "-" + node.getData().getCode());
            orderOperationDTO.setOperationOrder(node.getIndex());
            ResultVO<TemplateDTO> operationTemplate1 = routeTemplateClient.getOperationTemplate(node.getData().getOperationId(), 3);
            if (operationTemplate1.isSuccess()) {
                if (operationTemplate1.getData() != null) {
                    orderOperationDTO.setTemplateUrl(operationTemplate1.getData().getTemplateUrl());
                }
            }
            ResultVO<TemplateDTO> operationTemplate = routeTemplateClient.getOperationTemplate(node.getData().getOperationId(), 1);
            if (operationTemplate.isSuccess()) {
                if (operationTemplate.getData() != null) {
                    orderOperationDTO.setWebTemplateUrl(operationTemplate.getData().getTemplateUrl());
                }
            }

            // 获取工艺下绑定的工位数据
            List<NodeWorkCenterData> nodeWorkCenterDataList = node.getData().getNodeWorkCenterData();

            if (nodeWorkCenterDataList == null) {
                throw new BaseKnownException(WorkOrderExceptionEnum.ROUTE_WORK_CENTER_NOT_FOUND);
            }
            if (nodeWorkCenterDataList.size() < 1) {
                throw new BaseKnownException(WorkOrderExceptionEnum.ROUTE_WORK_CENTER_NOT_FOUND);
            }


            // 获取 产线 工艺 下工位的ID
            List<Integer> collect = data.stream().map(SysBaseDTO::getId).collect(Collectors.toList());
            List<Integer> collect1 = nodeWorkCenterDataList.stream().map(NodeWorkCenterData::getWorkCenterId).collect(Collectors.toList());
            // 获取产线与工艺路线下工位ID交集
            collect.retainAll(collect1);
            // 判断是否找到对应工位，如果没有 抛出异常
            if (collect.size() == 0) {
                throw new BaseKnownException(WorkOrderExceptionEnum.WORK_CENTER_NOT_FOUND);
            } else if (collect.size() == 1) {
                // 如果找到唯一工位  直接赋值
                LocationDTO dto = data.stream().filter(t -> t.getId().equals(collect.get(0))).findFirst().get();
                orderOperationDTO.setWorkCenterId(dto.getId());
                orderOperationDTO.setWorkCenterName(dto.getName());
            } else {
                // 多台工位时 判断优先级
                List<NodeWorkCenterData> workCenterData = nodeWorkCenterDataList.stream().filter(t -> collect.stream().anyMatch(a -> a.equals(t.getWorkCenterId()))).collect(Collectors.toList());
                // 找出 最小优先级
                Integer priorty = Collections.min(workCenterData.stream().map(NodeWorkCenterData::getPriority).collect(Collectors.toList()));
                // 根据最小优先级找工位
                List<NodeWorkCenterData> minPriEquip = workCenterData.stream().filter(t -> t.getPriority().equals(priorty)).collect(Collectors.toList());
                NodeWorkCenterData e;
                // 如果只有一台工位 直接赋值 ，多台工位 找ID最小的
                if (minPriEquip.size() == 1) {
                    e = minPriEquip.get(0);
                } else {
                    Integer id = Collections.min(workCenterData.stream().map(NodeWorkCenterData::getWorkCenterId).collect(Collectors.toList()));
                    e = workCenterData.stream().filter(t -> t.getWorkCenterId().equals(id)).findFirst().get();
                }
                orderOperationDTO.setWorkCenterId(e.getWorkCenterId());
                orderOperationDTO.setWorkCenterName(e.getWorkCenterName());
            }
            // 加入父级工序ID  用于拆分工序区别使用  工单下发的工序初始值为0
            orderOperationDTO.setParentOrderId(0);
            HvPmOrderOperation save = orderOperationRepository.save(DtoMapper.convert(orderOperationDTO, HvPmOrderOperation.class));
            // 创建生产任务
            int taskId = createTask(save, OperationStateEnum.NOT_READY.getCode());
            int operationId = save.getId();
            operationList.add(save);
            if (hvPmWorkOrder.getBomId() != null) {
                // 获取route服务的 step与operation合并后的物料数据
                List<NodeMaterialData> routeMaterial = node.getData().getNodeMaterialData();
                // 插入数据 与 工序ID
                if (routeMaterial != null) {
                    List<HvPmOperationMaterial> entityList = new ArrayList<>();
                    for (NodeMaterialData materialDTO : routeMaterial) {
                        HvPmOperationMaterial entity = new HvPmOperationMaterial();
                        entity.setMaterialId(materialDTO.getMaterialId());
                        entity.setMaterialCode(materialDTO.getMaterialCode());
                        entity.setMaterialEigenvalue(materialDTO.getEigenvalue());
                        entity.setMaterialName(materialDTO.getMaterialName());
                        entity.setOperationId(operationId);
                        entity.setTaskId(taskId);
                        if (materialDTO.getQuantity() != null) {
                            entity.setPlanCount(BigDecimal.valueOf(materialDTO.getQuantity().multiply(divide).doubleValue()));
                        }
                        entityList.add(entity);
                    }
                    materialRepository.saveAll(entityList);
                }
            }
            // 工艺参数存储  创建工序参数
            List<NodeParameterData> nodeParameterData = node.getData().getNodeParameterData();
            if (nodeParameterData != null) {
                List<HvPmOperationParameter> list = new ArrayList<>();
                for (NodeParameterData parameterDTO : nodeParameterData) {
                    // 如果是下发参数,或者是采集参数，跳过
                    if (parameterDTO.getCollectionParameter() && "手动".equals(parameterDTO.getCollectionMethod())) {
                        HvPmOperationParameter dto = new HvPmOperationParameter();
                        dto.setDataType(parameterDTO.getParameterDataType());
                        dto.setOperationId(operationId);
                        dto.setParameterCode(parameterDTO.getParameterCode());
                        dto.setParameterName(parameterDTO.getParameterName());
                        if (parameterDTO.getMaxValue() != null) {
                            dto.setMaxValue(parameterDTO.getMaxValue());
                        }
                        if (parameterDTO.getMinValue() != null) {
                            dto.setMinValue(parameterDTO.getMinValue());
                        }
                        if (parameterDTO.getDefaultValue() != null) {
                            dto.setDefaultValue(parameterDTO.getDefaultValue());
                        }
                        dto.setParameterVersion(1);
                        dto.setTaskId(taskId);
                        list.add(dto);
                    }
                }
                operationParameterRepository.saveAll(list);
            }
            // 添加工序与文件关联关系
            List<NodeFileData> fileDTOS = node.getData().getNodeFileData();
            if (fileDTOS != null) {
                List<Integer> fileIdList = fileDTOS.stream().map(NodeFileData::getFileId).collect(Collectors.toList());
                for (int a : fileIdList) {
                    HvPmOperationFile fileInfo = new HvPmOperationFile();
                    fileInfo.setOperationId(operationId);
                    fileInfo.setFileId(a);
                    operationFileRepository.save(fileInfo);
                }
            }

        }
        return operationList;

    }

    /**
     * 创建就绪生产任务生产任务
     *
     * @param hvPmOrderOperation 生产任务对象
     */
    private int createTask(HvPmOrderOperation hvPmOrderOperation, Integer state) {
        HvPmOrderTask orderTask = DtoMapper.convert(hvPmOrderOperation, HvPmOrderTask.class);
        orderTask.setId(null);
        orderTask.setState(state);
        orderTask.setOperationId(hvPmOrderOperation.getId());
        orderTask.setTemplateUrl(hvPmOrderOperation.getTemplateUrl());
        HvPmOrderTask save = orderTaskRepository.save(orderTask);
        return save.getId();
    }

    /**
     * 创建虚拟工序 （工单正向追溯¬）
     *
     * @param hvPmWorkOrder 工单对象
     * @param list          工序列表
     * @param workCenter    工位信息
     * @param routeDTO      工艺路线
     * @param divide        生产数量
     */
    private void createFictitiousOperationByRouteStep(HvPmWorkOrder hvPmWorkOrder, List<OperationAllDTO> list, ResultVO<List<LocationDTO>> workCenter, RouteDTO routeDTO, BigDecimal divide) {
        routeDTO.indexBreadthFirst();
        List<Node> nodes = routeDTO.findAllOperationNode();
        // 排序step，遍历k
        for (Node node : nodes) {
            // 如果是工艺操作  创建工序 工序物料 工序文件关系 工序参数
            OperationAllDTO allDTO = new OperationAllDTO();
            allDTO.setCellId(hvPmWorkOrder.getCellId());
            allDTO.setAreaId(hvPmWorkOrder.getAreaId());
            allDTO.setCrewId(hvPmWorkOrder.getCrewId());
            allDTO.setCrewName(hvPmWorkOrder.getCrewName());
            allDTO.setWorkOrderQuantity(hvPmWorkOrder.getQuantity());
            allDTO.setWorkOrderCode(hvPmWorkOrder.getWorkOrderCode());
            allDTO.setShiftId(hvPmWorkOrder.getShiftId());
            allDTO.setShiftName(hvPmWorkOrder.getShiftName());
            allDTO.setOrderId(hvPmWorkOrder.getId());
            allDTO.setOperationOrder(node.getIndex());
            allDTO.setCreateTime(hvPmWorkOrder.getIssuedTime());
            allDTO.setOperationCode(node.getData().getRouteStepName());
            allDTO.setMaterialCode(hvPmWorkOrder.getMaterialCode());
            allDTO.setMaterialName(hvPmWorkOrder.getMaterialName());
            allDTO.setSerialNum(hvPmWorkOrder.getWorkOrderCode() + "-" + node.getData().getCode());
            allDTO.setState(OperationStateEnum.NOT_ISSUED.getCode());
            // 获取产线下的工位数据
            List<LocationDTO> data = workCenter.getData();
            // 获取工艺下绑定的工位数据
            List<NodeWorkCenterData> nodeWorkCenterDataList = node.getData().getNodeWorkCenterData();
            if (nodeWorkCenterDataList == null) {
                throw new BaseKnownException(WorkOrderExceptionEnum.ROUTE_WORK_CENTER_NOT_FOUND);
            }
            if (nodeWorkCenterDataList.size() < 1) {
                throw new BaseKnownException(WorkOrderExceptionEnum.WORK_CENTER_NOT_FOUND);
            }
            // 获取 产线 工艺 下工位的ID
            List<Integer> collect = data.stream().map(SysBaseDTO::getId).collect(Collectors.toList());
            List<Integer> collect1 = nodeWorkCenterDataList.stream().map(NodeWorkCenterData::getWorkCenterId).collect(Collectors.toList());
            // 获取产线与工艺路线下工位ID交集
            collect.retainAll(collect1);
            // 判断是否找到对应工位，如果没有 抛出异常
            if (collect.size() == 0) {
                throw new BaseKnownException(WorkOrderExceptionEnum.WORK_CENTER_NOT_FOUND);
            } else if (collect.size() == 1) {
                // 如果找到唯一工位  直接赋值
                LocationDTO dto = data.stream().filter(t -> t.getId().equals(collect.get(0))).findFirst().get();
                allDTO.setWorkCenterId(dto.getId());
                allDTO.setWorkCenterName(dto.getName());
            } else {
                // 多台工位时 判断优先级
                List<NodeWorkCenterData> workCenterData = nodeWorkCenterDataList.stream().filter(t -> collect.stream().anyMatch(a -> a.equals(t.getWorkCenterId()))).collect(Collectors.toList());
                // 找出 最小优先级
                Integer priorty = Collections.min(workCenterData.stream().map(NodeWorkCenterData::getPriority).collect(Collectors.toList()));
                // 根据最小优先级找工位
                List<NodeWorkCenterData> minPriEquip = workCenterData.stream().filter(t -> t.getPriority().equals(priorty)).collect(Collectors.toList());
                NodeWorkCenterData e;
                // 如果只有一台工位 直接赋值 ，多台工位 找ID最小的
                if (minPriEquip.size() == 1) {
                    e = minPriEquip.get(0);
                } else {
                    Integer id = Collections.min(workCenterData.stream().map(NodeWorkCenterData::getWorkCenterId).collect(Collectors.toList()));
                    e = workCenterData.stream().filter(t -> t.getWorkCenterId().equals(id)).findFirst().get();
                }
                allDTO.setWorkCenterId(e.getWorkCenterId());
                allDTO.setWorkCenterName(e.getWorkCenterName());
            }
            // 加入父级工序ID  用于拆分工序区别使用  工单下发的工序初始值为0
            allDTO.setParentOrderId(0);
            // 如果工单没有bom则不创建计划物料
            if (hvPmWorkOrder.getBomId() != null) {
                // 获取route服务的 step与operation合并后的物料数据
                List<NodeMaterialData> routeMaterial = node.getData().getNodeMaterialData();
                if (routeMaterial != null) {
                    List<OperationMaterialDTO> materialDTOList = new ArrayList<>();
                    // 插入数据 与 工序ID
                    for (NodeMaterialData materialDTO : routeMaterial) {
                        OperationMaterialDTO operationMaterialDTO = new OperationMaterialDTO();
                        operationMaterialDTO.setMaterialId(materialDTO.getMaterialId());
                        operationMaterialDTO.setMaterialCode(materialDTO.getMaterialCode());
                        operationMaterialDTO.setMaterialEigenvalue(materialDTO.getEigenvalue());
                        operationMaterialDTO.setMaterialName(materialDTO.getMaterialName());
                        if (materialDTO.getQuantity() != null) {
                            operationMaterialDTO.setPlanCount(BigDecimal.valueOf(materialDTO.getQuantity().multiply(divide).doubleValue()));
                        }
                        materialDTOList.add(operationMaterialDTO);
                    }
                    allDTO.setOperationMaterialDTOS(materialDTOList);
                } else {
                    allDTO.setOperationMaterialDTOS(new ArrayList<>());
                }
            }
            // 工艺参数存储  创建工序参数
            List<NodeParameterData> nodeParameterData = node.getData().getNodeParameterData();
            if (nodeParameterData != null) {
                List<OperationParameterDTO> parameterDTOList = new ArrayList<>();
                for (NodeParameterData parameterDTO : nodeParameterData) {
                    // 如果是下发参数,或者是采集参数，跳过
                    if (BooleanUtils.isTrue(parameterDTO.getIssueParameter())) {
                        continue;
                    }
                    if (BooleanUtils.isTrue(parameterDTO.getCollectionParameter())) {
                        continue;
                    }
                    HvPmOperationParameter dto = new HvPmOperationParameter();
                    dto.setDataType(parameterDTO.getParameterDataType());
                    dto.setParameterCode(parameterDTO.getParameterCode());
                    dto.setParameterName(parameterDTO.getParameterName());
                    if (parameterDTO.getMaxValue() != null) {
                        dto.setMaxValue(parameterDTO.getMaxValue());
                    }
                    if (parameterDTO.getMinValue() != null) {
                        dto.setMinValue(parameterDTO.getMinValue());
                    }
                    dto.setParameterVersion(1);
                    OperationParameterDTO convert = DtoMapper.convert(dto, OperationParameterDTO.class);
                    convert.setParameterVersionDTOS(new ArrayList<>());
                    parameterDTOList.add(convert);
                }
                allDTO.setOperationParameterDTOS(parameterDTOList);
            } else {
                allDTO.setOperationParameterDTOS(new ArrayList<>());
            }
            // 添加工序与文件关联关系
            List<NodeFileData> nodeFileData = node.getData().getNodeFileData();
            if (nodeFileData != null) {
                List<OperationFileDTO> fileDTOList = new ArrayList<>();
                nodeFileData.forEach(a -> {
                    OperationFileDTO operationFileDTO = new OperationFileDTO();
                    operationFileDTO.setFileId(a.getFileId());
                    fileDTOList.add(operationFileDTO);
                });
                allDTO.setFileDTOS(fileDTOList);
            } else {
                allDTO.setFileDTOS(new ArrayList<>());
            }
            allDTO.setOperationOutPutMaterialDTOS(new ArrayList<>());
            list.add(allDTO);
        }

    }

    /**
     * 获取今日0点0分
     *
     * @return 当前日期0点0分
     * @throws ParseException 解析异常
     */
    private Date getNowDate() throws ParseException {
        return getDate();
    }

    /**
     * 获取第二天凌晨
     *
     * @return 时间
     * @throws ParseException
     */
    private Date getEndDate() {
        return getEDate();
    }

    /**
     * 获取实际结束时间在今天的工单
     *
     * @return 工单列表
     * @throws ParseException 异常
     */
    private List<WorkOrderDTO> getTodayWorkOrder(Integer orderTypeId, Integer usedType) throws ParseException {
        // 根据实际完成时间查询工单 传参为今天凌晨和第二天凌晨00:00 查询今天的工单
        List<HvPmWorkOrder> hvPmWorkOrderByActualEndTime;
        if (orderTypeId != null && orderTypeId != 0) {
            hvPmWorkOrderByActualEndTime = workOrderRepository.getHvPmWorkOrderByActualEndTimeAndOrderTypeIdAAndUsedType(getNowDate(), getEndDate(), orderTypeId, usedType);
        } else {
            hvPmWorkOrderByActualEndTime = workOrderRepository.getHvPmWorkOrderByActualEndTimeAndUsedType(getNowDate(), getEndDate(), usedType);
        }
        return DtoMapper.convertList(hvPmWorkOrderByActualEndTime, WorkOrderDTO.class);
    }


    private Date getDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR, calendar.get(Calendar.HOUR) - 8);
        return calendar.getTime();
    }


    @Override
    public List<WorkOrderDTO> findNotIssueParentWorkOrderList() {
        return orderMapper.findNotIssueParentWorkOrderList();
    }

    @Override
    public List<WorkOrderDTO> findCompletenessOrders() {
        return orderMapper.findCompletenessOrders();
    }

    //下载导入模版
    @Override
    public ResultVO<ExcelExportDto> getImportTemplate() throws IOException, IllegalAccessException {
        HSSFWorkbook hssfWorkbook = new HSSFWorkbook();
        List<WorkOrderTemplate> workOrderTemplates = new ArrayList<>();
        List<WorkOrderRouteInfoTemplate> workOrderRouteInfoTemplates = new ArrayList<>();
        List<WorkOrderBomInfoTemplate> workOrderBomInfoTemplates = new ArrayList<>();
        ExcelUtil.addSheetToWorkBook(workOrderTemplates, WorkOrderConst.ZL_WORK_TEMPLATE_NAME, WorkOrderTemplate.class, null, hssfWorkbook);
        ExcelUtil.addSheetToWorkBook(workOrderRouteInfoTemplates, WorkOrderConst.ZL_WORK_TEMPLATE_ROUTE_NAME, WorkOrderRouteInfoTemplate.class, null, hssfWorkbook);
        ExcelUtil.addSheetToWorkBook(workOrderBomInfoTemplates, WorkOrderConst.ZL_WORK_TEMPLATE_BOM_NAME, WorkOrderBomInfoTemplate.class, null, hssfWorkbook);
        ResponseEntity<byte[]> responseEntity = ExcelUtil.generateHttpExcelFile(hssfWorkbook, WorkOrderConst.ZL_WORK_TEMPLATE_FILE_NAME);
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(responseEntity.getBody());
        excelExportDto.setFileName(WorkOrderConst.ZL_WORK_TEMPLATE_FILE_NAME);
        return ResultVO.success(excelExportDto);
    }

    /**
     * 导出组立工单
     *
     * @param query
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    @Override
    public ResultVO<ExcelExportDto> exportZLWorkOrder(OrderQuery query) throws IOException, IllegalAccessException {
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(getZLExportInfo(query).getBody());
        excelExportDto.setFileName(WorkOrderConst.ZL_WORK_EXPORT_FILE_NAME);
        return ResultVO.success(excelExportDto);
    }


    //获取组立工单导出的数据内容
    @ApiIgnore
    public ResponseEntity<byte[]> getZLExportInfo(OrderQuery query) throws IOException, IllegalAccessException {
        //工单主数据
        List<WorkOrderExportDTO> workOrderExportDTOS = orderMapper.getWorkOrderExportList(query);

        //获取工单编码列表
        List<String> workOrderCodes = workOrderExportDTOS.stream()
                .map(WorkOrderExportDTO::getWorkOrderCode)
                .collect(Collectors.toList());

        // 零件信息列表
        List<PlanProductBomExportDTO> planProductBomExportDTOS = DtoMapper.convertList(hvPmPlanProductBomService.getListByPlanCodes(workOrderCodes), PlanProductBomExportDTO.class);

        HSSFWorkbook hssfWorkbook = new HSSFWorkbook();
        ExcelUtil.addSheetToWorkBook(workOrderExportDTOS, WorkOrderConst.ZL_WORK_EXPORT_SHEET_NAME, WorkOrderExportDTO.class, null, hssfWorkbook);
        ExcelUtil.addSheetToWorkBook(planProductBomExportDTOS, WorkOrderConst.ZL_WORK_EXPORT_PLAN_PRODUCT_BOM_NAME, PlanProductBomExportDTO.class, null, hssfWorkbook);
        return ExcelUtil.generateHttpExcelFile(hssfWorkbook, WorkOrderConst.ZL_WORK_EXPORT_FILE_NAME);
    }

    /**
     * 导出板材工单
     *
     * @param query
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    @Override
    public ResultVO<ExcelExportDto> exportBCWorkOrder(OrderQuery query) throws IOException, IllegalAccessException {
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(getBCExportInfo(query).getBody());
        excelExportDto.setFileName(WorkOrderConst.BC_WORK_EXPORT_FILE_NAME);
        return ResultVO.success(excelExportDto);
    }


    //获取板材工单导出的数据内容
    @ApiIgnore
    public ResponseEntity<byte[]> getBCExportInfo(OrderQuery query) throws IOException, IllegalAccessException {
        //工单主数据
        List<WorkOrderExportDTO> workOrderExportDTOS = orderMapper.getWorkOrderExportList(query);
        List<BcWorkOrderExportDTO> bcWorkOrderExportDTOS = DtoMapper.convertList(workOrderExportDTOS, BcWorkOrderExportDTO.class);

        HSSFWorkbook hssfWorkbook = new HSSFWorkbook();
        ExcelUtil.addSheetToWorkBook(bcWorkOrderExportDTOS, WorkOrderConst.BC_WORK_EXPORT_SHEET_NAME, WorkOrderExportDTO.class, null, hssfWorkbook);
        return ExcelUtil.generateHttpExcelFile(hssfWorkbook, WorkOrderConst.BC_WORK_EXPORT_FILE_NAME);
    }


    /**
     * 导出型材工单
     *
     * @param query
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    @Override
    public ResultVO<ExcelExportDto> exportXCWorkOrder(OrderQuery query) throws IOException, IllegalAccessException {
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(getXCExportInfo(query).getBody());
        excelExportDto.setFileName(WorkOrderConst.XC_WORK_EXPORT_FILE_NAME);
        return ResultVO.success(excelExportDto);
    }


    //获取型材工单导出的数据内容
    @ApiIgnore
    public ResponseEntity<byte[]> getXCExportInfo(OrderQuery query) throws IOException, IllegalAccessException {
        //工单主数据
        List<WorkOrderExportDTO> workOrderExportDTOS = orderMapper.getWorkOrderExportList(query);
        List<XcWorkOrderExportDTO> xcWorkOrderExportDTOS = DtoMapper.convertList(workOrderExportDTOS, XcWorkOrderExportDTO.class);

        HSSFWorkbook hssfWorkbook = new HSSFWorkbook();
        ExcelUtil.addSheetToWorkBook(xcWorkOrderExportDTOS, WorkOrderConst.XC_WORK_EXPORT_SHEET_NAME, WorkOrderExportDTO.class, null, hssfWorkbook);
        return ExcelUtil.generateHttpExcelFile(hssfWorkbook, WorkOrderConst.XC_WORK_EXPORT_FILE_NAME);
    }

}

