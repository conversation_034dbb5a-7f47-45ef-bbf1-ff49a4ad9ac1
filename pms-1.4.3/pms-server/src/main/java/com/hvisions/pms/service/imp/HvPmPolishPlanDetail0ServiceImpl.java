package com.hvisions.pms.service.imp;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.pms.entity.plan.HvPmPolishPlanDetail0;
import com.hvisions.pms.plan.HvPmPolishPlanDetail0DTO;
import com.hvisions.pms.repository.plan.HvPmPolishPlanDetail0Repository;
import com.hvisions.pms.service.HvPmPolishPlanDetail0Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/9/23
 */
@Service
@Slf4j
public class HvPmPolishPlanDetail0ServiceImpl implements HvPmPolishPlanDetail0Service {

    @Resource
    private HvPmPolishPlanDetail0Repository hvPmPolishPlanDetail0Repository;

    @Override
    public long createDetail0(HvPmPolishPlanDetail0DTO hvPmPolishPlanDetail0DTO) {
        HvPmPolishPlanDetail0 hvPmPolishPlanDetail0 = DtoMapper.convert(hvPmPolishPlanDetail0DTO, HvPmPolishPlanDetail0.class);
        HvPmPolishPlanDetail0 detail0 = hvPmPolishPlanDetail0Repository.saveAndFlush(hvPmPolishPlanDetail0);
        return detail0.getId();
    }

    @Override
    public List<HvPmPolishPlanDetail0> getAllByCodeId(long codeId) {
        return hvPmPolishPlanDetail0Repository.getAllByCodeId(codeId);
    }

    @Override
    public long updateDetail0(HvPmPolishPlanDetail0DTO hvPmPolishPlanDetail0DTO) {
        HvPmPolishPlanDetail0 hvPmPolishPlanDetail0 = DtoMapper.convert(hvPmPolishPlanDetail0DTO, HvPmPolishPlanDetail0.class);
        HvPmPolishPlanDetail0 detail0 = hvPmPolishPlanDetail0Repository.save(hvPmPolishPlanDetail0);
        return detail0.getId();
    }

    @Override
    public void deleteDetail0ById(long id) {
        hvPmPolishPlanDetail0Repository.deleteById(id);
    }

    @Override
    public List<HvPmPolishPlanDetail0> getDetailDTOListByCodeId(long orderId) {
        return hvPmPolishPlanDetail0Repository.getAllByCodeId(orderId);
    }

    @Override
    public List<HvPmPolishPlanDetail0> getDetail0ListByCodeIds(List<Long> codeIds) {
        return hvPmPolishPlanDetail0Repository.findByCodeIds(codeIds);
    }

}
