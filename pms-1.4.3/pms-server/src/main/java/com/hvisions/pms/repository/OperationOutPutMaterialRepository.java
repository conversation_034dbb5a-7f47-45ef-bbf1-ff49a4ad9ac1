package com.hvisions.pms.repository;

import com.hvisions.pms.entity.HvPmOperationOutPutMaterial;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <p>Title: OperationOutPutMaterialRepository</p >
 * <p>Description: 产出物料仓储层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/14</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface OperationOutPutMaterialRepository extends JpaRepository<HvPmOperationOutPutMaterial, Integer> {


    /**
     * 根据工序ID查询产出物料信息
     *
     * @param operationId 工序ID
     * @return 产出物料信息列表
     */
    List<HvPmOperationOutPutMaterial> getAllByOperationId(int operationId);


    /**
     * 根据ID列表查询产出料信息
     *
     * @param idIn id列表
     * @return 产出料信息
     */
    List<HvPmOperationOutPutMaterial> getAllByIdIn(List<Integer> idIn);

    /**
     * 根据ID列表删除
     *
     * @param idList ID列表
     */
    void deleteByIdIn(List<Integer> idList);

    /**
     * 根据录入时间查询产出料
     *
     * @param newDate  开始时间
     * @param overTime 结束时间
     * @return 产出料列表
     */
    @Query(value = "select h from HvPmOperationOutPutMaterial h where h.createTime <= ?1 and h.createTime >= ?2")
    List<HvPmOperationOutPutMaterial> getAllByDate(Date newDate, Date overTime);

}
