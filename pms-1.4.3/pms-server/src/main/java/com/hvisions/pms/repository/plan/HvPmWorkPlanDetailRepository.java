package com.hvisions.pms.repository.plan;

import com.hvisions.pms.entity.plan.HvPmWorkPlanDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: HvPmWorkPlanDetailRepository</p >
 * <p>Description: 生产计划明细 repository</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/14</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */

@Repository
public interface HvPmWorkPlanDetailRepository extends JpaRepository<HvPmWorkPlanDetail, Integer> {

    /**
     * @param planId 计划Id
     * @return 计划明细集合
     */
    List<HvPmWorkPlanDetail> findByWorkPlanIdEquals(int planId);

    /**
     * 根据计划号 查询
     *
     * @param code 计划号
     * @return 计划号
     */
    HvPmWorkPlanDetail getByWorkPlanCode(String code);

    /**
     * 根据ID列表查询
     *
     * @param idList id列表
     * @return 生产计划明细
     */
    List<HvPmWorkPlanDetail> findAllByWorkPlanIdIn(List<Integer> idList);


    /**
     * @param orderId 工单id
     * @return 计划详情
     */
    HvPmWorkPlanDetail findByWorkOrderIdEquals(int orderId);

    /**
     * @param code 工单编号
     * @param num  流水号
     * @return 计划详情
     */
    HvPmWorkPlanDetail findByWorkOrderCodeAndSerialNumber(String code, int num);

    /**
     * @param planCode 计划号
     * @return 工单流水
     */
    @Query(value = "select max(serial_number) from hv_pm_work_plan_detail  where work_plan_code like  %?1%",
            nativeQuery = true)
    Integer getMaxSerialNumberByOrderCodeLike(String planCode);


    /**
     * @param orderCode 工单号
     * @return 计划详情
     */
    HvPmWorkPlanDetail findByWorkOrderCodeEquals(String orderCode);

    /**
     * 根据工单号删除计划详情
     *
     * @param workOrderCode 工单号
     */
    void deleteByWorkOrderCodeEquals(String workOrderCode);


}
