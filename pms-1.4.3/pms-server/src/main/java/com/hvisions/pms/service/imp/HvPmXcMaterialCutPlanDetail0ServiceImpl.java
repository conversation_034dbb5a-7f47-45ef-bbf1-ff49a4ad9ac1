package com.hvisions.pms.service.imp;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.pms.entity.plan.HvPmXcMaterialCutPlanDetail0;
import com.hvisions.pms.entity.plan.HvPmXcMaterialCutPlanDetail1;
import com.hvisions.pms.plan.HvPmXcMaterialCutPlanDetail0DTO;
import com.hvisions.pms.plan.HvPmXcMaterialCutPlanDetail1DTO;
import com.hvisions.pms.repository.plan.HvPmXcMaterialCutPlanDetail0Repository;
import com.hvisions.pms.repository.plan.HvPmXcMaterialCutPlanDetail1Repository;
import com.hvisions.pms.service.HvPmXcMaterialCutPlanDetail0Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/8/26
 */
@Service
@Slf4j
public class HvPmXcMaterialCutPlanDetail0ServiceImpl implements HvPmXcMaterialCutPlanDetail0Service {

    @Autowired
    private HvPmXcMaterialCutPlanDetail0Repository hvPmXcMaterialCutPlanDetail0Repository;

    @Autowired
    private HvPmXcMaterialCutPlanDetail1Repository hvPmXcMaterialCutPlanDetail1Repository;

    @Override
    public long createDetail0(HvPmXcMaterialCutPlanDetail0DTO hvPmXcMaterialCutPlanDetail0DTO) {
        HvPmXcMaterialCutPlanDetail0 hvPmXcMaterialCutPlanDetail0 = DtoMapper.convert(hvPmXcMaterialCutPlanDetail0DTO, HvPmXcMaterialCutPlanDetail0.class);
        HvPmXcMaterialCutPlanDetail0 detail0 = hvPmXcMaterialCutPlanDetail0Repository.saveAndFlush(hvPmXcMaterialCutPlanDetail0);
        return detail0.getId();
    }

    @Override
    public List<HvPmXcMaterialCutPlanDetail0> getAllDetail0ByOrderId(long orderId) {
        return hvPmXcMaterialCutPlanDetail0Repository.getAllByOrderId(orderId);
    }

    @Override
    public long updateDetail0(HvPmXcMaterialCutPlanDetail0DTO hvPmXcMaterialCutPlanDetail0DTO) {
        HvPmXcMaterialCutPlanDetail0 hvPmXcMaterialCutPlanDetail0 = DtoMapper.convert(hvPmXcMaterialCutPlanDetail0DTO, HvPmXcMaterialCutPlanDetail0.class);
        HvPmXcMaterialCutPlanDetail0 detail0 = hvPmXcMaterialCutPlanDetail0Repository.save(hvPmXcMaterialCutPlanDetail0);
        return detail0.getId();
    }

    @Override
    public void deleteDetail0ById(long id) {
        hvPmXcMaterialCutPlanDetail0Repository.deleteById(id);
    }

    @Override
    public List<HvPmXcMaterialCutPlanDetail0DTO> getDetailDTOListByOrderId(long orderId) {
        //获取detail0的数据
        List<HvPmXcMaterialCutPlanDetail0> detail0List = hvPmXcMaterialCutPlanDetail0Repository.getAllByOrderId(orderId);
        List<HvPmXcMaterialCutPlanDetail0DTO>  detail0DTOS = new ArrayList<>();
        if (!detail0List.isEmpty()) {
            detail0DTOS = DtoMapper.convertList(detail0List, HvPmXcMaterialCutPlanDetail0DTO.class);
            //获取detail1的数据
            for (HvPmXcMaterialCutPlanDetail0DTO detail0: detail0DTOS){
                List<HvPmXcMaterialCutPlanDetail1> detail1List = hvPmXcMaterialCutPlanDetail1Repository.getAllBySubPlanId(detail0.getId());
                if(!detail1List.isEmpty()){
                    List<HvPmXcMaterialCutPlanDetail1DTO>  detail1DTOS = DtoMapper.convertList(detail1List, HvPmXcMaterialCutPlanDetail1DTO.class);
                    detail0.setDetail1List(detail1DTOS);
                }

            }
        }
        return detail0DTOS;
    }

    /**
     * 根据切割计划id列表获取gen文件列表
     * @param orderIds
     * @return
     */
    @Override
    public List<HvPmXcMaterialCutPlanDetail0> getDetail0ListByOrderIds(List<Long> orderIds) {
        return hvPmXcMaterialCutPlanDetail0Repository.findByOrderIds(orderIds);
    }

}
