package com.hvisions.pms.repository;

import com.hvisions.pms.entity.HvPmMaterialForm;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <p>Title: MaterialFormRepository</p >
 * <p>Description: 物料消耗表单仓储层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/19</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface MaterialFormRepository extends JpaRepository<HvPmMaterialForm, Integer> {


    /**
     * 根据时间段查询消耗
     *
     * @param actualStartTime 计划开始时间
     * @param actualEndTime   计划结束时间
     * @return 物料投入清单
     */
    @Query(value = "select h from HvPmMaterialForm h where h.actualStartTime between ?1 and ?2")
    List<HvPmMaterialForm> getAllByDate(Date actualStartTime, Date actualEndTime);

}
