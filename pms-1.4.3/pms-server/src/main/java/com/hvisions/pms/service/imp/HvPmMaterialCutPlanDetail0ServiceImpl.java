package com.hvisions.pms.service.imp;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.pms.dao.HvPmMaterialCutPlanDetail0Mapper;
import com.hvisions.pms.entity.plan.HvPmMaterialCutPlanDetail0;
import com.hvisions.pms.plan.HvPmMaterialCutPlanDetail0DTO;
import com.hvisions.pms.repository.plan.HvPmMaterialCutPlanDetail0Repository;
import com.hvisions.pms.service.HvPmMaterialCutPlanDetail0Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class HvPmMaterialCutPlanDetail0ServiceImpl implements HvPmMaterialCutPlanDetail0Service {

    @Autowired
    private HvPmMaterialCutPlanDetail0Repository hvPmMaterialCutPlanDetail0Repository;
    @Autowired
    private HvPmMaterialCutPlanDetail0Mapper hvPmMaterialCutPlanDetail0Mapper;

    @Override
    public List<HvPmMaterialCutPlanDetail0> getAllDetail0ByCutPlanId(long id) {
        return hvPmMaterialCutPlanDetail0Repository.getAllByCutPlanId(id);
    }

    @Override
    public long createDetail0(HvPmMaterialCutPlanDetail0DTO hvPmMaterialCutPlanDetail0DTO) {
        HvPmMaterialCutPlanDetail0 hvPmMaterialCutPlanDetail0 = DtoMapper.convert(hvPmMaterialCutPlanDetail0DTO, HvPmMaterialCutPlanDetail0.class);
        HvPmMaterialCutPlanDetail0 detail0 = hvPmMaterialCutPlanDetail0Repository.saveAndFlush(hvPmMaterialCutPlanDetail0);
        return detail0.getId();
    }

    @Override
    public long updateDetail0(HvPmMaterialCutPlanDetail0DTO hvPmMaterialCutPlanDetail0DTO) {
        HvPmMaterialCutPlanDetail0 hvPmMaterialCutPlanDetail0 = DtoMapper.convert(hvPmMaterialCutPlanDetail0DTO, HvPmMaterialCutPlanDetail0.class);
        HvPmMaterialCutPlanDetail0 detail0 = hvPmMaterialCutPlanDetail0Repository.save(hvPmMaterialCutPlanDetail0);
        return detail0.getId();
    }

    @Override
    public void deleteDetail0ById(long id) {
        hvPmMaterialCutPlanDetail0Repository.deleteById(id);
    }

    @Override
    public List<HvPmMaterialCutPlanDetail0DTO> getAllDetail0DTOByCutPlanId(long id) {
        return hvPmMaterialCutPlanDetail0Mapper.getDetail0DTOListByCutPlanId(id);
    }

    @Override
    public List<HvPmMaterialCutPlanDetail0DTO> getAll() {
        return hvPmMaterialCutPlanDetail0Mapper.getAll();
    }

    /**
     * 根据钢板切割计划id 获取零件信息
     * @param cutPlanIds
     * @return
     */
    @Override
    public List<HvPmMaterialCutPlanDetail0> getDetail0ListByCutPlanIds(List<Long> cutPlanIds) {
        return hvPmMaterialCutPlanDetail0Repository.findByCutPlanIds(cutPlanIds);
    }

    @Override
    public List<HvPmMaterialCutPlanDetail0> getDetail0ListByMaterialCode(String materialCode) {
        return hvPmMaterialCutPlanDetail0Repository.findByMaterialCode(materialCode);
    }

    /**
     * 批量添加切割零件明细数据
     *
     * @param entities 零件明细数据
     * @return
     */
    @Override
    public List<HvPmMaterialCutPlanDetail0> createDetail0Batch(List<HvPmMaterialCutPlanDetail0> entities) {
        List<HvPmMaterialCutPlanDetail0> savedEntities = hvPmMaterialCutPlanDetail0Repository.saveAll(entities);
        hvPmMaterialCutPlanDetail0Repository.flush(); // 确保立即生成ID
        return savedEntities;
    }
}
