package com.hvisions.pms.controller;

import com.hvisions.common.annotation.EnableFilter;
import com.hvisions.pms.plan.HvPmWorkPlanDetailDTO;
import com.hvisions.pms.service.HvPmWorkPlanDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>Title: HvPmWorkPlanDetailController</p >
 * <p>Description: 生产计划明细 controller</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/14</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/WorkPlanDetail")
@Slf4j
@Api(description = "生产计划明细 controller")
public class WorkPlanDetailController {

    private final HvPmWorkPlanDetailService hvPmWorkPlanDetailService;

    @Autowired
    public WorkPlanDetailController(HvPmWorkPlanDetailService hvPmWorkPlanDetailService) {
        this.hvPmWorkPlanDetailService = hvPmWorkPlanDetailService;
    }


    /**
     * 根据生产计划id查询生产计划明细
     *
     * @param planId 计划编号
     * @return 计划明细集合
     */
    @RequestMapping(value = "/findWorkPlanDetailByPlanCode/{planId}", method = RequestMethod.GET)
    @ApiOperation(value = "根据生产计划编号查询生产计划明细")
    public List<HvPmWorkPlanDetailDTO> findWorkPlanDetailByPlanId(@PathVariable Integer planId) {

        return hvPmWorkPlanDetailService.findByPlanId(planId);
    }


    /**
     * 报工更新计划明细数量
     * @param code 工单号
     * @param num  工单id
     * @param quantity 报工数量
     */
    @EnableFilter
    @RequestMapping(value = "/orderFinish/{code}/{num}/{quantity}", method = RequestMethod.PUT)
    @ApiOperation(value = "报工")
    public void orderFinish(@PathVariable String code, @PathVariable Integer num, @PathVariable BigDecimal quantity) {
        hvPmWorkPlanDetailService.orderFinish(code, num, quantity);
    }

}