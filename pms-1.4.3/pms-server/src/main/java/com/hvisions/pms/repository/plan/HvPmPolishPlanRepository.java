package com.hvisions.pms.repository.plan;

import com.hvisions.pms.entity.plan.HvPmPolishPlan;
import com.hvisions.pms.entity.plan.HvPmXcMaterialCutPlan;
import com.hvisions.pms.plan.HvPmPolishPlanDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/8/26
 */
@Repository
public interface HvPmPolishPlanRepository extends JpaRepository<HvPmPolishPlan,Long> {

    /**
     * 是否存在 code
     * @param code
     * @return
     */
    HvPmPolishPlan getByCodeEquals(String code);

}
