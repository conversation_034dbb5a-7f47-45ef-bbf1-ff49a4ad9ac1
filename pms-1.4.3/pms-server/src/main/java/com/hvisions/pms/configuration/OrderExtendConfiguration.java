package com.hvisions.pms.configuration;

import com.hvisions.common.dto.ExtendInfoParam;
import com.hvisions.common.service.BaseExtendService;
import com.hvisions.common.utils.SqlFactoryUtil;
import com.hvisions.pms.consts.OrderConst;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <p>Title: OrderExtendConfiguration</p >
 * <p>Description: 工单扩展</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/1/29</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Configuration
public class OrderExtendConfiguration {


    @Bean
    SqlFactoryUtil getSqlFactory() {
        return new SqlFactoryUtil();
    }

    /**
     * 工单扩展服务
     *
     * @return 工单扩展服务
     */
    @Bean(value = "hv_pm_work_order_extend")
    BaseExtendService getRouteExtendService(SqlFactoryUtil getSqlFactory) {
        ExtendInfoParam extendInfoParam = new ExtendInfoParam();
        extendInfoParam.setOriginTableName(OrderConst.ORDER_MANAGE_TABLE_NAME);
        extendInfoParam.setOriginTableIdName(OrderConst.ORDER_MANAGE_EXTEND_ID);
        extendInfoParam.setExtendTableName(OrderConst.ORDER_MANAGE_EXTEND);
        return getSqlFactory.getSqlBridge(extendInfoParam);
    }
}
