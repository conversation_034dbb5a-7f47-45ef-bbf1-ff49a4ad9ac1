package com.hvisions.pms.controller;

import com.hvisions.common.annotation.EnableFilter;
import com.hvisions.pms.changeshiftsdto.BuildOperationDTO;
import com.hvisions.pms.inspectdto.AllInspectionItemDTO;
import com.hvisions.pms.inspectdto.CheckPointDTO;
import com.hvisions.pms.inspectdto.InspectionItemDTO;
import com.hvisions.pms.inspectdto.InspectionItemQueryDTO;
import com.hvisions.pms.service.InspectionItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: InspectionItemController</p >
 * <p>Description: 检查项目控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-10-14</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@Slf4j
@Api(description = "检查项目控制器")
@RequestMapping(value = "/inspectionItem")
public class InspectionItemController {


    private final InspectionItemService inspectionItemService;

    @Autowired
    public InspectionItemController(InspectionItemService inspectionItemService) {
        this.inspectionItemService = inspectionItemService;
    }

    /**
     * 新增检查项目
     *
     * @param inspectionItemDTO 检查项目对象¬
     */
    @EnableFilter
    @PostMapping(value = "/createInspectionItem")
    @ApiOperation(value = "新增检查项目")
    public void createInspectionItem(@RequestBody InspectionItemDTO inspectionItemDTO) {
        inspectionItemService.createInspectionItem(inspectionItemDTO);
    }

    /**
     * 更新检查项目
     *
     * @param inspectionItemDTO 检查项目对象
     */
    @EnableFilter
    @PutMapping(value = "/updateInspectionItem")
    @ApiOperation(value = "更新检查项目")
    public void updateInspectionItem(@RequestBody InspectionItemDTO inspectionItemDTO) {
        inspectionItemService.updateInspectionItem(inspectionItemDTO);
    }

    /**
     * 删除检查项目
     *
     * @param id 检查项目ID
     */
    @DeleteMapping(value = "/deleteInspectionItem/{id}")
    @ApiOperation(value = "删除检查项目")
    public void deleteInspectionItem(@PathVariable int id) {
        inspectionItemService.deleteInspectionItem(id);
    }

    /**
     * 根据Id查询检查项目
     *
     * @param id 检查项目ID
     * @return 检查项目信息
     */
    @GetMapping(value = "/getInspectionItemById/{id}")
    @ApiOperation(value = "根据Id查询检查项目")
    public AllInspectionItemDTO getInspectionItemById(@PathVariable int id) {
        return inspectionItemService.getInspectionItemById(id);
    }

    /**
     * 根据 条件分页查询检查项目
     *
     * @param inspectionItemQueryDTO 查询条件
     * @return 检查项目分页信息
     */
    @EnableFilter
    @PostMapping(value = "/getInsapectionItemByQuery")
    @ApiOperation(value = "根据 条件分页查询检查项目")
    public Page<AllInspectionItemDTO> getInsapectionItemByQuery(@RequestBody InspectionItemQueryDTO inspectionItemQueryDTO) {
        return inspectionItemService.getInspectionItemByQuery(inspectionItemQueryDTO);
    }

    /**
     * 新增检查点
     *
     * @param checkPointDTO 检查点对象
     */
    @EnableFilter
    @PostMapping(value = "/createCheckPoint")
    @ApiOperation(value = "新增检查点")
    public void createCheckPoint(@RequestBody CheckPointDTO checkPointDTO) {
        inspectionItemService.createCheckPoint(checkPointDTO);
    }

    /**
     * 新增检查点列表
     *
     * @param checkPointDTO 检查点对象列表
     */
    @EnableFilter
    @PostMapping(value = "/createCheckPointList")
    @ApiOperation(value = "新增检查点列表")
    public void createCheckPointList(@RequestBody List<CheckPointDTO> checkPointDTO) {
        inspectionItemService.createCheckPointList(checkPointDTO);
    }

    /**
     * 更新检查点
     *
     * @param checkPointDTO 检查点对象
     */
    @EnableFilter
    @PutMapping(value = "/updateCheckPoint")
    @ApiOperation(value = "更新检查点")
    public void updateCheckPoint(@RequestBody CheckPointDTO checkPointDTO) {
        inspectionItemService.updateCheckPoint(checkPointDTO);
    }

    /**
     * 删除检查点
     *
     * @param id 检查点ID
     */
    @DeleteMapping(value = "/deleteCheckPoint/{id}")
    @ApiOperation(value = "删除检查点")
    public void deleteCheckPoint(@PathVariable int id) {
        inspectionItemService.deleteCheckPoint(id);
    }

    /**
     * 根据检查项目ID查询检查点
     *
     * @param inspectionItemId 检查项目id
     * @return 检查点信息
     */
    @GetMapping(value = "/getCheckPointByItemId/{inspectionItemId}")
    @ApiOperation(value = "根据检查项目ID查询检查点")
    public List<CheckPointDTO> getCheckPointByItemId(@PathVariable int inspectionItemId) {
        return inspectionItemService.getCheckPointByItemId(inspectionItemId);
    }


    /**
     * 关联工位检查项目
     *
     * @param buildOperationDTO 关联关系对象
     */
    @PostMapping(value = "/buildInspectWorkCenter")
    @ApiOperation(value = "关联工位检查项目")
    public void buildInspectWorkCenter(@RequestBody BuildOperationDTO buildOperationDTO) {
        inspectionItemService.buildInspectWorkCenter(buildOperationDTO);
    }


    /**
     * 删除工位项目关联关系
     *
     * @param workCenterId 工位ID
     * @param itemId       检查项目ID
     */
    @DeleteMapping(value = "/deleteInspectWorkCenter/{workCenterId}/{itemId}")
    @ApiOperation(value = "删除工位检查项目关联关系")
    public void deleteInspectWorkCenter(@PathVariable int workCenterId, @PathVariable int itemId) {
        inspectionItemService.deleteInspectWorkCenter(workCenterId, itemId);
    }

    /**
     * 根据工位ID查询工位关联的检查项目
     *
     * @param workCenterId 工位ID
     * @return 检查项目信息
     */
    @PostMapping(value = "/getItemByWorkCenter/{workCenterId}")
    @ApiOperation(value = "根据工位ID查询工位关联的检查项目")
    public AllInspectionItemDTO getItemByWorkCenter(@PathVariable int workCenterId) {
        return inspectionItemService.getItemByWorkCenter(workCenterId);
    }
}