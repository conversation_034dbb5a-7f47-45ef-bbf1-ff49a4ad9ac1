package com.hvisions.pms.service;

import com.hvisions.pms.entity.plan.HvPmXcMaterialCutPlanDetail0;
import com.hvisions.pms.plan.HvPmXcMaterialCutPlanDetail0DTO;

import java.util.List;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/8/26
 */
public interface HvPmXcMaterialCutPlanDetail0Service {

    long createDetail0(HvPmXcMaterialCutPlanDetail0DTO hvPmXcMaterialCutPlanDetail0DTO);

    List<HvPmXcMaterialCutPlanDetail0> getAllDetail0ByOrderId(long id);

    long updateDetail0(HvPmXcMaterialCutPlanDetail0DTO hvPmXcMaterialCutPlanDetail0DTO);

    void deleteDetail0ById(long id);

    List<HvPmXcMaterialCutPlanDetail0DTO>  getDetailDTOListByOrderId(long id);

    List<HvPmXcMaterialCutPlanDetail0> getDetail0ListByOrderIds(List<Long> ids);

}
