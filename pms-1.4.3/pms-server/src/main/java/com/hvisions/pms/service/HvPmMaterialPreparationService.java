package com.hvisions.pms.service;

import com.hvisions.pms.dto.HvPmMaterialPreparationDTO;
import com.hvisions.pms.dto.HvPmMaterialPreparationQueryDTO;
import com.hvisions.pms.entity.HvPmMaterialPreparation;
import com.hvisions.thirdparty.common.dto.MaterialPreparationContentDTO;
import com.hvisions.thirdparty.common.dto.MaterialPreparationResultDTO;
import com.hvisions.thirdparty.common.dto.StockInfoDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * @Description HvPmMaterialPreparationService
 * <AUTHOR>
 * @Date 2024-05-21
 */
public interface HvPmMaterialPreparationService {

    Page<HvPmMaterialPreparationDTO> getPage(HvPmMaterialPreparationQueryDTO queryDTO);

    Long addHvPmMaterialPreparation(HvPmMaterialPreparationDTO hvPmMaterialPreparationDTO);

    void deleteHvPmMaterialPreparation(Long id);

    void updateHvPmMaterialPreparation(HvPmMaterialPreparationDTO hvPmMaterialPreparationDTO);

    HvPmMaterialPreparationDTO getHvPmMaterialPreparationById(Long id);

    List<HvPmMaterialPreparationDTO> getAll();

    void addMaterialPreparationPlan(MaterialPreparationContentDTO materialPreparationContentDTO);

    void updatePreparation(MaterialPreparationResultDTO materialPreparationResultDTO);

    HvPmMaterialPreparation getByWorkOrderCode(String workOrderCode);

    void updatePreparationStatus(String workOrderCode);

    HvPmMaterialPreparationDTO getPreparationByWorkOrderCode(String workOrderCode);

    void stockSyncXcOccupyStock(StockInfoDTO stockInfoDTO);
}
