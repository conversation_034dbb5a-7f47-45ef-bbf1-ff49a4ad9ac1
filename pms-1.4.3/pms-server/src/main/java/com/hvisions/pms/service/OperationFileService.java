package com.hvisions.pms.service;

import com.hvisions.pms.dto.OperationFileDTO;

import java.util.List;

/**
 * <p>Title: OperationFileService</p >
 * <p>Description: 工序文件关系服务层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/2/21</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface OperationFileService {

    /**
     * 根据工序文件关系id查询工序文件关系
     *
     * @param id 工序文件关系id
     * @return 工序文件关系
     */
    OperationFileDTO getOperationFileById(int id);

    /**
     * 根据工序ID查询
     *
     * @param operationId 工序ID
     * @return 工序文件关系
     */
    List<OperationFileDTO> getOperationFileByOperationId(int operationId);


    /**
     * 新增或修改工序文件关系
     *
     * @param operationFileDTO 工序文件关系
     * @return 工序文件关系id
     */
    int createOrUpdateOperationFile(OperationFileDTO operationFileDTO);


    /**
     * 根据ID列表删除工序文件关系
     *
     * @param idList ID列表
     */
    void deleteOperationFileByIdList(List<Integer> idList);

}
