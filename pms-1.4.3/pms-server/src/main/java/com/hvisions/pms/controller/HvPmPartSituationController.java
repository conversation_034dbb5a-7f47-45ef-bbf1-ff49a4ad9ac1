package com.hvisions.pms.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.pms.dto.HvPmPartSituationDTO;
import com.hvisions.pms.dto.HvPmPartSituationQueryDTO;
import com.hvisions.pms.service.HvPmPartSituationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

/**
 * <P>  零件情况 <P>
 *
 * <AUTHOR>
 * @date 2025/1/8
 */
@Slf4j
@Api(tags = "零件情况")
@RestController
@RequestMapping("/partSituation")
public class HvPmPartSituationController {

    @Autowired
    private HvPmPartSituationService hvPmPartSituationService;

    /**
     * 分页查询
     *
     * @param hvPmMaterialCutPlanQueryDTO
     * @return
     */
    @PostMapping("/getPage")
    @ApiOperation("分页模糊查询")
    public Page<HvPmPartSituationDTO> getPage(@RequestBody HvPmPartSituationQueryDTO hvPmMaterialCutPlanQueryDTO) {
        return hvPmPartSituationService.getPage(hvPmMaterialCutPlanQueryDTO);
    }

    /**
     * 导出零件情况
     * @param hvPmPartSituationQueryDTO
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    @ApiResultIgnore
    @PostMapping(value = "/exportPartSituation")
    @ApiOperation(value = "导出零件情况")
    public ResultVO<ExcelExportDto> exportPartSituation(@RequestBody HvPmPartSituationQueryDTO hvPmPartSituationQueryDTO) throws IOException, IllegalAccessException {
        return hvPmPartSituationService.exportPartSituation(hvPmPartSituationQueryDTO);
    }


    @ApiOperation("查询刷新时间")
    @GetMapping("/getRefreshTime")
    public String getRefreshTime(){
        return hvPmPartSituationService.getRefreshTime();
    }


    @ApiOperation("刷新列表")
    @PostMapping("/refreshPartSituation")
    public void refreshPartSituation(){
         hvPmPartSituationService.refreshPartSituation();
    }

}
