package com.hvisions.pms.service.imp;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.pms.dao.HvPmMaterialTaoPicDetailMapper;
import com.hvisions.pms.dto.HvPmMaterialTaoPicDetailDTO;
import com.hvisions.pms.dto.HvPmMaterialTaoPicDetailQueryDTO;
import com.hvisions.pms.entity.HvPmMaterialTaoPicDetail;
import com.hvisions.pms.repository.HvPmMaterialTaoPicDetailRepository;
import com.hvisions.pms.service.HvPmMaterialTaoPicDetailService;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.hvisions.common.utils.DtoMapper;
import java.util.List;
import java.util.Optional;



/**
* <p>Title: HvPmMaterialTaoPicDetailServiceImpl</p>
* <p>Description: </p>
* <p>Company: www.h-visions.com</p>
* <p>create date: 2024年4月19日</p>
*
* <AUTHOR>
* @version :1.0.0
*/

@Service
public class HvPmMaterialTaoPicDetailServiceImpl implements HvPmMaterialTaoPicDetailService {

    @Autowired
    private HvPmMaterialTaoPicDetailRepository hvPmMaterialTaoPicDetailRepository;

    @Autowired
    private HvPmMaterialTaoPicDetailMapper hvPmMaterialTaoPicDetailMapper;

    /**
    * 保存
    *
    * @param hvPmMaterialTaoPicDetailDTO HvPmMaterialTaoPicDetail
    *
    */
    @Override
    public void addHvPmMaterialTaoPicDetail(HvPmMaterialTaoPicDetailDTO hvPmMaterialTaoPicDetailDTO){
        hvPmMaterialTaoPicDetailRepository.save(DtoMapper.convert(hvPmMaterialTaoPicDetailDTO, HvPmMaterialTaoPicDetail.class));
    }

    /**
    * 通过id删除
    *
    * @param id 主键
    *
    */
    @Override
    public void deleteHvPmMaterialTaoPicDetail(Long id){
        hvPmMaterialTaoPicDetailRepository.deleteById(id);
    }

    /**
    * 修改
    *
    * @param hvPmMaterialTaoPicDetailDTO HvPmMaterialTaoPicDetail
    *
    */
    @Override
    public void updateHvPmMaterialTaoPicDetail(HvPmMaterialTaoPicDetailDTO hvPmMaterialTaoPicDetailDTO){
        hvPmMaterialTaoPicDetailRepository.save(DtoMapper.convert(hvPmMaterialTaoPicDetailDTO, HvPmMaterialTaoPicDetail.class));
    }

    /**
    * 获取
    *
    * @param id 主键
    * @return HvPmMaterialTaoPicDetail hvPmMaterialTaoPicDetailDTO HvPmMaterialTaoPicDetail
    */
    @Override
    public HvPmMaterialTaoPicDetailDTO getHvPmMaterialTaoPicDetailById(Long id){
        Optional<HvPmMaterialTaoPicDetail> optional = hvPmMaterialTaoPicDetailRepository.findById(id);
        return optional.map(hvPmMaterialTaoPicDetail -> DtoMapper.convert(hvPmMaterialTaoPicDetail, HvPmMaterialTaoPicDetailDTO.class)).orElse(null);
    }

    /**
    * 获取列表
    *
    * @return  hvPmMaterialTaoPicDetailDTO 列表
    */
    @Override
    public List<HvPmMaterialTaoPicDetailDTO> getAll(){
        return DtoMapper.convertList(hvPmMaterialTaoPicDetailRepository.findAll(),HvPmMaterialTaoPicDetailDTO.class);
    }

    @Override
    public List<HvPmMaterialTaoPicDetail> getByCutPlanId(Long id) {
        QueryWrapper<HvPmMaterialTaoPicDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tao_pic_id", id);
        return hvPmMaterialTaoPicDetailMapper.selectList(queryWrapper);
    }

    @Override
    public Page<HvPmMaterialTaoPicDetailDTO> getByTaoPicDetail(HvPmMaterialTaoPicDetailQueryDTO queryDTO) {
        return PageHelperUtil.getPage(hvPmMaterialTaoPicDetailMapper::getPage, queryDTO);
    }

    @Override
    public void saveBatch(List<HvPmMaterialTaoPicDetailDTO> detailDTOS) {
        hvPmMaterialTaoPicDetailRepository.saveAll(DtoMapper.convertList(detailDTOS,HvPmMaterialTaoPicDetail.class));
    }


}