package com.hvisions.pms.service.imp;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.pms.dao.HvPmMaterialCutPlanDetail2Mapper;
import com.hvisions.pms.entity.plan.HvPmMaterialCutPlanDetail2;
import com.hvisions.pms.plan.HvPmMaterialCutPlanDetail2DTO;
import com.hvisions.pms.repository.plan.HvPmMaterialCutPlanDetail2Repository;
import com.hvisions.pms.service.HvPmMaterialCutPlanDetail2Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/8/2
 */
@Service
@Slf4j
public class HvPmMaterialCutPlanDetail2ServiceImpl implements HvPmMaterialCutPlanDetail2Service {

    @Autowired
    private HvPmMaterialCutPlanDetail2Repository hvPmMaterialCutPlanDetail2Repository;


    @Autowired
    private HvPmMaterialCutPlanDetail2Mapper hvPmMaterialCutPlanDetail2Mapper;


    @Override
    public long createDetail2(HvPmMaterialCutPlanDetail2DTO hvPmMaterialCutPlanDetail2DTO) {
        HvPmMaterialCutPlanDetail2 addDetail2 = DtoMapper.convert(hvPmMaterialCutPlanDetail2DTO, HvPmMaterialCutPlanDetail2.class);
        HvPmMaterialCutPlanDetail2 detail2 = hvPmMaterialCutPlanDetail2Repository.saveAndFlush(addDetail2);
        return detail2.getId();
    }

    @Override
    public List<HvPmMaterialCutPlanDetail2> getAllDetail2ByCutPlanId(long id) {
        return hvPmMaterialCutPlanDetail2Repository.getAllByCutPlanId(id);
    }

    @Override
    public void createDetail2List(List<HvPmMaterialCutPlanDetail2DTO> dtoList) {
        for (HvPmMaterialCutPlanDetail2DTO dto:dtoList){
            HvPmMaterialCutPlanDetail2 addDetail2 = DtoMapper.convert(dto, HvPmMaterialCutPlanDetail2.class);
            HvPmMaterialCutPlanDetail2 detail2 = hvPmMaterialCutPlanDetail2Repository.saveAndFlush(addDetail2);
        }
    }

    @Override
    public HvPmMaterialCutPlanDetail2 getOneByCutPlanCodeAndOperationType(String cutPlanCode, String operationType) {
        return hvPmMaterialCutPlanDetail2Mapper.getOneByCutPlanCodeAndOperationType(cutPlanCode, operationType);
    }

    @Override
    public void createOrUpdateDetail2List(List<HvPmMaterialCutPlanDetail2DTO> dtoList) {
        for(HvPmMaterialCutPlanDetail2DTO dto:dtoList){
            HvPmMaterialCutPlanDetail2 detail2 = hvPmMaterialCutPlanDetail2Repository.findByCutPlanCodeAndOperationType(dto.getCutPlanCode(), dto.getOperationType());
            if(detail2 !=null ){
                dto.setId(detail2.getId());
            }
            createDetail2(dto);
        }
    }

    @Override
    public List<HvPmMaterialCutPlanDetail2> getDetail2ListByCutPlanIds(List<Long> cutPlanIds) {
        return hvPmMaterialCutPlanDetail2Repository.findByCutPlanIds(cutPlanIds);
    }

}
