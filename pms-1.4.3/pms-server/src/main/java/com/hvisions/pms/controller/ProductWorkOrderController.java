package com.hvisions.pms.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.EasyExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.bom.dto.BaseBomDTO;
import com.hvisions.hiperbase.bom.dto.BomAllDTO;
import com.hvisions.hiperbase.bom.dto.BomItemIncreaseDTO;
import com.hvisions.hiperbase.bom.dto.BomItemsAllDTO;
import com.hvisions.hiperbase.client.BomClient;
import com.hvisions.hiperbase.client.BomItemClient;
import com.hvisions.pms.dto.materialRequirement.HvPmMaterialBomItemDTO;
import com.hvisions.pms.dto.materialRequirement.HvPmMaterialBomItemQueryDTO;
import com.hvisions.pms.dto.materialRequirement.MaterialRequirementQueryDTO;
import com.hvisions.pms.dto.productWorkOrder.ProductWorkOrderDTO;
import com.hvisions.pms.dto.productWorkOrder.ProductWorkOrderQueryDTO;
import com.hvisions.pms.entity.HvPmMaterialBomItem;
import com.hvisions.pms.entity.HvPmMaterialRequirement;
import com.hvisions.pms.entity.productWorkOrder.ProductWorkOrder;
import com.hvisions.pms.service.HvPmMaterialBomItemService;
import com.hvisions.pms.service.MaterialRequirementService;
import com.hvisions.pms.service.ProductWorkOrderService;
import com.hvisions.pms.utils.SerialCodeUtilsV2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/productWorkOrder")
@Api(tags = "生产工单")
public class ProductWorkOrderController {
    @Autowired
    private ProductWorkOrderService productWorkOrderService;
    @Autowired
    private BomClient bomClient;
    @Autowired
    private BomItemClient bomItemClient;
    @Autowired
    private SerialCodeUtilsV2 serialCodeUtilsV2;
    @Autowired
    private MaterialRequirementService materialRequirementService;
    @Autowired
    private HvPmMaterialBomItemService materialBomItemService;

    @PostMapping("/addProductWorkOrder")
    @ApiOperation(value = "新增工单")
    @Transactional
    public ResultVO addProductWorkOrder(@RequestBody ProductWorkOrderDTO productWorkOrderDTO,  @UserInfo @ApiIgnore UserInfoDTO userInfo) {
        HvPmMaterialRequirement hvPmMaterialRequirement = new HvPmMaterialRequirement();
//        需求编号
        hvPmMaterialRequirement.setRequirementCode(serialCodeUtilsV2.generateCode("RequirementCode"));
//        工单编号
        if (productWorkOrderDTO.getProductWorkOrderCode() == null || productWorkOrderDTO.getProductWorkOrderCode().isEmpty()) {
            String productWorkOrderCode = serialCodeUtilsV2.generateCode("ProductWorkOrderCode");
            productWorkOrderDTO.setProductWorkOrderCode(productWorkOrderCode);
            hvPmMaterialRequirement.setWorkOrderCode(productWorkOrderCode);
        }else {
            hvPmMaterialRequirement.setWorkOrderCode(productWorkOrderDTO.getProductWorkOrderCode());
        }
//        目标产线
        hvPmMaterialRequirement.setCellId(productWorkOrderDTO.getProductionLineCode());
//        需求时间
        hvPmMaterialRequirement.setWorkPlanStartTime(productWorkOrderDTO.getPlanStartTime());
//        根据产品编号 对应 Bom 表的 material_id 获取 bom_id
//        ResultVO<Integer> bomId = bomClient.getBomIdByMaterialId(productWorkOrderDTO.getProductCode());
//        根据bom编码以及bom版本获取bom信息
        ResultVO<BaseBomDTO> baseBomDTOByCodeAndVersion = bomClient.getBaseBomDTOByCodeAndVersion(productWorkOrderDTO.getProductCode(),productWorkOrderDTO.getBomVersion());
        Integer BomId = baseBomDTOByCodeAndVersion.getData().getId();
//        根据BomId获取所有Bom的子项Bom
//        List<BomItemIncreaseDTO> bomItems = bomItemClient.getAllByBomId(bomId.getData()).getData();
        List<BomItemIncreaseDTO> bomItems = bomItemClient.getAllByBomId(BomId).getData();
//        计划数量
        BigDecimal planQuantity = new BigDecimal(productWorkOrderDTO.getPlanQuantity());
//        子项Bom列表
        List<HvPmMaterialBomItem> hvPmMaterialBomItems = new ArrayList<>();

        try {
            for (BomItemIncreaseDTO bomItemIncreaseDTO :bomItems) {
                HvPmMaterialBomItem hvPmMaterialBomItem = new HvPmMaterialBomItem();
//            物料编码
                hvPmMaterialBomItem.setMaterialCode(bomItemIncreaseDTO.getMaterialDTO().getMaterialCode());
//            物料名称
                hvPmMaterialBomItem.setMaterialName(bomItemIncreaseDTO.getMaterialDTO().getMaterialName());
//            物料单位名称
                hvPmMaterialBomItem.setMaterialUnitName(bomItemIncreaseDTO.getMaterialUnitName());
//            物料所需数量=工单计划生产数量*单台产品BOM中物料数量
//            BigDecimal requiredQuantity = bomItemIncreaseDTO.getMaterialDTO().getMaterialCount().multiply(planQuantity);
                BigDecimal requiredQuantity = bomItemIncreaseDTO.getBomItemCount().multiply(planQuantity);
                hvPmMaterialBomItem.setMaterialRequiredQuantity(requiredQuantity);
//            bomItem的物料需求编号
                hvPmMaterialBomItem.setMaterialRequirementCode(hvPmMaterialRequirement.getRequirementCode());
                hvPmMaterialBomItems.add(hvPmMaterialBomItem);
            }
        } catch (NullPointerException e) {
            return ResultVO.error("该Bom没有关联物料");
        }

//        物料清单-子项Bom使用关联表保存
        materialBomItemService.saveBatch(hvPmMaterialBomItems);
//        物料需求清单保存
        hvPmMaterialRequirement.setCreateTime(LocalDateTime.now());
        hvPmMaterialRequirement.setUpdateTime(LocalDateTime.now());
        hvPmMaterialRequirement.setCreatorName(userInfo.getUserName());
        hvPmMaterialRequirement.setUpdaterName(userInfo.getUserName());
        materialRequirementService.save(hvPmMaterialRequirement);
        ProductWorkOrder productWorkOrder = DtoMapper.convert(productWorkOrderDTO, ProductWorkOrder.class);
        productWorkOrder.setCreateTime(LocalDateTime.now());
        productWorkOrder.setUpdateTime(LocalDateTime.now());
        productWorkOrder.setCreatorName(userInfo.getUserName());
        productWorkOrder.setUpdaterName(userInfo.getUserName());
        return ResultVO.success(productWorkOrderService.save(productWorkOrder));
    }

    @GetMapping("/getProductWorkOrderById/{id}")
    @ApiOperation(value = "根据ID查询")
    public ProductWorkOrderDTO getProductWorkOrderById(@PathVariable Integer id) {
        ProductWorkOrder one = productWorkOrderService.getOne(new LambdaQueryWrapper<ProductWorkOrder>().eq(ProductWorkOrder::getId, id));
        return DtoMapper.convert(one, ProductWorkOrderDTO.class);
    }

    @GetMapping("/list")
    @ApiOperation(value = "列表查询")
    public List<ProductWorkOrderDTO> list() {
        return DtoMapper.convertList(productWorkOrderService.list(), ProductWorkOrderDTO.class);
    }

    @PostMapping("/getProductWorkOrderPage")
    @ApiOperation(value = "分页查询")
    public Page<ProductWorkOrderDTO> getProductWorkOrderPage(@RequestBody ProductWorkOrderQueryDTO productWorkOrderQueryDTO) {
        return productWorkOrderService.getPage(productWorkOrderQueryDTO);
    }

    /**
     * 导出
     *
     * @param productWorkOrderQueryDTO
     */
    @ApiOperation(value = "导出")
    @PostMapping(value = "/exportData")
    @ApiResultIgnore
    public ResultVO<ExcelExportDto> exportData(@RequestBody ProductWorkOrderQueryDTO productWorkOrderQueryDTO) {
        List<ProductWorkOrder> list =  productWorkOrderService.findListByCondition(productWorkOrderQueryDTO);
        return ResultVO.success(EasyExcelUtil.getExcel(list, ProductWorkOrder.class, System.currentTimeMillis() + "生产工单数据.xlsx"));
    }



}
