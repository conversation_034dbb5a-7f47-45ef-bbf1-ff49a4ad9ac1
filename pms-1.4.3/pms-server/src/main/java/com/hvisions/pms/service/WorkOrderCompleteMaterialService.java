package com.hvisions.pms.service;

import com.hvisions.pms.dto.HvPmWorkOrderCompleteMaterialDTO;
import com.hvisions.pms.dto.WorkOrderCompleteMaterialQueryDTO;
import org.springframework.data.domain.Page;

/**
 * <P>    <P>
 *
 * <AUTHOR>
 * @date 2024/12/6
 */
public interface WorkOrderCompleteMaterialService {
    long addWorkOrderCompleteMaterial(HvPmWorkOrderCompleteMaterialDTO workOrderCompleteMaterialDTO);

    Page<HvPmWorkOrderCompleteMaterialDTO> getAllCompleteMaterial(WorkOrderCompleteMaterialQueryDTO workOrderCompleteMaterialQueryDTO);

    void deleteCompleteMaterialById(long id);

    long upWorkOrderCompleteMaterial(HvPmWorkOrderCompleteMaterialDTO workOrderCompleteMaterialDTO);
}
