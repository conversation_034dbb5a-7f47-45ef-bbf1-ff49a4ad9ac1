package com.hvisions.pms.repository;

import com.hvisions.pms.entity.HvPmOperationParameter;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: OperationParameterRepository</p >
 * <p>Description: 工序参数仓储层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/2/20</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface OperationParameterRepository extends JpaRepository<HvPmOperationParameter, Integer> {
    /**
     * 根据工序ID查询工序参数列表
     *
     * @param operationId 工序ID
     * @return 工序参数列表
     */
    List<HvPmOperationParameter> getByOperationId(int operationId);

    /**
     * 根据ID列表删除工序参数
     *
     * @param idIn ID列表
     */
    void deleteByIdIn(List<Integer> idIn);

    /**
     * 根据参数用途查询工序
     *
     * @param parameterUsage 参数用途
     * @return 工序信息列表
     */
    List<HvPmOperationParameter> getByParameterUsage(int parameterUsage);


    /**
     * 根据工序ID， 参数版本查询参数信息
     *
     * @param operationId      工序ID
     * @param parameterVersion 工序参数版本
     * @return 工序参数信息列表
     */
    List<HvPmOperationParameter> getByOperationIdAndParameterVersion(int operationId, int parameterVersion);

    /**
     * 根据工序ID与参数用途查询
     *
     * @param operationId    工序ID
     * @param parameterUsage 参数用途
     * @return 工序参数
     */
    HvPmOperationParameter getByOperationIdAndParameterUsage(int operationId, int parameterUsage);

    /**
     * 根据参数编码和名称查找参数列表
     *
     * @param code        编码
     * @param operationId 工序id
     * @return 参数列表
     */
    List<HvPmOperationParameter> getAllByParameterCodeAndOperationId(String code, int operationId);
}
