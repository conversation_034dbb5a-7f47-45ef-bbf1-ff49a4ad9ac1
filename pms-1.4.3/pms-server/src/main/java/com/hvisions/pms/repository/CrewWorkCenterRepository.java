package com.hvisions.pms.repository;

import com.hvisions.pms.entity.HvPmCrewWorkCenter;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>Title: CrewEquipmentRepository</p >
 * <p>Description: 班组工位绑定关系</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/13</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface CrewWorkCenterRepository extends JpaRepository<HvPmCrewWorkCenter, Integer> {

    /**
     * 根据班组ID和工位ID删除关联关系
     *
     * @param workCenterId 工位ID
     * @param crewId       用户ID
     */
    @Transactional(rollbackFor = Exception.class)
    @Modifying
    void deleteByWorkCenterIdAndCrewId(int workCenterId, int crewId);

    @Transactional(rollbackFor = Exception.class)
    @Modifying
    void deleteByIdIn(List<Integer> idIn);

    /**
     * 根据班组ID查询关联的工位列表
     *
     * @param crewId 人员ID
     * @return 工位列表
     */
    List<HvPmCrewWorkCenter> getByCrewId(int crewId);

    /**
     * 根据工位ID查找关联关系
     *
     * @param workCenterId 工位ID
     * @return 关联关系列表
     */
    List<HvPmCrewWorkCenter> getAllByWorkCenterId(int workCenterId);
}
