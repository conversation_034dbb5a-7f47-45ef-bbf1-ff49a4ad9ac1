package com.hvisions.pms.controller;

import com.hvisions.pms.entity.HvPmMaterialCutPlanProcessRecord;
import com.hvisions.pms.service.MaterialCutPlanProcessRecordService;
import com.hvisions.thirdparty.common.dto.AGVStatusDTO;
import com.hvisions.thirdparty.common.dto.DetailReportDTO;
import com.hvisions.thirdparty.common.dto.IWMSCallMaterialDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-06-28 9:02
 */

@RestController
@RequestMapping(value = "/materialCutPlanProcessRecord")
@Api(description = "切割计划流程日志")
public class MaterialCutPlanProcessRecordController {
    @Autowired
    private MaterialCutPlanProcessRecordService materialCutPlanProcessRecordService;

    /**
     * 查询全部
     *
     * @return 列表
     */
    @ApiOperation(value = "查询全部")
    @GetMapping(value = "/getAll")
    public List<HvPmMaterialCutPlanProcessRecord> getAll() {
        return materialCutPlanProcessRecordService.getAll();
    }

    /**
     * 根据线体编号查询
     *
     * @return 列表
     */
    @ApiOperation(value = "根据线体编号查询")
    @GetMapping(value = "/findByLineCode/{lineCode}")
    public List<HvPmMaterialCutPlanProcessRecord> findByLineCode(@PathVariable String lineCode) {
        return materialCutPlanProcessRecordService.findByLineCode(lineCode);
    }

    /**
     * 添加线体数据
     *
     * @return 列表
     */
    @ApiOperation(value = "添加线体数据")
    @PostMapping(value = "/addData")
    public boolean addData() {
        return materialCutPlanProcessRecordService.addData();
    }


    /**
     * 更新数据
     *
     * @return 列表
     */
    @ApiOperation(value = "更新数据")
    @PostMapping(value = "/updateData")
    public boolean updateData(@RequestBody HvPmMaterialCutPlanProcessRecord hvPmMaterialCutPlanProcessRecord) {
            return materialCutPlanProcessRecordService.updateData(hvPmMaterialCutPlanProcessRecord);
    }


    /**
     * 切割线步骤 2-6 状态变更
     *
     * @return 列表
     */
    @ApiOperation(value = "更新数据")
    @PostMapping(value = "/updateDataForQgTwoToSixStep")
    public boolean updateDataForQgTwoToSixStep(@RequestBody DetailReportDTO detailReportDTO) {
        return materialCutPlanProcessRecordService.updateDataForQgTwoToSixStep(detailReportDTO);
    }


    /**
     * AGV调度任务结果反馈 状态变更
     *
     * @return 列表
     */
    @ApiOperation(value = "更新数据")
    @PostMapping(value = "/updateDataForAGVStatusDTO")
    public void updateDataForAGVStatusDTO(@RequestBody AGVStatusDTO agvStatusDTO) {
        materialCutPlanProcessRecordService.updateDataForAGVStatusDTO(agvStatusDTO);
    }

    /**
     * 满框调度叫料出库 状态变更
     *
     * @return 列表
     */
    @ApiOperation(value = "更新数据")
    @PostMapping(value = "/updateDataForIWMSCallMaterialDTO")
    public void updateDataForIWMSCallMaterialDTO(@RequestBody IWMSCallMaterialDTO callMaterialDTO) {
        materialCutPlanProcessRecordService.updateDataForIWMSCallMaterialDTO(callMaterialDTO);
    }

}
