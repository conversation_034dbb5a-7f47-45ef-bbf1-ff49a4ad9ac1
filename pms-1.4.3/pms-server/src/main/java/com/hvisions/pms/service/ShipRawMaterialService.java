package com.hvisions.pms.service;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.pms.dto.ShipRawMaterialDTO;
import com.hvisions.pms.dto.ShipRawMaterialQueryDTO;
import com.hvisions.pms.entity.HvPmShipRawMaterial;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * <AUTHOR>
 * @Date 2024/5/22
 */
public interface ShipRawMaterialService {
    /**
     * 分页查询
     * @param query
     * @return
     */
    Page<ShipRawMaterialDTO> getPage(ShipRawMaterialQueryDTO query);

    /**
     * 添加
     * @param shipRawMaterialDTO
     * @return
     */
    Long createShipRawMaterial(ShipRawMaterialDTO shipRawMaterialDTO);

    /**
     * 修改
     * @param shipRawMaterialDTO
     * @return
     */
    Long updateShipRawMaterial(ShipRawMaterialDTO shipRawMaterialDTO);

    /**
     * 删除
     * @param id
     */
    void deleteShipRawMaterial(Long id);

    /**
     * 获取模板
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    ResultVO<ExcelExportDto> getImportTemplate() throws IOException,IllegalAccessException;

    /**
     * 导入
     * @param file
     * @return
     * @throws IllegalAccessException
     * @throws IOException
     */
    ImportResult importShipRawMaterial(MultipartFile file) throws IllegalAccessException,IOException;

    /**
     * 根据船型和分段查找
     * @param shipModel
     * @param segmentationCode
     * @return
     */
    HvPmShipRawMaterial getByShipModelAndSegmentationCode(String shipModel,String segmentationCode);
}
