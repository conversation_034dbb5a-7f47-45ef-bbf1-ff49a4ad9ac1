package com.hvisions.pms.repository.plan;

import com.hvisions.pms.entity.plan.HvPmXcMaterialCutPlan;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/8/26
 */
@Repository
public interface HvPmXcMaterialCutPlanRepository extends JpaRepository<HvPmXcMaterialCutPlan,Long> {

    /**
     * 是否存在orderNo
     * @param orderNo
     * @return
     */
    HvPmXcMaterialCutPlan getByOrderNoEquals(String orderNo);

}
