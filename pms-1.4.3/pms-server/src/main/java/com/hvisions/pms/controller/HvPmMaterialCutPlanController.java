package com.hvisions.pms.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.pms.entity.plan.HvPmMaterialCutPlan;
import com.hvisions.pms.entity.plan.HvPmMaterialCutPlanDetail0;
import com.hvisions.pms.entity.plan.HvPmMaterialCutPlanDetail1;
import com.hvisions.pms.plan.HvPmMaterialCutPlanDTO;
import com.hvisions.pms.plan.HvPmMaterialCutPlanDetail0DTO;
import com.hvisions.pms.plan.HvPmMaterialCutPlanDetail1DTO;
import com.hvisions.pms.plan.HvPmMaterialCutPlanQueryDTO;
import com.hvisions.pms.repository.plan.HvPmMaterialCutPlanRepository;
import com.hvisions.pms.service.HvPmMaterialCutPlanDetail0Service;
import com.hvisions.pms.service.HvPmMaterialCutPlanDetail1Service;
import com.hvisions.pms.service.HvPmMaterialCutPlanDetail2Service;
import com.hvisions.pms.service.HvPmMaterialCutPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Api(tags = "钢板切割计划控制器")
@RestController
@RequestMapping("/materialCutPlan")
public class HvPmMaterialCutPlanController {

    @Autowired
    private HvPmMaterialCutPlanService hvPmMaterialCutPlanService;
    @Autowired
    private HvPmMaterialCutPlanDetail0Service hvPmMaterialCutPlanDetail0Service;
    @Autowired
    private HvPmMaterialCutPlanDetail1Service hvPmMaterialCutPlanDetail1Service;
    @Autowired
    private HvPmMaterialCutPlanDetail2Service hvPmMaterialCutPlanDetail2Service;
    @Autowired
    private HvPmMaterialCutPlanRepository hvPmMaterialCutPlanRepository;

    /**
     * 分页查询
     *
     * @param hvPmMaterialCutPlanQueryDTO
     * @return
     */
    @PostMapping("/getPage")
    @ApiOperation("分页模糊查询")
    public Page<HvPmMaterialCutPlanDTO> getPage(@RequestBody HvPmMaterialCutPlanQueryDTO hvPmMaterialCutPlanQueryDTO) {
        return hvPmMaterialCutPlanService.getPage(hvPmMaterialCutPlanQueryDTO);
    }



    /**
     * 新增切割计划
     *
     * @param hvPmMaterialCutPlanDTO
     * @return
     */
    @PostMapping("/createCutPlan")
    @ApiOperation("新增切割计划")
    public long createCutPlan(@RequestBody HvPmMaterialCutPlanDTO hvPmMaterialCutPlanDTO) {
        return hvPmMaterialCutPlanService.createCutPlan(hvPmMaterialCutPlanDTO);
    }

    /**
     * 修改切割计划
     *
     * @param hvPmMaterialCutPlanDTO
     * @return
     */
    @PutMapping("/updateCutPlan")
    @ApiOperation("修改切割计划")
    public long  updateCutPlan(@RequestBody HvPmMaterialCutPlanDTO hvPmMaterialCutPlanDTO) {
        return hvPmMaterialCutPlanService.updateCutPlan(hvPmMaterialCutPlanDTO);
    }

    /**
     * 修改切割计划并通知产线取消任务
     *
     * @param hvPmMaterialCutPlanDTO
     * @return
     */
    @PutMapping("/updateCutPlanAndCancel")
    @ApiOperation("修改切割计划并通知产线取消任务")
    public ResultVO<?> updateCutPlanAndCancel(@RequestBody HvPmMaterialCutPlanDTO hvPmMaterialCutPlanDTO) {
        return hvPmMaterialCutPlanService.updateCutPlanAndCancel(hvPmMaterialCutPlanDTO);
    }

    /**
     * 删除切割计划
     *
     * @param id
     */
    @DeleteMapping("/deleteCutPlanById/{id}")
    @ApiOperation("删除切割计划")
    public ResultVO<?>  deleteCutPlanById(@PathVariable long id) {
        return hvPmMaterialCutPlanService.deleteCutPlanById(id);
    }

    /**
     * 查询切割计划编号是否存在
     *
     * @param cutPlanCode
     * @return
     */
    @GetMapping("/isExistsCutPlanCode/{cutPlanCode}")
    @ApiOperation("查询切割计划编号是否存在")
    public boolean isExistsCutPlanCode(@PathVariable String cutPlanCode) {
        return hvPmMaterialCutPlanService.isExistsCutPlanCode(cutPlanCode);
    }

    /**
     * 根据切割计划id获取零件余料列表
     *
     * @param id
     * @return
     */
    @GetMapping("/getByCutPlanId/{id}")
    @ApiOperation("根据切割计划id获取零件余料列表")
    public Map<String, Object> getByCutPlanId(@PathVariable long id) {
        Map<String, Object> detailMap = new HashMap<>();
        detailMap.put("detail0", hvPmMaterialCutPlanDetail0Service.getAllDetail0DTOByCutPlanId(id));
        detailMap.put("detail1", hvPmMaterialCutPlanDetail1Service.getAllDetail1ByCutPlanId(id));
        return detailMap;
    }

    /**
     * 根据切割计划id获取零件列表(带流向代码)
     *
     * @param id
     * @return
     */
    @GetMapping("/getDetail0DTOList/{id}")
    @ApiOperation("根据切割计划id获取零件列表(带流向代码)")
    public List<HvPmMaterialCutPlanDetail0DTO> getDetail0DTOList(@PathVariable long id) {
        return hvPmMaterialCutPlanDetail0Service.getAllDetail0DTOByCutPlanId(id);
    }

    /**
     * 根据切割计划id获取零件列表
     *
     * @param id
     * @return
     */
    @GetMapping("/getDetail0List/{id}")
    @ApiOperation("根据切割计划id获取零件列表")
    public List<HvPmMaterialCutPlanDetail0> getDetail0List(@PathVariable long id) {
        return hvPmMaterialCutPlanDetail0Service.getAllDetail0ByCutPlanId(id);
    }

    /**
     * 根据切割计划id获取余料列表
     *
     * @param id
     * @return
     */
    @GetMapping("/getDetail1List/{id}")
    @ApiOperation("根据切割计划id获取余料列表")
    public List<HvPmMaterialCutPlanDetail1> getDetail1List(@PathVariable long id) {
        return hvPmMaterialCutPlanDetail1Service.getAllDetail1ByCutPlanId(id);
    }

    /**
     * 添加零件
     *
     * @param hvPmMaterialCutPlanDetail0DTO
     * @return
     */
    @PostMapping("/createDetail0")
    @ApiOperation("添加零件")
    public long createDetail0(@RequestBody HvPmMaterialCutPlanDetail0DTO hvPmMaterialCutPlanDetail0DTO) {
        return hvPmMaterialCutPlanDetail0Service.createDetail0(hvPmMaterialCutPlanDetail0DTO);
    }

    /**
     * 添加余料
     *
     * @param hvPmMaterialCutPlanDetail1DTO
     * @return
     */
    @PostMapping("/createDetail1")
    @ApiOperation("添加余料")
    public long createDetail1(@RequestBody HvPmMaterialCutPlanDetail1DTO hvPmMaterialCutPlanDetail1DTO) {
        return hvPmMaterialCutPlanDetail1Service.createDetail1(hvPmMaterialCutPlanDetail1DTO);
    }

    /**
     * 修改零件
     *
     * @param hvPmMaterialCutPlanDetail0DTO
     * @return
     */
    @PutMapping("/updateDetail0")
    @ApiOperation("修改零件")
    public long updateDetail0(@RequestBody HvPmMaterialCutPlanDetail0DTO hvPmMaterialCutPlanDetail0DTO) {
        return hvPmMaterialCutPlanDetail0Service.updateDetail0(hvPmMaterialCutPlanDetail0DTO);
    }

    /**
     * 修改余料
     *
     * @param hvPmMaterialCutPlanDetail1DTO
     * @return
     */
    @PutMapping("/updateDetail1")
    @ApiOperation("修改余料")
    public long updateDetail1(@RequestBody HvPmMaterialCutPlanDetail1DTO hvPmMaterialCutPlanDetail1DTO) {
        return hvPmMaterialCutPlanDetail1Service.updateDetail1(hvPmMaterialCutPlanDetail1DTO);
    }

    /**
     * 删除零件
     *
     * @param id
     */
    @DeleteMapping("/deleteDetail0ById/{id}")
    @ApiOperation("删除零件")
    public void deleteDetail0ById(@PathVariable long id) {
        hvPmMaterialCutPlanDetail0Service.deleteDetail0ById(id);
    }

    /**
     * 删除余料
     *
     * @param id
     */
    @DeleteMapping("/deleteDetail1ById/{id}")
    @ApiOperation("删除余料")
    public void deleteDetail1ById(@PathVariable long id) {
        hvPmMaterialCutPlanDetail1Service.deleteDetail1ById(id);
    }

    /**
     * 钢板切割计划下发
     * @param hvPmMaterialCutPlanDTO
     */
    @PostMapping("/sendOrder")
    @ApiOperation("钢板切割计划下发")
    public ResultVO<?> sendOrder(@RequestBody HvPmMaterialCutPlanDTO hvPmMaterialCutPlanDTO, @UserInfo @ApiIgnore UserInfoDTO userInfo) {
        return hvPmMaterialCutPlanService.sendOrder(hvPmMaterialCutPlanDTO, userInfo);

    }

    /**
     * 获取导入模板
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    @ApiResultIgnore
    @GetMapping("/getImportTemplate")
    @ApiOperation("获取导入模板")
    public ResultVO<ExcelExportDto> getImportTemplate() throws IOException, IllegalAccessException {
        return hvPmMaterialCutPlanService.getImportTemplate();
    }

    /**
     * 导入切割计划
     * @param file
     * @return
     * @throws IllegalAccessException
     * @throws ParseException
     * @throws IOException
     */
    @PostMapping("/importMaterialCutPlan")
    @ApiOperation("导入切割计划")
    public ImportResult importMaterialCutPlan(@RequestParam("file")MultipartFile file) throws IllegalAccessException, ParseException, IOException {
        return hvPmMaterialCutPlanService.importMaterialCutPlan(file);
    }

    @ApiResultIgnore
    @PostMapping(value = "/exportMaterialCutPlan")
    @ApiOperation(value = "导出钢板切割计划")
    public ResultVO<ExcelExportDto> exportMaterialCutPlan(@RequestBody HvPmMaterialCutPlanQueryDTO hvPmMaterialCutPlanQueryDTO) throws IOException, IllegalAccessException {
        return hvPmMaterialCutPlanService.exportMaterialCutPlan(hvPmMaterialCutPlanQueryDTO);
    }

    /**
     * 根据套料系统下发添加切割任务
     *
     * @param CutPlan 接收的切割任务
     */
    @PostMapping("/addMaterialCutPlan")
    @ApiOperation("根据套料系统下发添加切割任务")
    public void addMaterialCutPlan(@RequestBody List<HvPmMaterialCutPlanDTO> CutPlan) {
        hvPmMaterialCutPlanService.addMaterialCutPlan(CutPlan);
    }

    /**
     * 根据切割计划id获取时间信息列表和指标数据
     *
     * @param id
     * @return
     */
    @GetMapping("/getDetailInfoById/{id}")
    @ApiOperation("根据切割计划id获取时间信息列表和指标数据")
    public Map<String, Object> getDetailInfoById(@PathVariable long id) {
        Map<String, Object> detailMap = new HashMap<>();
        List<HvPmMaterialCutPlan> hvPmMaterialCutPlanlist = new ArrayList<>();
        HvPmMaterialCutPlan hvPmMaterialCutPlan = hvPmMaterialCutPlanRepository.getOne(id);
        hvPmMaterialCutPlanlist.add(DtoMapper.convert(hvPmMaterialCutPlan,HvPmMaterialCutPlan.class));
        detailMap.put("cutPlan",hvPmMaterialCutPlanlist);
        detailMap.put("detail0",hvPmMaterialCutPlanDetail0Service.getAllDetail0ByCutPlanId(id));
        detailMap.put("detail2",hvPmMaterialCutPlanDetail2Service.getAllDetail2ByCutPlanId(id));
        return detailMap;
    }


    @PostMapping("/cutTaskCancel")
    @ApiOperation("零件切割任务取消")
    public ResultVO<?> cutTaskCancel(@RequestParam("lineId") int lineId,@RequestParam("type") String type,@RequestParam("code")  String code){
        return hvPmMaterialCutPlanService.cutTaskCancel(lineId,type,code);
    }

    @GetMapping("/getShipNumberList")
    @ApiOperation("获取切割计划存在的船号")
    public List<String> getShipNumberList(){
        return hvPmMaterialCutPlanService.getShipNumberList();
    }

}
