package com.hvisions.pms.service;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.bom.dto.BomAllDTO;
import com.hvisions.pms.dto.*;
import com.hvisions.pms.importTemplate.WorkOrderTemplate;
import com.hvisions.pms.materialdto.OperationMaterialsDTO;
import com.hvisions.pms.query.OrderQuery;
import com.hvisions.pms.rh.dto.MaterialDemandDTO;
import com.hvisions.pms.rh.dto.WorkMaterialDTO;
import com.hvisions.pms.viewdto.WorkOrderNumDTO;
import com.hvisions.thirdparty.common.dto.MesOrderDTO;
import org.springframework.data.domain.Page;

import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: DemoEntityServiceImp</p>
 * <p>Description: 工单创建服务层</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/01/17</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface WorkOrderService {


    /**
     * 创建工单
     *
     * @param workOrderDTO 数据传输DTO
     * @return 工单ID
     */
    WorkOrderDTO createOrderManage(WorkOrderDTO workOrderDTO);

    /**
     * 创建子工单群
     *
     * @param orderProjectDTO 创建工单对象
     */
    void createSubOrderManage(OrderProjectDTO orderProjectDTO);

    /**
     * 更新工单
     *
     * @param workOrderDTO 修改工单用DTO
     * @return id
     */
    int updateOrderManage(WorkOrderDTO workOrderDTO);

    /**
     * 计划工单复制
     *
     * @param id            计划工单ID
     * @param workOrderCode 工单计划编号
     * @return 新工单ID
     */
    int copyWorkOrder(Integer id, String workOrderCode);


    /**
     * 根据Id列表查询工单信息
     *
     * @param idIn Id列表
     * @return 工单列表信息
     */
    List<WorkOrderDTO> getHvPmWorkOrderListByIdIn(List<Integer> idIn);


    /**
     * 查看BOM
     *
     * @param orderManageId 工单ID
     * @return 工单绑定的所有Bom信息
     */

    BomAllDTO getBomAllDtoByOrderManageId(Integer orderManageId);


    /**
     * 分页查询
     *
     * @param workOrderQueryDTO 查询条件DTO
     * @return 分页信息
     */

    Page<WorkOrderDTO> findAllByPlanStartTimeAndMaterialCodeAndWorkOrderCode(WorkOrderQueryDTO workOrderQueryDTO);


    /**
     * 根据产线ID查询工单
     *
     * @param id 产线ID
     * @return 工单列表
     */
    List<WorkOrderDTO> getWorkOrderByCellId(Integer id);


    /**
     * 根据工单状态查询工单
     *
     * @param state 工单状态
     * @return 工单列表
     */
    List<WorkOrderDTO> getAllByWorkOrderState(Integer state);

    /**
     * 删除工单
     *
     * @param id 工单ID
     */
    void deleteOrderManageById(Integer id);

    /**
     * 根据ID列表删除工单
     *
     * @param id id列表
     */
    void deleteOrderMangeByIdList(List<Integer> id);




    /**
     * 工单下发 根据工单下发方式下发工单
     *
     * @param orderId  工单Id
     * @param nodeCode 工艺步骤编码
     * @param map      工单参数
     */
    void workOrderIssued(int orderId, String nodeCode, Map<String, Object> map);


    /**
     * 根据工单ID 查询工艺步骤
     *
     * @param orderId 工单Id
     * @return 工艺步骤列表
     */
    Map<String, Object> getRoutStepByOrderId(int orderId);

    /**
     * 批量下发
     *
     * @param idList Id列表
     * @param map    工单参数
     * @return 下发反馈信息
     */
    Map<Integer, String> workOrderIssuedByIdList(List<Integer> idList, Map<String, Object> map);

    /**
     * 批量下发工单
     *
     * @param workIssuedDTO 下发信息
     */
    void batchIssued(WorkIssuedDTO workIssuedDTO);

    /**
     * 撤销工单
     *
     * @param id 工单ID
     */
    void cancelOrderById(int id);


    /**
     * 批量撤销
     *
     * @param idList Id列表
     */
    void cancelOrderByIdList(List<Integer> idList);

    /**
     * 工单报废
     *
     * @param id 工单id
     */
    void scarpWorkOrder(int id);

    /**
     * 批量报废
     *
     * @param idList ID列表
     */
    void scarpWorkOrderByIdList(List<Integer> idList);


    /**
     * 工单报工
     *
     * @param id 工单ID
     */
    void finishWorkOrder(int id);

    /**
     * 批量报工
     *
     * @param idList id列表
     */
    void finishWorkOrderAll(List<Integer> idList);

    /**
     * 今日未完成工单
     *
     * @return 今日未完成工单
     * @throws ParseException 解析异常
     */
    int workOrderTodayNotFinish(Integer orderTypeId,Integer usedType) throws ParseException;

    /**
     * 今日完成工单数
     *
     * @return 今日完成工单的数值
     * @throws ParseException 解析异常
     */
    int workEndOrder(Integer orderTypeId,Integer usedType) throws ParseException;


    /**
     * 今日工单进度信息
     *
     * @return WorkOrderNumDTO 工单进度信息
     * @throws ParseException ParseException
     */
    WorkOrderNumDTO workOrderNum(String orderTypeCode,Integer usedType) throws ParseException;


    /**
     * 根据工单ID查询工单下工序的产出和投入料
     *
     * @param workOrderId 工单ID
     * @return 工单工序下投入和产出料列表
     */
    List<OperationMaterialsDTO> getOperationMaterialByWorkOrderId(int workOrderId);


    /**
     * 根据工单id查询工单所有信息
     *
     * @param workOrderId 工单id
     * @return 工单正向追溯信息
     */
    WorkAllDTO getWorkOrderAll(int workOrderId);

    /**
     * 获取当天的物料需求量
     *
     * @param workMaterialDTO 工单物料
     * @return 物料需求量
     * @throws ParseException ParseException
     */
    List<MaterialDemandDTO> getMaterial(WorkMaterialDTO workMaterialDTO) throws ParseException;

    
    List<WorkOrderDTO> findOrderByOperation(int operationId);

    /**
     * 获取工单计划用料单信息
     *
     * @param workOrderId 工单Id
     * @return 工单用料单信息
     */
    OrderBomDTO getWorkPlanQuantity(Integer workOrderId);

    /**
     * 获取工单属性
     * @return 工单属性列表
     */
    Map<String, String> getOrderParameters();

    ResultVO saveBatch(List<WorkOrderTemplate> data);


    WorkOrderDTO findByWorkOrderCode(String workOrderCode);

    WorkOrderDTO checkWorkOrderCodeBindRouteUnique(ArrayList<String> workOrderCodes);

    List<String> getBatchNoListByMaterialGroupId(Integer materialGroupId);

    Integer getWorkOrderCountByBatchNo(String batchNo);

    void updateCompleteSetCheckStatusByWorkOrderCode(WorkOrderDTO workOrderDTO);

    void sendWorkOrder(SendWorkerOrderDTO sendWorkerOrderDTO);

    /**
     * 根据mes下发的工单创建零件或组立工单
     *
     * @param orderDTO mes下发的工单信息
     */
    void saveWorkOrder(List<MesOrderDTO> orderDTO);

    List<WorkOrderDTO> findNotIssueParentWorkOrderList();

    List<WorkOrderDTO> findCompletenessOrders();

    ResultVO<ExcelExportDto> getImportTemplate() throws IOException, IllegalAccessException;

    ResultVO<ExcelExportDto> exportZLWorkOrder(OrderQuery query) throws IOException, IllegalAccessException;

    ResultVO<ExcelExportDto> exportBCWorkOrder(OrderQuery query) throws IOException, IllegalAccessException;

    ResultVO<ExcelExportDto> exportXCWorkOrder(OrderQuery query) throws IOException, IllegalAccessException;
}
