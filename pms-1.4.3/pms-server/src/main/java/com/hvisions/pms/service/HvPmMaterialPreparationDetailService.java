package com.hvisions.pms.service;

import com.hvisions.pms.dto.HvPmMaterialPreparationDetailDTO;
import com.hvisions.pms.dto.HvPmMaterialPreparationDetailGroupDTO;
import com.hvisions.pms.dto.HvPmMaterialPreparationDetailQueryDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * @Description HvPmMaterialPreparationDetailService
 * <AUTHOR>
 * @Date 2024-05-21
 */
public interface HvPmMaterialPreparationDetailService {
    void addHvPmMaterialPreparationDetail(HvPmMaterialPreparationDetailDTO hvPmMaterialPreparationDetailDTO);

    void deleteHvPmMaterialPreparationDetail(Long id);

    void updateHvPmMaterialPreparationDetail(HvPmMaterialPreparationDetailDTO hvPmMaterialPreparationDetailDTO);

    HvPmMaterialPreparationDetailDTO getHvPmMaterialPreparationDetailById(Long id);

    List<HvPmMaterialPreparationDetailDTO> getAll();


    List<HvPmMaterialPreparationDetailDTO> getByPreparationId(Long id);

    Long addHvPmMaterialPreparationDetailReturnId(HvPmMaterialPreparationDetailDTO hvPmMaterialPreparationDetailDTO);

    List<HvPmMaterialPreparationDetailGroupDTO> getGroupByPreparationId(Long id);

    Page<HvPmMaterialPreparationDetailDTO> getGroupDetailsByQuery(HvPmMaterialPreparationDetailQueryDTO queryDTO);

    List<HvPmMaterialPreparationDetailDTO> getDetailByMaterialCodeAndStatus(String materialCode,Integer status);
}
