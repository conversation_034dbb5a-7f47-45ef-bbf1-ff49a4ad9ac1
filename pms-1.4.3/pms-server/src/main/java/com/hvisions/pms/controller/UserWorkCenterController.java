package com.hvisions.pms.controller;

import com.hvisions.hiperbase.equipment.location.LocationDTO;
import com.hvisions.pms.dto.CrewWorkCenterDTO;
import com.hvisions.pms.dto.WorkCenterUserNameDTO;
import com.hvisions.pms.dto.WorkCenterUserQueryDTO;
import com.hvisions.pms.dto.UserWorkCenterDTO;
import com.hvisions.pms.service.UserWorkCenterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: UserWorkCenterController</p >
 * <p>Description: 人员工位绑定关系控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/12</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/userWorkCenter")
@Slf4j
@Api(description = "工位人员绑定")
public class UserWorkCenterController {

    @Autowired
    UserWorkCenterService userWorkCenterService;


    /**
     * 绑定人员与工位关系
     *
     * @param userWorkCenterDTO dto
     */
    @ApiOperation(value = "绑定人员与工位关系")
    @PostMapping(value = "/createUserWorkCenter")
    public void createUserWorkCenter(@RequestBody UserWorkCenterDTO userWorkCenterDTO) {
        userWorkCenterService.createUserWorkCenter(userWorkCenterDTO);
    }

    /**
     * 绑定班组与工位关系
     *
     * @param crewWorkCenterDTO dto
     */
    @ApiOperation(value = "绑定班组与工位关系")
    @PostMapping(value = "/createCrewWorkCenter")
    public void createCrewWorkCenter(@RequestBody CrewWorkCenterDTO crewWorkCenterDTO) {
        userWorkCenterService.createCrewWorkCenter(crewWorkCenterDTO);
    }

    /**
     * 根据人员ID获取工位列表
     *
     * @param userId 人员ID
     * @return 工位列表信息
     */
    @ApiOperation("根据人员ID获取工位列表")
    @GetMapping(value = "/getWorkCenterByUserId/{userId}")
    public List<LocationDTO> getWorkCenterByUserId(@PathVariable int userId) {
        return userWorkCenterService.getWorkCenterByUserId(userId);
    }

    /**
     * 根据用户ID和工位ID删除关联关系
     *
     * @param WorkCenterId 工位ID
     * @param userId       用户ID
     */
    @ApiOperation(value = "删除工位人员关联关系")
    @DeleteMapping(value = "/deleteByWorkCenterIdAndUserId/{WorkCenterId}/{userId}")
    public void deleteByWorkCenterIdAndUserId(@PathVariable int WorkCenterId, @PathVariable int userId) {
        userWorkCenterService.deleteByWorkCenterIdAndUserId(WorkCenterId, userId);
    }

    /**
     * 根据班组ID查询工位列表信息
     *
     * @param crewId 用户ID
     * @return 班组关联下工位信息
     */
    @ApiOperation(value = "根据班组ID查询关联的工位信息")
    @GetMapping(value = "/getWorkCenterByCrewId/{crewId}")
    public List<LocationDTO> getWorkCenterByCrewId(@PathVariable int crewId) {
        return userWorkCenterService.getWorkCenterByCrewId(crewId);
    }

    /**
     * 根据班组ID和工位ID删除关联关系
     *
     * @param WorkCenterId 工位ID
     * @param crewId       班组ID
     */
    @ApiOperation(value = "删除工位班组关联关系")
    @DeleteMapping(value = "/deleteByWorkCenterIdAndCrewId/{WorkCenterId}/{crewId}")
    public void deleteByWorkCenterIdAndCrewId(@PathVariable int WorkCenterId, @PathVariable int crewId) {
        userWorkCenterService.deleteByWorkCenterIdAndCrewId(WorkCenterId, crewId);
    }

    /**
     * 根据产线查询工位人员/班组绑定信息
     *
     * @param cellId 产线信息
     * @return 工位人员绑定信息
     */
    @ApiOperation(value = "根据产线查询工位人员/班组绑定信息")
    @GetMapping(value = "/getAllWorkCenterByCellId/{cellId}")
    public List<WorkCenterUserQueryDTO> getAllWorkCenterByCellId(@PathVariable int cellId) {
        return userWorkCenterService.getAllWorkCenterByCellId(cellId);
    }

    /**
     * 根据工位查询工位所绑定人员信息
     *
     * @param workCenterId 工位信息
     * @return 人员信息
     */
    @ApiOperation(value = "根据工位查询工位所绑定人员信息")
    @GetMapping(value = "/getUserByWorkCenterId/{workCenterId}")
    public List<WorkCenterUserNameDTO> getUserByWorkCenterId(@PathVariable Integer workCenterId) {
        return userWorkCenterService.getUserByWorkCenterId(workCenterId);
    }

    /**
     * 根据工位查询工位所绑定人员信息并且所绑班组下人员
     *
     * @param workCenterId 工位信息
     * @return 人员信息
     */
    @ApiOperation(value = "根据工位查询工位所绑定人员信息并且所绑班组下人员")
    @GetMapping(value = "/getAllUserByWorkCenterId/{workCenterId}")
    public List<WorkCenterUserNameDTO> getAllUserByWorkCenterId(@PathVariable Integer workCenterId) {
        return userWorkCenterService.getAllUserByWorkCenterId(workCenterId);
    }
}
