package com.hvisions.pms.controller;

import com.hvisions.pms.dto.MaterialActualCodeDTO;
import com.hvisions.pms.dto.OperationMaterialDTO;
import com.hvisions.pms.service.OperationMaterialService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>Title: OperationMaterialController</p>
 * <p>Description: 工序和物料的关系控制器</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/1/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@Api(description = "工序和物料的关系控制器")
@RequestMapping("/operationMaterial")
public class OperationMaterialController {
    private final OperationMaterialService operationMaterialService;

    @Autowired
    public OperationMaterialController(OperationMaterialService operationMaterialService) {
        this.operationMaterialService = operationMaterialService;
    }

    /**
     * 新增或者更新工序和物料的对应关系
     *
     * @param operationMaterialDTOS 工序和物料的对应关系列表
     */
    @PostMapping("/createOrUpdateOperationMaterial")
    @ApiOperation(value = "新增或者更新工序和物料的对应关系")
    public void createOrUpdateOperationMaterial(@RequestBody List<OperationMaterialDTO> operationMaterialDTOS) {
        operationMaterialService.createOrUpdateOperationMaterial(operationMaterialDTOS);
    }

    /**
     * 根据工序id获取相关的物料列表
     *
     * @param operationId 工序id
     * @return 物料列表
     */
    @GetMapping("/getOperationMaterialListByOperationId/{operationId}")
    @ApiOperation(value = "根据工序id获取相关的物料列表")
    public List<OperationMaterialDTO> getOperationMaterialListByOperationId(
            @PathVariable int operationId) {
        return operationMaterialService.getOperationMaterialListByOperationId(operationId);
    }

    /**
     * 根据id删除工序物料
     *
     * @param id 工序物料关系id
     */
    @DeleteMapping("/deleteOperationMaterialById/{id}")
    @ApiOperation(value = "根据id删除工序物料")
    public void deleteOperationMaterialById(@PathVariable int id) {
        operationMaterialService.deleteOperationMaterialById(id);
    }

    /**
     * 根据id列表删除工序物料
     *
     * @param idList 工序物料关系id列表
     */
    @DeleteMapping("/deleteOperationMaterialByIdList")
    @ApiOperation(value = "根据id列表删除工序物料")
    public void deleteOperationMaterialByIdList(@RequestBody List<Integer> idList) {
        operationMaterialService.deleteOperationMaterialByIdList(idList);
    }

    /**
     * 根据工序ID删除工序物料
     *
     * @param operationId 工序ID
     */
    @ApiOperation(value = "根据工序ID删除工序物料")
    @DeleteMapping(value = "/deleteOperationMaterialByOperationId/{operationId}")
    public void deleteOperationMaterialByOperationId(@PathVariable int operationId) {
        operationMaterialService.deleteOperationMaterialByOperationId(operationId);
    }


    /**
     * 录入实际物料
     *
     * @param materialActualCodeDTO 实际物料
     */
    @ApiOperation(value = "录入实际物料")
    @PutMapping(value = "/inPutActualCode")
    public void inPutActualCode(@RequestBody MaterialActualCodeDTO materialActualCodeDTO) {
        operationMaterialService.inPutActualCode(materialActualCodeDTO);
    }

    /**
     * 回料
     *
     * @param id                  投入料信息Id
     * @param returnMaterialCount 回料数量
     */
    @ApiOperation(value = "回料")
    @PutMapping(value = "/returnMaterial")
    public void returnMaterial(@RequestParam int id, @RequestParam BigDecimal returnMaterialCount) {
        operationMaterialService.returnMaterial(id, returnMaterialCount);
    }

}









