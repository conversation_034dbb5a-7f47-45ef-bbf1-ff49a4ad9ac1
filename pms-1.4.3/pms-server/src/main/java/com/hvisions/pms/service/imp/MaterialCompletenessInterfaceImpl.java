package com.hvisions.pms.service.imp;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.framework.client.DictionaryItemClient;
import com.hvisions.framework.dto.dictionary.DictionaryItemDTO;
import com.hvisions.hiperbase.bom.dto.HvBmFrameDTO;
import com.hvisions.hiperbase.client.HvBmFrameClient;
import com.hvisions.hiperbase.client.LocationExtendClient;
import com.hvisions.hiperbase.client.MaterialClient;
import com.hvisions.hiperbase.client.MaterialGroupClient;
import com.hvisions.hiperbase.equipment.location.LocationDTO;
import com.hvisions.hiperbase.materials.dto.MaterialDTO;
import com.hvisions.hiperbase.materials.dto.MaterialGroupDTO;
import com.hvisions.pms.dao.HvPmPlanProductBomMapper;
import com.hvisions.pms.dto.HvPmPlanProductBomDTO;
import com.hvisions.pms.dto.WorkIssuedDTO;
import com.hvisions.pms.dto.WorkOrderDTO;
import com.hvisions.pms.entity.*;
import com.hvisions.pms.entity.plan.HvPmCallFrameMaterial;
import com.hvisions.pms.pools.CommonPoolExecutor;
import com.hvisions.pms.repository.HvPmPartsOutRepository;
import com.hvisions.pms.repository.HvPmProductBomDetailRepository;
import com.hvisions.pms.repository.HvPmStockMovementRepository;
import com.hvisions.pms.repository.WorkOrderRepository;
import com.hvisions.pms.service.HvPmCallFrameMaterialService;
import com.hvisions.pms.service.MaterialCompletenessInterface;
import com.hvisions.pms.service.WorkOrderService;
import com.hvisions.pms.utils.SerialCodeUtilsV2;
import com.hvisions.thirdparty.common.dto.ArrivalMaterialDTO;
import com.hvisions.thirdparty.common.dto.IWMSInStockTaskDTO;
import com.hvisions.thirdparty.common.dto.StockMovementDTO;
import com.hvisions.thirdparty.common.dto.StockMovementDataDTO;
import com.hvisions.thridparty.client.IWMSClient;
import com.hvisions.thridparty.client.PartsStorageClient;
import com.hvisions.wms.client.StockClient;
import com.hvisions.wms.dto.stock.AdjustStorageDTO;
import com.hvisions.wms.dto.stock.StockMaterialDTO;
import com.hvisions.wms.dto.stock.StockOccupyDTO;
import com.hvisions.wms.dto.stock.StockQueryDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MaterialCompletenessInterfaceImpl implements MaterialCompletenessInterface {
    private static final String OTHER_TASK_LOCK_KEY = "otherTaskLock"; // 分布式锁的键
    @Autowired
    private RedissonClient redissonClient; // 注入 Redisson 客户端
    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private MaterialClient materialClient;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private StockClient stockClient;
    @Resource
    private MaterialGroupClient materialGroupClient;
    @Resource
    private WorkOrderRepository workOrderRepository;
    @Resource
    private HvPmProductBomDetailRepository hvPmProductBomDetailRepository;
    @Resource
    private HvBmFrameClient frameClient;
    @Resource
    private DictionaryItemClient dictionaryItemClient;
    @Autowired
    private IWMSClient iwmsClient;
    @Resource
    private HvPmPlanProductBomMapper hvPmPlanProductBomMapper;
    @Resource
    private LocationExtendClient locationExtendClient;
    @Resource
    private PartsStorageClient partsStorageClient;
    @Resource
    private HvPmCallFrameMaterialService hvPmCallFrameMaterialService;
    @Resource
    private HvPmStockMovementRepository hvPmStockMovementRepository;
    @Resource
    private HvPmPartsOutRepository hvPmPartsOutRepository;
    @Autowired
    private SerialCodeUtilsV2 serialCodeUtilsV2;

    private static HvPmStockMovement getHvPmStockMovement(HvPmProductBomDetail hvPmProductBomDetail, StockMovementDataDTO stockMovementDataDTO) {
        HvPmStockMovement movement = new HvPmStockMovement();
        movement.setTaskNo(stockMovementDataDTO.getTaskNo());
        movement.setPalletCode(stockMovementDataDTO.getPalletCode());
        movement.setWorkOrderCode(hvPmProductBomDetail.getWorkOrderCode());
        movement.setStockId(hvPmProductBomDetail.getStockId());
        movement.setPlanProductBomId(hvPmProductBomDetail.getPlanProductBomId());
        movement.setState(0);
        return movement;
    }

    private int recursionCount = 0;
    private static final int MAX_RECURSION_DEPTH = 10; // 设置最大递归深度

    @Override
    public void checkStockAndSend(List<WorkOrderDTO> workOrderDTOS) {
        if (recursionCount >= MAX_RECURSION_DEPTH) {
            recursionCount = 0;
            return; // 达到最大递归深度，直接返回
        }
        recursionCount++;
        try {

        //库存信息查询结果stocksVo
        ResultVO<List<StockMaterialDTO>> stocksVo;
        //库存信息查询结果stockMaterialDTOS
        List<StockMaterialDTO> stockMaterialDTOS;
        //库存信息查询条件queryDTO
        StockQueryDTO queryDTO;
        //齐套的工单集合
        List<WorkOrderDTO> completeWorkOrders = new ArrayList<>();
        //获取新建的组立工单 》 组立bom > 板材 物料
        // findNotIssueParentWorkOrderList查询的是：1、状态为新建的。2、物料对应的bom数据没有绑定料框。3、是组立工单的，只有组立有bom
        //如果workOrderDTOS为空，则查询新建状态的工单
        if (workOrderDTOS == null || workOrderDTOS.isEmpty()) {
            workOrderDTOS = workOrderService.findNotIssueParentWorkOrderList();
        }
        //workOrderDTOS是所有新建状态的工单，这些工单根据bom进行齐套
        //逐个工单的零件匹配库存 (查询没有锁定的料库存数量)
        for (WorkOrderDTO workOrderDTO : workOrderDTOS) {
            boolean status = true;
            List<HvPmProductBomDetail> bomDetail;
            //循环mes下发的 bom，工单中的bom列表
            for (HvPmPlanProductBomDTO hvPmPlanProductBomDTO : workOrderDTO.getBomDTOList()) {

                //--------TODO 外协件默认绑定00000料框
                ResultVO<MaterialDTO> materialCodeVo = materialClient.getByMaterialCode(hvPmPlanProductBomDTO.getMaterialCode());
                MaterialDTO materialDTO = materialCodeVo.getData();
                if (materialDTO != null) {
                    Integer specialPurchaseTypeCode = materialDTO.getSpecialPurchaseTypeCode();
                    if (specialPurchaseTypeCode != null && 1 == specialPurchaseTypeCode) {
                        hvPmPlanProductBomDTO.setCompleteTime(new Date());
                        hvPmPlanProductBomDTO.setFrameCode("000000");
                        hvPmPlanProductBomDTO.setActQuantity(0);
                        hvPmPlanProductBomMapper.updateById(DtoMapper.convert(hvPmPlanProductBomDTO, HvPmPlanProductBom.class));
                        continue;
                    }
                }
                //--------TODO 外协件默认绑定00000料框

                //所需零件数量
                //剩余零件所需
                BigDecimal remainingRequired = BigDecimal.valueOf(hvPmPlanProductBomDTO.getQuantity());
                BigDecimal remaining = remainingRequired;
                // 用于存储满足需求的料框和分配数量
                List<Map<BigDecimal, HvPmCallFrameMaterial>> allocatedFrames = new ArrayList<>();
                // 查找所有可用料框，这是线边的料框，如果线边有所需的零件，先进行占用
                List<HvPmCallFrameMaterial> frameMaterials =
                        //findFrameMaterials:查询线边的零件中，占用数量没有满的零件
                        hvPmCallFrameMaterialService.findFrameMaterials(hvPmPlanProductBomDTO.getMaterialCode(), workOrderDTO.getShipNo(), workOrderDTO.getBlockCode());

                BigDecimal num = BigDecimal.ZERO;
                if (frameMaterials != null && !frameMaterials.isEmpty()) {
                    for (HvPmCallFrameMaterial frameMaterial : frameMaterials) {
                        BigDecimal toOccupy = BigDecimal.ZERO;
                        BigDecimal availableQuantity = frameMaterial.getQuantity()
                                .subtract(frameMaterial.getOccupationQuantity());

                        if (availableQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                            continue;
                        }
                        num = num.add(availableQuantity);
                        toOccupy = availableQuantity.min(remaining);
                        if (num.compareTo(remainingRequired) < 0) {
                            HashMap<BigDecimal, HvPmCallFrameMaterial> hashMap = new HashMap<>();
                            hashMap.put(toOccupy, frameMaterial);
                            allocatedFrames.add(hashMap);
                            remaining = remainingRequired.subtract(toOccupy);
                        }
                        if (num.compareTo(remainingRequired) >= 0) {
                            HashMap<BigDecimal, HvPmCallFrameMaterial> hashMap = new HashMap<>();
                            hashMap.put(toOccupy, frameMaterial);
                            allocatedFrames.add(hashMap);
                            break;
                        }
                    }
                    if (num.compareTo(remainingRequired) >= 0) {
                        StringBuilder frameMaterialBuilder = new StringBuilder();
                        for (Map<BigDecimal, HvPmCallFrameMaterial> allocatedFrame : allocatedFrames) {
                            for (Map.Entry<BigDecimal, HvPmCallFrameMaterial> entry : allocatedFrame.entrySet()) {
                                // 获取键
                                BigDecimal key = entry.getKey();
                                // 获取值
                                HvPmCallFrameMaterial value = entry.getValue();
                                frameMaterialBuilder.append(value.getFrameCode()).append(",");
                                value.setOccupationQuantity(value.getOccupationQuantity().add(key));
                                boolean b = hvPmCallFrameMaterialService.updateById(value);
                                if (!b) {
                                    log.error("组立工单：{}的<{}>线边物料占用失败！", workOrderDTO.getWorkOrderCode(), hvPmPlanProductBomDTO.getMaterialCode());
                                }
                                HvPmProductBomDetail detail = getHvPmProductBomDetail(workOrderDTO.getWorkOrderCode(), hvPmPlanProductBomDTO.getId(), value.getFrameCode(), key.intValue(), value.getId());
                                hvPmProductBomDetailRepository.saveAndFlush(detail);
                            }
                        }
                        String palletCode = frameMaterialBuilder.toString();
                        if (palletCode.endsWith(",")) {
                            palletCode = palletCode.substring(0, palletCode.length() - 1);
                            hvPmPlanProductBomDTO.setCompleteTime(new Date());
                            hvPmPlanProductBomDTO.setFrameCode(palletCode);
                            hvPmPlanProductBomDTO.setActQuantity(num.intValue());
                            hvPmPlanProductBomMapper.updateById(DtoMapper.convert(hvPmPlanProductBomDTO, HvPmPlanProductBom.class));
                        }
                        remainingRequired = BigDecimal.ZERO;
                    } else {
                        remainingRequired = remainingRequired.subtract(num);
                    }
                }
                if (remainingRequired.compareTo(BigDecimal.ZERO) > 0) {
                    // 记录剩余需求，例如更新到某个日志或状态字段
                    //物料查询库存，得到料框数据
                    queryDTO = new StockQueryDTO();
                    queryDTO.setMaterialCode(hvPmPlanProductBomDTO.getMaterialCode());
                    queryDTO.setShipNo(workOrderDTO.getShipNo());
                    queryDTO.setBlockCode(workOrderDTO.getBlockCode());
                    stocksVo = stockClient.getStocks(queryDTO);
                    stockMaterialDTOS = stocksVo.getData();
                    if (stockMaterialDTOS == null || stockMaterialDTOS.isEmpty()) {
                        //有一个料没有库存，都可以理解为未齐套
                        status = false;
                        //hvPmPlanProductBomDTO 退出bom的循环
                        continue;
                    }
                    BigDecimal margin, total = BigDecimal.ZERO;
                    //循环所有符合条件的物料列表
                    for (StockMaterialDTO stockMaterialDTO : stockMaterialDTOS) {

                        margin = stockMaterialDTO.getQuantity().subtract(stockMaterialDTO.getUsedCount());
                        if (margin.compareTo(BigDecimal.ZERO) <= 0) {
                            continue;
                        }
                        total = total.add(margin);
                        if (total.compareTo(remainingRequired) >= 0) {
                            break; // 已经找到足够的物料，结束循环
                        }
                    }
                    if (total.compareTo(remainingRequired) < 0) {
                        //总数不够，也是不齐套
                        status = false;
                        //hvPmPlanProductBomDTO 退出bom的循环
                        continue;
                    }
                    //上面的验证通过到此、说明某个零件已经满足要求
                    //更新库存为占用
                    List<AdjustStorageDTO> adjustStorageDTOS = new ArrayList<>();
                    //占用库存
                    List<StockMaterialDTO> materialDTOS = new ArrayList<>();

                    List<Map<BigDecimal, StockMaterialDTO>> stockMaterialList = new ArrayList<>();

                    for (StockMaterialDTO stockMaterialDTO : stockMaterialDTOS) {
                        //库存余量
                        margin = stockMaterialDTO.getQuantity().subtract(stockMaterialDTO.getUsedCount());

                        if (margin.compareTo(BigDecimal.ZERO) <= 0) {
                            continue;
                        }
                        if (margin.compareTo(remainingRequired) < 0) {
                            HashMap<BigDecimal, StockMaterialDTO> hashMap = new HashMap<>();
                            hashMap.put(margin, stockMaterialDTO);
                            stockMaterialList.add(hashMap);
                            saveAdjustStorageDTO(stockMaterialDTO, materialDTOS, adjustStorageDTOS, margin, workOrderDTO.getWorkOrderCode());
                            remainingRequired = remainingRequired.subtract(margin);
                        } else {
                            saveAdjustStorageDTO(stockMaterialDTO, materialDTOS, adjustStorageDTOS, remainingRequired, workOrderDTO.getWorkOrderCode());
                            HashMap<BigDecimal, StockMaterialDTO> hashMap = new HashMap<>();
                            hashMap.put(remainingRequired, stockMaterialDTO);
                            stockMaterialList.add(hashMap);
                            remainingRequired = BigDecimal.ZERO;
                        }
                        if (remainingRequired.compareTo(BigDecimal.ZERO) <= 0) {
                            break;
                        }
                    }
                    //空
                    if (adjustStorageDTOS.isEmpty()) return;

                    //物料占用
                    ResultVO<?> occVo = stockClient.occupyStorage(adjustStorageDTOS);
                    if (!occVo.isSuccess()) {
                        log.error("物料占用失败！{}", occVo.getMessage());
                        return;
                    }

                    //添加当前bom 的料框和数量 关系
                    bomDetail = new ArrayList<>();
                    StringBuilder palletCodeBuilder = new StringBuilder();
                    for (Map<BigDecimal, StockMaterialDTO> stockMaterialDTOMap : stockMaterialList) {
                        for (Map.Entry<BigDecimal, StockMaterialDTO> entry : stockMaterialDTOMap.entrySet()) {
                            // 获取键
                            BigDecimal key = entry.getKey();
                            // 获取值
                            StockMaterialDTO stockMaterialDTO = entry.getValue();
                            //拼接 托盘编号
                            palletCodeBuilder.append(stockMaterialDTO.getFrameCode()).append(",");
                            //避免重复
                            HvPmProductBomDetail detail = hvPmProductBomDetailRepository.findByPalletCodeAndPlanProductBomId(stockMaterialDTO.getFrameCode(), hvPmPlanProductBomDTO.getId());
                            if (detail == null) {
                                HvPmProductBomDetail hvPmProductBomDetail = new HvPmProductBomDetail();
                                hvPmProductBomDetail.setWorkOrderCode(workOrderDTO.getWorkOrderCode());
                                hvPmProductBomDetail.setPlanProductBomId(hvPmPlanProductBomDTO.getId());
                                hvPmProductBomDetail.setPalletCode(stockMaterialDTO.getFrameCode());
                                hvPmProductBomDetail.setStockId(stockMaterialDTO.getId());
                                hvPmProductBomDetail.setQuality(key.intValue());
                                bomDetail.add(hvPmProductBomDetail);
                            }
                        }
                    }
                    /*for (StockMaterialDTO stockMaterialDTO : materialDTOS) {
                        //拼接 托盘编号
                        palletCodeBuilder.append(stockMaterialDTO.getFrameCode()).append(",");
                        //避免重复
                        HvPmProductBomDetail detail = hvPmProductBomDetailRepository.findByPalletCodeAndPlanProductBomId(stockMaterialDTO.getFrameCode(), hvPmPlanProductBomDTO.getId());
                        if (detail == null) {
                            HvPmProductBomDetail hvPmProductBomDetail = getHvPmProductBomDetail(workOrderDTO, hvPmPlanProductBomDTO, stockMaterialDTO);
                            bomDetail.add(hvPmProductBomDetail);
                        }
                    */
                    if (!allocatedFrames.isEmpty()) {
                        for (Map<BigDecimal, HvPmCallFrameMaterial> allocatedFrame : allocatedFrames) {
                            for (Map.Entry<BigDecimal, HvPmCallFrameMaterial> entry : allocatedFrame.entrySet()) {
                                // 获取键
                                BigDecimal key = entry.getKey();
                                // 获取值
                                HvPmCallFrameMaterial value = entry.getValue();
                                value.setOccupationQuantity(value.getOccupationQuantity().add(key));
                                hvPmCallFrameMaterialService.updateById(value);
                                HvPmProductBomDetail detail = new HvPmProductBomDetail();
                                detail.setWorkOrderCode(workOrderDTO.getWorkOrderCode());
                                detail.setPlanProductBomId(hvPmPlanProductBomDTO.getId());
                                detail.setPalletCode(value.getFrameCode());
                                detail.setQuality(key.intValue());
                                detail.setStockId(Math.toIntExact(value.getId()));
                                bomDetail.add(detail);

                                palletCodeBuilder.append(value.getFrameCode()).append(",");
                            }
                        }
                    }
                    hvPmProductBomDetailRepository.saveAll(bomDetail);

                    //更新mes下发 的 bom 的 托盘编号 和 数量
                    String palletCode = palletCodeBuilder.toString();
                    if (palletCode.endsWith(",")) {
                        palletCode = palletCode.substring(0, palletCode.length() - 1);
                        hvPmPlanProductBomDTO.setCompleteTime(new Date());
                        hvPmPlanProductBomDTO.setFrameCode(palletCode);
                        hvPmPlanProductBomDTO.setActQuantity(total.intValue());
                        hvPmPlanProductBomMapper.updateById(DtoMapper.convert(hvPmPlanProductBomDTO, HvPmPlanProductBom.class));
                    }
                }
            }
            LambdaQueryWrapper<HvPmPlanProductBom> lqw = new LambdaQueryWrapper<>();
            lqw.eq(HvPmPlanProductBom::getPlanCode, workOrderDTO.getWorkOrderCode());
            List<HvPmPlanProductBom> productBomList = hvPmPlanProductBomMapper.selectList(lqw);
            for (HvPmPlanProductBom hvPmPlanProductBom : productBomList) {
                if (hvPmPlanProductBom.getFrameCode() == null || "".equals(hvPmPlanProductBom.getFrameCode())) {
                    status = false;
                    break;
                }
            }
            //齐套
            if (status) {
                //齐套的工单集合
                completeWorkOrders.add(workOrderDTO);
                //更新工单齐套状态
                workOrderDTO.setCompleteSetCheckStatus(1);
                workOrderService.updateCompleteSetCheckStatusByWorkOrderCode(workOrderDTO);
            }
        }
        //去掉已齐套的工单
        workOrderDTOS.removeAll(completeWorkOrders);
        //未齐套的工单将进行 二次分配
        secondaryAllocation(workOrderDTOS);
        //异步执行其他 齐套后的操作
        CommonPoolExecutor.execute(this::otherTask);
        } finally {
            recursionCount--; // 确保递归计数器减小
        }
    }

    /**
     * @param workOrder            组立工单号
     * @param hvPmPlanProductBomId bom_id
     * @param frameCode            料框编号
     * @param key                  占用数量
     * @param stockId              库存id
     * @return HvPmProductBomDetail
     */
    private HvPmProductBomDetail getHvPmProductBomDetail(String workOrder, Long hvPmPlanProductBomId, String frameCode, Integer key, Long stockId) {
        HvPmProductBomDetail detail = new HvPmProductBomDetail();
        detail.setWorkOrderCode(workOrder);
        detail.setPlanProductBomId(hvPmPlanProductBomId);
        detail.setPalletCode(frameCode);
        detail.setQuality(key);
        detail.setStockId(Math.toIntExact(stockId));
        return detail;
    }

    private HvPmProductBomDetail getHvPmProductBomDetail(WorkOrderDTO workOrderDTO, HvPmPlanProductBomDTO hvPmPlanProductBomDTO, StockMaterialDTO stockMaterialDTO) {
        HvPmProductBomDetail hvPmProductBomDetail = new HvPmProductBomDetail();
        hvPmProductBomDetail.setWorkOrderCode(workOrderDTO.getWorkOrderCode());
        hvPmProductBomDetail.setPlanProductBomId(hvPmPlanProductBomDTO.getId());
        hvPmProductBomDetail.setPalletCode(stockMaterialDTO.getFrameCode());
        hvPmProductBomDetail.setStockId(stockMaterialDTO.getId());
        hvPmProductBomDetail.setQuality(stockMaterialDTO.getQuantity().intValue());
        return hvPmProductBomDetail;
    }

    private void saveAdjustStorageDTO(StockMaterialDTO stockMaterialDTO, List<StockMaterialDTO> materialDTOS, List<AdjustStorageDTO> adjustStorageDTOS, BigDecimal quality, String workOrderCode) {
        //库存增减计算条件
        AdjustStorageDTO storageDTO = new AdjustStorageDTO();
        BeanUtils.copyProperties(stockMaterialDTO, storageDTO);
        storageDTO.setOperation("占用");
        storageDTO.setQuantity(quality);
        storageDTO.setStockId(stockMaterialDTO.getId());
        storageDTO.setOrderCode(workOrderCode);
        adjustStorageDTOS.add(storageDTO);

        materialDTOS.add(stockMaterialDTO);
    }

    //二次分配、这是个将锁定的物料进行二次分配的操作，目的是将物料分配给那些库存足够，但是实际没有在首次分配中分配到料框物料的组立工单
    private void secondaryAllocation(List<WorkOrderDTO> workOrderDTOS) {
        if (recursionCount >= MAX_RECURSION_DEPTH) {
            return; // 达到最大递归深度，直接返回
        }
        recursionCount++;
        try {
        StockQueryDTO queryDTO;
        ResultVO<List<StockMaterialDTO>> stocksVo;
        //将工单循环,并将有库存、但是占用的物料进行二次库存验证
        List<StockMaterialDTO> stockMaterialDTOS;
        List<WorkOrderDTO> completenessOrder = new ArrayList<>();
        Map<String, List<StockMaterialDTO>> order_adjustStorage_map = new HashMap<>();
        Map<String, List<HvPmCallFrameMaterial>> order_frameMaterials_map = new HashMap<>();
        Map<Integer, List<HvPmProductBomDetail>> bomDetailsMap = new HashMap<>();
        Map<String, HvPmWorkOrder> workOrderDTOMap = new HashMap<>();


        for (WorkOrderDTO workOrderDTO : workOrderDTOS) {
            boolean status = false;
            List<StockMaterialDTO> adjustStorageList = new ArrayList<>();
            List<HvPmCallFrameMaterial> callFrameMaterials = new ArrayList<>();
            for (HvPmPlanProductBomDTO hvPmPlanProductBomDTO : workOrderDTO.getBomDTOList()) {
                Long quantity = hvPmPlanProductBomDTO.getQuantity();

                if (checkType(hvPmPlanProductBomDTO) || StringUtils.isNotBlank(hvPmPlanProductBomDTO.getFrameCode()) || !(hvPmPlanProductBomDTO.getFrameCode() == null)) {
                    continue;
                }

                List<HvPmCallFrameMaterial> frameMaterials = hvPmCallFrameMaterialService
                        .list(new LambdaQueryWrapper<HvPmCallFrameMaterial>()
                                .eq(HvPmCallFrameMaterial::getMaterialCode, hvPmPlanProductBomDTO.getMaterialCode())
                                .eq(HvPmCallFrameMaterial::getShipNo, workOrderDTO.getShipNo())
                                .eq(HvPmCallFrameMaterial::getBlockCode, workOrderDTO.getBlockCode())
                        );
                BigDecimal total = BigDecimal.ZERO;
                if (!frameMaterials.isEmpty()) {
                    for (HvPmCallFrameMaterial frameMaterial : frameMaterials) {
                        BigDecimal subtract = frameMaterial.getQuantity().subtract(frameMaterial.getUsedQuantity());
                        total = total.add(subtract);
                        List<HvPmProductBomDetail> bomDetailList = hvPmProductBomDetailRepository.findByStockId(frameMaterial.getId().intValue());
                        bomDetailsMap.put(frameMaterial.getId().intValue(), bomDetailList);
                        //BigDecimal occ = BigDecimal.ZERO;
                        //for (HvPmProductBomDetail bomDetail : bomDetailList) {
                        //    HvPmWorkOrder workOrder = workOrderRepository.getHvPmWorkOrderByWorkOrderCode(bomDetail.getWorkOrderCode());
                        //    if (workOrder == null) continue;
                        //    if (1 == workOrder.getCompleteSetCheckStatus() && bomDetail.getState() != 2) {
                        //        occ = occ.add(BigDecimal.valueOf(bomDetail.getQuality()));
                        //    }
                        //}
                        //frameMaterial.setOccupationQuantity(occ);
                        //hvPmCallFrameMaterialService.updateById(frameMaterial);
                        BigDecimal occ = BigDecimal.ZERO;
                        for (HvPmProductBomDetail bomDetail : bomDetailList) {
                            occ = occ.add(BigDecimal.valueOf(bomDetail.getQuality()));
                            if (workOrderDTO.getWorkOrderCode().equals(bomDetail.getWorkOrderCode())) {
                                continue;
                            }
                            HvPmWorkOrder workOrder = workOrderRepository.getHvPmWorkOrderByWorkOrderCode(bomDetail.getWorkOrderCode());
                            if (workOrder == null) continue;
                            workOrderDTOMap.put(workOrder.getWorkOrderCode(), workOrder);
                            if (1 == workOrder.getCompleteSetCheckStatus()) {
                                total = total.subtract(BigDecimal.valueOf(bomDetail.getQuality()));
                            }
                        }
                        frameMaterial.setOccupationQuantity(occ);
                        hvPmCallFrameMaterialService.updateById(frameMaterial);

                        // 检查总数是否满足或超过需求量
                        if (total.compareTo(BigDecimal.valueOf(quantity)) >= 0) {
                            // 添加当前的 frameMaterial 到列表中
                            callFrameMaterials.add(frameMaterial);
                            break;
                        } else {
                            callFrameMaterials.add(frameMaterial);
                        }
                    }
                    order_frameMaterials_map.put(workOrderDTO.getWorkOrderCode(), callFrameMaterials);
                }

                if (total.compareTo(BigDecimal.valueOf(quantity)) >= 0) {
                    status = true;
                } else {
                    //数量不足
                    //物料查询库存，得到料框数据
                    queryDTO = new StockQueryDTO();
                    queryDTO.setMaterialCode(hvPmPlanProductBomDTO.getMaterialCode());
                    queryDTO.setShipNo(workOrderDTO.getShipNo());
                    stocksVo = stockClient.getStocks(queryDTO);
                    stockMaterialDTOS = stocksVo.getData();
                    if (stockMaterialDTOS == null || stockMaterialDTOS.isEmpty()) {
                        status = false;
                        order_frameMaterials_map.remove(workOrderDTO.getWorkOrderCode());
                        order_adjustStorage_map.remove(workOrderDTO.getWorkOrderCode());
                        continue;
                    }
                    for (StockMaterialDTO stockMaterialDTO : stockMaterialDTOS) {
                        //库存总数量-占用
                        total = stockMaterialDTO.getQuantity().add(total);

                        //库存id 查询占用的工单 和 bom
                        List<HvPmProductBomDetail> bomDetails = hvPmProductBomDetailRepository.findByStockId(stockMaterialDTO.getId());
                        bomDetailsMap.put(stockMaterialDTO.getId(), bomDetails);
                        for (HvPmProductBomDetail bomDetail : bomDetails) {
                            //跳过自己
                           /* if (workOrderDTO.getWorkOrderCode().equals(bomDetail.getWorkOrderCode())) {
                                continue;
                            }*/
                            //其他工单
                            HvPmWorkOrder workOrder = workOrderRepository.getHvPmWorkOrderByWorkOrderCode(bomDetail.getWorkOrderCode());
                            if (workOrder == null) continue;
                            workOrderDTOMap.put(workOrder.getWorkOrderCode(), workOrder);
                            //齐套、将之前分配的 减掉
                            if (1 == workOrder.getCompleteSetCheckStatus()) {
                                //可用总数  = 总数 - 齐套了的工单 的 物料数量
                                total = total.subtract(BigDecimal.valueOf(bomDetail.getQuality()));
                            }
                        }


                        if (total.compareTo(BigDecimal.valueOf(quantity)) > 0) {
                            //总数 减去 当前循环 库存数量
                            BigDecimal l_q = total.subtract(stockMaterialDTO.getQuantity());
                            if (l_q.compareTo(BigDecimal.valueOf(quantity)) >= 0) {
                                break;
                            }
                        }
                        adjustStorageList.add(stockMaterialDTO);
                    }
                    //库存总数 大于等于 需求数量
                    if (total.compareTo(BigDecimal.valueOf(quantity)) >= 0) {
                        status = true;
                    } else {
                        //数量不够，立刻终止其他物料的验证
                        status = false;
                        order_frameMaterials_map.remove(workOrderDTO.getWorkOrderCode());
                        order_adjustStorage_map.remove(workOrderDTO.getWorkOrderCode());
                        continue;
                    }
                    order_adjustStorage_map.put(workOrderDTO.getWorkOrderCode(), adjustStorageList);
                }
            }
            if (status) {
                //齐套的工单
                completenessOrder.add(workOrderDTO);
            }
        }
        //新-验证齐套的工单，且已删除绑定信息(防止意外)
        List<WorkOrderDTO> orderDTOS = new ArrayList<>();
        for (WorkOrderDTO workOrderDTO : completenessOrder) {
            boolean completeness = false;

            List<HvPmCallFrameMaterial> hvPmCallFrameMaterials = order_frameMaterials_map.get(workOrderDTO.getWorkOrderCode());
            List<HvPmProductBomDetail> removeBomDetails = new ArrayList<>();
            if (hvPmCallFrameMaterials != null) {
                for (HvPmCallFrameMaterial hvPmCallFrameMaterial : hvPmCallFrameMaterials) {
                    List<HvPmProductBomDetail> bomDetails = bomDetailsMap.get(hvPmCallFrameMaterial.getId().intValue());
                    for (HvPmProductBomDetail bomDetail : bomDetails) {
            /*//跳过自己
            if (workOrderDTO.getWorkOrderCode().equals(bomDetail.getWorkOrderCode())) {
                continue;
            }*/
                        //其他工单
                        HvPmWorkOrder workOrder = workOrderDTOMap.get(bomDetail.getWorkOrderCode());
                        if (workOrder == null) continue; // 跳过无效条目

                        // 检查 workOrder 是否为 null
                        if (workOrder != null && 1 != workOrder.getCompleteSetCheckStatus()) {
                            HvPmPlanProductBom pmPlanProductBom = hvPmPlanProductBomMapper.selectById(bomDetail.getPlanProductBomId());
                            pmPlanProductBom.setFrameCode(null);
                            pmPlanProductBom.setActQuantity(0);
                            hvPmPlanProductBomMapper.updateFrameCodeById(pmPlanProductBom);
                            //删除的bom detail
                            removeBomDetails.add(bomDetail);
                            completeness = true;
                        } else {
                            // 如果 workOrder 为 null 或状态不符合条件，记录日志
                            if (workOrder == null) {
                                log.warn("workOrder is null for WorkOrderCode: {}", bomDetail.getWorkOrderCode());
                            } else {
                                log.debug("workOrder status is not 1 for WorkOrderCode: {}", bomDetail.getWorkOrderCode());
                            }
                        }
                    }
                }
            }


            List<StockMaterialDTO> stockMaterialList = order_adjustStorage_map.get(workOrderDTO.getWorkOrderCode());
            if (stockMaterialList == null) continue;
            //待删除的bom detail

            //齐套后需要验证所需要占用的料的库存是否有被占用，如果已被占用的工单是未齐套，被占用的料需要调整占用对象
            for (StockMaterialDTO stockMaterialDTO : stockMaterialList) {
                //库存id找占用信息
                ResultVO<List<StockOccupyDTO>> stockOccupyList = stockClient.getOccupy(stockMaterialDTO.getId());
                List<StockOccupyDTO> stockOccupyDTOList = stockOccupyList.getData();

                if (stockOccupyDTOList == null) {
                    continue;
                }
                //库存占用
                for (StockOccupyDTO stockOccupyDTO : stockOccupyDTOList) {
                    //删除占用
                    stockClient.occupyRemove(stockOccupyDTO.getId());
                   /* for (HvPmProductBomDetail removeBomDetail : removeBomDetails) {
                        //验证占用数量库存id 是否一致
                        if (stockOccupyDTO.getStockId().equals(removeBomDetail.getStockId())
                                && stockOccupyDTO.getQuantity().compareTo(BigDecimal.valueOf(removeBomDetail.getQuality())) == 0) {

                        }
                    }*/
                }

                //库存id 查询占用的工单 和 bom
                List<HvPmProductBomDetail> bomDetails = bomDetailsMap.get(stockMaterialDTO.getId());

                for (HvPmProductBomDetail bomDetail : bomDetails) {
                    //跳过自己
                    /*if (workOrderDTO.getWorkOrderCode().equals(bomDetail.getWorkOrderCode())) {
                        continue;
                    }*/
                    //其他工单
                    HvPmWorkOrder workOrder = workOrderDTOMap.get(bomDetail.getWorkOrderCode());
                    //未齐套、将之前分配的料框和实际数量重置
                    if (1 != workOrder.getCompleteSetCheckStatus()) {
                        HvPmPlanProductBom pmPlanProductBom = hvPmPlanProductBomMapper.selectById(bomDetail.getPlanProductBomId());
                        pmPlanProductBom.setFrameCode(null);
                        pmPlanProductBom.setActQuantity(null);
                        hvPmPlanProductBomMapper.updateFrameCodeById(pmPlanProductBom);
                        //待删除的bom detail 信息
                        removeBomDetails.add(bomDetail);

                        completeness = true;
                    }
                }

            }
            if (!removeBomDetails.isEmpty()) {
                //删除bom 和 料框/库存 关系
                hvPmProductBomDetailRepository.deleteAll(removeBomDetails);
            }
            if (completeness) {
                orderDTOS.add(workOrderDTO);
            }
        }
        //将验证通过的工单，重新执行分配
        if (!orderDTOS.isEmpty())
            checkStockAndSend(orderDTOS);
        } finally {
            recursionCount--; // 确保递归计数器减小
        }
    }

    private boolean checkType(HvPmPlanProductBomDTO hvPmPlanProductBomDTO) {
        //如果类型不是零部件则返回true
        if (!"1".equals(hvPmPlanProductBomDTO.getMatType())) return true;
        MaterialDTO materialDTO = getMaterialDTO(hvPmPlanProductBomDTO.getMaterialCode());
        if (materialDTO == null) {
            log.error("物料号：{}未维护！", hvPmPlanProductBomDTO.getMaterialCode());
            return true;
        }
        MaterialGroupDTO materialGroupDTO = getMaterialGroup(materialDTO.getMaterialCode(), materialDTO.getMaterialGroup());
        if (materialGroupDTO == null) {
            log.error("物料号：{}未配置物料分组！无法确定是板材/型材零件！", materialDTO.getMaterialCode());
            return true;
        }
        return false;
        //非钢板、不做齐套验证
        //return !"P1".equals(materialGroupDTO.getGroupCode());
    }

    private void otherTask() {
        //找齐套,新建状态的工单
        List<WorkOrderDTO> workOrderDTOS2 = workOrderService.findCompletenessOrders();
        List<WorkOrderDTO> sendList = new ArrayList<>();
        Map<String, List<HvPmProductBomDetail>> bomDetailMap = new HashMap<>();
        for (WorkOrderDTO workOrderDTO : workOrderDTOS2) {
            //获取工单所有的bom 明细、主要是找到料框
            List<HvPmProductBomDetail> bomDetailAll = hvPmProductBomDetailRepository.findByWorkOrderCode(workOrderDTO.getWorkOrderCode());
            if (bomDetailAll.isEmpty()) continue;
            sendList.add(workOrderDTO);
            bomDetailMap.put(workOrderDTO.getWorkOrderCode(), bomDetailAll);
        }

        RLock lock = redissonClient.getLock(OTHER_TASK_LOCK_KEY);
        // 尝试获取锁，等待10秒，锁持有时间为60秒
        boolean isLocked = false;
        try {
            isLocked = lock.tryLock(10, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        if (isLocked) {
            try {
                //等上个循环所有工单验证完成在下发
                for (WorkOrderDTO workOrderDTO : sendList) {
                    boolean flag = false;
                    List<HvPmProductBomDetail> hvPmProductBomDetails = bomDetailMap.get(workOrderDTO.getWorkOrderCode());
                    for (HvPmProductBomDetail hvPmProductBomDetail : hvPmProductBomDetails) {
                        //判断是否在料框线边
                        List<HvPmCallFrameMaterial> frameMaterials = hvPmCallFrameMaterialService.list(new LambdaQueryWrapper<HvPmCallFrameMaterial>()
                                .eq(HvPmCallFrameMaterial::getFrameCode, hvPmProductBomDetail.getPalletCode()));
                        if (frameMaterials != null && !frameMaterials.isEmpty()) {
                            Boolean isPresent = frameMaterials.get(0).getIsPresent();
                            if (isPresent) {
                                continue;
                            }
                        }
                        //判断是否在料框线边

                        //立库齐套时间
                        String singleCode = hvPmProductBomDetail.getPalletCode().trim();
                        StockQueryDTO stock = new StockQueryDTO();
                        stock.setFrameCode(singleCode);
                        ResultVO<List<StockMaterialDTO>> stocks = stockClient.getStocks(stock);
                        List<StockMaterialDTO> stocksData = stocks.getData();
                        HvPmPlanProductBom hvPmPlanProductBom = hvPmPlanProductBomMapper.selectById(hvPmProductBomDetail.getPlanProductBomId());
                        if (stocksData != null && !stocksData.isEmpty()) {
                            hvPmPlanProductBom.setCompleteTime(stocksData.get(0).getCreateTime());
                        }
                        hvPmPlanProductBomMapper.updateById(hvPmPlanProductBom);
                        //立库齐套时间

                        flag = isFlag(hvPmProductBomDetail, flag, stocksData);
                    }

                    if (flag) {
                        continue;
                    }


                    //下发
                    WorkIssuedDTO workIssuedDTO = new WorkIssuedDTO();
                    workIssuedDTO.setOrderIds(Collections.singletonList(workOrderDTO.getId()));
                    workIssuedDTO.setWorkOrderDTO(workOrderDTO);
                    workOrderService.batchIssued(workIssuedDTO);
                    LocationDTO locationDTO = getLocation(workOrderDTO.getCellId());
                    if (locationDTO == null) {
                        log.error("产线Id{}未找到产线信息！", workOrderDTO.getCellId());
                        return;
                    }
                    // 如果需要，可以在这里添加更多与locationDTO相关的代码


                    //板材零件 立库 转 平库
                    //toOtherWarehouse(workOrderDTO.getBomDTOList(), bomDetailMap.get(workOrderDTO.getWorkOrderCode()), locationDTO.getCode());
                }
            } finally {
                lock.unlock(); // 释放锁
            }
        }
    }

    private void sendOutboundTask(StockMovementDTO task) {
        ResultVO resultVO = partsStorageClient.sendOutboundTask(task);
        if (!resultVO.isSuccess()) {
            log.error("出库任务发送异常！{}", resultVO.getMessage());
            throw new BaseKnownException("出库任务发送异常！{}", resultVO.getMessage());
        }
    }

    private LocationDTO getLocation(Integer cellId) {
        String cellIdStr = cellId.toString();
        String jsonString = (String) stringRedisTemplate.opsForHash().get("LOCATION", cellIdStr);
        LocationDTO locationDTO = null;

        if (StringUtils.isBlank(jsonString)) {
            try {
                ResultVO<LocationDTO> groupVo = locationExtendClient.getLocationById(cellId);
                if (groupVo.getData() != null) {
                    stringRedisTemplate.opsForHash().put("LOCATION", cellIdStr, JSON.toJSONString(groupVo.getData()));
                    locationDTO = groupVo.getData();
                }
            } catch (Exception e) {
                // 处理异常，例如记录日志、抛出运行时异常等
                throw new RuntimeException("获取位置信息时出错", e);
            }
        } else {
            try {
                locationDTO = JSON.parseObject(jsonString, LocationDTO.class);
            } catch (Exception e) {
                // 处理 JSON 解析异常，例如记录日志、从远程服务重新获取数据等
                // 这里可以选择抛出异常、返回 null 或使用默认值
                throw new RuntimeException("解析 Redis 中的位置信息时出错", e);
            }
        }
        return locationDTO;
    }

    private MaterialDTO getMaterialDTO(String materialCode) {
        MaterialDTO materialDTO;
        String group = (String) stringRedisTemplate.opsForHash().get("MATERIAL_LIST", materialCode);
        if (StringUtils.isBlank(group)) {
            ResultVO<MaterialDTO> groupVo = materialClient.getByCode(materialCode, "1");
            if (groupVo.getData() != null)
                stringRedisTemplate.opsForHash().put("MATERIAL_LIST", materialCode, JSON.toJSONString(groupVo.getData()));
            materialDTO = groupVo.getData();
        } else {
            materialDTO = JSON.parseObject(group, MaterialDTO.class);
        }
        return materialDTO;

    }

    private MaterialGroupDTO getMaterialGroup(String materialCode, Integer materialGroup) {
        MaterialGroupDTO materialGroupDTO;
        String group = (String) stringRedisTemplate.opsForHash().get("MATERIAL_GROUP", materialCode);
        if (StringUtils.isBlank(group)) {

            ResultVO<MaterialGroupDTO> groupVo = materialGroupClient.getMaterialGroupById(materialGroup);
            if (groupVo.getData() != null)
                stringRedisTemplate.opsForHash().put("MATERIAL_GROUP", materialCode, JSON.toJSONString(groupVo.getData()));
            materialGroupDTO = groupVo.getData();
        } else {
            materialGroupDTO = JSON.parseObject(group, MaterialGroupDTO.class);
        }
        return materialGroupDTO;

    }


    private boolean isFlag(HvPmProductBomDetail hvPmProductBomDetail, boolean flag, List<StockMaterialDTO> stocksData) {
        boolean codeGenerated = false;  // 用于跟踪是否已生成代码
        String taskCode = null;
        StockMovementDTO task = new StockMovementDTO();
        if (stocksData != null && !stocksData.isEmpty()) {
            List<StockMovementDataDTO> dataList = new ArrayList<>();
            StockMovementDataDTO stockMovementDataDTO = null;
            List<ArrivalMaterialDTO> matList = new ArrayList<>();
            HvPmPartsOut partsOut = null;
            for (StockMaterialDTO stockMaterialDTO : stocksData) {
                if (!stockMaterialDTO.getLocationAreaCode().contains("LJ")) break;
                partsOut = hvPmPartsOutRepository.findByPalletCodeAndLocationAndStatus(stockMaterialDTO.getFrameCode(), stockMaterialDTO.getMaterialPointCode(), 0);
                if (partsOut != null) {
                    taskCode = partsOut.getTaskNo();
                }
                if (!codeGenerated) {  // 检查是否已生成代码
                    if (taskCode == null) {
                        taskCode = serialCodeUtilsV2.generateCode("agvRequestCode");
                    }
                    stockMovementDataDTO = new StockMovementDataDTO();
                    stockMovementDataDTO.setTaskNo(taskCode);
                    stockMovementDataDTO.setLocation(stockMaterialDTO.getMaterialPointCode());
                    stockMovementDataDTO.setPalletCode(stockMaterialDTO.getFrameCode());
                    stockMovementDataDTO.setStatus(0);
                    // 仅当未生成时才生成代码
                    codeGenerated = true;  // 更新状态为已生成
                }
                ArrivalMaterialDTO arrivalMaterialDTO = new ArrivalMaterialDTO();
                arrivalMaterialDTO.setMaterialCode(stockMaterialDTO.getMaterialCode());
                arrivalMaterialDTO.setQuantity(String.valueOf(stockMaterialDTO.getQuantity()));
                matList.add(arrivalMaterialDTO);
                stockMovementDataDTO.setMaterialList(matList);
            }
            if (stockMovementDataDTO != null) {
                dataList.add(stockMovementDataDTO);
                task.setDatas(dataList);
                HvPmStockMovement stockMovement = hvPmStockMovementRepository
                        .findByWorkOrderCodeAndPlanProductBomId
                                (hvPmProductBomDetail.getWorkOrderCode(), hvPmProductBomDetail.getPlanProductBomId());

                if (stockMovement == null) {
                    HvPmStockMovement movement = getHvPmStockMovement(hvPmProductBomDetail, stockMovementDataDTO);
                    hvPmStockMovementRepository.save(movement);
                }
                if (partsOut == null) {
                    HvPmPartsOut hvPmPartsOut = new HvPmPartsOut();
                    hvPmPartsOut.setTaskNo(stockMovementDataDTO.getTaskNo());
                    hvPmPartsOut.setLocation(stockMovementDataDTO.getLocation());
                    hvPmPartsOut.setPalletCode(stockMovementDataDTO.getPalletCode());
                    hvPmPartsOut.setStatus(0);
                    hvPmPartsOutRepository.save(hvPmPartsOut);
                    sendOutboundTask(task);
                }
                flag = true;
            }
        }
        return flag;
    }

    private boolean getSendFlag(String orderCode, List<HvPmProductBomDetail> bomDetail) {
        if (bomDetail == null || bomDetail.isEmpty()) {
            throw new BaseKnownException("工单:" + orderCode + ",与料框的关系不能为空！");
        }
        Set<String> palletCodes = new HashSet<>();
        for (HvPmProductBomDetail hvPmProductBomDetail : bomDetail) {
            palletCodes.add(hvPmProductBomDetail.getPalletCode());
        }
        for (String palletCode : palletCodes) {
            //料框再去找 其他绑定  的未下发的 组立工单
            Set<String> bomDetails = hvPmProductBomDetailRepository.getOrderOrderBomDetailByPalletCode(palletCode, orderCode);
            for (String workOrderCode : bomDetails) {
                //验证工单状态
                HvPmWorkOrder workOrder = workOrderRepository.getHvPmWorkOrderByWorkOrderCode(workOrderCode);
                if (workOrder.getCompleteSetCheckStatus() != 1) {
                    return false;
                }
            }

            //----------------------------------------------
        }

        return true;
    }

    private void toOtherWarehouse(List<HvPmPlanProductBomDTO> pmPlanProductBoms, List<HvPmProductBomDetail> bomDetail, String lineCode) {
        IWMSInStockTaskDTO iwmsInStockTaskDTO;
        Map<String, List<HvPmPlanProductBomDTO>> mapBom = new HashMap<>();
        List<HvPmPlanProductBomDTO> boms;
        Map<Long, HvPmPlanProductBomDTO> map = pmPlanProductBoms.stream().collect(Collectors.toMap(HvPmPlanProductBomDTO::getId, Function.identity()));
        Map<Long, HvPmProductBomDetail> detailMap = new HashMap<>();
        for (HvPmProductBomDetail hvPmProductBomDetail : bomDetail) {
            boms = mapBom.get(hvPmProductBomDetail.getPalletCode());
            if (boms == null) {
                boms = new ArrayList<>();
            }
            boms.add(map.get(hvPmProductBomDetail.getPlanProductBomId()));
            mapBom.put(hvPmProductBomDetail.getPalletCode(), boms);

            detailMap.put(hvPmProductBomDetail.getPlanProductBomId(), hvPmProductBomDetail);
        }
        StockMovementDTO task = new StockMovementDTO();
        List<StockMovementDataDTO> stockMovementData = new ArrayList<>();
        for (Map.Entry<String, List<HvPmPlanProductBomDTO>> planProductBom : mapBom.entrySet()) {
            //查询料框类型
            ResultVO<List<DictionaryItemDTO>> itemClientAll = dictionaryItemClient.findAll("work_finish_pallet_and_stock_type");
            List<DictionaryItemDTO> itemDTOS = itemClientAll.getData();
            if (itemDTOS.isEmpty()) {
                throw new BaseKnownException("字典未配置下料完工托盘与库区类型关系：work_finish_pallet_and_stock_type");
            }
            Map<String, String> pallet_stockType = new HashMap<>();
            for (DictionaryItemDTO itemDTO : itemDTOS) {
                pallet_stockType.put(itemDTO.getItemKey(), itemDTO.getItemValue());
            }
            //查询料框类型
            ResultVO<HvBmFrameDTO> frameDTOResultVO = frameClient.findByFrameCode(planProductBom.getKey());
            HvBmFrameDTO frameDTO = frameDTOResultVO.getData();
            if (frameDTO == null) {
                throw new BaseKnownException("料框：" + planProductBom.getKey() + "未配置！");
            }
            String stockType = pallet_stockType.get(frameDTO.getFrameTypeCode());
            if (StringUtils.isBlank(stockType)) {
                throw new BaseKnownException("字典未配置料框类型：" + frameDTO.getFrameTypeCode() + "对应的入库库区类型");
            }
            //不是立库不做调度
            if (!stockType.contains("LK")) {
                log.info("料框：" + frameDTO.getFrameTypeCode() + "对应的库位类型编号：" + stockType + "，不做立库转平库操作");
                return;
            }
            StockMovementDataDTO dto = new StockMovementDataDTO();
            String taskCode = serialCodeUtilsV2.generateCode("agvRequestCode");
            dto.setTaskNo(taskCode);
            dto.setPalletCode(planProductBom.getKey());
            stockMovementData.add(dto);
            task.setDatas(stockMovementData);
            ResultVO resultVO = partsStorageClient.sendOutboundTask(task);
            if (!resultVO.isSuccess()) {
                //log.error("出库任务发送异常！" + resultVO.getMessage());
            }
/*



            iwmsInStockTaskDTO = new IWMSInStockTaskDTO();
            iwmsInStockTaskDTO.setPodCode(planProductBom.getKey());
            iwmsInStockTaskDTO.setLineCode(lineCode);
            //默认送到平库
            iwmsInStockTaskDTO.setStgCategory("PK");
            iwmsInStockTaskDTO.setInitPodFlag("0");//"立库填：1，其他库填：0"
            List<IWMSMaterialDataDTO> materialDataDTOS = new ArrayList<>();
            IWMSMaterialDataDTO materialDataDTO;

            for (HvPmPlanProductBomDTO hvPmPlanProductBom : planProductBom.getValue()) {
                materialDataDTO = new IWMSMaterialDataDTO();
                materialDataDTO.setMatCode(hvPmPlanProductBom.getMaterialCode());
                materialDataDTO.setMatQty(detailMap.get(hvPmPlanProductBom.getId()).getQuality());
                materialDataDTO.setSurplusMaterial("0");
                materialDataDTO.setMatText(hvPmPlanProductBom.getMaterialCode());
                materialDataDTOS.add(materialDataDTO);
            }

            iwmsInStockTaskDTO.setData(materialDataDTOS);
            ResultVO<?> vo = iwmsClient.sendFullMaterialInStockTask(iwmsInStockTaskDTO);
            if (vo.isSuccess()) {
                log.error("立库转平库异常！" + vo.getMessage());
            }*/
        }
    }
}
