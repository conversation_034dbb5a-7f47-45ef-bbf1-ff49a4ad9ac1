package com.hvisions.pms.repository.plan;

import com.hvisions.pms.entity.plan.HvPmPolishPlanDetail0;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/8/26
 */
@Repository
public interface HvPmPolishPlanDetail0Repository extends JpaRepository<HvPmPolishPlanDetail0,Long> {

    List<HvPmPolishPlanDetail0> getAllByCodeId(Long CodeId);

    void deleteByCodeId(long id);

    @Query("SELECT d FROM HvPmPolishPlanDetail0 d WHERE d.codeId IN :codeIds")
    List<HvPmPolishPlanDetail0> findByCodeIds(@Param("codeIds") List<Long> codeIds);
}
