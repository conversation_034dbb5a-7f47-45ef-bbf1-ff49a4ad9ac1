package com.hvisions.pms.controller;

import com.hvisions.pms.service.WorkOrderParameterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <p>Title: WorkOrderParameterCOntroller</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/3/8</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/workOrderParameter")
@Slf4j
@Api(description = "工单参数")
public class WorkOrderParameterController {

    @Autowired
    WorkOrderParameterService workOrderParameterService;

    /**
     * 创建工单参数
     *
     * @param map     参数列表
     * @param orderId 工单ID
     */
    @ApiOperation(value = "创建工单参数")
    @PostMapping(value = "/createParameter/{orderId}")
    public void createParameter(@RequestBody Map<String, Object> map, @PathVariable int orderId) {
        workOrderParameterService.createParameter(map, orderId);
    }


    /**
     * 根据工单获取参数值
     *
     * @param orderId 工单Id
     * @return 参数值map
     */
    @ApiOperation(value = "根据工单获取参数值")
    @GetMapping(value = "/getValueByOrder/{orderId}")
    public Map<String, Object> getValueByOrder(@PathVariable Integer orderId) {
        return workOrderParameterService.getValueByOrder(orderId);
    }

    /**
     * 根据工单ID 参数编码查询 参数值
     *
     * @param code    参数编码
     * @param orderId 工单ID
     * @return 参数值
     */
    @ApiOperation(value = "根据工单ID 参数编码查询 参数值")
    @GetMapping(value = "/getValueByCodeAndOrderId/{code}/{orderId}")
    public Object getValueByCodeAndOrderId(@PathVariable String code, @PathVariable Integer orderId) {
        return workOrderParameterService.getValueByCodeAndOrderId(code, orderId);
    }
}