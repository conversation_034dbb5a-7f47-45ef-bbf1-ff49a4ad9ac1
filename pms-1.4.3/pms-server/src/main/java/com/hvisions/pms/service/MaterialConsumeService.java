package com.hvisions.pms.service;

import com.hvisions.pms.viewdto.MaterialByCrewDTO;
import com.hvisions.pms.viewdto.MaterialByCrewQueryDTO;
import com.hvisions.pms.viewdto.MaterialConsumeDTO;
import com.hvisions.pms.viewdto.MaterialConsumeFormDTO;

import java.util.List;

/**
 * <p>Title: MaterialConsumeService</p >
 * <p>Description: 物料消耗统计服务层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/18</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface MaterialConsumeService {


    /**
     * 创建视图
     */
    void createView();


    /**
     * 根据工序ID查询物料使用情况
     *
     * @param operationId 工序ID
     * @return 物料使用情况
     */
    List<MaterialConsumeDTO> getByOperationId(int operationId);


    /**
     * 根据工单计划时间查询物料投入
     *
     * @param materialByCrewQueryDTO 条件DTO
     * @return 投入物料信息
     */
    List<MaterialConsumeFormDTO> getInvestmentByDate(MaterialByCrewQueryDTO materialByCrewQueryDTO);


    /**
     * 查询产出信息
     *
     * @return 物料产出信息
     */
    List<MaterialByCrewDTO> getOutPutMaterial();


    /**
     * 根据时间且产线查询物料产出信息
     *
     * @param materialByCrewQueryDTO 查询条件
     * @return 物料产出信息
     */
    List<MaterialByCrewDTO> getAllByDateAndCell(MaterialByCrewQueryDTO materialByCrewQueryDTO);


}
