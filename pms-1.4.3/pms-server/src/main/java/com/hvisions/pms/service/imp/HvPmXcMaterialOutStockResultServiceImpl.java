package com.hvisions.pms.service.imp;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.pms.dao.HvPmXcMaterialOutStockResultMapper;
import com.hvisions.pms.dto.HvPmXcMaterialOutStockResultDTO;
import com.hvisions.pms.dto.HvPmXcMaterialOutStockResultDetail1DTO;
import com.hvisions.pms.entity.HvPmXcMaterialOutStockResult;
import com.hvisions.pms.entity.plan.HvPmXcMaterialCutPlan;
import com.hvisions.pms.entity.plan.HvPmXcMaterialOutStockPlan;
import com.hvisions.pms.enums.XcCutPlanStatusEnum;
import com.hvisions.pms.repository.HvPmXcMaterialOutStockResultRepository;
import com.hvisions.pms.repository.plan.HvPmXcMaterialCutPlanRepository;
import com.hvisions.pms.repository.plan.XcMaterialOutStockPlanRepository;
import com.hvisions.pms.service.HvPmXcMaterialOutStockResultDetail1Service;
import com.hvisions.pms.service.HvPmXcMaterialOutStockResultService;
import com.hvisions.thirdparty.common.dto.OutboundMaterialDTO;
import com.hvisions.thirdparty.common.dto.ProfileOutboundInfoDTO;
import com.hvisions.wms.client.StockClient;
import com.hvisions.wms.dto.stock.StockOccupyDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/5/8
 */
@Service
@Slf4j
public class HvPmXcMaterialOutStockResultServiceImpl implements HvPmXcMaterialOutStockResultService {

    @Autowired
    private HvPmXcMaterialOutStockResultMapper resultMapper;

    @Autowired
    private HvPmXcMaterialOutStockResultRepository resultRepository;

    @Autowired
    private XcMaterialOutStockPlanRepository xcMaterialOutStockPlanRepository;

    @Autowired
    private HvPmXcMaterialOutStockResultDetail1Service detail1Service;

    @Autowired
    private HvPmXcMaterialCutPlanRepository hvPmXcMaterialCutPlanRepository;

    @Autowired
    private StockClient stockClient;

    @Override
    public HvPmXcMaterialOutStockResultDTO getByTaskNo(String taskNo) {
        return resultMapper.getByTaskNo(taskNo);
    }

    @Override
    public long createOutStockResult(HvPmXcMaterialOutStockResultDTO resultDTO) {
        HvPmXcMaterialOutStockResult result = resultRepository.saveAndFlush(DtoMapper.convert(resultDTO, HvPmXcMaterialOutStockResult.class));
        return result.getId();
    }

    @Override
    public long updateOutStockResult(HvPmXcMaterialOutStockResultDTO resultDTO) {
        HvPmXcMaterialOutStockResult result = resultRepository.save(DtoMapper.convert(resultDTO, HvPmXcMaterialOutStockResult.class));
        return result.getId();
    }

    @Override
    public void deleteOutStockResult(int id) {
        resultRepository.deleteById(id);
    }

    @Override
    @Transactional
    public void addOutStockResultAndDetail(ProfileOutboundInfoDTO profileOutboundInfoDTO) throws ParseException {
        //校验数据
        this.checkProfileOutboundInfoDTO(profileOutboundInfoDTO);

        //获取出库计划
        HvPmXcMaterialOutStockPlan outStockPlan = xcMaterialOutStockPlanRepository.getByTaskNo(profileOutboundInfoDTO.getTaskCode());
        if (outStockPlan == null) {
            throw new BaseKnownException("任务号:" + profileOutboundInfoDTO.getTaskCode() + "不存在");
        }
        //更新状态
        outStockPlan.setStatus(1);
        xcMaterialOutStockPlanRepository.save(outStockPlan);
        //保存出库结果
        for (OutboundMaterialDTO outboundMaterialDTO : profileOutboundInfoDTO.getMaterialList()) {
            HvPmXcMaterialOutStockResultDetail1DTO detail1DTO = getHvPmXcMaterialOutStockResultDetail1DTO(profileOutboundInfoDTO, outboundMaterialDTO, outStockPlan);
            detail1Service.createDetail1(detail1DTO);

            //修改或删除型材备料占用的库存
            deleteOccupyStock(outStockPlan.getWorkOrderCode(),outboundMaterialDTO);
        }

    }
    //修改或删除型材备料占用的库存
    public void deleteOccupyStock(String workOrderCode, OutboundMaterialDTO outboundMaterialDTO) {
        //查询库存占用记录
        ResultVO<List<StockOccupyDTO>> stockOccupyVO = stockClient.getStockOccupyByOrderCodeAndMaterialCode(workOrderCode, outboundMaterialDTO.getMaterialCode());

        if (stockOccupyVO.isSuccess() && stockOccupyVO.getData() !=null){
            List<StockOccupyDTO> stockOccupyDTOList = stockOccupyVO.getData();
            Integer needQuantity= outboundMaterialDTO.getQuality().intValue(); //需要删除被占用的数量

            for (StockOccupyDTO stockOccupyDTO : stockOccupyDTOList) {
                //已占用的库存数量
                Integer occupyQuantity = stockOccupyDTO.getQuantity().intValue();
                if (occupyQuantity>0 && occupyQuantity<=needQuantity){
                    //删除占用库存记录
                    stockClient.occupyRemove(stockOccupyDTO.getId());
                    needQuantity = needQuantity - occupyQuantity;

                }else if (occupyQuantity>needQuantity){
                    // 修改占用库存记录
                    int upOccupyQuantity = occupyQuantity - needQuantity;
                    stockOccupyDTO.setQuantity(BigDecimal.valueOf(upOccupyQuantity));
                    stockClient.upOccupyStock(stockOccupyDTO);
                    break;
                }
            }
        }
    }



    private HvPmXcMaterialOutStockResultDetail1DTO getHvPmXcMaterialOutStockResultDetail1DTO(ProfileOutboundInfoDTO profileOutboundInfoDTO, OutboundMaterialDTO outboundMaterialDTO, HvPmXcMaterialOutStockPlan outStockPlan) {
        HvPmXcMaterialOutStockResultDetail1DTO detail1DTO = new HvPmXcMaterialOutStockResultDetail1DTO();
        //主表id
        detail1DTO.setResultId(outStockPlan.getId().longValue());
        //任务号
        detail1DTO.setTaskNo(profileOutboundInfoDTO.getTaskCode());
        //仓库编号
        detail1DTO.setWarehouseCode(outboundMaterialDTO.getWarehouseCode());
        //库位编号
        detail1DTO.setLocationCode(outboundMaterialDTO.getLocationCode());
        //物料编号
        detail1DTO.setMaterialCode(outboundMaterialDTO.getMaterialCode());
        //批次号
        detail1DTO.setBatchCode(outboundMaterialDTO.getBatchCode());
        //出库型材数量
        detail1DTO.setQuality(outboundMaterialDTO.getQuality().intValue());
        //托盘型材总数
        detail1DTO.setTotalQuality(outboundMaterialDTO.getTotalQuality().intValue());
        //托盘号
        detail1DTO.setPalletCode(outboundMaterialDTO.getPalletCode());
        //出库时间
        detail1DTO.setOutTime(profileOutboundInfoDTO.getOutTime());
        //出库人
        detail1DTO.setOperationUserCode(profileOutboundInfoDTO.getOperationUserCode());

        return detail1DTO;
    }

    /**
     * 根据任务号修改切割计划状态为“开始切割”
     * @param taskCode
     */
    @Override
    public void upXcCutPlanStatusByTaskCode(String taskCode) {
        //查询型材出库计划
        HvPmXcMaterialOutStockPlan outStockPlan = xcMaterialOutStockPlanRepository.getByTaskNo(taskCode);
        if (outStockPlan != null) {
            //查询型材切割计划
            HvPmXcMaterialCutPlan hvPmXcMaterialCutPlan = hvPmXcMaterialCutPlanRepository.getByOrderNoEquals(outStockPlan.getWorkOrderCode());
            //修改型材切割计划状态为 “开始切割”
            hvPmXcMaterialCutPlan.setStatus(XcCutPlanStatusEnum.START_CUT.getCode());
            hvPmXcMaterialCutPlanRepository.save(hvPmXcMaterialCutPlan);
        }
    }

    //校验数据是否合法
    public void checkProfileOutboundInfoDTO(ProfileOutboundInfoDTO profileOutboundInfoDTO){
        if (profileOutboundInfoDTO.getTaskCode() == null || profileOutboundInfoDTO.getTaskCode().isEmpty()) {
            throw new BaseKnownException("任务号不能为空");
        }

        if (profileOutboundInfoDTO.getMaterialList().isEmpty()) {
            throw new BaseKnownException("出库物料信息不能为空");
        }

        for (OutboundMaterialDTO outboundMaterialDTO : profileOutboundInfoDTO.getMaterialList()) {
            if (outboundMaterialDTO.getWarehouseCode().isEmpty()) {
                throw new BaseKnownException("仓库编号不能为空");
            }
            if (outboundMaterialDTO.getLocationCode()== null || outboundMaterialDTO.getLocationCode().isEmpty()) {
                throw new BaseKnownException("库位编号不能为空");
            }
            if (outboundMaterialDTO.getMaterialCode()== null || outboundMaterialDTO.getMaterialCode().isEmpty()) {
                throw new BaseKnownException("物料号不能为空");
            }
            if (outboundMaterialDTO.getQuality()== null || outboundMaterialDTO.getQuality()<=0) {
                throw new BaseKnownException("物料数量不能小于等于0");
            }
            if (outboundMaterialDTO.getPalletCode()== null || outboundMaterialDTO.getPalletCode().isEmpty()) {
                throw new BaseKnownException("托盘编号（料框）不能为空");
            }
        }
    }

}
