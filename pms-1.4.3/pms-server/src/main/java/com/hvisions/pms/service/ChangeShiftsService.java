package com.hvisions.pms.service;

import com.hvisions.pms.changeshiftsdto.ChangeShiftsRecordDTO;
import com.hvisions.pms.changeshiftsdto.ChangeShiftsRecordQueryDTO;
import org.springframework.data.domain.Page;

import java.text.ParseException;

/**
 * <p>Title: ChangeShiftsService</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-10-12</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
public interface ChangeShiftsService {

    /**
     * 获取交接班记录表单
     *
     * @param workCenterId 工位ID
     * @return ChangeShiftsRecordDTO
     * @throws ParseException 异常
     */
    ChangeShiftsRecordDTO getRecord(int workCenterId) throws ParseException;


    /**
     * 确认交班
     *
     * @param changeShiftsRecordDTO 交接班记录Id
     */
    void changeRecordState(ChangeShiftsRecordDTO changeShiftsRecordDTO);


    /**
     * 撤销交班申请
     *
     * @param id 交接班记录ID
     */
    void revoke(int id);

    /**
     * 撤销交班申请
     *
     * @param handMan     交班人
     * @param workCenterId 工位ID
     */
    void revokeChangeShift(int handMan, int workCenterId);

    /**
     * 接班确认操作
     *
     * @param id        交接班记录ID
     * @param operation 操作状态¬
     */
    void returnChangeShift(int id, Integer operation);

    /**
     * 获取待接班记录
     *
     * @param overMan     接班人
     * @param workCenterId 工位ID
     * @return 接班记录
     */
    ChangeShiftsRecordDTO getOverRecord(int overMan, int workCenterId);


    /**
     * 分页查询 交接班记录
     *
     * @param changeShiftsRecordQueryDTO 查询条件
     * @return 交接班记录
     */
    Page<ChangeShiftsRecordDTO> getRecordByQuery(ChangeShiftsRecordQueryDTO changeShiftsRecordQueryDTO);
}