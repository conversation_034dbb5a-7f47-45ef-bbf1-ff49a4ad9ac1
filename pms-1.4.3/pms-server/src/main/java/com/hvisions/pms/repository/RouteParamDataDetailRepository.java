package com.hvisions.pms.repository;

import com.hvisions.pms.entity.HvRouteParamDataDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: RouteParamDataDetailRepository</p >
 * <p>Description: </p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/9/16</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Repository
public interface RouteParamDataDetailRepository extends JpaRepository<HvRouteParamDataDetail, Integer> {
    /**
     * 根据记录id查询详情数据
     *
     * @param recordId 记录id
     * @return 详情数据
     */
    List<HvRouteParamDataDetail> findByParamDataId(Integer recordId);
}
