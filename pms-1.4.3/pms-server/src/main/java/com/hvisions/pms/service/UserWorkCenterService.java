package com.hvisions.pms.service;

import com.hvisions.hiperbase.equipment.location.LocationDTO;
import com.hvisions.pms.dto.CrewWorkCenterDTO;
import com.hvisions.pms.dto.UserWorkCenterDTO;
import com.hvisions.pms.dto.WorkCenterUserNameDTO;
import com.hvisions.pms.dto.WorkCenterUserQueryDTO;

import java.util.List;

/**
 * <p>Title: UserEquipmentService</p >
 * <p>Description: 人员工位关系服务层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/11</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface UserWorkCenterService {


    /**
     * 绑定人员与工位关系
     *
     * @param userWorkCenterDTO dto
     */
    void createUserWorkCenter(UserWorkCenterDTO userWorkCenterDTO);


    /**
     * 根据用户ID和工位ID删除关联关系
     *
     * @param workCenterId 工位ID
     * @param userId       用户ID
     */
    void deleteByWorkCenterIdAndUserId(int workCenterId, int userId);

    /**
     * 根据班组ID和工位ID删除关联关系
     *
     * @param workCenterId 工位ID
     * @param crewId       用户ID
     */
    void deleteByWorkCenterIdAndCrewId(int workCenterId, int crewId);


    /**
     * 根据人员ID查询工位列表信息
     *
     * @param userId 用户ID
     * @return 人员关联下工位信息
     */
    List<LocationDTO> getWorkCenterByUserId(int userId);

    /**
     * 班组与工位绑定关系
     *
     * @param crewWorkCenterDTO DTO
     */
    void createCrewWorkCenter(CrewWorkCenterDTO crewWorkCenterDTO);

    /**
     * 根据产线查询工位人员绑定信息
     *
     * @param cellId 产线信息
     * @return 工位人员绑定信息
     */
    List<WorkCenterUserQueryDTO> getAllWorkCenterByCellId(int cellId);

    /**
     * 根据班组ID查询工位信息
     *
     * @param crewId 班组ID
     * @return 工位信息列表
     */
    List<LocationDTO> getWorkCenterByCrewId(int crewId);

    /**
     * 根据工位查询工位所绑定人员信息
     *
     * @param workCenterId 工位信息
     * @return 人员信息
     */
    List<WorkCenterUserNameDTO> getUserByWorkCenterId(Integer workCenterId);

    /**
     * 根据工位查询工位所绑定人员信息并且所绑班组下人员
     *
     * @param workCenterId 工位信息
     * @return 人员信息
     */
    List<WorkCenterUserNameDTO> getAllUserByWorkCenterId(Integer workCenterId);
}
