package com.hvisions.pms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.pms.dto.productWorkOrder.ProductWorkOrderDTO;
import com.hvisions.pms.dto.productWorkOrder.ProductWorkOrderQueryDTO;
import com.hvisions.pms.entity.productWorkOrder.ProductWorkOrder;
import org.springframework.data.domain.Page;

import java.util.List;

public interface ProductWorkOrderService extends IService<ProductWorkOrder> {
    Page<ProductWorkOrderDTO> getPage(ProductWorkOrderQueryDTO queryDTO);
    List<ProductWorkOrder> findListByCondition (ProductWorkOrderQueryDTO queryDTO);
}

