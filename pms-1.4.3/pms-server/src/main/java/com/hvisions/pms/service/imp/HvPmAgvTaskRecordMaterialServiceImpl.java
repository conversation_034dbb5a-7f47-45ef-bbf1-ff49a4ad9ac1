package com.hvisions.pms.service.imp;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.pms.dao.HvPmAgvTaskRecordMaterialMapper;
import com.hvisions.pms.entity.HvPmAgvTaskRecordMaterial;
import com.hvisions.pms.service.HvPmAgvTaskRecordMaterialService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-05-15 9:02
 */
@Service
public class HvPmAgvTaskRecordMaterialServiceImpl extends ServiceImpl<HvPmAgvTaskRecordMaterialMapper, HvPmAgvTaskRecordMaterial> implements HvPmAgvTaskRecordMaterialService {


    @Autowired
    private HvPmAgvTaskRecordMaterialMapper hvPmAgvTaskRecordMaterialMapper;

    @Override
    public List<HvPmAgvTaskRecordMaterial> findListByRaskRecordId(Long raskRecordId) {
        QueryWrapper<HvPmAgvTaskRecordMaterial> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("rask_record_id",raskRecordId);
        return this.list(queryWrapper);
    }

}
