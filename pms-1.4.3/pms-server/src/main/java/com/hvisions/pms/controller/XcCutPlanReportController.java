package com.hvisions.pms.controller;

import com.hvisions.pms.dto.HomeSevenDataDTO;
import com.hvisions.pms.entity.CutPartsQueryDTO;
import com.hvisions.pms.entity.CuttingCountResultDTO;
import com.hvisions.pms.service.XcCutPlanReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <P>    <P>
 *
 * <AUTHOR>
 * @date 2024/11/19
 */
@Slf4j
@Api("/型材切割计划报表")
@RestController
@RequestMapping("/xcCutPlanReport")
public class XcCutPlanReportController {

    @Autowired
    private XcCutPlanReportService xcCutPlanReportService;

    @ApiOperation("/获取某天型材切割完成数量")
    @RequestMapping("/getOneDataXcCutByLineCode/{lineCode}/{date}")
    public Integer getOneDataXcCutByLineCode(@PathVariable("lineCode") String  lineCode, @PathVariable("date") String date){
        return xcCutPlanReportService.getOneDataXcCutByLineCode(lineCode,date);
    }

    @ApiOperation("/获取某天型材切割计划数量")
    @RequestMapping("/getOneDataXcPlanCutByLineCode/{lineCode}/{date}")
    public Integer getOneDataXcPlanCutByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
        return xcCutPlanReportService.getOneDataXcPlanCutByLineCode(lineCode,date);
    }

    @ApiOperation("/获取型材切割计划的七天完成数量")
    @RequestMapping("/getXcCutSevenDataByLineCode/{lineCode}/{date}")
    public HomeSevenDataDTO getXcCutSevenDataByLineCode(@PathVariable("lineCode") String  lineCode, @PathVariable("date") String date){
        return xcCutPlanReportService.getXcCutSevenDataByLineCode(lineCode,date);
    }


    @ApiOperation("/获取型材切割计划的一周完成数量")
    @RequestMapping("/getOneWeekXcCutByLineCode/{lineCode}/{date}")
    public Integer getOneWeekXcCutByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
        return xcCutPlanReportService.getOneWeekXcCutByLineCode(lineCode,date);
    }

    @ApiOperation("/获取型材切割计划的一个月完成数量")
    @RequestMapping("/getOneMonthXcCutByLineCode/{lineCode}/{date}")
    public Integer getOneMonthXcCutByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
        return xcCutPlanReportService.getOneMonthXcCutByLineCode(lineCode,date);
    }

    @ApiOperation("/获取型材板切割计划的一个年完成数量")
    @RequestMapping("/getOneYearXcCutByLineCode/{lineCode}/{date}")
    public Integer getOneYearXcCutByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
        return xcCutPlanReportService.getOneYearXcCutByLineCode(lineCode,date);
    }

    @ApiOperation("/获取型材切割计划的一周计划数量")
    @RequestMapping("/getOneWeekXcPlanCutByLineCode/{lineCode}/{date}")
    public Integer getOneWeekXcPlanCutByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
        return xcCutPlanReportService.getOneWeekXcPlanCutByLineCode(lineCode,date);
    }


    @ApiOperation("/获取型材切割计划的一个月计划数量")
    @RequestMapping("/getOneMonthXcPlanCutByLineCode/{lineCode}/{date}")
    public Integer getOneMonthXcPlanCutByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
        return xcCutPlanReportService.getOneMonthXcPlanCutByLineCode(lineCode,date);
    }

    @ApiOperation("/获取型材切割计划的一年计划数量")
    @RequestMapping("/getOneYearXcPlanCutByLineCode/{lineCode}/{date}")
    public Integer getOneYearXcPlanCutByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
        return xcCutPlanReportService.getOneYearXcPlanCutByLineCode(lineCode,date);
    }


    @ApiOperation("/获取某天型材切割零件切割完成数量")
    @RequestMapping("/getOneDataXcCutPartByLineCode/{lineCode}/{date}")
    public Integer getOneDataXcCutPartByLineCode(@PathVariable("lineCode") String  lineCode, @PathVariable("date") String date){
        return xcCutPlanReportService.getOneDataXcCutPartByLineCode(lineCode,date);
    }

    @ApiOperation("/获取某天型材切割零件计划数量")
    @RequestMapping("/getOneDataXcPlanCutPartByLineCode/{lineCode}/{date}")
    public Integer getOneDataXcPlanCutPartByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
        return xcCutPlanReportService.getOneDataXcPlanCutPartByLineCode(lineCode,date);
    }

    @ApiOperation("/获取型材切割零件的七天完成数量")
    @RequestMapping("/getXcCutPartSevenDataByLineCode/{lineCode}/{date}")
    public HomeSevenDataDTO getXcCutPartSevenDataByLineCode(@PathVariable("lineCode") String  lineCode, @PathVariable("date") String date){
        return xcCutPlanReportService.getXcCutPartSevenDataByLineCode(lineCode,date);
    }


    @ApiOperation("/获取型材切割零件计划的一周完成数量")
    @RequestMapping("/getOneWeekXcCutPartByLineCode/{lineCode}/{date}")
    public Integer getOneWeekXcCutPartByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
        return xcCutPlanReportService.getOneWeekXcCutPartByLineCode(lineCode,date);
    }

    @ApiOperation("/获取型材切割零件计划的一个月完成数量")
    @RequestMapping("/getOneMonthXcCutPartByLineCode/{lineCode}/{date}")
    public Integer getOneMonthXcCutPartByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
        return xcCutPlanReportService.getOneMonthXcCutPartByLineCode(lineCode,date);
    }

    @ApiOperation("/获取型材板切割零件计划的一个年完成数量")
    @RequestMapping("/getOneYearXcCutPartByLineCode/{lineCode}/{date}")
    public Integer getOneYearXcCutPartByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
        return xcCutPlanReportService.getOneYearXcCutPartByLineCode(lineCode,date);
    }

    @ApiOperation("/获取型材切割零件计划的一周计划数量")
    @RequestMapping("/getOneWeekXcPlanCutPartByLineCode/{lineCode}/{date}")
    public Integer getOneWeekXcPlanCutPartByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
        return xcCutPlanReportService.getOneWeekXcPlanCutPartByLineCode(lineCode,date);
    }


    @ApiOperation("/获取型材切割计划的一个月计划数量")
    @RequestMapping("/getOneMonthXcPlanCutPartByLineCode/{lineCode}/{date}")
    public Integer getOneMonthXcPlanCutPartByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
        return xcCutPlanReportService.getOneMonthXcPlanCutPartByLineCode(lineCode,date);
    }

    @ApiOperation("/获取型材切割计划的一年计划数量")
    @RequestMapping("/getOneYearXcPlanCutPartByLineCode/{lineCode}/{date}")
    public Integer getOneYearXcPlanCutPartByLineCode(@PathVariable("lineCode") String  lineCode,@PathVariable("date") String date){
        return xcCutPlanReportService.getOneYearXcPlanCutPartByLineCode(lineCode,date);
    }

    @ApiOperation("/获取型材切割指定时间内的切割根数")
    @PostMapping("/cut-parts/count")
    public Integer getCuttingCountDateRange(@RequestBody CutPartsQueryDTO cutPartsQueryDTO) {
        return xcCutPlanReportService.getCuttingCountDateRange(cutPartsQueryDTO);
    }

    @ApiOperation("/获取型材切割指定时间内的计划切割根数")
    @PostMapping("/cut-parts/planCount")
    public Integer getCuttingPlanCountDateRange(@RequestBody CutPartsQueryDTO cutPartsQueryDTO) {
        return xcCutPlanReportService.getCuttingPlanCountDateRange(cutPartsQueryDTO);
    }

    @ApiOperation("获取型材切割指定的每日切割根数")
    @PostMapping("/cut-parts/dayCount")
    public List<CuttingCountResultDTO> getCuttingCountByDate(@RequestBody CutPartsQueryDTO cutPartsQueryDTO) {
        return xcCutPlanReportService.getCuttingCountByDate(cutPartsQueryDTO);
    }

}
