package com.hvisions.pms.repository.changeshift;

import com.hvisions.pms.entity.changeshift.HvPmInspect;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: HvPmInspectRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-10-12</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Repository
public interface InspectRepository extends JpaRepository<HvPmInspect, Integer> {

    /**
     * 根据交接班记录ID查询检查记录
     *
     * @param changeShiftsId 交接班记录ID
     * @return 检查记录列表
     */
    List<HvPmInspect> getAllByChangeShiftsId(int changeShiftsId);
}