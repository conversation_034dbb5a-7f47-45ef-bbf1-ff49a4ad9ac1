package com.hvisions.pms.service;

import com.hvisions.pms.dto.LocationWithTaskCount;

import java.util.List;

/**
 * <p>Title: QueryService</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2022/7/5</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface QueryService {
    /**
     * 获取人员的工位信息
     *
     * @param userId 用户id
     * @return 人员工位信息
     */
    List<LocationWithTaskCount> getWorkCenterWithTaskCount(Integer userId);
}

    
    
    
    