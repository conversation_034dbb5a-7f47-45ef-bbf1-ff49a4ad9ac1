package com.hvisions.pms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.pms.dto.materialRequirement.MaterialRequirementDTO;
import com.hvisions.pms.dto.materialRequirement.MaterialRequirementQueryDTO;
import com.hvisions.pms.entity.HvPmMaterialRequirement;
import org.springframework.data.domain.Page;

import java.util.List;

public interface MaterialRequirementService extends IService<HvPmMaterialRequirement> {
    Page<MaterialRequirementDTO> getPage(MaterialRequirementQueryDTO queryDTO);
    List<HvPmMaterialRequirement> findListByCondition(MaterialRequirementQueryDTO materialRequirementQueryDTO);
}

