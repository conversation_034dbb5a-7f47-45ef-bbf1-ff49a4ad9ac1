package com.hvisions.pms.service;

import com.hvisions.pms.dto.OperationQueryDTO;
import com.hvisions.pms.task.dto.AssignDTO;
import com.hvisions.pms.task.dto.OrderTaskDTO;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;

/**
 * <p>Title: OrderTaskService</p >
 * <p>Description: 生产任务service</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/3/24</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */

public interface OrderTaskService {

    /**
     * 根据工单ID查询生产任务
     *
     * @param orderId 工单Id
     * @return 生产任务
     */
    List<OrderTaskDTO> getAllByOrderId(Integer orderId);


    /**
     * 根据工位Id查询 生产任务
     *
     * @param workCenterId 工位ID
     * @return 生产任务信息
     */
    List<OrderTaskDTO> getAllByWorkCenterId(Integer workCenterId);


    /**
     * 根据ID列表查询生产任务
     *
     * @param idIn 生产任务ID列表
     * @return 生产任务
     */
    List<OrderTaskDTO> getOperationByIdIn(List<Integer> idIn);


    /**
     * 根据工位id列表查询生产任务数量
     *
     * @param workCenterIds 工位id列表
     * @return 生产任务数量
     */
    Map<Integer, Integer> getTaskCountByWorkCenterId(List<Integer> workCenterIds);

    /**
     * 根据工位id，工序状态查询工单信息
     *
     * @param operationQueryDTO 分页查询对象
     * @return 工序的分页列表
     */
    Page<OrderTaskDTO> getTaskByQuery(OperationQueryDTO operationQueryDTO);


    /**
     * 分配任务
     *
     * @param assignDTO 任务与人员
     */
    void assignTask(AssignDTO assignDTO);

    /**
     * 根据产线报工记录更新数据
     * @param orderTaskDTO
     */
    void updateByLineReportRecord(OrderTaskDTO orderTaskDTO);

    int deleteByWorkOrderCode(String workOrderCode);
}