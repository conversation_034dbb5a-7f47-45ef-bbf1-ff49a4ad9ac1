package com.hvisions.pms.repository.changeshift;

import com.hvisions.pms.entity.changeshift.HvPmBuildInspectWorkCenter;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>Title: BulidOperationRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-10-16</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Repository
public interface BuildInspectWorkCenterRepository extends JpaRepository<HvPmBuildInspectWorkCenter, Integer> {

    /**
     * 根据工位Id查询检查项目
     *
     * @param workCenterId 工位ID
     * @return 检查项目
     */
    HvPmBuildInspectWorkCenter getAllByWorkCenterId(int workCenterId);

    /**
     * 根据检查项目ID查询所绑定到工位ID列表
     *
     * @param itemId 检查项目ID
     * @return 工位ID;
     */
    List<HvPmBuildInspectWorkCenter> getAllByInspectionItemId(int itemId);


    /**
     * 根据Id列表删除检查项目工位关联关系
     *
     * @param idList 关联关系ID列表
     */
    @Transactional
    void deleteByIdIn(List<Integer> idList);

    /**
     * 根据工位Id还有检查项目Id删除关联关系
     *
     * @param workCenterId 工位Id
     * @param itemId       关联项目Id
     */
    @Transactional
    void deleteByWorkCenterIdAndInspectionItemId(int workCenterId, int itemId);
}