package com.hvisions.pms.service.imp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.client.MaterialClient;
import com.hvisions.pms.dao.HvPmCallFrameMaterialMapper;
import com.hvisions.pms.dto.HvPmCallMaterialDTO;
import com.hvisions.pms.dto.WorkOrderDTO;
import com.hvisions.pms.entity.HvPmPlanProductBom;
import com.hvisions.pms.entity.HvPmProductBomDetail;
import com.hvisions.pms.entity.plan.HvPmCallFrameMaterial;
import com.hvisions.pms.repository.HvPmProductBomDetailRepository;
import com.hvisions.pms.service.HvPmCallFrameMaterialService;
import com.hvisions.pms.service.HvPmPlanProductBomService;
import com.hvisions.pms.service.WorkOrderService;
import com.hvisions.thirdparty.common.dto.*;
import com.hvisions.wms.client.StockClient;
import com.hvisions.wms.dto.stock.StockMaterialDTO;
import com.hvisions.wms.dto.stock.StockQueryDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class HvPmCallFrameMaterialServiceImpl extends ServiceImpl<HvPmCallFrameMaterialMapper, HvPmCallFrameMaterial> implements HvPmCallFrameMaterialService {

    @Autowired
    private HvPmCallFrameMaterialService hvPmCallFrameMaterialService;

    @Autowired
    private StockClient stockClient;

    @Autowired
    private HvPmProductBomDetailRepository bomDetailRepository;

    @Autowired
    private WorkOrderService workOrderService;

    @Autowired
    private MaterialClient materialClient;

    @Autowired
    private HvPmPlanProductBomService hvPmPlanProductBomService;

    @Autowired
    private HvPmCallFrameMaterialMapper hvPmCallFrameMaterialMapper;

    @Override
    public void updateFrameMaterial(MaterialCuttingLineReportDTO steelPlateDTO) {
        for (MaterialsDTO materialsDTO : steelPlateDTO.getMaterialList()) {
            LambdaQueryWrapper<HvPmCallFrameMaterial> lqw = new LambdaQueryWrapper<>();
            lqw.eq(HvPmCallFrameMaterial::getFrameCode, materialsDTO.getPalletCode())
                    .eq(HvPmCallFrameMaterial::getMaterialCode, materialsDTO.getMaterialCode());
            HvPmCallFrameMaterial hvPmCallFrameMaterial = baseMapper.selectOne(lqw);
            if (hvPmCallFrameMaterial != null) {
                hvPmCallFrameMaterial.setOccupationQuantity(
                        hvPmCallFrameMaterial.getOccupationQuantity()
                                .subtract(BigDecimal.valueOf(materialsDTO.getQualifiedQty()))
                );
                hvPmCallFrameMaterial.setUsedQuantity(
                        hvPmCallFrameMaterial.getUsedQuantity()
                                .add(BigDecimal.valueOf(materialsDTO.getQualifiedQty())));
                hvPmCallFrameMaterial.setRemainingQuantity(
                        hvPmCallFrameMaterial.getQuantity()
                                .subtract(hvPmCallFrameMaterial.getUsedQuantity())
                );
                // 确保 RemainingQuantity 不会为负数
                if (hvPmCallFrameMaterial.getRemainingQuantity().compareTo(BigDecimal.ZERO) < 0) {
                    throw new BaseKnownException("线边料框扣减异常<" + materialsDTO.getPalletCode() + ">被扣减为负数!");
                }
                baseMapper.updateById(hvPmCallFrameMaterial);
                if (hvPmCallFrameMaterial.getOccupationQuantity().compareTo(BigDecimal.ZERO) == 0 &&
                        hvPmCallFrameMaterial.getRemainingQuantity().compareTo(BigDecimal.ZERO) == 0) {
                    baseMapper.deleteById(hvPmCallFrameMaterial);
                }
            }
        }
    }

    @Override
    public List<HvPmCallFrameMaterial> findFrameMaterials(String materialCode, String shipNo, String blockCode) {
        return baseMapper.findFrameMaterials(materialCode, shipNo, blockCode);
    }

    @Transactional
    @Override
    public void saveFrameMaterial(String frameCode) {
        Map<Long, String> map = new HashMap<>();
        //保存料框与bom、工单、零件id的详细关系
        List<HvPmProductBomDetail> bomDetailList = new ArrayList<>();
        //根据料框查询详细关系
        List<HvPmProductBomDetail> bomDetails = bomDetailRepository.findByPalletCode(frameCode);
        for (HvPmProductBomDetail bomDetail : bomDetails) {
            //根据工单号查询工单
            WorkOrderDTO workOrderDTO = workOrderService.findByWorkOrderCode(bomDetail.getWorkOrderCode());
            //存在工单，查询该工单绑定的bom
            if (workOrderDTO != null) {
                //保存bomDetailId对应的零件 --- bomDetail存储的是零件id
                HvPmPlanProductBom productBom = hvPmPlanProductBomService.getById(bomDetail.getPlanProductBomId());
                map.put(bomDetail.getId(),productBom.getMaterialCode());
                bomDetailList.add(bomDetail);
            }
        }
        //保存送去产线的料框的所有物料数据
        List<HvPmCallFrameMaterial> recordMaterialList = new ArrayList<>();
        StockQueryDTO query = new StockQueryDTO();
        query.setFrameCode(frameCode);
        //查询库存中该料框的物料数据
        ResultVO<List<StockMaterialDTO>> stocks = stockClient.getStocks(query);
        List<StockMaterialDTO> stockMaterialDTOS = stocks.getData();
        if (stockMaterialDTOS != null) {
            for (StockMaterialDTO stockMaterialDTO : stockMaterialDTOS) {
                HvPmCallFrameMaterial recordMaterial = getHvPmCallFrameMaterial(stockMaterialDTO);
                recordMaterialList.add(recordMaterial);
            }
        }
        //保存库存中的料到线边库
        boolean flag = hvPmCallFrameMaterialService.saveBatch(recordMaterialList);
        if (!flag) {
            log.error("线边料框保存失败！");
        }
        //库存占用转移  平库-->线边
        //LambdaQueryWrapper<HvPmCallFrameMaterial> lqw = new LambdaQueryWrapper<>();
        //lqw.eq(HvPmCallFrameMaterial::getFrameCode,frameCode);
        //List<HvPmCallFrameMaterial> frameMaterials = hvPmCallFrameMaterialService.list(lqw);
        //for (HvPmCallFrameMaterial frameMaterial : frameMaterials) {
        //    for (HvPmProductBomDetail hvPmProductBomDetail : bomDetailList) {
        //        if (frameMaterial.getMaterialCode().equals(map.get(hvPmProductBomDetail.getId()))) {
        //            hvPmProductBomDetail.setStockId(Math.toIntExact(frameMaterial.getId()));
        //            bomDetailRepository.save(hvPmProductBomDetail);
        //        }
        //    }
        //}
    }

    @Override
    public HvPmCallFrameMaterial findOneByFrameCodeAndMCode(String frameCode, String materialCode) {
        LambdaQueryWrapper<HvPmCallFrameMaterial> lqw = new LambdaQueryWrapper<>();
        lqw.eq(HvPmCallFrameMaterial::getFrameCode, frameCode)
                .eq(HvPmCallFrameMaterial::getMaterialCode, materialCode);
        return baseMapper.selectOne(lqw);
    }

    @Override
    public void updateByQuery(HvPmCallFrameMaterialDTO callFrameMaterialDTO) {
        baseMapper.update(null,
                new LambdaUpdateWrapper<HvPmCallFrameMaterial>()
                        .eq(HvPmCallFrameMaterial::getFrameCode, callFrameMaterialDTO.getFrameCode())
                        .set(callFrameMaterialDTO.getIsPresent() != null,
                                HvPmCallFrameMaterial::getIsPresent, callFrameMaterialDTO.getIsPresent())
                        .set(callFrameMaterialDTO.getWarehouseCode() != null && !callFrameMaterialDTO.getWarehouseCode().isEmpty(),
                                HvPmCallFrameMaterial::getWarehouseCode, callFrameMaterialDTO.getWarehouseCode())
        );
    }

    @Override
    @Transactional
    public void updateMaterialDTO(LineSchedulingDTO lineSchedulingDTO) {
        // 获取所有相关的线边库物料信息
        String frameCode = lineSchedulingDTO.getFrameCode();
        List<HvPmCallFrameMaterial> hvPmCallFrameMaterials = baseMapper.selectList(
                new LambdaQueryWrapper<HvPmCallFrameMaterial>()
                        .eq(HvPmCallFrameMaterial::getFrameCode, frameCode));

        List<HvPmProductBomDetail> bomDetails = bomDetailRepository.findByPalletCode(frameCode);

        if (lineSchedulingDTO.getMaterialList() == null || lineSchedulingDTO.getMaterialList().isEmpty()) {
            List<Long> collect = hvPmCallFrameMaterials.stream().map(HvPmCallFrameMaterial::getId).collect(Collectors.toList());
            baseMapper.deleteBatchIds(collect);
            bomDetailRepository.deleteAll(bomDetails);
            return;
        }

        // 根据物料代码分组，计算每种物料的总数量
        Map<String, BigDecimal> materialTotalQuantityMap = lineSchedulingDTO.getMaterialList().stream()
                .collect(Collectors.groupingBy(LineSchedulingMaterialDTO::getMaterialCode,
                        Collectors.reducing(BigDecimal.ZERO, materialDTO -> BigDecimal.valueOf(materialDTO.getQuality()), BigDecimal::add)));

        Map<String, HvPmProductBomDetail> map = new HashMap<>();

        for (HvPmProductBomDetail bomDetail : bomDetails) {
            if (bomDetail.getState() == 1) {
                HvPmCallFrameMaterial callFrameMaterial = baseMapper.selectById(bomDetail.getStockId());
                if (callFrameMaterial == null) continue;
                map.put(bomDetail.getWorkOrderCode() + callFrameMaterial.getMaterialCode(), bomDetail);
            }
        }
        for (LineSchedulingMaterialDTO materialDTO : lineSchedulingDTO.getMaterialList()) {
            HvPmProductBomDetail hvPmProductBomDetail = map.get(materialDTO.getWorkOrderCode() + materialDTO.getMaterialCode());
            if (hvPmProductBomDetail != null) {
                hvPmProductBomDetail.setState(0);
                bomDetailRepository.save(hvPmProductBomDetail);
                map.remove(hvPmProductBomDetail.getWorkOrderCode() + materialDTO.getMaterialCode());
            }
        }
        if (!map.isEmpty()) {
            for (Map.Entry<String, HvPmProductBomDetail> bomDetailEntry : map.entrySet()) {
                bomDetailEntry.getValue().setState(2);
                bomDetailRepository.save(bomDetailEntry.getValue());
            }
        }

        // 更新已使用数量
        for (HvPmCallFrameMaterial hvPmCallFrameMaterial : hvPmCallFrameMaterials) {
            BigDecimal totalQuantity = materialTotalQuantityMap.getOrDefault(hvPmCallFrameMaterial.getMaterialCode(), BigDecimal.ZERO);
            if (totalQuantity.compareTo(hvPmCallFrameMaterial.getQuantity()) > 0) {
                throw new BaseKnownException("线边料框扣减异常<" + hvPmCallFrameMaterial.getMaterialCode() + ">余料大于料框中该物料总数！");
            }
            if (totalQuantity.compareTo(BigDecimal.ZERO) == 0) {
                baseMapper.deleteById(hvPmCallFrameMaterial);
                bomDetailRepository.deleteByStockId(Math.toIntExact(hvPmCallFrameMaterial.getId()));
            }
            hvPmCallFrameMaterial.setRemainingQuantity(totalQuantity);
            hvPmCallFrameMaterial.setUsedQuantity(hvPmCallFrameMaterial.getQuantity().subtract(totalQuantity));
            hvPmCallFrameMaterial.setIsPresent(Boolean.FALSE);
            baseMapper.updateById(hvPmCallFrameMaterial);
        }

    }

    @Override
    public void updateStatus(String frameCode) {
        // 获取所有相关的线边库物料信息
        List<HvPmCallFrameMaterial> hvPmCallFrameMaterials = baseMapper.selectList(
                new LambdaQueryWrapper<HvPmCallFrameMaterial>()
                        .eq(HvPmCallFrameMaterial::getFrameCode, frameCode));
        for (HvPmCallFrameMaterial hvPmCallFrameMaterial : hvPmCallFrameMaterials) {
            hvPmCallFrameMaterial.setStatus(true);
            baseMapper.updateById(hvPmCallFrameMaterial);
        }
    }

    @Override
    public Page<HvPmCallFrameMaterial> getPage(HvPmCallMaterialDTO callMaterialDTO) {
        return PageHelperUtil.getPage(hvPmCallFrameMaterialMapper::getPage, callMaterialDTO);

    }

    private HvPmCallFrameMaterial getHvPmCallFrameMaterial(StockMaterialDTO stockMaterialDTO) {
        HvPmCallFrameMaterial recordMaterial = new HvPmCallFrameMaterial();
        recordMaterial.setId(Long.valueOf(stockMaterialDTO.getId()));
        //库区编码
        recordMaterial.setWarehouseCode(stockMaterialDTO.getLocationAreaCode());
        //零件编号
        recordMaterial.setMaterialCode(stockMaterialDTO.getMaterialCode());
        //料框编码
        recordMaterial.setFrameCode(stockMaterialDTO.getFrameCode());
        //总数
        recordMaterial.setQuantity(stockMaterialDTO.getQuantity());
        //剩余数量：默认为总数，在报工时候进行实际扣减
        recordMaterial.setRemainingQuantity(stockMaterialDTO.getQuantity());
        //占用数量
        recordMaterial.setOccupationQuantity(stockMaterialDTO.getUsedCount());
        //船号
        recordMaterial.setShipNo(stockMaterialDTO.getShipNo());
        recordMaterial.setStatus(false);
        recordMaterial.setCreateTime(stockMaterialDTO.getCreateTime());
        recordMaterial.setUpdateTime(new Date());
        recordMaterial.setIsPresent(true);
        recordMaterial.setBlockCode(stockMaterialDTO.getBlockCode());
        return recordMaterial;
    }
}
