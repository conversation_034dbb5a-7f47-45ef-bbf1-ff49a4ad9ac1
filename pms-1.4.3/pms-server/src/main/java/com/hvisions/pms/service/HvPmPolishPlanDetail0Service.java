package com.hvisions.pms.service;

import com.hvisions.pms.entity.plan.HvPmPolishPlanDetail0;
import com.hvisions.pms.plan.HvPmPolishPlanDetail0DTO;

import java.util.List;

/**
 * <P> 打磨计划从表 零件信息<P>
 *
 * <AUTHOR>
 * @date 2024/9/23
 */
public interface HvPmPolishPlanDetail0Service {

    long createDetail0(HvPmPolishPlanDetail0DTO hvPmPolishPlanDetail0DTO);

    List<HvPmPolishPlanDetail0> getAllByCodeId(long id);

    long updateDetail0(HvPmPolishPlanDetail0DTO hvPmPolishPlanDetail0DTO);

    void deleteDetail0ById(long id);

    List<HvPmPolishPlanDetail0>  getDetailDTOListByCodeId(long id);

    List<HvPmPolishPlanDetail0> getDetail0ListByCodeIds(List<Long> codeIds);
}
