package com.hvisions.pms.repository;

import com.hvisions.pms.entity.HvPmPartsOut;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface HvPmPartsOutRepository extends JpaRepository<HvPmPartsOut, Long> {
    HvPmPartsOut findByPalletCodeAndLocationAndStatus(String palletCode, String location, Integer status);

    HvPmPartsOut findByTaskNo(String taskCode);

    List<HvPmPartsOut> findByLocation(String location);

    void removeByTaskNo(String taskNo);
}
