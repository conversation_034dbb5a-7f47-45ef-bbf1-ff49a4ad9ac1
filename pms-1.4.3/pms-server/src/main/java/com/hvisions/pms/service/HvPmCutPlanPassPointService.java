package com.hvisions.pms.service;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.pms.dto.HvPmCutPlanPassPointDTO;
import com.hvisions.pms.entity.HvPmCutPlanPassPoint;
import com.hvisions.thirdparty.common.dto.DetailReportDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;



public interface HvPmCutPlanPassPointService {
    /**
     * 保存
     *
     * @param hvPmCutPlanPassPointDTO HvPmCutPlanPassPoint
     */
    void addHvPmCutPlanPassPoint(HvPmCutPlanPassPointDTO hvPmCutPlanPassPointDTO);

    /**
     * 通过id删除
     *
     * @param id 主键
     */
    void deleteHvPmCutPlanPassPoint(Long id);

    /**
     * 修改
     *
     * @param hvPmCutPlanPassPointDTO HvPmCutPlanPassPoint
     */
    void updateHvPmCutPlanPassPoint(HvPmCutPlanPassPointDTO hvPmCutPlanPassPointDTO);

    /**
     * 获取
     *
     * @param id 主键
     * @return HvPmCutPlanPassPoint hvPmCutPlanPassPointDTO HvPmCutPlanPassPoint
     */
    HvPmCutPlanPassPointDTO getHvPmCutPlanPassPointById(Long id);

    /**
     * 获取列表
     *
     * @return List<HvPmCutPlanPassPointDTO> HvPmCutPlanPassPoint列表
     */
    List<HvPmCutPlanPassPointDTO> getAll();

    Page<HvPmCutPlanPassPointDTO> getPage(HvPmCutPlanPassPointDTO hvPmCutPlanPassPointDTO);


    ImportResult importExcel(MultipartFile file);

    List<HvPmCutPlanPassPoint> exportList();

    void save(DetailReportDTO detailReportDTO,Long planId);
}