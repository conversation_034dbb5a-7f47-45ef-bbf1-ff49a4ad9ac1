package com.hvisions.pms.service;

import com.hvisions.pms.dto.HvPmMaterialTaoPicDetailDTO;
import com.hvisions.pms.dto.HvPmMaterialTaoPicDetailQueryDTO;
import com.hvisions.pms.entity.HvPmMaterialTaoPicDetail;
import org.springframework.data.domain.Page;

import java.util.List;

/**
* <p>Title: HvPmMaterialTaoPicDetailService</p>
* <p>Description: </p>
* <p>Company: www.h-visions.com</p>
* <p>create date: 2024年4月19日</p>
*
* <AUTHOR>
* @version :1.0.0
*/

public interface HvPmMaterialTaoPicDetailService{
    /**
    * 保存
    *
    * @param hvPmMaterialTaoPicDetailDTO HvPmMaterialTaoPicDetail
    *
    */
    void addHvPmMaterialTaoPicDetail(HvPmMaterialTaoPicDetailDTO hvPmMaterialTaoPicDetailDTO);

    /**
    * 通过id删除
    *
    * @param id 主键
    *
    */
    void deleteHvPmMaterialTaoPicDetail(Long id);

    /**
    * 修改
    *
    * @param hvPmMaterialTaoPicDetailDTO HvPmMaterialTaoPicDetail
*
    */
    void updateHvPmMaterialTaoPicDetail(HvPmMaterialTaoPicDetailDTO hvPmMaterialTaoPicDetailDTO);

    /**
    * 获取
    *
    * @param id 主键
    * @return HvPmMaterialTaoPicDetail hvPmMaterialTaoPicDetailDTO HvPmMaterialTaoPicDetail
    */
    HvPmMaterialTaoPicDetailDTO getHvPmMaterialTaoPicDetailById(Long id);

    /**
    * 获取列表
    *
    * @return List<HvPmMaterialTaoPicDetailDTO> HvPmMaterialTaoPicDetail列表
    */
    List<HvPmMaterialTaoPicDetailDTO> getAll();

    List<HvPmMaterialTaoPicDetail> getByCutPlanId(Long id);

    Page<HvPmMaterialTaoPicDetailDTO> getByTaoPicDetail(HvPmMaterialTaoPicDetailQueryDTO queryDTO);

    void saveBatch(List<HvPmMaterialTaoPicDetailDTO> detailDTOS);
}