package com.hvisions.pms.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.bom.dto.FrameStatusDTO;
import com.hvisions.hiperbase.bom.dto.HvBmFrameDTO;
import com.hvisions.hiperbase.client.HvBmFrameClient;
import com.hvisions.pms.dto.deliveryMission.DeliveryMissionDTO;
import com.hvisions.pms.dto.deliveryMission.DeliveryMissionQueryDTO;
import com.hvisions.pms.dto.materialKittingTask.MaterialKittingTaskDTO;
import com.hvisions.pms.entity.deliveryMission.DeliveryMission;
import com.hvisions.pms.entity.productWorkOrder.ProductWorkOrder;
import com.hvisions.pms.service.DeliveryMissionService;
import com.hvisions.pms.service.ProductWorkOrderService;
import com.hvisions.pms.utils.SerialCodeUtilsV2;
import com.hvisions.thirdparty.common.dto.RcsCancelTaskRequestDTO;
import com.hvisions.thridparty.client.RCSClient;
import com.hvisions.wms.client.AgvSchedulingClient;
import com.hvisions.wms.dto.agvScheduling.HvWmAgvSchedulingDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.time.LocalDateTime;

@RestController
@RequestMapping("/deliveryMission")
@Api(tags = "配送任务")
public class DeliveryMissionController {

    @Autowired
    private ProductWorkOrderService productWorkOrderService;
    @Autowired
    private DeliveryMissionService deliveryMissionService;
    @Autowired
    private HvBmFrameClient frameClient;
    @Autowired
    private AgvSchedulingClient agvSchedulingClient;
    @Autowired
    private SerialCodeUtilsV2 serialCodeUtilsV2;
    @Autowired
    private RCSClient rcsClient;

    @PostMapping("/addMission")
    @ApiOperation("添加配送任务")
    @Transactional
    public ResultVO addMission(@RequestBody MaterialKittingTaskDTO materialKittingTaskDTO, @ApiIgnore @UserInfo UserInfoDTO userInfoDTO) {
        ProductWorkOrder productWorkOrder = productWorkOrderService.getOne(new LambdaQueryWrapper<ProductWorkOrder>()
                .eq(ProductWorkOrder::getProductWorkOrderCode, materialKittingTaskDTO.getProductWorkOrderCode()));
        if (productWorkOrder == null) {
            return ResultVO.error(500, "未找到生产工单");
        }
        if (productWorkOrder.getIsCollect() == 1) {
            return ResultVO.error(500, "该工单已集配完成");
        }
        DeliveryMissionDTO deliveryMissionDTO = new DeliveryMissionDTO();
        deliveryMissionDTO.setDeliveryMissionCode(serialCodeUtilsV2.generateCode("DeliveryMissionCode"));
        deliveryMissionDTO.setProductWorkOrderCode(materialKittingTaskDTO.getProductWorkOrderCode());
        deliveryMissionDTO.setRequirementCode(materialKittingTaskDTO.getRequirementCode());
        deliveryMissionDTO.setPriority(materialKittingTaskDTO.getPriority());
        deliveryMissionDTO.setPalletNo(materialKittingTaskDTO.getPalletNo());
        deliveryMissionDTO.setTargetPointCode(materialKittingTaskDTO.getTargetPointCode());
        deliveryMissionDTO.setStartPointCode(materialKittingTaskDTO.getPalletCurrentPointCode());
//        人工配送
        if (materialKittingTaskDTO.getDeliveryType() == 1){
            deliveryMissionDTO.setOperator(userInfoDTO.getUserName());
            deliveryMissionDTO.setPlanDeliveryTime(materialKittingTaskDTO.getPlanDeliveryTime());
            deliveryMissionDTO.setDeliveryType(materialKittingTaskDTO.getDeliveryType());
            deliveryMissionDTO.setStatus(materialKittingTaskDTO.getState());
            deliveryMissionDTO.setCreatorName(userInfoDTO.getUserName());
            deliveryMissionDTO.setUpdateTime(LocalDateTime.now());
            deliveryMissionDTO.setUpdaterName(userInfoDTO.getUserName());
            deliveryMissionDTO.setCreateTime(LocalDateTime.now());
            return ResultVO.success(deliveryMissionService.save(DtoMapper.convert(deliveryMissionDTO, DeliveryMission.class)));
        }
//        根据托盘编号获取运输工具类型
        HvBmFrameDTO frameDTO = frameClient.findByFrameCode(materialKittingTaskDTO.getPalletNo()).getData();
//        更新料框工单
        frameDTO.setProductWorkCode(materialKittingTaskDTO.getProductWorkOrderCode());
        frameClient.updateHvPmAgvTaskRecord(frameDTO);
        HvWmAgvSchedulingDTO hvWmAgvSchedulingDTO = new HvWmAgvSchedulingDTO();
        hvWmAgvSchedulingDTO.setAgvTypeNo(frameDTO.getTransportToolTypeCode());
        hvWmAgvSchedulingDTO.setStartPoint(deliveryMissionDTO.getStartPointCode());
        hvWmAgvSchedulingDTO.setEndPoint(deliveryMissionDTO.getTargetPointCode());
        hvWmAgvSchedulingDTO.setPriority(String.valueOf(deliveryMissionDTO.getPriority()));
        hvWmAgvSchedulingDTO.setPlannedStartTime(deliveryMissionDTO.getPlanDeliveryTime());
        ResultVO resultVO = agvSchedulingClient.agvTaskAddAndDispatch(hvWmAgvSchedulingDTO);
        if (resultVO.isSuccess()) {
            return ResultVO.success(deliveryMissionService.save(DtoMapper.convert(deliveryMissionDTO, DeliveryMission.class)));
        } else {
            return ResultVO.error(500, "agv任务下发失败");
        }
    }


    @PutMapping("/updateMission")
    @ApiOperation("更新配送任务")
    public ResultVO updateMission(@RequestBody DeliveryMissionDTO deliveryMissionDTO, @ApiIgnore @UserInfo UserInfoDTO userInfoDTO) {
        deliveryMissionDTO.setUpdaterName(userInfoDTO.getUserName());
        deliveryMissionDTO.setUpdateTime(LocalDateTime.now());
        return ResultVO.success(deliveryMissionService.update(DtoMapper.convert(deliveryMissionDTO, DeliveryMission.class),
                new LambdaQueryWrapper<DeliveryMission>()
                .eq(DeliveryMission::getDeliveryMissionCode,deliveryMissionDTO.getDeliveryMissionCode())));
    }

    @PostMapping("/getPage")
    @ApiOperation("分页查询配送任务")
    public Page<DeliveryMissionDTO> getPage(@RequestBody DeliveryMissionQueryDTO queryDTO) {
        return DtoMapper.convertPage(deliveryMissionService.getPage(queryDTO),DeliveryMissionDTO.class);
    }

    @PutMapping("/cancelMission")
    @ApiOperation("取消配送任务")
    public ResultVO cancelMission(@RequestBody DeliveryMissionDTO deliveryMissionDTO, @ApiIgnore @UserInfo UserInfoDTO userInfoDTO) {
        deliveryMissionDTO.setUpdaterName(userInfoDTO.getUserName());
        deliveryMissionDTO.setUpdateTime(LocalDateTime.now());
        RcsCancelTaskRequestDTO rcsCancelTaskRequestDTO = new RcsCancelTaskRequestDTO();
        rcsCancelTaskRequestDTO.setRobotTaskCode(deliveryMissionDTO.getDeliveryMissionCode());
        rcsCancelTaskRequestDTO.setCancelType("CANCEL");
        ResultVO resultVO = rcsClient.cancelTask(rcsCancelTaskRequestDTO);
        if (resultVO.isSuccess()) {
            return ResultVO.success(deliveryMissionService.update(DtoMapper.convert(deliveryMissionDTO, DeliveryMission.class),
                    new LambdaQueryWrapper<DeliveryMission>()
                            .eq(DeliveryMission::getDeliveryMissionCode,deliveryMissionDTO.getDeliveryMissionCode())));
        }
        return ResultVO.error(500, "取消任务失败");
    }


}
