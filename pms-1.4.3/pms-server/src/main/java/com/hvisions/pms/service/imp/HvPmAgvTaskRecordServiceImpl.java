package com.hvisions.pms.service.imp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.framework.client.DictionaryItemClient;
import com.hvisions.framework.dto.dictionary.DictionaryItemDTO;
import com.hvisions.hiperbase.bom.dto.HvBmFrameDTO;
import com.hvisions.hiperbase.client.HvBmFrameClient;
import com.hvisions.hiperbase.client.LocationExtendClient;
import com.hvisions.hiperbase.client.MaterialClient;
import com.hvisions.hiperbase.client.MaterialPointClient;
import com.hvisions.hiperbase.equipment.location.LocationDTO;
import com.hvisions.hiperbase.materials.dto.MaterialDTO;
import com.hvisions.hiperbase.materials.dto.MaterialPointAreaDTO;
import com.hvisions.hiperbase.materials.dto.MaterialPointDTO;
import com.hvisions.pms.consts.HvPmAvgTaskRecordConst;
import com.hvisions.pms.dao.HvPmAgvTaskRecordMapper;
import com.hvisions.pms.dto.HvPmAgvTaskRecordDTO;
import com.hvisions.pms.dto.WorkOrderDTO;
import com.hvisions.pms.entity.HvPmAgvTaskRecord;
import com.hvisions.pms.entity.HvPmAgvTaskRecordMaterial;
import com.hvisions.pms.entity.plan.HvPmCallFrameMaterial;
import com.hvisions.pms.enums.SchedulingStateEnum;
import com.hvisions.pms.exportdto.AgvTaskRecordExportDTO;
import com.hvisions.pms.plan.HvPmAgvTaskRecordTabQueryDTO;
import com.hvisions.pms.service.*;
import com.hvisions.pms.util.RedisUtils;
import com.hvisions.thirdparty.common.dto.*;
import com.hvisions.thirdparty.common.enums.AGVStatusMethodEnum;
import com.hvisions.thirdparty.common.enums.InterfaceCodeEnum;
import com.hvisions.thirdparty.common.enums.LineSchedulingTypeEnum;
import com.hvisions.thridparty.client.IWMSClient;
import com.hvisions.thridparty.client.ThirdpartExecuteClient;
import com.hvisions.thridparty.client.ThirdpartyInterfaceClient;
import com.hvisions.thridparty.client.ThirdpartySystemClient;
import com.hvisions.wms.client.StockClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-03-29 11:54
 */
@Slf4j
@Service
public class HvPmAgvTaskRecordServiceImpl extends ServiceImpl<HvPmAgvTaskRecordMapper, HvPmAgvTaskRecord> implements HvPmAgvTaskRecordService {
    @Autowired
    private DictionaryItemClient dictionaryItemClient;
    @Resource
    private HvPmAgvTaskRecordMapper hvPmAgvTaskRecordMapper;
    @Autowired
    private HvPmAgvTaskRecordMaterialService hvPmAgvTaskRecordMaterialService;
    @Autowired
    private IWMSClient iwmsClient;
    @Autowired
    private HvBmFrameClient frameClient;
    @Autowired
    private ThirdpartySystemClient thirdpartySystemClient;
    @Autowired
    private LocationExtendClient locationExtendClient;
    @Resource
    private MaterialPointClient materialPointClient;
    @Autowired
    private MaterialClient materialClient;
    @Autowired
    private ThirdpartExecuteClient thirdpartExecuteClient;
    @Autowired
    private ThirdpartyInterfaceClient thirdpartyInterfaceClient;
    @Resource
    private HvBmFrameClient hvBmFrameClient;
    @Autowired
    private StockClient stockClient;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private HvPmCallFrameMaterialService frameMaterialService;
    @Autowired
    private WorkOrderService workOrderService;
    @Autowired
    private HvPmProductBomDetailService bomDetailService;
    @Autowired
    private PmsStockService pmsStockService;

    @Override
    public Page<HvPmAgvTaskRecord> pageList( HvPmAgvTaskRecordTabQueryDTO hvPmAgvTaskRecordTabQueryDTO) {
        List<HvPmAgvTaskRecord> mapperPage = hvPmAgvTaskRecordMapper.getPage(hvPmAgvTaskRecordTabQueryDTO);
        Page<HvPmAgvTaskRecord> list = PageHelperUtil.getPage(hvPmAgvTaskRecordMapper::getPage, hvPmAgvTaskRecordTabQueryDTO);
        //获取任务类型字典项
        ResultVO<List<DictionaryItemDTO>> taskTypeDictionaryItemClientAll = dictionaryItemClient.findAll("task_type");
        //获取调度状态字典项
        ResultVO<List<DictionaryItemDTO>> schedulingStateDictionaryItemClientAll = dictionaryItemClient.findAll("scheduling_state");
        List<DictionaryItemDTO> taskTypeDictionaryData = taskTypeDictionaryItemClientAll.getData();
        List<DictionaryItemDTO> schedulingStateDictionaryData = schedulingStateDictionaryItemClientAll.getData();
        for (HvPmAgvTaskRecord record : list) {
            for (DictionaryItemDTO datum : taskTypeDictionaryData) {
                if (record.getTaskType() == Integer.parseInt(datum.getItemKey())) {
                    record.setTaskTypeDescription(datum.getItemValue());
                    break;
                }
            }

            for (DictionaryItemDTO datum : schedulingStateDictionaryData) {
                if (record.getSchedulingState() == Integer.parseInt(datum.getItemKey())) {
                    record.setSchedulingStateDescription(datum.getDescription());
                    break;
                }
            }
        }
        return list;
    }


    /**
     * 导出调度记录
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    @Override
    public ResultVO<ExcelExportDto> exportHvPmAvgTaskRecord(HvPmAgvTaskRecordTabQueryDTO hvPmAgvTaskRecordTabQueryDTO) throws IOException, IllegalAccessException {
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(geteExportInfo(hvPmAgvTaskRecordTabQueryDTO).getBody());
        excelExportDto.setFileName( HvPmAvgTaskRecordConst.PLAN_EXPORT_FILE_NAME);
        return ResultVO.success(excelExportDto);
    }

    //获取需要导出的数据内容
    @ApiIgnore
    public ResponseEntity<byte[]> geteExportInfo(HvPmAgvTaskRecordTabQueryDTO hvPmAgvTaskRecordTabQueryDTO) throws IOException, IllegalAccessException {
        // 获取调度记录主表列表
        List<AgvTaskRecordExportDTO> agvTaskRecordExportDTOS = hvPmAgvTaskRecordMapper.getAgvTaskRecordExport(hvPmAgvTaskRecordTabQueryDTO);

        //获取任务类型字典项
        ResultVO<List<DictionaryItemDTO>> taskTypeDictionaryItemClientAll = dictionaryItemClient.findAll("task_type");
        //获取调度状态字典项
        ResultVO<List<DictionaryItemDTO>> schedulingStateDictionaryItemClientAll = dictionaryItemClient.findAll("scheduling_state");
        List<DictionaryItemDTO> taskTypeDictionaryData = taskTypeDictionaryItemClientAll.getData();
        List<DictionaryItemDTO> schedulingStateDictionaryData = schedulingStateDictionaryItemClientAll.getData();

        //设置任务类型描述 和 调度状态描述
        for (AgvTaskRecordExportDTO record : agvTaskRecordExportDTOS) {
            for (DictionaryItemDTO datum : taskTypeDictionaryData) {
                if (record.getTaskType() == Integer.parseInt(datum.getItemKey())) {
                    record.setTaskTypeDescription(datum.getItemValue());
                    break;
                }
            }

            for (DictionaryItemDTO datum : schedulingStateDictionaryData) {
                if (record.getSchedulingState() == Integer.parseInt(datum.getItemKey())) {
                    record.setSchedulingStateDescription(datum.getDescription());
                    break;
                }
            }
        }

        HSSFWorkbook hssfWorkbook = new HSSFWorkbook();
        ExcelUtil.addSheetToWorkBook(agvTaskRecordExportDTOS, HvPmAvgTaskRecordConst.PLAN_EXPORT_SHEET_NAME, AgvTaskRecordExportDTO.class,null, hssfWorkbook);
        return ExcelUtil.generateHttpExcelFile(hssfWorkbook, HvPmAvgTaskRecordConst.PLAN_EXPORT_FILE_NAME);
    }

    @Override
    public void saveLineTask(LineSchedulingDTO schedulingDTO) {
        LambdaQueryWrapper<HvPmAgvTaskRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HvPmAgvTaskRecord::getTaskCode, schedulingDTO.getRequestCode());
        HvPmAgvTaskRecord record = getOne(queryWrapper);
        if (record != null) {
            throw new BaseKnownException("任务号:" + record.getTaskCode() + "重复！");
        }
        HvPmAgvTaskRecordDTO taskRecordDTO = getHvPmAgvTaskRecordDTO(schedulingDTO);
        HvPmAgvTaskRecord hvPmAgvTaskRecord = DtoMapper.convert(taskRecordDTO, HvPmAgvTaskRecord.class);
        //默认开始，I-WMS 给不了开始时间
        hvPmAgvTaskRecord.setStartTime(new Date());
        hvPmAgvTaskRecordMapper.insert(hvPmAgvTaskRecord);

        //如果满框调度物料不为空
        if (schedulingDTO.getMaterialList() != null && !schedulingDTO.getMaterialList().isEmpty()) {
            for (LineSchedulingMaterialDTO materialDTO : schedulingDTO.getMaterialList()) {
                WorkOrderDTO order = workOrderService.findByWorkOrderCode(materialDTO.getWorkOrderCode());
                if (order != null) {
                    materialDTO.setBlockCode(order.getBlockCode());
                    materialDTO.setShipNo(order.getShipNo());
                }
            }
            //物料信息存入数据库
            Long hvPmAgvTaskRecordId = hvPmAgvTaskRecord.getId();
            List<HvPmAgvTaskRecordMaterial> materials = DtoMapper.convertList(schedulingDTO.getMaterialList(), HvPmAgvTaskRecordMaterial.class);
            List<HvPmAgvTaskRecordMaterial> collect = materials.stream().peek(hvPmAgvTaskRecordMaterial -> hvPmAgvTaskRecordMaterial.setRaskRecordId(hvPmAgvTaskRecordId)).collect(Collectors.toList());

            hvPmAgvTaskRecordMaterialService.saveBatch(collect);
        }
    }

    private HvPmAgvTaskRecordDTO getHvPmAgvTaskRecordDTO(LineSchedulingDTO schedulingDTO) {
        HvPmAgvTaskRecordDTO taskRecordDTO = new HvPmAgvTaskRecordDTO();
        if (LineSchedulingTypeEnum.SURPLUS_RETURN.getCode().equals(schedulingDTO.getTaskType())
                || LineSchedulingTypeEnum.FULL.getCode().equals(schedulingDTO.getTaskType())
                || LineSchedulingTypeEnum.EMPTY_RETURN.getCode().equals(schedulingDTO.getTaskType())) {
            taskRecordDTO.setStartPoint(schedulingDTO.getPointCode());
        } else {
            taskRecordDTO.setStartPoint(schedulingDTO.getStartpointCode());
        }
        taskRecordDTO.setRequestCode(schedulingDTO.getRequestCode());
        taskRecordDTO.setTaskType(Integer.parseInt(schedulingDTO.getTaskType()));
        taskRecordDTO.setFrameCode(schedulingDTO.getFrameCode());
        taskRecordDTO.setFrameTypeCode(schedulingDTO.getFrameType());
        taskRecordDTO.setSchedulingState(schedulingDTO.getStatus());
        taskRecordDTO.setRequestSystem(schedulingDTO.getRequestSystem());
        taskRecordDTO.setTaskCode(schedulingDTO.getTaskCode());
        //目前满框和叫空框都有终点，没有明确起点
        if (LineSchedulingTypeEnum.EMPTY_RETURN.getCode().equals(schedulingDTO.getTaskType())
                || LineSchedulingTypeEnum.FULL.getCode().equals(schedulingDTO.getTaskType())
        ) {
            taskRecordDTO.setEndPoint(null);
        } else {
            taskRecordDTO.setEndPoint(schedulingDTO.getPointCode());
        }
        return taskRecordDTO;
    }

    @Override
    public void updateTaskByTaskCode(String taskCode, String status, String currentPositionCode) {
        //找任务
        LambdaQueryWrapper<HvPmAgvTaskRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HvPmAgvTaskRecord::getTaskCode, taskCode);
        HvPmAgvTaskRecord taskRecord = getOne(wrapper);
        if (taskRecord == null) {
            throw new BaseKnownException("任务号：" + taskCode + "未找到对应的调度任务！");
        }
        //有end ,没有start 和其他状态
        if (AGVStatusMethodEnum.start.getCode().equals(status) ||
                AGVStatusMethodEnum.outbin4JH.getCode().equals(status)
        ) {
            taskRecord.setSchedulingState(SchedulingStateEnum.SCHEDULING.getType());
            taskRecord.setStartTime(new Date());
            taskRecord.setStartPoint(currentPositionCode);
        } else if (AGVStatusMethodEnum.end.getCode().equals(status) ||
                AGVStatusMethodEnum.cancel.getCode().equals(status) ||
                AGVStatusMethodEnum.end4JH.getCode().equals(status)) {
            taskRecord.setEndTime(new Date());
            taskRecord.setEndPoint(currentPositionCode);
            taskRecord.setSchedulingState(SchedulingStateEnum.FINISH.getType());
        } else {
            throw new BaseKnownException("更新失败！调度状态:" + status + "未定义！");
        }
        //更新开始、结束时间
        updateById(taskRecord);

    }

    @Override
    public HvPmAgvTaskRecord getByTaskCode(String taskCode) {
        LambdaQueryWrapper<HvPmAgvTaskRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HvPmAgvTaskRecord::getTaskCode, taskCode);
        return getOne(wrapper);
    }

    /**
     * 重复执行
     *
     * @param raskRecordId
     */
    @Override
    public void repeatedExecution(long raskRecordId) {
        ResultVO resultVO;
        HvPmAgvTaskRecord hvPmAgvTaskRecord = this.getById(raskRecordId);
        switch (hvPmAgvTaskRecord.getTaskType()) {
            case 10: //空框请求
                IWMEmptyTaskDTO iwmEmptyTaskDTO = new IWMEmptyTaskDTO();
                iwmEmptyTaskDTO.setTaskCode(hvPmAgvTaskRecord.getTaskCode());
                iwmEmptyTaskDTO.setPodType(hvPmAgvTaskRecord.getFrameTypeCode());
                iwmEmptyTaskDTO.setWorkAreaCode(hvPmAgvTaskRecord.getEndPoint());
                resultVO = iwmsClient.sendEmptyTask(iwmEmptyTaskDTO);
                if (!resultVO.isSuccess()) {
                    throw new BaseKnownException("重复执行失败;" + resultVO.getMessage());
                }
                break;
            case 20: //满框调度
                IWMSInStockTaskDTO iwmsInStockTaskDTO = new IWMSInStockTaskDTO();
                iwmsInStockTaskDTO.setTaskCode(hvPmAgvTaskRecord.getTaskCode());
                iwmsInStockTaskDTO.setWbCode(hvPmAgvTaskRecord.getEndPoint());
                iwmsInStockTaskDTO.setPodCode(hvPmAgvTaskRecord.getFrameCode());
                ResultVO<List<DictionaryItemDTO>> itemClientAll = dictionaryItemClient.findAll("work_finish_pallet_and_stock_type");
                List<DictionaryItemDTO> itemDTOS = itemClientAll.getData();
                if (itemDTOS.isEmpty()) {
                    throw new BaseKnownException("字典未配置下料完工托盘与库区类型关系：work_finish_pallet_and_stock_type");
                }
                Map<String, String> pallet_stockType = new HashMap<>();
                for (DictionaryItemDTO itemDTO : itemDTOS) {
                    pallet_stockType.put(itemDTO.getItemKey(), itemDTO.getItemValue());
                }
                //查询料框类型
                ResultVO<HvBmFrameDTO> frameDTOResultVO = frameClient.findByFrameCode(hvPmAgvTaskRecord.getFrameCode());
                HvBmFrameDTO frameDTO = frameDTOResultVO.getData();
                if (frameDTO == null) {
                    throw new BaseKnownException("料框：" + hvPmAgvTaskRecord.getFrameCode() + "未配置！");
                }
                String stockType = pallet_stockType.get(frameDTO.getFrameTypeCode());
                if (StringUtils.isBlank(stockType)) {
                    throw new BaseKnownException("字典未配置料框类型：" + frameDTO.getFrameTypeCode() + "对应的入库库区类型");
                }
                iwmsInStockTaskDTO.setStgCategory(stockType);
                iwmsInStockTaskDTO.setInitPodFlag(stockType.contains("PK") ? "0" : "1");
                //根据请求系统获取线体ID
                ThirdpartySystemDTO thirdpartySystemDTO = thirdpartySystemClient.getThirdpartySystemBySystemCode(hvPmAgvTaskRecord.getRequestSystem()).getData();
                //根据线体ID获取线体编号
                LocationDTO locationDTO = locationExtendClient.getLocationById(thirdpartySystemDTO.getLineId()).getData();
                iwmsInStockTaskDTO.setLineCode(locationDTO == null ? null : locationDTO.getCode());
                List<IWMSMaterialDataDTO> iwmsMaterialDataDTOS = new ArrayList<>();
                List<HvPmAgvTaskRecordMaterial> hvPmAgvTaskRecordMaterials = hvPmAgvTaskRecordMaterialService.findListByRaskRecordId(raskRecordId);
                IWMSMaterialDataDTO iwmsMaterialDataDTO;
                for (HvPmAgvTaskRecordMaterial hvPmAgvTaskRecordMaterial : hvPmAgvTaskRecordMaterials) {
                    iwmsMaterialDataDTO = new IWMSMaterialDataDTO();
                    MaterialDTO materialDTO = materialClient.getByMaterialCode(hvPmAgvTaskRecordMaterial.getMaterialCode()).getData();
                    iwmsMaterialDataDTO.setMatCode(materialDTO.getMaterialCode());
                    iwmsMaterialDataDTO.setMatText(materialDTO.getMaterialName());
                    iwmsMaterialDataDTO.setMatQty(hvPmAgvTaskRecordMaterial.getQuality().intValue());
                    iwmsMaterialDataDTO.setMatTypeCode(materialDTO.getMaterialTypeCode());
                    iwmsMaterialDataDTO.setPn(hvPmAgvTaskRecordMaterial.getPn());
                    iwmsMaterialDataDTO.setSurplusMaterial("4".equals(iwmsMaterialDataDTO.getMatTypeCode()) ? "1" : "0");//余料0:否：1：是
                    iwmsMaterialDataDTOS.add(iwmsMaterialDataDTO);
                }
                iwmsInStockTaskDTO.setData(iwmsMaterialDataDTOS);
                resultVO = iwmsClient.sendFullMaterialInStockTask(iwmsInStockTaskDTO);
                if (!resultVO.isSuccess()) {
                    throw new BaseKnownException("重复执行失败;" + resultVO.getMessage());
                }
                break;
            case 30: //空框回库
                IWMSEmptyFrameTaskDTO iwmsEmptyFrameTaskDTO = new IWMSEmptyFrameTaskDTO();
                iwmsEmptyFrameTaskDTO.setTaskCode(hvPmAgvTaskRecord.getTaskCode());
                iwmsEmptyFrameTaskDTO.setWbCode(hvPmAgvTaskRecord.getEndPoint());
                iwmsEmptyFrameTaskDTO.setPodCode(hvPmAgvTaskRecord.getFrameCode());
                resultVO = iwmsClient.sendEmptyReturnTask(iwmsEmptyFrameTaskDTO);
                if (!resultVO.isSuccess()) {
                    throw new BaseKnownException("重复执行失败;" + resultVO.getMessage());
                }
                break;
            default:
                throw new BaseKnownException("未知的任务类型,重复执行失败");

        }

    }

    /**
     * 人工调度
     *
     * @param raskRecordId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void manualScheduling(long raskRecordId) {
        try {
            HvPmAgvTaskRecord hvPmAgvTaskRecord = this.getById(raskRecordId);
            hvPmAgvTaskRecord.setId(null);
            this.save(hvPmAgvTaskRecord);
            List<HvPmAgvTaskRecordMaterial> hvPmAgvTaskRecordMaterials = hvPmAgvTaskRecordMaterialService.findListByRaskRecordId(raskRecordId);
            List<HvPmAgvTaskRecordMaterial> collect = hvPmAgvTaskRecordMaterials.stream().peek(hvPmAgvTaskRecordMaterial -> hvPmAgvTaskRecordMaterial.setId(null)).collect(Collectors.toList());
            hvPmAgvTaskRecordMaterialService.saveBatch(collect);
            repeatedExecution(raskRecordId);
        } catch (Exception e) {
            throw new BaseKnownException("人工调度失败");
        }
    }


    @Transactional
    @Override
    public void emptyPalletCirculation(AGVStatusDTO agvStatusDTO) {
        //验证是否是自动补充料框任务
        if (StringUtils.isBlank(agvStatusDTO.getCurrentPositionCode())) {
            throw new BaseKnownException("【料点编号】不能为空！");
        }
        if (StringUtils.isBlank(agvStatusDTO.getPalletCode())) {
            throw new BaseKnownException("【料框编号】不能为空！");
        }
        ResultVO<HvBmFrameDTO> frameDTOResultVO = hvBmFrameClient.findByFrameCode(agvStatusDTO.getPalletCode());
        HvBmFrameDTO frameDTO = frameDTOResultVO.getData();
        if (frameDTO == null) {
            throw new BaseKnownException("托盘：" + agvStatusDTO.getPalletCode() + "为维护！");
        }

        ResultVO r;
        HvPmAgvTaskRecord taskRecord = getByTaskCode(agvStatusDTO.getTaskCode());
        if (taskRecord != null) {
            throw new BaseKnownException("自动补充料框任务号：" + agvStatusDTO.getTaskCode() + "重复！");
        }
        taskRecord = new HvPmAgvTaskRecord();
        //空框流转
        taskRecord.setTaskType(50);
        taskRecord.setTaskCode(agvStatusDTO.getTaskCode());
        taskRecord.setRequestCode(agvStatusDTO.getTaskCode());
        taskRecord.setRequestSystem("AGV/I-WMS");
        taskRecord.setEndPoint(agvStatusDTO.getCurrentPositionCode());
        taskRecord.setFrameTypeCode(frameDTO.getFrameTypeCode());
        taskRecord.setFrameCode(agvStatusDTO.getPalletCode());
        taskRecord.setCreateTime(new Date());
        taskRecord.setEndTime(new Date());
        taskRecord.setStartTime(new Date());
        taskRecord.setUpdateTime(new Date());
        //完成
        taskRecord.setSchedulingState(1);
        //保存
        save(taskRecord);


        //自动补充料框的任务，需要执行反馈产线调度任务接口，告诉对方系统料点和料框
        //料点》区域》工位》产线
        //料点
        ResultVO<MaterialPointDTO> r2 = materialPointClient.getMaterialPointByPointCode(agvStatusDTO.getCurrentPositionCode());
        MaterialPointDTO pointDTO = r2.getData();
        if (pointDTO == null) {
            throw new BaseKnownException("料点：" + agvStatusDTO.getCurrentPositionCode() + "未维护！");
        }
        //料点区域（这几个上料点都是一个库区）
        MaterialPointAreaDTO pointAreaDTO = pointDTO.getPointArea();
        //找工位信息
        ResultVO<LocationDTO> locationVo = locationExtendClient.getLocationById(pointAreaDTO.getLocationId());
        LocationDTO station = locationVo.getData();
        if (station == null) {
            throw new BaseKnownException("料点区域： " + pointAreaDTO.getPointAreaName() + "的工位未维护！");
        }
        //通过产线和接口编号集合（调度反馈编号），确定唯一的编号
        ResultVO<String> interfaceCodeVo = thirdpartyInterfaceClient.getFeedBackInterfaceByLineId(station.getParentId());
        String interfaceCode = interfaceCodeVo.getData();
        if (StringUtils.isBlank(interfaceCode)) {
            throw new BaseKnownException("工位：" + station.getName() + "对应的产线未配置【调度反馈任务】接口！");
        }
        //各产线的调度反馈
        LineSchedulingFeedBackDTO lineSchedulingFeedBackDTO = new LineSchedulingFeedBackDTO();
        lineSchedulingFeedBackDTO.setFrameCode(agvStatusDTO.getPalletCode());
        lineSchedulingFeedBackDTO.setFrameTypeCode(frameDTO.getFrameTypeCode());
        lineSchedulingFeedBackDTO.setRequestCode(agvStatusDTO.getTaskCode());
        //反馈对应的是空框调度
        lineSchedulingFeedBackDTO.setTaskType("50");
        lineSchedulingFeedBackDTO.setPointCode(agvStatusDTO.getCurrentPositionCode());
        lineSchedulingFeedBackDTO.setLineId(station.getParentId());
        lineSchedulingFeedBackDTO.setResult("OK");

        //按顺序执行第三方接口 (未考虑分布式事务)
        r = thirdpartExecuteClient.send(interfaceCode, lineSchedulingFeedBackDTO);
        if (!r.isSuccess()) {
            //todo 发动产线空框
            log.error("{}!{}", InterfaceCodeEnum.getByCode(interfaceCode).getDesc(), r.getMessage());
        }
        pmsStockService.deleteByFrameCode(agvStatusDTO.getPalletCode());
    }

    @Override
    @Transactional
    public void saveTaskRecordByCllMaterialsTask(List<WeldLineCallMaterialsDTO> callMaterialsDTOS) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<HvPmAgvTaskRecord> list = new ArrayList<>();
        List<HvPmAgvTaskRecordMaterial> materialList = new ArrayList<>();
        HvPmAgvTaskRecord agvTaskRecord;
        for (WeldLineCallMaterialsDTO callMaterialsDTO : callMaterialsDTOS) {
            try {
                agvTaskRecord = new HvPmAgvTaskRecord();
                ResultVO<HvBmFrameDTO> frameCode = frameClient.findByFrameCode(callMaterialsDTO.getFrameCode());
                if (frameCode.getData() == null) {
                    throw new BaseKnownException("料框：" + callMaterialsDTO.getFrameCode() + "未配置！");
                }
                agvTaskRecord.setFrameTypeCode(frameCode.getData().getFrameTypeCode());
                agvTaskRecord.setTaskCode(callMaterialsDTO.getRequestCode());
                agvTaskRecord.setRequestCode(callMaterialsDTO.getRequestCode());
                agvTaskRecord.setTaskType(Integer.parseInt(callMaterialsDTO.getTaskType()));
                agvTaskRecord.setWorkOrderCode(callMaterialsDTO.getWorkOrder());
                agvTaskRecord.setEndPoint(callMaterialsDTO.getPointCode());
                agvTaskRecord.setFrameCode(callMaterialsDTO.getFrameCode());
                agvTaskRecord.setRequestSystem(callMaterialsDTO.getRequestSystem());
                agvTaskRecord.setSchedulingState(4);
                agvTaskRecord.setRequestUser(callMaterialsDTO.getRequestUserCode());
                agvTaskRecord.setRequestTime(simpleDateFormat.parse(callMaterialsDTO.getRequestTime()));
                agvTaskRecord.setStartTime(new Date());
                list.add(agvTaskRecord);
            }catch (NumberFormatException e){
                throw new BaseKnownException("任务类型编码:" + callMaterialsDTO.getTaskType() + "解析失败,请确认");
            }catch (ParseException e){
                throw new BaseKnownException("请求时间:" + callMaterialsDTO.getRequestTime() + "解析失败,请确认是否为yyyy-MM-dd HH:mm:ss格式的字符串");
            }
        }
        saveBatch(list);

        for (WeldLineCallMaterialsDTO callMaterialsDTO : callMaterialsDTOS) {
            LambdaQueryWrapper<HvPmAgvTaskRecord> lqw = new LambdaQueryWrapper<>();
            lqw.eq(HvPmAgvTaskRecord::getTaskCode,callMaterialsDTO.getRequestCode() );
            HvPmAgvTaskRecord one = getOne(lqw);

            LambdaQueryWrapper<HvPmCallFrameMaterial> lqw2 = new LambdaQueryWrapper<>();
            lqw2.eq(HvPmCallFrameMaterial::getFrameCode, callMaterialsDTO.getFrameCode());
            List<HvPmCallFrameMaterial> frameMaterials = frameMaterialService.list(lqw2);
            if (!frameMaterials.isEmpty()) {
                for (HvPmCallFrameMaterial frameMaterial : frameMaterials) {
                    HvPmAgvTaskRecordMaterial hvPmAgvTaskRecordMaterial = getHvPmAgvTaskRecordMaterial(frameMaterial, one);
                    materialList.add(hvPmAgvTaskRecordMaterial);
                }
            }

            boolean b = hvPmAgvTaskRecordMaterialService.saveBatch(materialList);
        }
    }

    private HvPmAgvTaskRecordMaterial getHvPmAgvTaskRecordMaterial(HvPmCallFrameMaterial frameMaterial, HvPmAgvTaskRecord one) {
        HvPmAgvTaskRecordMaterial hvPmAgvTaskRecordMaterial = new HvPmAgvTaskRecordMaterial();
        hvPmAgvTaskRecordMaterial.setRaskRecordId(one.getId());
        hvPmAgvTaskRecordMaterial.setPn(frameMaterial.getMaterialCode());
        hvPmAgvTaskRecordMaterial.setMaterialCode(frameMaterial.getMaterialCode());
        hvPmAgvTaskRecordMaterial.setMaterialName(frameMaterial.getMaterialCode());
        hvPmAgvTaskRecordMaterial.setQuality((frameMaterial.getQuantity().subtract(frameMaterial.getUsedQuantity())).longValue());
        hvPmAgvTaskRecordMaterial.setWorkOrderCode(one.getWorkOrderCode());
        hvPmAgvTaskRecordMaterial.setBlockCode(frameMaterial.getMaterialCode());
        return hvPmAgvTaskRecordMaterial;
    }

    // 外协平库入场内平库
    @Override
    public void outFlatStockToIwmsInStock(OutCoordinationFlatStockDTO outCoordinationFlatStockDTO) {

        if (outCoordinationFlatStockDTO.getTaskCode() ==null || outCoordinationFlatStockDTO.getTaskCode().isEmpty()) {
            throw new BaseKnownException("任务编号不能为空!!!");
        }
        if (outCoordinationFlatStockDTO.getMaterialsList().isEmpty()){
            throw new BaseKnownException("物料列表不能为空!!!");
        }

        IWMSInStockTaskDTO iwmsInStockTask = new IWMSInStockTaskDTO();
        iwmsInStockTask.setTaskCode(outCoordinationFlatStockDTO.getTaskCode());
        iwmsInStockTask.setWbCode(outCoordinationFlatStockDTO.getWbCode());
        iwmsInStockTask.setPodCode(outCoordinationFlatStockDTO.getPodCode());
        iwmsInStockTask.setLineCode("WX");
        iwmsInStockTask.setStgCategory("PK");
        iwmsInStockTask.setInitPodFlag("0");
        ArrayList<IWMSMaterialDataDTO> iwmsMaterialDataDTOS = new ArrayList<>();
        for (OutCoordinationFlatStockMaterialDTO materialDTO:outCoordinationFlatStockDTO.getMaterialsList()){
            IWMSMaterialDataDTO iwmsMaterialDataDTO = new IWMSMaterialDataDTO();
            iwmsMaterialDataDTO.setMatCode(materialDTO.getMatCode());
            iwmsMaterialDataDTO.setPn(materialDTO.getPn());
            iwmsMaterialDataDTO.setSurplusMaterial("0");
            iwmsMaterialDataDTO.setMatQty(materialDTO.getMatQty());
            iwmsMaterialDataDTOS.add(iwmsMaterialDataDTO);
        }
        iwmsInStockTask.setData(iwmsMaterialDataDTOS);
        //调用“满框调度-物料入库”接口
        ResultVO resultVO = iwmsClient.sendFullMaterialInStockTask(iwmsInStockTask);
        if (!resultVO.isSuccess()) {
            throw new BaseKnownException(resultVO.getMessage());
        }

    }


}
