package com.hvisions.pms.repository;


import com.hvisions.pms.entity.HvPmStockMovement;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface HvPmStockMovementRepository extends JpaRepository<HvPmStockMovement, Long> {
    List<HvPmStockMovement> findByTaskNo(String taskNo);

    HvPmStockMovement findByWorkOrderCodeAndPlanProductBomId(String workOrderCode, Long planProductBomId);

    List<HvPmStockMovement> findByWorkOrderCodeAndState(String workOrderCode, Integer state);
}
