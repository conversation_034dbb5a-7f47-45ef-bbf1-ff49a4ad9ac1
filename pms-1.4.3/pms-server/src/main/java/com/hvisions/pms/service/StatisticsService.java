package com.hvisions.pms.service;

import com.hvisions.pms.dto.DailyWorkPlanDTO;
import com.hvisions.pms.dto.HomeSevenDataDTO;
import com.hvisions.pms.dto.TodayWorkOrderCountDTO;
import com.hvisions.pms.statistics.CellMaterial;
import com.hvisions.pms.statistics.CrewMaterial;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <P>统计信息业务逻辑层 - 接口 <P>
 *
 * <AUTHOR>
 * @date 2024/6/12
 */
public interface StatisticsService {

    /**
     * 统计工单计划信息
     * @return
     */
    DailyWorkPlanDTO statisticsWorkPlanInfo();

    Map<String, TodayWorkOrderCountDTO> getTodayCount();

    HomeSevenDataDTO getSevenDataByRouteName(String routeName,String date);

    Integer getTodayDataByRouteName(String routeName , String date);

    List<CellMaterial> findMaterialByCell(Date beginTime, Date endTime);

    List<CrewMaterial> findMaterialByCrew(Date beginTime, Date endTime);

    Integer getTodayDataPlanByRouteName(String routeName, String date);
}
