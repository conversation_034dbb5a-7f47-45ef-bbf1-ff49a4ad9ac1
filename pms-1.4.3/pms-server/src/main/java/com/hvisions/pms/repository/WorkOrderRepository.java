package com.hvisions.pms.repository;

import com.hvisions.pms.entity.HvPmWorkOrder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <p>Title: DemoEntityRepository</p>
 * <p>Description: 工单创建仓储层</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date:2019/01/17</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface WorkOrderRepository extends JpaRepository<HvPmWorkOrder, Integer> {

    List<HvPmWorkOrder> findByWorkOrderCodeIn(Set<String> workOrderCodes);

    /**
     * 根据ID列表查询
     *
     * @param id 列表
     * @return 工单信息
     */
    List<HvPmWorkOrder> getHvPmOrderManageByIdIn(List<Integer> id);

    /**
     * 根据产线ID查询工单
     *
     * @param cellId 产线id
     * @return 工单列表
     */
    List<HvPmWorkOrder> getAllByCellId(Integer cellId);

    /**
     * 根据工单状态查询
     *
     * @param state 状态
     * @return 工单列表
     */
    List<HvPmWorkOrder> getAllByWorkOrderState(Integer state);

    /**
     * 根据计划编码和流水号查询
     *
     * @param planCode 计划编码
     * @param num      流水号
     * @return 工单信息
     */
    HvPmWorkOrder getByPlanCodeAndSerialNumber(String planCode, Integer num);

    /**
     * 根据ID查询工单状态
     *
     * @param id ID
     * @return 工单状态
     */
    @Query(value = "select h.workOrderState FROM  HvPmWorkOrder h where h.id = :id ")
    int getOrderManageById(@Param("id") Integer id);

    /**
     * 根据工单编号查询工单信息
     *
     * @param workOrderCode 工单编号
     * @return 工单信息
     */
    HvPmWorkOrder getHvPmWorkOrderByWorkOrderCode(String workOrderCode);

    List<HvPmWorkOrder> findByWorkOrderCodeIn(List<String> workOrderCodes);

    /**
     * 根据ID列表删除
     *
     * @param id id列表
     */
    void deleteHvPmOrderManageByIdIn(List<Integer> id);

    /**
     * 工单类型是否使用
     *
     * @param integer id
     * @return true false
     */
    Boolean existsByOrderTypeId(Integer integer);

    /**
     * 更新工单状态
     *
     * @param id             工单ID
     * @param workOrderState 工单状态
     */
    @Modifying
    @Transactional(rollbackFor = Exception.class)
    @Query(value = "update HvPmWorkOrder h set h.workOrderState =:workOrderState where h.id=:id ")
    void updateWorkOrderState(@Param("workOrderState") Integer workOrderState, @Param("id") int id);


    @Modifying
    @Transactional(rollbackFor = Exception.class)
    @Query(value = "update HvPmWorkOrder h set h.workOrderState =:workOrderState ,h.actualStartTime=:startTime where h.workOrderCode=:workOrderCode")
    void updateWorkOrderState(@Param("workOrderState") Integer workOrderState, @Param("workOrderCode") String workOrderCode,@Param("startTime") Date startTime);

    @Modifying
    @Transactional(rollbackFor = Exception.class)
    @Query(value = "update HvPmWorkOrder h set h.workOrderState =:workOrderState ,h.actualEndTime=:endTime where h.workOrderCode=:workOrderCode")
    void updateWorkOrderStateAndEndTime(@Param("workOrderState") Integer workOrderState, @Param("workOrderCode") String workOrderCode,@Param("endTime") Date endTime);
    /**
     * 模糊查询
     *
     * @param materialCode   物料编码
     * @param workOrderCode  工单编码
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param workOrderState 工单状态
     * @param pageable       分页条件
     * @param planOrNew      计划下发或者工单单独创建
     * @return 分页信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Query(value = "select * from hv_pm_work_order  where if (?1 is not null, material_code like %?1%,1=1 )" +
            "and if(?2 is not null, work_order_code like %?2% ,1=1) " +
            "and  plan_start_time between ?3 and ?4 " +
            "and if ( ?5 is not null , work_order_state =?5 ,1=1)  and if(?6 is not null ,plan_or_new =?6,1=1) order " +
            "by " +
            "?#{#pageable} ",
            nativeQuery = true)
    Page<HvPmWorkOrder> findAllByMaterialCodeAndWorkOrderCodeAndDate(String materialCode,
                                                                     String workOrderCode,
                                                                     Date startTime,
                                                                     Date endTime,
                                                                     Integer workOrderState,
                                                                     Integer planOrNew,
                                                                     Pageable pageable);


    /**
     * 获取实际完成时间在今天的工单
     *
     * @param todayTime 当天0点
     * @param endTime   第二天0点
     * @return 工单信息列表
     */
    @Query(value = "select w from HvPmWorkOrder  w WHERE w.actualEndTime >= ?1 and w.actualEndTime < ?2 and w.usedType =?3")
    List<HvPmWorkOrder> getHvPmWorkOrderByActualEndTimeAndUsedType(Date todayTime, Date endTime,Integer usedType);

    /**
     * 获取实际完成时间在今天的工单
     *
     * @param todayTime 当天0点
     * @param endTime   第二天0点
     * @return 工单信息列表
     */
    @Query(value = "select w from HvPmWorkOrder  w WHERE w.actualEndTime >= ?1" +
            " and w.actualEndTime < ?2 and w.orderTypeId =?3 and w.usedType =?4")
    List<HvPmWorkOrder> getHvPmWorkOrderByActualEndTimeAndOrderTypeIdAAndUsedType(Date todayTime, Date endTime, Integer orderTypeId,Integer usedType);

    /**
     * 根据工单状态获取工单数量
     *
     * @param workState 工单状态
     * @return 工单数量
     */
    @Query(value = "select count(w.workOrderState) from HvPmWorkOrder w where w.workOrderState = ?1")
    int getWorkOrderNumByState(int workState);

    @Query(value = "select count(w.workOrderState) from HvPmWorkOrder w where w.workOrderState = ?1 and w.usedType = ?2")
    int getWorkOrderNumByState(int workState,int usedType);
    /**
     * 根据工单状态获取工单数量
     *
     * @param workState 工单状态
     * @return 工单数量
     */
    @Query(value = "select count(w.workOrderState) from HvPmWorkOrder w where w.workOrderState = ?1 " +
            "and w.orderTypeId = ?2")
    int getWorkOrderNumByStateAndTypeId(int workState, Integer typeId);


    @Query(value = "select count(w.workOrderState) from HvPmWorkOrder w where w.workOrderState = ?1 " +
            "and w.orderTypeId = ?2 and w.usedType = ?3")
    int getWorkOrderNumByStateAndTypeId(int workState, Integer typeId,Integer usedType);
    /**
     * 根据工单状态获取工单数量
     *
     * @param workState 工单状态
     * @return 工单数量
     */
    @Query(value = "select count(w.workOrderState) from HvPmWorkOrder w where w.workOrderState = ?1 " +
            "and w.orderTypeId = ?2 and w.usedType = ?3")
    int getWorkOrderNumByStateAndTypeIdAndUsedType(int workState, Integer typeId,Integer usedType);

    /**
     * 查询时间段内生产对工单
     *
     * @param start 开始时间
     * @param end   结束时间
     * @return 工单
     */
    @Query(value = "select h from HvPmWorkOrder  h where h.planStartTime >=?1 and h.planStartTime<=?2")
    List<HvPmWorkOrder> getAllByPlanStartTime(Date start, Date end);

    HvPmWorkOrder getByWorkOrderCode(String workOrderCode);


}
