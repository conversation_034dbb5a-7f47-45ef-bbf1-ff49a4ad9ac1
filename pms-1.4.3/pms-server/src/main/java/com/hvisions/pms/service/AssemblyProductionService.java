package com.hvisions.pms.service;

import com.hvisions.thirdparty.common.dto.CuttingEmptyFramesDTO;
import com.hvisions.thirdparty.common.dto.WeldLineCallMaterialsDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/4/26 11:21
 */
public interface AssemblyProductionService {

    /**
     * 组立生产叫料（焊接线上线叫料）
     * @param callMaterialsDTOS
     */
    void assemblyProductionCallMaterials(List<WeldLineCallMaterialsDTO> callMaterialsDTOS);

    /**
     * 同步空托返回
     * @param cuttingEmptyFramesDTOS
     */
    void cuttingEmptyFrames(List<CuttingEmptyFramesDTO>  cuttingEmptyFramesDTOS);

}
