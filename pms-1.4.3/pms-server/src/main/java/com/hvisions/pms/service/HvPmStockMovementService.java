package com.hvisions.pms.service;

import com.hvisions.pms.dto.HvPmStockMovementDTO;
import com.hvisions.pms.entity.HvPmStockMovement;

import java.util.List;

public interface HvPmStockMovementService {
    void save(HvPmStockMovement hvPmStockMovement);

    List<HvPmStockMovementDTO> findByTaskNo(String taskNo);

    HvPmStockMovementDTO findByWorkOrderCodeAndTaskNo(String workOrderCode, Long planProductBomId);

    List<HvPmStockMovementDTO> findByWorkOrderCodeAndState(String workOrderCode, Integer state);

    void modifyInventory(List<HvPmStockMovementDTO> data);
}
