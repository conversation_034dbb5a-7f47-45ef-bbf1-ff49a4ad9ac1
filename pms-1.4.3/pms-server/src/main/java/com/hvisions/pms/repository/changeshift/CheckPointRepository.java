package com.hvisions.pms.repository.changeshift;

import com.hvisions.pms.entity.changeshift.HvPmCheckPoint;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: CheckPointRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-10-15</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Repository
public interface CheckPointRepository extends JpaRepository<HvPmCheckPoint, Integer> {


    /**
     * 根据检查项目ID查询检查点列表
     *
     * @param inspectionItemId 检查项目ID
     * @return 检查点列表
     */
    List<HvPmCheckPoint> getAllByInspectionItemId(int inspectionItemId);
}