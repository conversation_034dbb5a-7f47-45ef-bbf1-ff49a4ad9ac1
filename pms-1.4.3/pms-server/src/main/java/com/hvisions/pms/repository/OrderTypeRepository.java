package com.hvisions.pms.repository;

import com.hvisions.pms.entity.HvPmOrderType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <p>Title: OrderTypeRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/11/6</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Repository
public interface OrderTypeRepository extends JpaRepository<HvPmOrderType, Integer> {

    HvPmOrderType getAllByOrderTypeCode(String code);
}