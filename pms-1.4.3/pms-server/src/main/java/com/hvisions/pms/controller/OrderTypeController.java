package com.hvisions.pms.controller;

import com.hvisions.common.dto.ExtendColumnInfo;
import com.hvisions.pms.dto.OrderTypeDTO;
import com.hvisions.pms.dto.OrderTypeQueryDTO;
import com.hvisions.pms.dto.SetTypeExtendDTO;
import com.hvisions.pms.service.OrderTypeService;
import com.hvisions.pms.type.OrderTypeMaterialDTO;
import com.hvisions.pms.type.ReturnOrderTypeMaterialDTO;
import com.hvisions.pms.type.TypeMaterialQuery;
import com.hvisions.pms.type.WorkOrderMaterialQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: OrderTypeController</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/11/6</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/order_type")
@Api(description = "工单类型查询")
@Slf4j
public class OrderTypeController {

    @Autowired
    OrderTypeService orderTypeService;

    /**
     * 新增工单类型
     *
     * @param orderTypeDTO 工单类型对象
     * @return 工单类型对象
     */
    @ApiOperation(value = "新增工单类型")
    @PostMapping(value = "/createOrderType")
    public OrderTypeDTO createOrderType(@RequestBody OrderTypeDTO orderTypeDTO) {
        return orderTypeService.createOrderType(orderTypeDTO);
    }


    /**
     * 修改工单类型
     *
     * @param orderTypeDTO 工单类型对象
     * @return 工单类型对象
     */
    @ApiOperation(value = "修改工单类型")
    @PutMapping(value = "/updateOrderType")
    public OrderTypeDTO updateOrderType(@RequestBody OrderTypeDTO orderTypeDTO) {
        return orderTypeService.updateOrderType(orderTypeDTO);
    }

    /**
     * 设置工单类型的扩展属性
     *
     * @param setTypeExtendDTO
     */
    @ApiOperation(value = "设置工单类型的扩展属性")
    @PostMapping(value = "/setTypeExtend")
    public void setTypeExtend(@RequestBody SetTypeExtendDTO setTypeExtendDTO) {
        orderTypeService.setTypeExtend(setTypeExtendDTO);
    }

    /**
     * 根据类型id查询扩展字段
     *
     * @param typeCode 类型code
     * @return 工单绑定的扩展字段
     */
    @ApiOperation(value = "根据类型id查询扩展字段")
    @GetMapping(value = "/getExtendByTypeId/{typeCode}")
    public List<ExtendColumnInfo> getExtendByTypeId(@PathVariable String typeCode) {
        return orderTypeService.getExtendByTypeCode(typeCode);
    }

    /**
     * 根据id删除工单类型
     *
     * @param id 工单类型Id
     */
    @ApiOperation(value = "根据id删除工单类型")
    @DeleteMapping(value = "/deleteById/{id}")
    public void deleteById(@PathVariable Integer id) {
        orderTypeService.deleteById(id);
    }


    @ApiOperation(value = "根据工单类型id,扩展编码删除工单类型扩展")
    @DeleteMapping(value = "/deleteTypeExtend/{orderTypeId}/{extendCode}")
    public void deleteTypeExtend(@PathVariable Integer orderTypeId, @PathVariable String extendCode) {
        orderTypeService.deleteTypeExtend(orderTypeId, extendCode);
    }

    /**
     * 分页查询
     *
     * @param orderTypeQueryDTO 查询条件
     * @return 工单类型信息
     */
    @ApiOperation(value = "分页查询")
    @PostMapping(value = "/getOrderType")
    public Page<OrderTypeDTO> getOrderType(@RequestBody OrderTypeQueryDTO orderTypeQueryDTO) {
        return orderTypeService.getOrderType(orderTypeQueryDTO);
    }

    /**
     * 根据工单类型编码查询工单类型
     *
     * @param orderTypeCode 工单类型编码
     * @return 工单类型
     */
    @GetMapping(value = "getOrderTypeByCode")
    @ApiOperation(value = "根据工单类型编码查询工单类型")
    public OrderTypeDTO getOrderTypeByCode(@RequestParam String orderTypeCode) {
        return orderTypeService.getOrderTypeByCode(orderTypeCode);
    }

    /**
     * 工单类型 物料绑定
     *
     * @param orderTypeMaterialDTO 工单类型物料对象
     * @return 已有绑定关系的返回
     */
    @PostMapping(value = "bingOrderType")
    @ApiOperation(value = "工单类型 物料绑定")
    public ReturnOrderTypeMaterialDTO bingOrderType(@RequestBody OrderTypeMaterialDTO orderTypeMaterialDTO) {
        return orderTypeService.bingOrderType(orderTypeMaterialDTO);
    }

    /**
     * 分页查询工单类型绑定的物料
     *
     * @param typeMaterialQuery 分页查询条件
     * @return 绑定结果
     */
    @PostMapping(value = "getOrderTypeMaterial")
    @ApiOperation(value = "分页查询工单类型绑定的物料")
    public Page<OrderTypeMaterialDTO> getOrderTypeMaterial(@RequestBody TypeMaterialQuery typeMaterialQuery) {
        return orderTypeService.getOrderTypeMaterial(typeMaterialQuery);
    }

    /**
     * 删除绑定关系
     *
     * @param orderTypeId 类型id
     * @param materialId  物料id
     */
    @DeleteMapping(value = "deleteTypeMaterial/{orderTypeId}/{materialId}")
    @ApiOperation(value = "删除绑定关系")
    public void deleteTypeMaterial(@PathVariable Integer orderTypeId, @PathVariable Integer materialId) {
        orderTypeService.deleteTypeMaterial(orderTypeId, materialId);
    }

    /**
     * 根据工单类型查询物料
     *
     * @param workOrderMaterialQuery 工单类型编码
     * @return 物料
     */
    @PostMapping(value = "getMaterialByOrderTypeCode")
    @ApiOperation(value = "根据工单类型查询物料")
    public Page<OrderTypeMaterialDTO> getMaterialByOrderTypeCode(@RequestBody WorkOrderMaterialQuery workOrderMaterialQuery) {
        return orderTypeService.getMaterialByOrderTypeCode(workOrderMaterialQuery);
    }
}