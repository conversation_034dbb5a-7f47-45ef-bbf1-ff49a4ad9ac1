package com.hvisions.pms.service.imp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.pms.dao.HvPmMaterialPreparationDetailMapper;
import com.hvisions.pms.dto.HvPmMaterialPreparationDetailDTO;
import com.hvisions.pms.dto.HvPmMaterialPreparationDetailGroupDTO;
import com.hvisions.pms.dto.HvPmMaterialPreparationDetailQueryDTO;
import com.hvisions.pms.entity.HvPmMaterialPreparationDetail;
import com.hvisions.pms.repository.HvPmMaterialPreparationDetailRepository;
import com.hvisions.pms.service.HvPmMaterialPreparationDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * @Description HvPmMaterialPreparationDetailServiceImpl
 * <AUTHOR>
 * @Date 2024-05-21
 */
@Service
public class HvPmMaterialPreparationDetailServiceImpl implements HvPmMaterialPreparationDetailService {
    @Autowired
    private HvPmMaterialPreparationDetailRepository hvPmMaterialPreparationDetailRepository;

    @Autowired
    private HvPmMaterialPreparationDetailMapper hvPmMaterialPreparationDetailMapper;


    @Override
    public void addHvPmMaterialPreparationDetail(HvPmMaterialPreparationDetailDTO hvPmMaterialPreparationDetailDTO) {
        hvPmMaterialPreparationDetailRepository.save(DtoMapper.convert(hvPmMaterialPreparationDetailDTO, HvPmMaterialPreparationDetail.class));
    }

    @Override
    public void deleteHvPmMaterialPreparationDetail(Long id) {
        hvPmMaterialPreparationDetailRepository.deleteById(id);
    }

    @Override
    public void updateHvPmMaterialPreparationDetail(HvPmMaterialPreparationDetailDTO hvPmMaterialPreparationDetailDTO) {
        hvPmMaterialPreparationDetailRepository.save(DtoMapper.convert(hvPmMaterialPreparationDetailDTO, HvPmMaterialPreparationDetail.class));
    }

    @Override
    public HvPmMaterialPreparationDetailDTO getHvPmMaterialPreparationDetailById(Long id) {
        Optional<HvPmMaterialPreparationDetail> optional = hvPmMaterialPreparationDetailRepository.findById(id);
        return optional.map(hvPmMaterialPreparationDetail -> DtoMapper.convert(hvPmMaterialPreparationDetail, HvPmMaterialPreparationDetailDTO.class)).orElse(null);
    }

    @Override
    public List<HvPmMaterialPreparationDetailDTO> getAll() {
        return DtoMapper.convertList(hvPmMaterialPreparationDetailRepository.findAll(), HvPmMaterialPreparationDetailDTO.class);
    }

    @Override
    public List<HvPmMaterialPreparationDetailDTO> getByPreparationId(Long id) {
        LambdaQueryWrapper<HvPmMaterialPreparationDetail> queryWrapper = new LambdaQueryWrapper<HvPmMaterialPreparationDetail>();
        queryWrapper.eq(HvPmMaterialPreparationDetail::getPreparationId, id);
        return DtoMapper.convertList(hvPmMaterialPreparationDetailMapper.selectList(queryWrapper), HvPmMaterialPreparationDetailDTO.class);
    }

    @Override
    public Long addHvPmMaterialPreparationDetailReturnId(HvPmMaterialPreparationDetailDTO hvPmMaterialPreparationDetailDTO) {
        return hvPmMaterialPreparationDetailRepository.save(DtoMapper.convert(hvPmMaterialPreparationDetailDTO, HvPmMaterialPreparationDetail.class)).getId();
    }

    @Override
    public List<HvPmMaterialPreparationDetailGroupDTO> getGroupByPreparationId(Long id) {
        return hvPmMaterialPreparationDetailMapper.getGroupByPreparationId(id);
    }

    @Override
    public Page<HvPmMaterialPreparationDetailDTO> getGroupDetailsByQuery(HvPmMaterialPreparationDetailQueryDTO queryDTO) {
        return PageHelperUtil.getPage(hvPmMaterialPreparationDetailMapper::getGroupDetailsByQuery,queryDTO);
    }

    @Override
    public List<HvPmMaterialPreparationDetailDTO> getDetailByMaterialCodeAndStatus(String materialCode,Integer status) {
        return hvPmMaterialPreparationDetailMapper.getDetailByMaterialCodeAndStatus(materialCode,status);
    }
}
