package com.hvisions.pms.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.pms.entity.HvPmAgvTaskRecord;
import com.hvisions.pms.plan.HvPmAgvTaskRecordTabQueryDTO;
import com.hvisions.pms.service.HvPmAgvTaskRecordService;
import com.hvisions.thirdparty.common.dto.LineSchedulingDTO;
import com.hvisions.thirdparty.common.dto.WeldLineCallMaterialsDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-03-29 11:58
 */

@RestController
@RequestMapping(value = "/hvPmAgvTaskRecord")
@Api(description = "调度记录")
public class AgvTaskRecordController {

    @Autowired
    private HvPmAgvTaskRecordService hvPmAgvTaskRecordService;

    /**
     * 分页查询
     */
    @ApiOperation("分页查询")
    @PostMapping("/list")
    public Page<HvPmAgvTaskRecord>  list(@RequestBody HvPmAgvTaskRecordTabQueryDTO hvPmAgvTaskRecordTabQueryDTO,
                                                @RequestParam(name = "pageNum", defaultValue = "0") Integer pageNo,
                                                @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {

        hvPmAgvTaskRecordTabQueryDTO.setPage(pageNo >0?pageNo-1:pageNo);
        hvPmAgvTaskRecordTabQueryDTO.setPageSize(pageSize);
        hvPmAgvTaskRecordTabQueryDTO.setSort(true);
        hvPmAgvTaskRecordTabQueryDTO.setDirection(false);
        hvPmAgvTaskRecordTabQueryDTO.setSortCol("id");
        return hvPmAgvTaskRecordService.pageList(hvPmAgvTaskRecordTabQueryDTO);
    }


    /**
     * 导出
     * @param hvPmAgvTaskRecordTabQueryDTO
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    @ApiResultIgnore
    @PostMapping(value = "/exportHvPmAvgTaskRecord")
    @ApiOperation(value = "导出调度记录")
    public ResultVO<ExcelExportDto> exportHvPmAvgTaskRecord(@RequestBody HvPmAgvTaskRecordTabQueryDTO hvPmAgvTaskRecordTabQueryDTO) throws IOException, IllegalAccessException {
        return hvPmAgvTaskRecordService.exportHvPmAvgTaskRecord(hvPmAgvTaskRecordTabQueryDTO);
    }

    /**
     * 添加
     *
     * @param hvPmAgvTaskRecord HvPmAgvTaskRecord
     */
    @ApiOperation(value = "添加HvPmAgvTaskRecord信息")
    @PostMapping(value = "/add")
    public ResultVO addHvPmAgvTaskRecord(@RequestBody HvPmAgvTaskRecord hvPmAgvTaskRecord) {
        hvPmAgvTaskRecord.setSchedulingState(4);
        return ResultVO.success(hvPmAgvTaskRecordService.save(hvPmAgvTaskRecord));
    }

    /**
     * 删除
     *
     * @param id 主键
     */
    @ApiOperation(value = "删除HvPmAgvTaskRecord信息")
    @DeleteMapping(value = "/delete/{id}")
    public ResultVO deleteHvPmAgvTaskRecord(@PathVariable Integer id) {
        return ResultVO.success(hvPmAgvTaskRecordService.removeById(id));
    }

    /**
     * 修改
     *
     * @param hvPmAgvTaskRecord HvPmAgvTaskRecord
     */
    @ApiOperation(value = "修改HvPmAgvTaskRecord")
    @PutMapping(value = "/update")
    public ResultVO updateHvPmAgvTaskRecord(@RequestBody HvPmAgvTaskRecord hvPmAgvTaskRecord) {
        return ResultVO.success(hvPmAgvTaskRecordService.updateById(hvPmAgvTaskRecord));
    }

    /**
     * 根据id获取
     *
     * @param id 主键
     * @return HvPmAgvTaskRecord hvPmAgvTaskRecordDTO
     */
    @ApiOperation(value = "根据id获取HvPmAgvTaskRecord")
    @GetMapping(value = "/get/{id}")
    public ResultVO getList(@PathVariable Integer id) {
        return ResultVO.success(hvPmAgvTaskRecordService.getById(id));
    }

    /**
     * 查询全部
     *
     * @return 列表
     */
    @ApiOperation(value = "获取HvPmAgvTaskRecord列表")
    @GetMapping(value = "/getAll")
    public ResultVO getAll() {
        return ResultVO.success(hvPmAgvTaskRecordService.list());
    }

    /**
     * 根据任务号获取调度记录
     *
     * @return 列表
     */
    @ApiOperation(value = "根据任务号获取调度记录")
    @GetMapping(value = "/getRecordByTaskCode")
    public HvPmAgvTaskRecord getRecordByTaskCode(@RequestParam String taskCode) {
        return hvPmAgvTaskRecordService.getByTaskCode(taskCode);
    }


    @ApiOperation(value = "产线调度存储记录")
    @PostMapping("saveLineTask")
    public void saveLineTask(@RequestBody LineSchedulingDTO lineSchedulingDTO) {
        hvPmAgvTaskRecordService.saveLineTask(lineSchedulingDTO);
    }

    @ApiOperation(value = "产线调度存储记录")
    @PostMapping("updateTaskByTaskCode")
    private void updateTaskByTaskCode(@RequestParam("taskCode") String taskCode,
                                      @RequestParam("status") String status, @RequestParam("currentPositionCode") String currentPositionCode) {
        hvPmAgvTaskRecordService.updateTaskByTaskCode(taskCode,status,currentPositionCode);
    }

    @ApiOperation(value = "任务号找最新一条记录（正常就一条）")
    @GetMapping("getByTaskCode")
    private HvPmAgvTaskRecord getByTaskCode(@RequestParam("taskCode") String taskCode){
        return hvPmAgvTaskRecordService.getByTaskCode(taskCode);
    }

    @ApiOperation(value = "重复执行")
    @GetMapping("/repeatedExecution/{raskRecordId}")
    public void repeatedExecution(@PathVariable("raskRecordId") long raskRecordId){
        hvPmAgvTaskRecordService.repeatedExecution(raskRecordId);
    }

    @ApiOperation(value = "人工调度")
    @GetMapping("/manualScheduling/{raskRecordId}")
    public void manualScheduling(@PathVariable("raskRecordId") long raskRecordId){
        hvPmAgvTaskRecordService.manualScheduling(raskRecordId);
    }


    @ApiOperation(value = "根据叫料记录生成调度任务")
    @PostMapping(value = "/saveTaskRecordByCllMaterialsTask")
    public void saveTaskRecordByCllMaterialsTask(@RequestBody List<WeldLineCallMaterialsDTO> callMaterialsDTOS){
        hvPmAgvTaskRecordService.saveTaskRecordByCllMaterialsTask(callMaterialsDTOS);
    }

}
