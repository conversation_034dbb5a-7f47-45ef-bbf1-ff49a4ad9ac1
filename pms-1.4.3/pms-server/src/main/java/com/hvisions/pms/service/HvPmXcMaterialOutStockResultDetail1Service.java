package com.hvisions.pms.service;

import com.hvisions.pms.dto.HvPmXcMaterialOutStockResultDetail1DTO;
import com.hvisions.pms.entity.HvPmXcMaterialOutStockResultDetail1;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/5/8
 */
public interface HvPmXcMaterialOutStockResultDetail1Service {

    List<HvPmXcMaterialOutStockResultDetail1DTO> getByTaskNo(String taskNo);
    /**
     * 添加
     * @param detail1DTO
     * @return
     */
    long createDetail1(HvPmXcMaterialOutStockResultDetail1DTO detail1DTO);

    /**
     * 修改
     * @param detail1DTO
     * @return
     */
    long updateDetail1(HvPmXcMaterialOutStockResultDetail1DTO detail1DTO);

    /**
     * 删除
     * @param id
     */
    void deleteDetail1(long id,String taskNo);

    /**
     * 根据工单编号查询出库计划结果
     * @return
     */
    List<HvPmXcMaterialOutStockResultDetail1> getHvPmXcMaterialOutStockResultDetail1ByWorkOrderCode(String workOrderCode);

    List<HvPmXcMaterialOutStockResultDetail1DTO> getByTaskNoList(List<String> taskNoList);
}
