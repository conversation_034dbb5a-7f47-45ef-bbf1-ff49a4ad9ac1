package com.hvisions.pms.repository.changeshift;

import com.hvisions.pms.entity.changeshift.HvPmInputMaterials;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: HvPmInPutMaterialsRepository</p >
 * <p>Description: 交班班组工作时间投入料</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-10-12</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Repository
public interface InPutMaterialsRepository extends JpaRepository<HvPmInputMaterials, Integer> {

    /**
     * 根据交接班记录ID查询交班班组工作时间投入料
     *
     * @param changeShiftsId 交接班记录ID
     * @return 交班班组工作时间投入料信息列表
     */
    List<HvPmInputMaterials> getAllByChangeShiftsId(int changeShiftsId);
}