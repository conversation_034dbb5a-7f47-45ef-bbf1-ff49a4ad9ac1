package com.hvisions.pms.controller;

import com.hvisions.pms.dto.HvPmMaterialTaoPicDetailDTO;
import com.hvisions.pms.service.HvPmMaterialTaoPicDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title: HvPmMaterialTaoPicDetailController</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2024年4月19日</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Deprecated
@RestController
@RequestMapping(value = "/hvPmMaterialTaoPicDetail")
@Api(description = "")
public class HvPmMaterialTaoPicDetailController {

    private final HvPmMaterialTaoPicDetailService hvPmMaterialTaoPicDetailService;

    @Autowired
    public HvPmMaterialTaoPicDetailController(HvPmMaterialTaoPicDetailService hvPmMaterialTaoPicDetailService) {
        this.hvPmMaterialTaoPicDetailService = hvPmMaterialTaoPicDetailService;
    }
    /**
     * 添加
     *
     * @param hvPmMaterialTaoPicDetailDTO HvPmMaterialTaoPicDetail
     */
    @ApiOperation(value = "添加HvPmMaterialTaoPicDetail信息")
    @PostMapping(value = "/add")
    public void addHvPmMaterialTaoPicDetail(@Valid @RequestBody HvPmMaterialTaoPicDetailDTO hvPmMaterialTaoPicDetailDTO) {
            hvPmMaterialTaoPicDetailService.addHvPmMaterialTaoPicDetail(hvPmMaterialTaoPicDetailDTO);
    }

    /**
     * 删除
     *
     * @param id 主键
     */
    @ApiOperation(value = "删除HvPmMaterialTaoPicDetail信息")
    @DeleteMapping(value = "/delete/{id}")
    public void deleteHvPmMaterialTaoPicDetail(@PathVariable Long id) {
            hvPmMaterialTaoPicDetailService.deleteHvPmMaterialTaoPicDetail(id);
    }

    /**
     * 修改
     *
     * @param hvPmMaterialTaoPicDetailDTO HvPmMaterialTaoPicDetail
     */
    @ApiOperation(value = "修改HvPmMaterialTaoPicDetail")
    @PutMapping(value = "/update")
    public void updateHvPmMaterialTaoPicDetail(@Valid @RequestBody HvPmMaterialTaoPicDetailDTO hvPmMaterialTaoPicDetailDTO) {
            hvPmMaterialTaoPicDetailService.updateHvPmMaterialTaoPicDetail(hvPmMaterialTaoPicDetailDTO);
    }

    /**
     * 根据id获取
     *
     * @param id 主键
     * @return HvPmMaterialTaoPicDetail hvPmMaterialTaoPicDetailDTO
     */
    @ApiOperation(value = "根据id获取HvPmMaterialTaoPicDetail")
    @GetMapping(value = "/get/{id}")
    public HvPmMaterialTaoPicDetailDTO getList(@PathVariable Long id) {
        return hvPmMaterialTaoPicDetailService.getHvPmMaterialTaoPicDetailById(id);
    }

    /**
    * 查询全部
    * @return 列表
    */
    @ApiOperation(value = "获取HvPmMaterialTaoPicDetail列表")
    @GetMapping(value = "/getAll")
    public List<HvPmMaterialTaoPicDetailDTO> getAll(){
        return hvPmMaterialTaoPicDetailService.getAll();
    }
}