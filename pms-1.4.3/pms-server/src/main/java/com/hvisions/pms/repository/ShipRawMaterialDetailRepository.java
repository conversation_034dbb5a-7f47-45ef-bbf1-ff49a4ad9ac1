package com.hvisions.pms.repository;

import com.hvisions.pms.entity.HvPmShipRawMaterialDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/5/22
 */
@Repository
public interface ShipRawMaterialDetailRepository extends JpaRepository<HvPmShipRawMaterialDetail,Long> {
    List<HvPmShipRawMaterialDetail> getAllByRawMaterialId(Long id);
    void deleteByRawMaterialId(Long id);
    HvPmShipRawMaterialDetail getByRawMaterialIdAndSpecifications(Long rawMaterialId,String specifications);
}
