package com.hvisions.pms.controller;

import com.hvisions.hiperbase.materials.dto.MaterialDTO;
import com.hvisions.pms.dto.OperationOutPutMaterialDTO;
import com.hvisions.pms.dto.OutPutIDListDTO;
import com.hvisions.pms.service.OperationOutPutMaterialService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: OperationOutPutMaterialController</p >
 * <p>Description: 工序产出物料录入控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/18</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Api(description = "工序产出物料控制器")
@RestController
@RequestMapping(value = "/OperationOutPutMaterial")
public class OperationOutPutMaterialController {


    @Autowired
    OperationOutPutMaterialService operationOutPutMaterialService;

    /**
     * 获取可以产出的物料信息列表
     *
     * @param taskId 操作id
     * @return 物料列表
     */
    @ApiOperation(value = "获取步骤可以产出的物料信息")
    @GetMapping(value = "/getOutMaterial/{taskId}")
    public List<MaterialDTO> getOutMaterial(@PathVariable Integer taskId) {
        return operationOutPutMaterialService.getOutMaterial(taskId);
    }


    /**
     * 录入产出物料
     *
     * @param operationOutPutMaterialDTOS 产出物料列表
     */
    @ApiOperation(value = "录入产出物料")
    @PostMapping(value = "/createOutMaterial")
    public void createOutMaterial(@RequestBody List<OperationOutPutMaterialDTO> operationOutPutMaterialDTOS) {
        operationOutPutMaterialService.createOutMaterial(operationOutPutMaterialDTOS);
    }

    /**
     * 根据工序ID查询产出物料
     *
     * @param operationId 工序ID
     * @return 产出物料信息列表
     */
    @GetMapping(value = "/getAllByOperationId/{operationId}")
    @ApiOperation(value = "根据工序ID查询产出物料")
    public List<OperationOutPutMaterialDTO> getAllByOperationId(@PathVariable int operationId) {
        return operationOutPutMaterialService.getAllByOperationId(operationId);
    }

    /**
     * 根据ID列表删除产出物料
     *
     * @param outPutIDListDTO ID列表
     */
    @ApiOperation(value = "根据ID列表删除产出物料")
    @DeleteMapping(value = "/deleteByIdList")
    public void deleteByIdList(@RequestBody OutPutIDListDTO outPutIDListDTO) {
        operationOutPutMaterialService.deleteByIdList(outPutIDListDTO);
    }

}
