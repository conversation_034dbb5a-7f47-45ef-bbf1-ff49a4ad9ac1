package com.hvisions.pms.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.bom.dto.HvBmFrameDTO;
import com.hvisions.hiperbase.client.HvBmFrameClient;
import com.hvisions.pms.dto.materialKittingTask.MaterialKittingTaskDTO;
import com.hvisions.pms.entity.productWorkOrder.ProductWorkOrder;
import com.hvisions.pms.service.DeliveryMissionService;
import com.hvisions.pms.service.ProductWorkOrderService;
import com.hvisions.pms.utils.SerialCodeUtilsV2;
import com.hvisions.thridparty.client.RCSClient;
import com.hvisions.wms.client.AgvSchedulingClient;
import com.hvisions.wms.dto.agvScheduling.HvWmAgvSchedulingDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

@RestController
@RequestMapping("/emptyPalletRecycling")
@Api(tags = "空框回库")
public class EmptyPalletRecyclingController {

    @Autowired
    private ProductWorkOrderService productWorkOrderService;
    @Autowired
    private DeliveryMissionService deliveryMissionService;
    @Autowired
    private HvBmFrameClient frameClient;
    @Autowired
    private AgvSchedulingClient agvSchedulingClient;
    @Autowired
    private SerialCodeUtilsV2 serialCodeUtilsV2;
    @Autowired
    private RCSClient rcsClient;

    @PostMapping("/recycling")
    @ApiOperation("添加配送任务")
    @Transactional
    public ResultVO addMission(@RequestBody MaterialKittingTaskDTO materialKittingTaskDTO, @ApiIgnore @UserInfo UserInfoDTO userInfoDTO) {
        ProductWorkOrder productWorkOrder = productWorkOrderService.getOne(new LambdaQueryWrapper<ProductWorkOrder>()
                .eq(ProductWorkOrder::getProductWorkOrderCode, materialKittingTaskDTO.getProductWorkOrderCode()));
        if (productWorkOrder == null) {
            return ResultVO.error(500, "未找到生产工单");
        }
        //        根据托盘编号获取运输工具类型
        HvBmFrameDTO frameDTO = frameClient.findByFrameCode(materialKittingTaskDTO.getPalletNo()).getData();
        return null;
    }
}