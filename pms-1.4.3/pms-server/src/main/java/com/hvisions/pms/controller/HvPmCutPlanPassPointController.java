package com.hvisions.pms.controller;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.utils.EasyExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.pms.dto.HvPmCutPlanPassPointDTO;
import com.hvisions.pms.importTemplate.HvPmCutPlanPassPointTemplate;
import com.hvisions.pms.service.HvPmCutPlanPassPointService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;


@RestController
@RequestMapping(value = "/hvPmCutPlanPassPoint")
@Api(tags = "切割任务过点")
public class HvPmCutPlanPassPointController {

    private final HvPmCutPlanPassPointService hvPmCutPlanPassPointService;

    @Autowired
    public HvPmCutPlanPassPointController(HvPmCutPlanPassPointService hvPmCutPlanPassPointService) {
        this.hvPmCutPlanPassPointService = hvPmCutPlanPassPointService;
    }

    /**
     * 分页查询
     *
     * @return List<HvPmCutPlanPassPointDTO>
     */
    @ApiOperation(value = "分页查询HvPmCutPlanPassPoint信息")
    @PostMapping(value = "/getPage")
    public Page<HvPmCutPlanPassPointDTO> getPage(@RequestBody HvPmCutPlanPassPointDTO hvPmCutPlanPassPointDTO) {
        return hvPmCutPlanPassPointService.getPage(hvPmCutPlanPassPointDTO);
    }

    /**
     * 添加
     *
     * @param hvPmCutPlanPassPointDTO HvPmCutPlanPassPoint
     */
    @ApiOperation(value = "添加HvPmCutPlanPassPoint信息")
    @PostMapping(value = "/add")
    public void addHvPmCutPlanPassPoint(@Valid @RequestBody HvPmCutPlanPassPointDTO hvPmCutPlanPassPointDTO) {

        hvPmCutPlanPassPointService.addHvPmCutPlanPassPoint(hvPmCutPlanPassPointDTO);
    }

    /**
     * 删除
     *
     * @param id 主键
     */
    @ApiOperation(value = "删除HvPmCutPlanPassPoint信息")
    @DeleteMapping(value = "/delete/{id}")
    public void deleteHvPmCutPlanPassPoint(@PathVariable Long id) {
        hvPmCutPlanPassPointService.deleteHvPmCutPlanPassPoint(id);
    }

    /**
     * 修改
     *
     * @param hvPmCutPlanPassPointDTO HvPmCutPlanPassPoint
     */
    @ApiOperation(value = "修改HvPmCutPlanPassPoint")
    @PutMapping(value = "/update")
    public void updateHvPmCutPlanPassPoint(@Valid @RequestBody HvPmCutPlanPassPointDTO hvPmCutPlanPassPointDTO) {
        hvPmCutPlanPassPointService.updateHvPmCutPlanPassPoint(hvPmCutPlanPassPointDTO);
    }

    /**
     * 根据id获取
     *
     * @param id 主键
     * @return HvPmCutPlanPassPoint hvPmCutPlanPassPointDTO
     */
    @ApiOperation(value = "根据id获取HvPmCutPlanPassPoint")
    @GetMapping(value = "/get/{id}")
    public HvPmCutPlanPassPointDTO getList(@PathVariable Long id) {
        return hvPmCutPlanPassPointService.getHvPmCutPlanPassPointById(id);
    }

    /**
     * 查询全部
     *
     * @return 列表
     */
    @ApiOperation(value = "获取HvPmCutPlanPassPoint列表")
    @GetMapping(value = "/getAll")
    public List<HvPmCutPlanPassPointDTO> getAll() {
        return hvPmCutPlanPassPointService.getAll();
    }
    

    @ApiOperation(value = "导入")
    @PostMapping(value = "/import")
    public void importExcel(@RequestParam("file") MultipartFile file) {
            hvPmCutPlanPassPointService.importExcel(file);
    }


    @ApiOperation(value = "获取导入模板")
    @GetMapping(value = "/getImportTemplate")
    public ResultVO<ExcelExportDto> getImportTemplate() {
        List<HvPmCutPlanPassPointTemplate> list = new ArrayList<>();
        return ResultVO.success(EasyExcelUtil.getExcel(list, HvPmCutPlanPassPointTemplate.class, System.currentTimeMillis() + "生产工单导入模版.xlsx"));
    }


}