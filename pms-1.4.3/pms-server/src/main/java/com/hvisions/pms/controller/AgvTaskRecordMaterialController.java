package com.hvisions.pms.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.client.MaterialClient;
import com.hvisions.hiperbase.materials.dto.MaterialDTO;
import com.hvisions.pms.entity.HvPmAgvTaskRecordMaterial;
import com.hvisions.pms.service.HvPmAgvTaskRecordMaterialService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 调度记录物料信息
 * <AUTHOR>
 * @date 2024-05-15 9:23
 */

@RestController
@RequestMapping(value = "/hvPmAgvTaskRecordMaterial")
@Api(description = "调度记录物料信息")
public class AgvTaskRecordMaterialController {
    @Autowired
    private HvPmAgvTaskRecordMaterialService hvPmAgvTaskRecordMaterialService;
    @Autowired
    private MaterialClient materialClient;


    /**
     * 分页查询
     */
    @ApiOperation("分页查询")
    @PostMapping("/list")
    public ResultVO list(@RequestBody HvPmAgvTaskRecordMaterial hvPmAgvTaskRecordMaterial,
                         @RequestParam(name = "pageNum", defaultValue = "1") Integer pageNo,
                         @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        QueryWrapper<HvPmAgvTaskRecordMaterial> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .eq("rask_record_id", hvPmAgvTaskRecordMaterial.getRaskRecordId())
                .eq(!StringUtils.isEmpty(hvPmAgvTaskRecordMaterial.getMaterialCode()), "material_code", hvPmAgvTaskRecordMaterial.getMaterialCode())
                .eq(!StringUtils.isEmpty(hvPmAgvTaskRecordMaterial.getPn()), "pn", hvPmAgvTaskRecordMaterial.getPn())
                .eq(!StringUtils.isEmpty(hvPmAgvTaskRecordMaterial.getWorkOrderCode()), "work_order_code", hvPmAgvTaskRecordMaterial.getWorkOrderCode())
                .eq(!StringUtils.isEmpty(hvPmAgvTaskRecordMaterial.getParentWorkOrderCode()), "parent_work_order_code", hvPmAgvTaskRecordMaterial.getParentWorkOrderCode())
                .orderByDesc("id");
        Page<HvPmAgvTaskRecordMaterial> page = new Page<>(pageNo, pageSize);
        Page<HvPmAgvTaskRecordMaterial> list = hvPmAgvTaskRecordMaterialService.page(page, queryWrapper);

        return ResultVO.success(list);
    }

    /**
     * 添加数据
     *
     * @param agvTaskRecordMaterial
     */
    @ApiOperation(value = "添加")
    @PostMapping(value = "/add")
    public boolean add(@RequestBody HvPmAgvTaskRecordMaterial agvTaskRecordMaterial) {
        //获取物料名
        MaterialDTO materialDTO = materialClient.getByMaterialCode(agvTaskRecordMaterial.getMaterialCode()).getData();
        if(materialDTO != null){
            agvTaskRecordMaterial.setMaterialName(materialDTO.getMaterialName());
        }
        return hvPmAgvTaskRecordMaterialService.save(agvTaskRecordMaterial);
    }


    /**
     * 批量插入
     *
     * @param list
     */
    @ApiOperation(value = "批量插入")
    @PostMapping(value = "/batchAdd")
    public boolean batchAdd(@RequestBody List<HvPmAgvTaskRecordMaterial> list) {
        return hvPmAgvTaskRecordMaterialService.saveBatch(list);
    }

    /**
     * 根据调度记录ID查询物料信息
     *
     * @param raskRecordId
     */
    @ApiOperation(value = "根据调度记录ID查询物料信息")
    @GetMapping(value = "/findListByRaskRecordId/{raskRecordId}")
    public List<HvPmAgvTaskRecordMaterial> findListByRaskRecordId(@PathVariable Long raskRecordId) {

        return hvPmAgvTaskRecordMaterialService.findListByRaskRecordId(raskRecordId);
    }
}
