package com.hvisions.pms.service;

import com.hvisions.pms.changeshiftsdto.BuildOperationDTO;
import com.hvisions.pms.inspectdto.AllInspectionItemDTO;
import com.hvisions.pms.inspectdto.CheckPointDTO;
import com.hvisions.pms.inspectdto.InspectionItemDTO;
import com.hvisions.pms.inspectdto.InspectionItemQueryDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <p>Title: InspectionItemService</p >
 * <p>Description: 检查项目服务层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-10-14</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
public interface InspectionItemService {


    /**
     * 新增检查项目
     *
     * @param inspectionItemDTO 检查项目对象¬
     */
    void createInspectionItem(InspectionItemDTO inspectionItemDTO);


    /**
     * 更新检查项目
     *
     * @param inspectionItemDTO 检查项目对象
     */
    void updateInspectionItem(InspectionItemDTO inspectionItemDTO);


    /**
     * 删除检查项目
     *
     * @param id 检查项目ID
     */
    void deleteInspectionItem(int id);


    /**
     * 根据Id查询检查项目
     *
     * @param id 检查项目ID
     * @return 检查项目信息
     */
    AllInspectionItemDTO getInspectionItemById(int id);


    /**
     * 根据 条件分页查询检查项目
     *
     * @param inspectionItemQueryDTO 查询条件
     * @return 检查项目分页信息
     */
    Page<AllInspectionItemDTO> getInspectionItemByQuery(InspectionItemQueryDTO inspectionItemQueryDTO);

    /**
     * 关联检查项目到工艺步骤
     *
     * @param buildOperationDTO 关联关系
     */
    void buildInspectWorkCenter(BuildOperationDTO buildOperationDTO);


    /**
     * 删除工位项目关联关系
     *
     * @param workCenterId 工位ID
     * @param itemId       检查项目ID
     */
    void deleteInspectWorkCenter(int workCenterId, int itemId);

    /**
     * 根据工位ID查询工位关联的检查项目
     *
     * @param workCenterId 工位ID
     * @return 检查项目信息
     */
    AllInspectionItemDTO getItemByWorkCenter(int workCenterId);

    /**
     * 新增检查点
     *
     * @param checkPointDTO 检查点对象
     */
    void createCheckPoint(CheckPointDTO checkPointDTO);

    /**
     * 新增检查点
     *
     * @param checkPointDTOS 检查点对象列表
     */
    void createCheckPointList(List<CheckPointDTO> checkPointDTOS);

    /**
     * 更新检查点
     *
     * @param checkPointDTO 检查点对象
     */
    void updateCheckPoint(CheckPointDTO checkPointDTO);


    /**
     * 删除检查点
     *
     * @param id 检查点
     */
    void deleteCheckPoint(int id);


    /**
     * 根据检查项目ID查询检查点
     *
     * @param inspectionItemId 检查项目id
     * @return 检查点信息
     */
    List<CheckPointDTO> getCheckPointByItemId(int inspectionItemId);

}