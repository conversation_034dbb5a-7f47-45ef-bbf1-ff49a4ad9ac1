package com.hvisions.pms.repository;


import com.hvisions.pms.entity.HvPmWorkOrderCompleteMaterial;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <P>    <P>
 *
 * <AUTHOR>
 * @date 2024/12/10
 */
@Repository
public interface WorkOrderCompleteMaterialRepository extends JpaRepository<HvPmWorkOrderCompleteMaterial, Long> {
    List<HvPmWorkOrderCompleteMaterial> getByOrderCode(String orderCode);

}
