package com.hvisions.pms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.pms.entity.HvPmCallMaterialRecord;
import com.hvisions.pms.service.HvPmCallMaterialRecordService;
import com.hvisions.thirdparty.common.dto.WeldLineCallMaterialsDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-05-27 15:02
 */
@RestController
@RequestMapping(value = "/callMaterialRecord")
@Api(description = "叫料记录")
public class CallMaterialRecordController {

    @Autowired
    private HvPmCallMaterialRecordService hvPmCallMaterialRecordService;

    /**
     * 分页查询
     */
    @ApiOperation("分页查询")
    @PostMapping("/list")
    public Page<HvPmCallMaterialRecord> list(@RequestBody HvPmCallMaterialRecord condition,
                                             @RequestParam(name = "pageNum", defaultValue = "1") Integer pageNo,
                                             @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        QueryWrapper<HvPmCallMaterialRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.gt(!StringUtils.isEmpty(condition.getStartTimeUTC()), "request_time", condition.getStartTimeUTC())
                .lt(!StringUtils.isEmpty(condition.getEndTimeUTC()), "request_time", condition.getEndTimeUTC())
                .eq(!StringUtils.isEmpty(condition.getRequestCode()), "request_code", condition.getRequestCode())
                .eq(!StringUtils.isEmpty(condition.getRequestUserCode()), "request_user_code", condition.getRequestUserCode())
                .eq(!StringUtils.isEmpty(condition.getWorkOrderCode()), "work_order_code", condition.getWorkOrderCode())
                .eq(!StringUtils.isEmpty(condition.getFrameCode()), "frame_code", condition.getFrameCode())
                .eq(!StringUtils.isEmpty(condition.getPointCode()), "point_code", condition.getPointCode())
                .orderByDesc("id");
        return hvPmCallMaterialRecordService.pageList(queryWrapper, pageNo, pageSize);
    }

    @ApiOperation(value = "添加")
    @PostMapping(value = "/add")
    public boolean add(@RequestBody HvPmCallMaterialRecord hvPmCallMaterialRecord) {
        return hvPmCallMaterialRecordService.save(hvPmCallMaterialRecord);
    }

    /**
     * 删除
     *
     * @param id 主键
     */
    @ApiOperation(value = "删除")
    @DeleteMapping(value = "/delete/{id}")
    public boolean delete(@PathVariable long id) {
        return hvPmCallMaterialRecordService.removeById(id);
    }

    @ApiOperation(value = "修改")
    @PutMapping(value = "/update")
    public boolean update(@RequestBody HvPmCallMaterialRecord hvPmCallMaterialRecord) {
        return hvPmCallMaterialRecordService.updateById(hvPmCallMaterialRecord);
    }

    @ApiOperation(value = "第三方接口生成叫料数据")
    @PostMapping(value = "/createCallMaterialRecord")
    public void createCallMaterialRecord(@RequestBody List<WeldLineCallMaterialsDTO> weldLineCallMaterialsDTOS) {
        hvPmCallMaterialRecordService.createCallMaterialRecord(weldLineCallMaterialsDTOS);
    }

    @ApiResultIgnore
    @PostMapping("/exportCallMaterialRecord")
    @ApiOperation("导出叫料记录")
    public ResultVO<ExcelExportDto> exportCallMaterialRecord(@RequestBody HvPmCallMaterialRecord hvPmCallMaterialRecord) throws IOException, IllegalAccessException {
        return hvPmCallMaterialRecordService.exportCallMaterialRecord(hvPmCallMaterialRecord);
    }

}