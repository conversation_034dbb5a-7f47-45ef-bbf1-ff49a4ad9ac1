package com.hvisions.pms.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.pms.dto.HvPmPlanRouteInfoDTO;
import com.hvisions.pms.entity.HvPmPlanRouteInfo;
import com.hvisions.pms.service.HvPmPlanRouteInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-06
 */

@RestController
@RequestMapping(value = "/HvPmPlanRouteInfo")
@Api(description = "订单工艺")
public class HvPmPlanRouteInfoController {

    @Autowired
    private HvPmPlanRouteInfoService hvPmPlanRouteInfoService;

    /**
     * 分页查询
     */
    @ApiOperation("分页查询")
    @PostMapping("/list")
    public Page<HvPmPlanRouteInfo> list(@RequestBody HvPmPlanRouteInfo hvPmPlanRouteInfo,
                         @RequestParam(name="pageNum", defaultValue="1") Integer pageNo,
                         @RequestParam(name="pageSize", defaultValue="10") Integer pageSize)

    {
        LambdaQueryWrapper<HvPmPlanRouteInfo> lqw = new LambdaQueryWrapper<>();
        // 计划ID
        lqw.eq(hvPmPlanRouteInfo.getPlanId() != null,HvPmPlanRouteInfo::getPlanId, hvPmPlanRouteInfo.getPlanId());
        // 计划编号
        lqw.eq(!StringUtils.isEmpty(hvPmPlanRouteInfo.getPlanCode()),HvPmPlanRouteInfo::getPlanCode, hvPmPlanRouteInfo.getPlanCode());
        // 零件物料编号
        lqw.eq(!StringUtils.isEmpty(hvPmPlanRouteInfo.getStepCode()),HvPmPlanRouteInfo::getStepCode, hvPmPlanRouteInfo.getStepCode());
        // 船型
        lqw.eq(!StringUtils.isEmpty(hvPmPlanRouteInfo.getStepName()),HvPmPlanRouteInfo::getStepName, hvPmPlanRouteInfo.getStepName());
        // 分段号
        lqw.eq(!StringUtils.isEmpty(hvPmPlanRouteInfo.getStepId()),HvPmPlanRouteInfo::getStepId, hvPmPlanRouteInfo.getStepId());

        return hvPmPlanRouteInfoService.pageList(lqw,pageNo,pageSize);

    }

    /**
     * 添加
     *
     * @param hvPmPlanRouteInfo HvPmPlanRouteInfo
     */
    @ApiOperation(value = "添加HvPmPlanRouteInfo信息")
    @PostMapping(value = "/add")
    public Boolean addHvPmPlanRouteInfo(@RequestBody HvPmPlanRouteInfo hvPmPlanRouteInfo) {
        return hvPmPlanRouteInfoService.save(hvPmPlanRouteInfo);
    }

    /**
     * 批量添加
     *
     * @param hvPmPlanRouteInfoList HvPmPlanRouteInfoList
     */
    @ApiOperation(value = "批量添加HvPmPlanRouteInfo信息")
    @PostMapping(value = "/addList")
    public Integer addHvPmPlanRouteInfoList(@RequestBody List<HvPmPlanRouteInfo> hvPmPlanRouteInfoList) {
        return hvPmPlanRouteInfoService.InsertHvPmPlanRouteInfoList(hvPmPlanRouteInfoList);
    }



    /**
     * 删除
     *
     * @param id 主键
     */
    @ApiOperation(value = "删除HvPmPlanRouteInfo信息")
    @DeleteMapping(value = "/delete/{id}")
    public Boolean deleteHvPmPlanRouteInfo(@PathVariable Integer id) {
        return hvPmPlanRouteInfoService.removeById(id);
    }

    /**
     * 修改
     *
     * @param hvPmPlanRouteInfo HvPmPlanRouteInfo
     */
    @ApiOperation(value = "修改HvPmPlanRouteInfo")
    @PutMapping(value = "/update")
    public Boolean updateHvPmPlanRouteInfo(@RequestBody HvPmPlanRouteInfo hvPmPlanRouteInfo) {
        return hvPmPlanRouteInfoService.updateById(hvPmPlanRouteInfo);
    }

    /**
     * 根据id获取
     *
     * @param id 主键
     * @return HvPmPlanRouteInfo HvPmPlanRouteInfo
     */
    @ApiOperation(value = "根据id获取HvPmPlanRouteInfo")
    @GetMapping(value = "/get/{id}")
    public HvPmPlanRouteInfo getList(@PathVariable Integer id) {
        return hvPmPlanRouteInfoService.getById(id);
    }


    /**
     * 根据工单号获取工序工单id
     */
    @ApiOperation(value = "根据工单号获取工序工单id")
    @GetMapping(value = "/getByWorkOrderCode/{workOrderCode}")
    public HvPmPlanRouteInfoDTO getStepIdByWorkOrderCode(@PathVariable String workOrderCode) {
        HvPmPlanRouteInfo hvPmPlanRouteInfo = hvPmPlanRouteInfoService.getStepIdByWorkOrderCode(workOrderCode);
        HvPmPlanRouteInfoDTO hvPmPlanRouteInfoDTO = new HvPmPlanRouteInfoDTO();
        BeanUtils.copyProperties(hvPmPlanRouteInfo, hvPmPlanRouteInfoDTO);
        return hvPmPlanRouteInfoDTO;
    }


    @ApiOperation("根据工单编码删除HvPmPlanRouteInfo")
    @DeleteMapping("/deleteByPlanCode/{planCode}")
    public Boolean deleteByPlanCode(@PathVariable String planCode){
       return hvPmPlanRouteInfoService.deleteByPlanCode(planCode);
    }

}
