package com.hvisions.pms.controller;

import com.hvisions.pms.dto.HvPmAgvTaskRecordDTO;
import com.hvisions.pms.entity.HvPmAgvTaskRecord;
import com.hvisions.pms.service.PointTaskService;
import com.hvisions.thirdparty.common.dto.AGVStatusDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024-05-15 15:15
 */

@RestController
@RequestMapping(value = "/hvBmPointTask")
@Api(description = "料点任务")
public class PointTaskController {

    @Autowired
    private PointTaskService pointTaskService;

    /**
     * 调度任务执行
     * @return
     */
    @ApiOperation(value = "调度任务执行")
    @PostMapping(value = "/execute")
    public void execute(@RequestBody AGVStatusDTO agvStatusDTO){
        pointTaskService.execute(agvStatusDTO);
    }

    /**
     * 产线调度执行反馈 hvPmAgvTaskRecordDTO
     *
     */
    @ApiOperation(value = "产线调度执行反馈")
    @PostMapping(value = "/lineSchedulingResp")
    public void schedulingResp(@RequestBody HvPmAgvTaskRecordDTO hvPmAgvTaskRecordDTO){
        pointTaskService.schedulingResp(hvPmAgvTaskRecordDTO);
    }
}
