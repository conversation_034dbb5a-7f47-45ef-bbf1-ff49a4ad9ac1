package com.hvisions.pms.repository.plan;

import com.hvisions.pms.entity.plan.HvPmMaterialCutPlanDetail2;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/8/2
 */
@Repository
public interface HvPmMaterialCutPlanDetail2Repository extends JpaRepository<HvPmMaterialCutPlanDetail2,Long> {

    List<HvPmMaterialCutPlanDetail2> getAllByCutPlanId(long id);

    HvPmMaterialCutPlanDetail2 findByCutPlanCodeAndOperationType(String cutPlanCode, String operationType);

    @Query("SELECT d FROM HvPmMaterialCutPlanDetail2 d WHERE d.cutPlanId IN :cutPlanIds")
    List<HvPmMaterialCutPlanDetail2> findByCutPlanIds(@Param("cutPlanIds") List<Long> cutPlanIds);
}
