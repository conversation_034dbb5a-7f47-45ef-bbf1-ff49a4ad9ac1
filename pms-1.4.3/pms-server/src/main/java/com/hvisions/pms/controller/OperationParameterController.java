package com.hvisions.pms.controller;

import com.hvisions.common.annotation.EnableFilter;
import com.hvisions.pms.dto.OperationParameterByVersionDTO;
import com.hvisions.pms.dto.OperationParameterDTO;
import com.hvisions.pms.dto.OperationParameterUpdateDTO;
import com.hvisions.pms.parameterdto.OperationParameterPhoneDTO;
import com.hvisions.pms.service.OperationParameterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: OperationParameterController</p >
 * <p>Description: 工序参数控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/2/20</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Api(description = "工序参数")
@RequestMapping(value = "/operationParameter")
@RestController
public class OperationParameterController {


    @Autowired
    OperationParameterService operationParameterService;


    /***
     * 更新工序参数
     * @param operationParameterUpdateDTO 工序参数DTO
     */
    @ApiOperation(value = "录入工序参数")
    @PutMapping(value = "/updateParameter")
    public void updateParameter(@RequestBody List<OperationParameterUpdateDTO> operationParameterUpdateDTO) {
        operationParameterService.updateParameter(operationParameterUpdateDTO);
    }

    /**
     * 手机端录入工序参数
     *
     * @param operationParameterPhoneDTO 工序参数值
     */
    @ApiOperation(value = "/手机端录入工序参数")
    @PutMapping(value = "/insertParameter")
    public void insertParameter(@RequestBody OperationParameterPhoneDTO operationParameterPhoneDTO) {
        operationParameterService.insertParameterActualValue(operationParameterPhoneDTO);
    }

    /**
     * 根据工序ID查询工序参数
     *
     * @param operationId 工序ID
     * @return 工序参数列表
     */
    @ApiOperation(value = "根据工序ID查询工序参数")
    @GetMapping(value = "/getParameterByOperationId/{operationId}")
    public List<OperationParameterDTO> getParameterByOperationId(@PathVariable int operationId) {
        return operationParameterService.getParameterByOperationId(operationId);
    }

    /**
     * 根据ID获取工序参数
     *
     * @param id 工序参数ID
     * @return 工序参数ID
     */
    @GetMapping("/getOperationParameterDTOById/{id}")
    @ApiOperation(value = "根据ID获取工序参数")
    public OperationParameterByVersionDTO getOperationParameterDTOById(@PathVariable int id) {
        return operationParameterService.getParameterById(id);
    }


    /**
     * 根据参数用途查询工序参数
     *
     * @param parameterUsage 参数用途
     * @return 工序参数列表
     */
    @EnableFilter
    @GetMapping(value = "/getParameterByUsage/{parameterUsage}")
    @ApiOperation(value = "根据参数用途查询工序参数")
    public List<OperationParameterDTO> getParameterByUsage(@PathVariable int parameterUsage) {
        return operationParameterService.getParameterByUsage(parameterUsage);
    }


    /**
     * 根据工序ID与参数用途查询
     *
     * @param operationId    工序ID
     * @param parameterUsage 参数用途
     * @return 工序参数
     */
    @GetMapping(value = "/getByOperationIdAndParameterUsage/{operationId}/{parameterUsage}")
    @ApiOperation(value = "根据工序ID与参数用途查询参数")
    public OperationParameterDTO getByOperationIdAndParameterUsage(@PathVariable int operationId, @PathVariable int parameterUsage) {
        return operationParameterService.getByOperationIdAndParameterUsage(operationId, parameterUsage);
    }


    /**
     * 新增工序参数录入表单
     *
     * @param  taskId 生产任务ID
     */
    @PostMapping(value = "/createParameterVersion/{taskId}")
    @ApiOperation(value = "/新增工序参数录入表单")
    public void createParameterVersion(@PathVariable int taskId) {
        operationParameterService.createParameterVersion(taskId);
    }

    /**
     * 根据工序ID 参数版本查询
     *
     * @param parameterVersion 工序参数版本
     * @param operationId      工序ID
     * @return 工序参数信息列表
     */
    @GetMapping(value = "/getParameterByVersionAndOperationId/{operationId}/{parameterVersion}")
    @ApiOperation(value = "根据工序ID参数版本获取工序参数列表")
    public List<OperationParameterByVersionDTO> getParameterByVersionAndOperationId(@PathVariable int operationId, @PathVariable int parameterVersion) {
        return operationParameterService.getParameterByVersionAndOperationId(operationId, parameterVersion);
    }

    /**
     * 获取工序下所有参数版本
     *
     * @param operationId 工序ID
     * @return 工序参数版本
     */
    @GetMapping(value = "/getVersionsByOperationId/{operationId}")
    @ApiOperation(value = "获取工序下所有参数版本号")
    public List<Integer> getVersionsByOperationId(@PathVariable int operationId) {
        return operationParameterService.getVersionsByOperationId(operationId);

    }

}
