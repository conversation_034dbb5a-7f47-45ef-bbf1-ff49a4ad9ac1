package com.hvisions.pms.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.annotation.EnableFilter;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.ExtendColumnInfo;
import com.hvisions.common.dto.ExtendInfo;
import com.hvisions.common.service.BaseExtendService;
import com.hvisions.common.utils.EasyExcelUtil;
import com.hvisions.common.utils.EnumUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.bom.dto.BomAllDTO;
import com.hvisions.pms.dao.SearchMapper;
import com.hvisions.pms.dto.*;
import com.hvisions.pms.enums.WorkOrderStateEnum;
import com.hvisions.pms.importTemplate.WorkOrderTemplate;
import com.hvisions.pms.materialdto.OperationMaterialsDTO;
import com.hvisions.pms.query.OrderQuery;
import com.hvisions.pms.service.WorkOrderCompleteMaterialService;
import com.hvisions.pms.service.WorkOrderCompleteService;
import com.hvisions.pms.service.WorkOrderService;
import com.hvisions.pms.viewdto.WorkOrderNumDTO;
import com.hvisions.thirdparty.common.dto.MesOrderDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * <p>Title: HvPmWorkOrder</p>
 * <p>Description: orderManageController </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/01/17</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/workOrder")
@Slf4j
@Api(description = "工单管理")
public class WorkOrderController {
    private final WorkOrderService workOrderService;

    private final SearchMapper searchMapper;

    @Resource(name = "hv_pm_work_order_extend")
    BaseExtendService workOrderExtend;

    @Autowired
    private WorkOrderCompleteService workOrderCompleteService;

    @Autowired
    private WorkOrderCompleteMaterialService workOrderCompleteMaterialService;


    @Autowired
    public WorkOrderController(WorkOrderService workOrderService, SearchMapper searchMapper) {
        this.workOrderService = workOrderService;
        this.searchMapper = searchMapper;
    }

    /**
     * 新增
     *
     * @param workOrderDTO 传入的对象
     * @return 添加后的实体Id
     */
    @EnableFilter
    @ApiOperation(value = "创建工单")
    @PostMapping(value = "/createOrderManage")
    public int createOrderManage(@RequestBody WorkOrderDTO workOrderDTO) {
        return workOrderService.createOrderManage(workOrderDTO).getId();
    }

    /**
     * 修改
     *
     * @param workOrderDTO 传入的对象
     * @return 添加后的实体Id
     */
    @EnableFilter
    @ApiOperation(value = "修改工单信息")
    @PutMapping(value = "/updateOrderManage")
    public int updateOrderManage(@RequestBody WorkOrderDTO workOrderDTO) {
        return workOrderService.updateOrderManage(workOrderDTO);
    }

    /**
     * 删除
     *
     * @param id 实体id
     */
    @ApiOperation(value = "删除工单")
    @DeleteMapping(value = "/deleteOrderManageById/{id}")
    public void deleteOrderManageById(@PathVariable Integer id) {
        workOrderService.deleteOrderManageById(id);
    }

    /**
     * 批量删除工单
     *
     * @param idList ID列表
     */
    @ApiOperation(value = "批量删除工单")
    @DeleteMapping(value = "/deleteOrderManageByIdList")
    public void deleteOrderManageByIdList(@RequestBody List<Integer> idList) {
        workOrderService.deleteOrderMangeByIdList(idList);
    }

    /**
     * 创建子工单群
     *
     * @param orderProjectDTO 创建工单对象
     */
    @ApiOperation(value = "子工单")
    @PostMapping(value = "/createSubOrderManage")
    public void createSubOrderManage(@RequestBody OrderProjectDTO orderProjectDTO) {
        workOrderService.createSubOrderManage(orderProjectDTO);
    }

    /**
     * 添加orderManage扩展属性
     *
     * @param extendColumnInfo 扩展属性信息
     */
    @EnableFilter
    @PostMapping(value = "/createWorkOrderColumn")
    @ApiOperation(value = "添加工单扩展属性")
    public void createWorkOrderColumn(@RequestBody ExtendColumnInfo extendColumnInfo) {
        workOrderExtend.addExtend(extendColumnInfo);
    }


    /**
     * 更新orderManage扩展属性
     *
     * @param extendInfo 扩展属性信息
     */
    @EnableFilter
    @PutMapping(value = "/updateWorkOrderExtendInfo")
    @ApiOperation(value = "更新工单扩展属性")
    public void updateWorkOrderExtendInfo(@RequestBody ExtendInfo extendInfo) {
        workOrderExtend.updateExtendInfo(extendInfo);
    }

    /**
     * 删除orderManage扩展属性
     *
     * @param columnName 扩展属性名称
     */
    @EnableFilter
    @DeleteMapping(value = "/deleteWorkOrderColumn/{columnName}")
    @ApiOperation(value = "删除工单扩展属性")
    public void deleteWorkOrderColumn(@PathVariable String columnName) {
        workOrderExtend.dropExtend(columnName);
    }

    /**
     * 获取orderManage扩展属性
     *
     * @return 工单扩展属性列信息
     */
    @EnableFilter
    @GetMapping(value = "/getWorkOrderColumn")
    @ApiOperation(value = "获取工单扩展属性")
    public List<ExtendColumnInfo> getWorkOrderColumn() {
        return workOrderExtend.getExtendColumnInfo();
    }


    /**
     * 根据ID列表查询工单信息列表
     *
     * @param idIn 工单ID列表
     * @return 工单列表信息
     */
    @EnableFilter
    @GetMapping(value = "/getHvPmWorkOrderListByIdIn/{idIn}")
    @ApiOperation(value = "根据ID列表查询工单信息列表")
    public List<WorkOrderDTO> getHvPmWorkOrderListByIdIn(@PathVariable List<Integer> idIn) {
        return workOrderService.getHvPmWorkOrderListByIdIn(idIn);
    }


    /**
     * 根据产线ID查询工单
     *
     * @param id 产线ID
     * @return 工单列表
     */
    @GetMapping(value = "/getWorkOrderByCellId/{id}")
    @ApiOperation(value = "根据产线ID查询工单")
    public List<WorkOrderDTO> getWorkOrderByCellId(@PathVariable Integer id) {
        return workOrderService.getWorkOrderByCellId(id);
    }

    /**
     * 根据工单状态查询工单
     *
     * @param state 工单状态
     * @return 工单列表
     */
    @EnableFilter
    @GetMapping(value = "/getAllByWorkOrderState/{state}")
    @ApiOperation(value = "根据工单状态查询工单")
    public List<WorkOrderDTO> getAllByWorkOrderState(@PathVariable Integer state) {
        return workOrderService.getAllByWorkOrderState(state);
    }

    /**
     * 工单信息分页查询
     *
     * @param workOrderQueryDTO 分页查询条件Dto
     * @return 分页信息
     */
    @EnableFilter
    @ApiOperation(value = "工单信息分页查询")
    @RequestMapping(value = "/findAllByPlanStartTimeAndMaterialCodeAndWorkOrderCode", method = RequestMethod.POST)
    public Page<WorkOrderDTO> findAllByPlanStartTimeAndPlanCodeAndMaterialCode(@RequestBody WorkOrderQueryDTO workOrderQueryDTO) {
        return workOrderService.findAllByPlanStartTimeAndMaterialCodeAndWorkOrderCode(workOrderQueryDTO);
    }

    /**
     * 查看BOM信息
     *
     * @param orderManageId 工单ID
     * @return bomAllDto
     */
    @EnableFilter
    @ApiOperation(value = "查看BOM")
    @GetMapping(value = "/getBomAllDtoByOrderManageId/{orderManageId}")
    public BomAllDTO getBomAllDtoByOrderManageId(@PathVariable Integer orderManageId) {
        return workOrderService.getBomAllDtoByOrderManageId(orderManageId);
    }

    /**
     * 工单复制
     *
     * @param id            工单ID
     * @param workOrderCode 工单编码
     * @return 复制得到新工单的ID
     */
    @ApiOperation(value = "工单复制")
    @PostMapping(value = "/copyWorkOrder/{id}/{workOrderCode}")
    public int copyWorkOrder(@PathVariable int id, @PathVariable String workOrderCode) {
        return workOrderService.copyWorkOrder(id, workOrderCode);
    }


    /**
     * 工单下发
     *
     * @param orderId  工单ID
     * @param nodeCode 工艺步骤ID
     */
    @ApiOperation(value = "工单下发")
    @RequestMapping(value = {"/workOrderIssued/{orderId}/{nodeCode}", "/workOrderIssued/{orderId}"}, method =
            RequestMethod.POST)
    public void workOrderIssued(@PathVariable(required = true) int orderId,
                                @PathVariable(required = false) String nodeCode,
                                @RequestBody(required = false) Map<String, Object> map) {
        workOrderService.workOrderIssued(orderId, nodeCode, map);
    }

    /**
     * 根据工单ID查询第一步开 始的工艺步骤
     *
     * @param orderId 工单ID
     * @return 工艺步骤列表
     */
    @GetMapping(value = "/getRoutStepByOrderId/{orderId}")
    @ApiOperation(value = "/根据工单ID查询第一步开始的工艺步骤")
    public Map<String, Object> getRoutStepByOrderId(@PathVariable int orderId) {
        return workOrderService.getRoutStepByOrderId(orderId);
    }

    /**
     * 批量下发
     *
     * @param issuedDTO Id列表
     * @return 下发反馈信息
     */
    @Deprecated
    @ApiOperation(value = "工单批量下发")
    @PostMapping(value = "/workOrderIssuedByIdList")
    public Map<Integer, String> workOrderIssuedByIdList(@RequestBody IssuedDTO issuedDTO) {
        return workOrderService.workOrderIssuedByIdList(issuedDTO.getOrderId(), issuedDTO.getMap());
    }


    /**
     * 批量下发
     *
     * @param workIssuedDTO Id列表
     */
    @ApiOperation(value = "批量下发")
    @PostMapping(value = "/batchIssued")
    public void batchIssued(@RequestBody WorkIssuedDTO workIssuedDTO) {
        workOrderService.batchIssued(workIssuedDTO);
    }

    /**
     * 撤销工单
     *
     * @param id 工单id
     */
    @DeleteMapping(value = "/cancelOrderById/{id}")
    @ApiOperation(value = "根据工单id撤销已下发工单")
    public void cancelOrderById(@PathVariable int id) {
        workOrderService.cancelOrderById(id);
    }

    /**
     * 批量撤销工单
     *
     * @param idList 工单id
     */
    @DeleteMapping(value = "/cancelOrderByIdList")
    @ApiOperation(value = "批量撤销工单")
    public void cancelOrderByIdList(@RequestBody List<Integer> idList) {
        workOrderService.cancelOrderByIdList(idList);
    }


    /**
     * 工单报废
     *
     * @param id 工单ID
     */
    @PutMapping(value = "/scarpWorkOrder/{id}")
    @ApiOperation(value = "工单报废")
    public void scarpWorkOrder(@PathVariable int id) {
        workOrderService.scarpWorkOrder(id);
    }

    /**
     * 工单批量报废
     *
     * @param idList id列表
     */
    @ApiOperation(value = "工单批量报废")
    @PutMapping(value = "/scarpWorkOrderByIdList")
    public void scarpWorkOrderByIdList(@RequestBody List<Integer> idList) {
        workOrderService.scarpWorkOrderByIdList(idList);
    }


    /**
     * 工单报工
     *
     * @param id 工单ID
     */
    @PutMapping(value = "/finishWorkOrder/{id}")
    @ApiOperation(value = "工单报工")
    public void finishWorkOrder(@PathVariable int id) {
        workOrderService.finishWorkOrder(id);
    }


    /**
     * 工单批量报工
     *
     * @param idList id列表
     */
    @PutMapping(value = "/finishWorkOrderAll")
    @ApiOperation(value = "工单批量报工")
    public void finishWorkOrderAll(@RequestBody List<Integer> idList) {
        workOrderService.finishWorkOrderAll(idList);
    }

    /**
     * 获取工单状态
     *
     * @return 工单状态键值对
     */
    @GetMapping("/getOrderState")
    @ApiOperation(value = "获取工单状态")
    public Map<Integer, String> getOrderState() {
        return EnumUtil.enumToMap(WorkOrderStateEnum.class);
    }

    /**
     * 获取今日工单进度
     *
     * @return 今日工单总数
     * @throws ParseException 解析异常
     */
    @EnableFilter
    @GetMapping(value = "/workOrderToday")
    @ApiOperation(value = "获取今日工单进度数量")
    public WorkOrderNumDTO workOrderToday(@RequestParam(required = false) String orderTypeCode,@RequestParam(required = false) Integer usedType) throws ParseException {
        return workOrderService.workOrderNum(orderTypeCode,usedType);
    }


    /**
     * 根据工单ID查询工单下工序的产出和投入料
     *
     * @param workOrderId 工单ID
     * @return 工单工序下投入和产出料列表
     */
    @GetMapping(value = "/getOperationMaterialByWorkOrderId/{workOrderId}")
    @ApiOperation(value = "根据工单ID查询工单下工序的产出和投入料")
    public List<OperationMaterialsDTO> getOperationMaterialByWorkOrderId(@PathVariable int workOrderId) {
        return workOrderService.getOperationMaterialByWorkOrderId(workOrderId);
    }

    /**
     * 工单正向追溯
     *
     * @param workOrderId 工单id
     * @return 工单所有信息
     */
    @GetMapping(value = "/getWorkAllDTO/{workOrderId}")
    @ApiOperation(value = "工单正向追溯 获取工单及工序下所有信息")
    public WorkAllDTO getWorkAllDTO(@PathVariable int workOrderId) {
        return workOrderService.getWorkOrderAll(workOrderId);
    }

    /**
     * 根据工艺操作Id查询工单
     *
     * @param operationId 工艺操作ID
     * @return 工单列表
     */
    @GetMapping(value = "/findOrderByOperation/{operationId}")
    @ApiOperation(value = "根据工艺操作Id查询工单")
    public List<WorkOrderDTO> findOrderByOperation(@PathVariable int operationId) {
        return workOrderService.findOrderByOperation(operationId);
    }


    /**
     * 获取不同状态工单数量
     *
     * @return
     */
    @PostMapping(value = "/getOrderStateCount")
    @ApiOperation(value = "获取不同状态工单数量")
    public List<OrderStateCountDTO> getOrderStateCount(@RequestBody OrderQuery query) {
        return searchMapper.getOrderStateCount(query);
    }


    /**
     * 获取工单计划用料单信息
     *
     * @param workOrderId 工单Id
     * @return 工单用料单信息
     */
    @GetMapping(value = "/getWorkPlanQuantity/{workOrderId}")
    @ApiOperation(value = "获取工单计划用料单信息")
    public OrderBomDTO getWorkPlanQuantity(@PathVariable Integer workOrderId) {
        return workOrderService.getWorkPlanQuantity(workOrderId);
    }

    /**
     * 获取工单属性
     *
     * @return 工单属性列表
     */
    @GetMapping(value = "/getOrderParameters")
    @ApiOperation(value = "获取工单的属性")
    public Map<String, String> getOrderParameters() {
        return workOrderService.getOrderParameters();
    }

    /**
     * 下载模版
     *
     */
    @ApiOperation(value = "下载模版")
    @PostMapping(value = "/downloadTemplate")
    @ApiResultIgnore
    public ResultVO<ExcelExportDto> downloadTemplate() throws IOException, IllegalAccessException{
        List<WorkOrderTemplate> list = new ArrayList<>();
        return workOrderService.getImportTemplate();
    }

    /**
     * 导入
     *
     * @param file 文件
     */
    @PostMapping("/import")
    @ApiOperation("导入")
    public ResultVO importExample(MultipartFile file) {
        List<WorkOrderTemplate> data = EasyExcelUtil.getImport(file, WorkOrderTemplate.class);
        return workOrderService.saveBatch(data);

    }

    /**
     * 导出组立工单
     * @param query
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    @ApiResultIgnore
    @PostMapping(value = "/exportZLWorkOrder")
    @ApiOperation(value = "导出组立工单")
    public ResultVO<ExcelExportDto> exportZLWorkOrder(@RequestBody OrderQuery query) throws IOException, IllegalAccessException {
        return workOrderService.exportZLWorkOrder(query);
    }


    /**
     * 导出板材工单
     * @param query
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    @ApiResultIgnore
    @PostMapping(value = "/exportBCWorkOrder")
    @ApiOperation(value = "导出板材工单")
    public ResultVO<ExcelExportDto> exportBCWorkOrder(@RequestBody OrderQuery query) throws IOException, IllegalAccessException {
        return workOrderService.exportBCWorkOrder(query);
    }

    /**
     * 导出型材工单
     * @param query
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    @ApiResultIgnore
    @PostMapping(value = "/exportXCWorkOrder")
    @ApiOperation(value = "导出型材工单")
    public ResultVO<ExcelExportDto> exportXCWorkOrder(@RequestBody OrderQuery query) throws IOException, IllegalAccessException {
        return workOrderService.exportXCWorkOrder(query);
    }



    /**
     * 根据工单号查询工单信息
     *
     * @param workOrderCode 工单编号
     */
    @PostMapping("/findByWorkOrderCode")
    @ApiOperation("根据工单号查询工单信息")
    public WorkOrderDTO findByWorkOrderCode(@RequestParam String workOrderCode) {
        return workOrderService.findByWorkOrderCode(workOrderCode);
    }


    /**
     * 根据工单号检测工单绑定信息
     *
     * @param workOrderCodes 工单编号
     */
    @PostMapping("/checkWorkOrderCodeBindRouteUnique")
    @ApiOperation("根据工单号检测工单绑定信息")
    public WorkOrderDTO checkWorkOrderCodeBindRouteUnique(@RequestBody ArrayList<String> workOrderCodes){
        return workOrderService.checkWorkOrderCodeBindRouteUnique(workOrderCodes);
    }

    /**
     * 根据批次号和物料分组下发工单
     * @param workOrderDTO
     */
    @PostMapping("/sendWorkOrder")
    @ApiOperation("根据批次号和物料分组下发工单")
    public void sendWorkOrder(@RequestBody SendWorkerOrderDTO workOrderDTO){
        workOrderService.sendWorkOrder(workOrderDTO);
    }

    /**
     * 根据物料分组ID查询下属物料关联的未下发工单的所有批次号(计划编号)
     *
     * @param materialGroupId 物料分组ID
     */
    @GetMapping("/getBatchNoListByMaterialGroupId/{materialGroupId}")
    @ApiOperation("根据物料分组ID查询下属物料关联的未下发工单的所有批次号(计划编号)")
    public List<String> getBatchNoListByMaterialGroupId(@PathVariable Integer materialGroupId){
        return workOrderService.getBatchNoListByMaterialGroupId(materialGroupId);
    }

    /**
     * 根据批次号(计划编号)查询关联的工单数量
     *
     * @param batchNo 批次号(计划编号)
     */
    @GetMapping("/getWorkOrderCountByBatchNo/{batchNo}")
    @ApiOperation("根据批次号(计划编号)查询关联的工单数量")
    public Integer getWorkOrderCountByBatchNo(@PathVariable String batchNo){
        return workOrderService.getWorkOrderCountByBatchNo(batchNo);
    }

    /**
     * 根据mes下发的工单创建零件或组立工单
     *
     * @param orderDTO mes下发的工单信息
     */
    @PostMapping("/saveWorkOrder")
    @ApiOperation("根据mes下发的工单创建零件或组立工单")
    public void saveWorkOrder(@RequestBody List<MesOrderDTO> orderDTO) {
         workOrderService.saveWorkOrder(orderDTO);
    }


    /**
     * 根据工单号查询报工主数据
     *
     * @param
     * @return
     */
    @GetMapping("/getWorkOrderCompleteByOrderCode/{orderCode}")
    @ApiOperation("查询手动报工主数据")
    public HvPmWorkOrderCompleteDTO getWorkOrderCompleteByOrderCode(@PathVariable String orderCode) {
        return workOrderCompleteService.getWorkOrderCompleteByOrderCode(orderCode);
    }

    /**
     * 新增工单手动报工主数据
     *
     * @param workOrderCompleteDTO
     */
    @PostMapping("/addWorkOrderComplete")
    @ApiOperation("新增工单手动报工主数据")
    public long addWorkOrderComplete(@RequestBody HvPmWorkOrderCompleteDTO workOrderCompleteDTO ) {
        return  workOrderCompleteService.addWorkOrderComplete(workOrderCompleteDTO);
    }


    @ApiOperation("删除手动报工物料")
    @DeleteMapping("/deleteWorkOrderCompleteByOrderCode/{orderCode}")
    public void deleteWorkOrderCompleteByOrderCode(@PathVariable String orderCode){
        workOrderCompleteService.deleteWorkOrderCompleteByOrderCode(orderCode);
    }

    /**
     * 新增工单手动报工零件数据
     *
     * @param workOrderCompleteMaterialDTO
     */
    @PostMapping("/addWorkOrderCompleteMaterial")
    @ApiOperation("新增手动报工物料数据")
    public long addWorkOrderCompleteMaterial(@RequestBody HvPmWorkOrderCompleteMaterialDTO workOrderCompleteMaterialDTO ) {
        return  workOrderCompleteMaterialService.addWorkOrderCompleteMaterial(workOrderCompleteMaterialDTO);
    }



    /**
     * 根据工单号查询报工主数据
     *
     * @param
     * @return
     */
    @PostMapping("/getAllCompleteMaterial")
    @ApiOperation("查询手动报工物料数据")
    public Page<HvPmWorkOrderCompleteMaterialDTO> getAllCompleteMaterial(@RequestBody WorkOrderCompleteMaterialQueryDTO
                                                       workOrderCompleteMaterialQueryDTO) {
        return workOrderCompleteMaterialService.getAllCompleteMaterial(workOrderCompleteMaterialQueryDTO);
    }

    @ApiOperation("删除手动报工物料")
    @DeleteMapping("/deleteCompleteMaterialById/{id}")
    public void deleteCompleteMaterialById(@PathVariable long id){
        workOrderCompleteMaterialService.deleteCompleteMaterialById(id);
    }

    /**
     * 新增工单手动报工零件数据
     *
     * @param workOrderCompleteMaterialDTO
     */
    @PostMapping("/upWorkOrderCompleteMaterial")
    @ApiOperation("修改手动报工零件数据")
    public long upWorkOrderCompleteMaterial(@RequestBody HvPmWorkOrderCompleteMaterialDTO workOrderCompleteMaterialDTO ) {
        return  workOrderCompleteMaterialService.upWorkOrderCompleteMaterial(workOrderCompleteMaterialDTO);
    }



    /**
     * 手动报工给MES
     *
     * @param orderCode
     */
    @PostMapping("/handReportWorkToMES/{orderCode}")
    @ApiOperation("手动报工给MES")
    public void handReportWorkToMES(@PathVariable String orderCode ) {
          workOrderCompleteService.handReportWorkToMES(orderCode);
    }


}








