package com.hvisions.pms.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.hvisions.common.dto.ExtendColumnInfo;
import com.hvisions.common.service.BaseExtendService;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.hiperbase.bom.dto.BomAllDTO;
import com.hvisions.hiperbase.client.BomClient;
import com.hvisions.hiperbase.client.LocationExtendClient;
import com.hvisions.hiperbase.client.RouteClient;
import com.hvisions.hiperbase.equipment.location.LocationDTO;
import com.hvisions.pms.dao.OrderMapper;
import com.hvisions.pms.dao.SearchMapper;
import com.hvisions.pms.dto.TypeExtendRelationDTO;
import com.hvisions.pms.dto.WorkOrderDTO;
import com.hvisions.pms.entity.HvPmTypeExtendRelation;
import com.hvisions.pms.materialdto.MaterialTraceDTO;
import com.hvisions.pms.materialdto.TraceQuery;
import com.hvisions.pms.query.OrderQuery;
import com.hvisions.pms.repository.TypeExtendRepository;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: SearchController</p>
 * <p>Description: 查询控制器</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/10/23</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/search")
@Api(description = "查询控制器")
public class SearchController {
    @Autowired
    OrderMapper orderMapper;
    @Autowired
    SearchMapper searchMapper;
    @Resource(name = "hv_pm_work_order_extend")
    BaseExtendService workOrderExtend;

    @Autowired
    TypeExtendRepository typeExtendRepository;
    @Autowired
    private RouteClient routeClient;
    @Autowired
    private LocationExtendClient locationExtendClient;
    @Autowired
    private BomClient bomClient;

    /**
     * 获取工单列表
     *
     * @param query 查询条件
     * @return 工单信息
     */
    @PostMapping("/getOrder")
    @ApiOperation(value = "获取工单列表")
    public Page<WorkOrderDTO> getOrder(@RequestBody OrderQuery query) {
        if (query !=null && query.getPlanStartTime() !=null && !query.getPlanStartTime().isEmpty() ){
            checkIso8601Time(query.getPlanStartTime(),query);
        }

        Page<Map<String, Object>> page = PageHelperUtil.getPage(orderMapper::getOrder, query);
        List<WorkOrderDTO> orderList = parseOrder(page.getContent());
        Page<WorkOrderDTO> workOrderDTOS = new PageImpl(orderList, query.getRequest(), page.getTotalElements());
        for (WorkOrderDTO workOrderDTO : workOrderDTOS) {
            if (workOrderDTO.getOrderTypeId() != null) {
                Map<String, Object> extend = workOrderExtend.getExtend(workOrderDTO.getId());
                List<HvPmTypeExtendRelation> hvPmTypeExtendRelations =
                        typeExtendRepository.getAllByWorkOrderTypeId(workOrderDTO.getOrderTypeId());
                Map<String, Object> map = new HashMap<>();
                if (hvPmTypeExtendRelations != null) {
                    for (HvPmTypeExtendRelation hvPmTypeExtendRelation : hvPmTypeExtendRelations) {
                        if (hvPmTypeExtendRelation.getExtendCode() != null) {
                            if (extend != null) {
                                map.put(hvPmTypeExtendRelation.getExtendCode(),
                                        extend.get(hvPmTypeExtendRelation.getExtendCode()));
                            } else {
                                map.put(hvPmTypeExtendRelation.getExtendCode(), null);
                            }
                        }
                    }
                }
                workOrderDTO.setTypeExtend(map);
                parseFactoryBomDetailInfo(workOrderDTO);
                workOrderDTO.setRelationDTOS(DtoMapper.convertList(hvPmTypeExtendRelations, TypeExtendRelationDTO.class));
            }
        }

        return workOrderDTOS;
    }

    //Iso8601时间格式转换
    public void checkIso8601Time(List<String> timeList,OrderQuery query) {

        if (timeList.get(0)!=null && ! timeList.get(0).isEmpty() && timeList.get(0).contains("Z")) {
            // 解析ISO 8601格式的时间字符串
            Instant instant = Instant.parse(timeList.get(0));
            // 将Instant对象转换为LocalDateTime，这里使用UTC时区
            // 如果需要考虑时区转换，可以使用ZoneId.of("时区名称")，例如ZoneId.of("Asia/Shanghai")
            java.time.LocalDateTime localDateTime = instant.atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime();
            // 定义日期时间格式器
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            // 格式化日期时间
            String formattedDateTime = localDateTime.format(formatter);

            query.setTimeAreaStart(formattedDateTime);
        }
        if (timeList.get(1)!=null && ! timeList.get(1).isEmpty() && timeList.get(1).contains("Z")) {
            // 解析ISO 8601格式的时间字符串
            Instant instant = Instant.parse(timeList.get(1));
            // 将Instant对象转换为LocalDateTime，这里使用UTC时区
            // 如果需要考虑时区转换，可以使用ZoneId.of("时区名称")，例如ZoneId.of("Asia/Shanghai")
            java.time.LocalDateTime localDateTime = instant.atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime();
            // 定义日期时间格式器
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            // 格式化日期时间
            String formattedDateTime = localDateTime.format(formatter);
            query.setTimeAreaEnd(formattedDateTime);
        }
    }

    /**
     * 获取工厂、工艺路线详细信息
     *
     * @param workOrderDTO
     * @return
     */
    private void parseFactoryBomDetailInfo(WorkOrderDTO workOrderDTO) {
        if(workOrderDTO.getAreaId() !=null) {
            //获取当前工厂
            LocationDTO locationDTO = locationExtendClient.getLocationById(workOrderDTO.getAreaId()).getData();
            if (locationDTO != null) {
                workOrderDTO.setAreaName(locationDTO.getName());
            }
        }
        /*RouteDTO routeDTO = routeClient.getRoute(workOrderDTO.getRouteId()).getData();
        workOrderDTO.setRouteVersion(routeDTO.getRouteVersion());
        workOrderDTO.setRouteName(routeDTO.getRouteName());*/
        if(workOrderDTO.getBomId() !=null) {
            BomAllDTO bomDto = bomClient.getBomBomItemSubstituteItemByBomId(workOrderDTO.getBomId()).getData();
            if (bomDto != null) {
                workOrderDTO.setBomCode(bomDto.getBomCode());
                workOrderDTO.setBomVersion(bomDto.getBomVersions());
                workOrderDTO.setBomName(bomDto.getBomName());
            }
        }
        //return workOrderDTO;
    }


    /**
     * 转换工单
     *
     * @param content map列表
     * @return 工单列表
     */
    private List<WorkOrderDTO> parseOrder(List<Map<String, Object>> content) {
        if (content != null && content.size() > 0) {
            List<WorkOrderDTO> result = new ArrayList<>();
            //查询现在工单的扩展属性
            List<ExtendColumnInfo> extendColumnInfo = workOrderExtend.getExtendColumnInfo();
            for (Map<String, Object> map : content) {
                WorkOrderDTO order = BeanUtil.mapToBean(map, WorkOrderDTO.class, CopyOptions.create());
                //语句中id重复。要用单独的列，没办法通过mapToBean设置
                order.setId((Integer) map.get("orderId"));
                //设置扩展属性
                order.setExtend(new HashMap<>(extendColumnInfo.size()));
                //添加扩展属性值
                for (ExtendColumnInfo columnInfo : extendColumnInfo) {
                    order.getExtend().put(columnInfo.getColumnName(), map.get(columnInfo.getColumnName()));
                }
                result.add(order);
            }
            return result;
        } else {
            return new ArrayList<>();
        }
    }

    @PostMapping(value = "/getWorkOrderTrace")
    @ApiOperation(value = "反向追溯")
    public Page<MaterialTraceDTO> getWorkOrderTrace(@RequestBody TraceQuery traceQuery) {
        return PageHelperUtil.getPage(searchMapper::getTraceByBatchNum, traceQuery);
    }
}









