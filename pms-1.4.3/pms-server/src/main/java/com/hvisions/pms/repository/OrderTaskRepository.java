package com.hvisions.pms.repository;

import com.hvisions.pms.entity.HvPmOrderTask;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <p>Title: OrderTaskRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/3/23</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Repository
public interface OrderTaskRepository extends JpaRepository<HvPmOrderTask, Integer> {


    /**
     * 根据工单id查询生产任务
     *
     * @param orderId 工单Id
     * @return 生产任务信息
     */
    List<HvPmOrderTask> getAllByOrderId(int orderId);


    /**
     * 根据工位id查询生产任务
     *
     * @param workCenterId 工位Id
     * @return 生产
     */
    List<HvPmOrderTask> getAllByWorkCenterId(int workCenterId);


    /**
     * '
     * 根据ID列表查询生产renew
     *
     * @param idList id列表¬
     * @return 生产任务
     */
    List<HvPmOrderTask> getAllByIdIn(List<Integer> idList);


    /**
     * 根据加工中心和状态查询任务数量
     * @param workCenterId 加工中心id
     * @param states 状态列表
     * @return 任务信息
     */
    List<HvPmOrderTask> getAllByWorkCenterIdInAndStateIn(List<Integer> workCenterId,List<Integer> states);



    /**
     * 根据工位ID 工序状态获取生产任务数量
     *
     * @param workState   工单状态
     * @param equipmentId 工位ID
     * @return 生产任务数量
     */
    @Query(value = "select count(w.state) from HvPmOrderTask w where w.state = ?1 and w.workCenterId =?2 ")
    int getWorkOrderNumByState(int workState, int equipmentId);

    /**
     * 获取实际完成时间在今天的工序
     *
     * @param todayTime    当天0点
     * @param endTime      第二天0点
     * @param workCenterId ID
     * @return 工序信息列表
     */
    @Query(value = "select w from HvPmOrderTask  w WHERE w.endTime >= ?1 and w.endTime <= ?2 and w.workCenterId =" +
            " ?3")
    List<HvPmOrderTask> getTaskByActualEndTime(Date todayTime, Date endTime, int workCenterId);

    //根据工单号删除
    @Modifying
    @Query("DELETE FROM HvPmOrderTask o WHERE o.workOrderCode = :workOrderCode")
    int deleteByWorkOrderCode(@Param("workOrderCode") String workOrderCode);
}