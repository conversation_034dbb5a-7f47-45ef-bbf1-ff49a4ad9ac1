package com.hvisions.pms.service;

import com.hvisions.hiperbase.materials.dto.MaterialDTO;
import com.hvisions.pms.dto.OperationOutPutMaterialDTO;
import com.hvisions.pms.dto.OutPutIDListDTO;

import java.util.List;

/**
 * <p>Title: OperationOutPutMaterialService</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/14</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface OperationOutPutMaterialService {


    /**
     * 添加产出物料
     *
     * @param operationOutPutMaterialDTOS 产出物料DTO列表
     */
    void createOutMaterial(List<OperationOutPutMaterialDTO> operationOutPutMaterialDTOS);


    /**
     * 根据工序ID查询产出物料
     *
     * @param operationId 工序ID
     * @return 产出物料列表
     */
    List<OperationOutPutMaterialDTO> getAllByOperationId(int operationId);


    /**
     * 工序产出物料删除
     *
     * @param outPutIDListDTO id列表
     */
    void deleteByIdList(OutPutIDListDTO outPutIDListDTO);
    /**
     * 获取可以产出的物料信息列表
     *
     * @param taskId 操作id
     * @return 物料列表
     */
    List<MaterialDTO> getOutMaterial(Integer taskId);
}
