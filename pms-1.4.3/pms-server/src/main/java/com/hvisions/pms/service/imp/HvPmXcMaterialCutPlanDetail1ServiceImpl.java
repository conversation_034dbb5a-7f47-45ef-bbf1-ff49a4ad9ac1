package com.hvisions.pms.service.imp;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.pms.entity.plan.HvPmXcMaterialCutPlanDetail1;
import com.hvisions.pms.plan.HvPmXcMaterialCutPlanDetail1DTO;
import com.hvisions.pms.repository.plan.HvPmXcMaterialCutPlanDetail1Repository;
import com.hvisions.pms.service.HvPmXcMaterialCutPlanDetail1Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/8/26
 */
@Service
@Slf4j
public class HvPmXcMaterialCutPlanDetail1ServiceImpl implements HvPmXcMaterialCutPlanDetail1Service {

    @Resource
    private HvPmXcMaterialCutPlanDetail1Repository hvPmXcMaterialCutPlanDetail1Repository;

    @Override
    public long createDetail1(HvPmXcMaterialCutPlanDetail1DTO hvPmXcMaterialCutPlanDetail1DTO) {
        HvPmXcMaterialCutPlanDetail1 hvPmXcMaterialCutPlanDetail1= DtoMapper.convert(hvPmXcMaterialCutPlanDetail1DTO, HvPmXcMaterialCutPlanDetail1.class);
        HvPmXcMaterialCutPlanDetail1 detail1 = hvPmXcMaterialCutPlanDetail1Repository.saveAndFlush(hvPmXcMaterialCutPlanDetail1);
        return detail1.getId();
    }

    @Override
    public List<HvPmXcMaterialCutPlanDetail1> getDetail1List(long subPlanId) {
        return hvPmXcMaterialCutPlanDetail1Repository.getAllBySubPlanId(subPlanId);
    }

    @Override
    public long updateDetail1(HvPmXcMaterialCutPlanDetail1DTO hvPmXcMaterialCutPlanDetail1DTO) {
        HvPmXcMaterialCutPlanDetail1 hvPmXcMaterialCutPlanDetail1= DtoMapper.convert(hvPmXcMaterialCutPlanDetail1DTO, HvPmXcMaterialCutPlanDetail1.class);
        HvPmXcMaterialCutPlanDetail1 detail1 = hvPmXcMaterialCutPlanDetail1Repository.save(hvPmXcMaterialCutPlanDetail1);
        return detail1.getId();
    }

    @Override
    public void deleteDetail1ById(long id) {
        hvPmXcMaterialCutPlanDetail1Repository.deleteById(id);
    }

    @Override
    public List<HvPmXcMaterialCutPlanDetail1> getDetail1ListByOrderIds(List<Long> orderIds) {
        return hvPmXcMaterialCutPlanDetail1Repository.findByOrderIds(orderIds);
    }

}
