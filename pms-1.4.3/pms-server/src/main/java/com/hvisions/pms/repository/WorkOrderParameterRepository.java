package com.hvisions.pms.repository;

import com.hvisions.pms.entity.HvPmWorkOrderParameter;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: WorkOrderParameterRepository</p >
 * <p>Description: 工单参数仓储层 </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/3/8</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Repository
public interface WorkOrderParameterRepository extends JpaRepository<HvPmWorkOrderParameter, Integer> {


    /**
     * 根据工单ID 参数编码查询 参数值
     *
     * @param parameterCode 参数编码
     * @param orderId       工单ID
     * @return 参数值
     */
    HvPmWorkOrderParameter getByParameterCodeAndOrderId(String parameterCode, Integer orderId);

    /**
     * 获取工单参数
     *
     * @param orderId 工单Id
     * @return 工单参数列表
     */
    List<HvPmWorkOrderParameter> getByOrderId(int orderId);

}