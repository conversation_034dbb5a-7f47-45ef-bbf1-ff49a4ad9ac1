package com.hvisions.pms.repository;

import com.hvisions.pms.entity.HvPmOperationMaterial;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <p>Title: OperationMaterialRepository</p>
 * <p>Description: 工序和物料关系仓储层</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/1/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface OperationMaterialRepository extends JpaRepository<HvPmOperationMaterial, Integer> {
    /**
     * 根据工序ID查找工序 物料关系
     *
     * @param operationId 工序ID
     * @return 工序物料关系
     */
    List<HvPmOperationMaterial> findAllByOperationId(int operationId);

    /**
     * 根据ID列表删除工序物料关系
     *
     * @param idIn id列表
     */
    void deleteAllByIdIn(List<Integer> idIn);

    /**
     * 根据工序ID和物料ID查询投入料
     *
     * @param materialId  物料ID
     * @param operationId 工序ID
     * @return 投入料列表
     */
    List<HvPmOperationMaterial> findAllByMaterialIdAndOperationId(int materialId, int operationId);

    /**
     * 根据录入时间查询投入料
     *
     * @param newDate  开始时间
     * @param handTime 交班时间
     * @return 投入料列表
     */
    @Query(value = "select h from HvPmOperationMaterial h where h.createTime <= ?1 and h.createTime >= ?2 ")
    List<HvPmOperationMaterial> getAllByDate(Date newDate, Date handTime);
}









