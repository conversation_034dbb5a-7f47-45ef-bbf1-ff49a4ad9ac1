package com.hvisions.pms.service;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.pms.entity.plan.HvPmXcMaterialOutStockPlan;
import com.hvisions.pms.plan.XcMaterialOutStockPlanDTO;
import com.hvisions.pms.plan.XcMaterialOutStockPlanQueryDTO;
import com.hvisions.thirdparty.common.dto.MaterialCuttingLineOnLineDTO;
import com.hvisions.thirdparty.common.dto.MaterialRequestsDTO;
import com.hvisions.thirdparty.common.dto.ProfileOutboundInfoDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/5/7
 */
public interface XcMaterialOutStockPlanService {

    /**
     * 分页条件查询
     * @param xcMaterialOutStockPlanQueryDTO
     * @return
     */
    Page<XcMaterialOutStockPlanDTO> getPage(XcMaterialOutStockPlanQueryDTO xcMaterialOutStockPlanQueryDTO);

    /**
     * 根据工单号查询出库任务列表
     *
     * @param workOrderCode
     * @return
     */
    List<HvPmXcMaterialOutStockPlan> getByWorkOrderCode(String workOrderCode);


    /**
     * 添加型材出库计划
     * @param xcMaterialOutStockPlanDTO
     * @return
     */
    int createOutStockPlan (XcMaterialOutStockPlanDTO xcMaterialOutStockPlanDTO);

    /**
     * 修改型材出库计划
     * @param xcMaterialOutStockPlanDTO
     * @return
     */
    int updateOutStockPlan (XcMaterialOutStockPlanDTO xcMaterialOutStockPlanDTO);

    /**
     * 删除型材出库计划
     * @param id
     */
    void deleteOutStockPlanById(int id,String taskNo);

    /**
     * 是否存在型材出库计划
     * @param taskNo
     * @return
     */
    boolean isExistsOutStockPlan(String taskNo);

    /**
     * 获取模板
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    ResultVO<ExcelExportDto> getImportTemplate() throws IOException, IllegalAccessException;

    /**
     * 导入型材出库计划
     * @param file
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    ImportResult importPlan(MultipartFile file) throws IOException, IllegalAccessException;

    ResultVO<ExcelExportDto> exportOutStockPlan(XcMaterialOutStockPlanQueryDTO xcMaterialOutStockPlanQueryDTO) throws IOException, IllegalAccessException;

    int createOutStockPlanByRequests(MaterialRequestsDTO materialRequestsDTO);

    MaterialCuttingLineOnLineDTO getCuttingLineOneLineInfo(ProfileOutboundInfoDTO profileOutboundInfoDTO);

    void sendCuttingLineOneLineInfo(ProfileOutboundInfoDTO profileOutboundInfoDTO );

    List<XcMaterialOutStockPlanDTO> getListByWorkOrderCode(String workOrderCode);

    ResultVO<?> handSendOrder(XcMaterialOutStockPlanDTO xcMaterialOutStockPlanDTO, UserInfoDTO userInfo);


}
