package com.hvisions.pms.controller;

import com.hvisions.common.annotation.EnableFilter;
import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.pms.dto.*;
import com.hvisions.pms.service.OrderOperationService;
import com.hvisions.pms.service.OrderTaskService;
import com.hvisions.pms.task.dto.OrderTaskDTO;
import com.hvisions.pms.util.LockingTask;
import com.hvisions.pms.viewdto.OrderOperationNumDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: OrderOperationController</p>
 * <p>Description: 工单工序控制器</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/1/17</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@Api(description = "工单工序控制器")
@RequestMapping(value = "/operation")
public class OrderOperationController {
    private final OrderOperationService orderOperationService;
    private final OrderTaskService orderTaskService;

    @Autowired
    public OrderOperationController(OrderOperationService orderOperationService, OrderTaskService orderTaskService) {
        this.orderOperationService = orderOperationService;
        this.orderTaskService = orderTaskService;
    }


    /**
     * 工序开始
     *
     * @param operationOperatorDTO 工序传入信息
     */
    @LockingTask
    @PutMapping(value = "/startOperation")
    @ApiOperation(value = "工序开始")

    public void startOperation(@RequestBody OperationOperatorDTO operationOperatorDTO, @ApiIgnore @UserInfo UserInfoDTO userInfo) {
        orderOperationService.startOperation(operationOperatorDTO, userInfo);
    }


    /**
     * 工序批量开始
     *
     * @param operationOperatorDTOS 工序操作传输对象列表
     */
    @PutMapping(value = "/startOperationBatch")
    @ApiOperation(value = "工序批量开始")

    public void startOperationBatch(@RequestBody List<OperationOperatorDTO> operationOperatorDTOS
            , @ApiIgnore UserInfoDTO userInfo) {
        orderOperationService.startOperationBatch(operationOperatorDTOS, userInfo);
    }


    /**
     * 工序中断
     *
     * @param operationOperatorDTO 工序传入对象
     */
    @PutMapping("/breakOperation")
    @ApiOperation(value = "工序中断")

    @LockingTask
    public void breakOperation(@RequestBody OperationOperatorDTO operationOperatorDTO) {
        orderOperationService.breakOperation(operationOperatorDTO);
    }


    /**
     * 工序批量中断
     *
     * @param operationOperatorDTOS 工序操作传输对象列表
     */
    @PutMapping(value = "/breakOperationBatch")
    @ApiOperation(value = "工序批量中断")
    public void breakOperationBatch(@RequestBody List<OperationOperatorDTO> operationOperatorDTOS) {
        orderOperationService.breakOperationBatch(operationOperatorDTOS);
    }

    /**
     * 工序中断继续
     *
     * @param operationOperatorDTO 工序传入对象
     */
    @PutMapping("/resumeOperation")
    @ApiOperation(value = "工序继续")
    public void resumeOperation(@RequestBody OperationOperatorDTO operationOperatorDTO) {
        orderOperationService.resumeOperation(operationOperatorDTO);
    }


    /**
     * 生产任务批量继续
     *
     * @param operationOperatorDTOS 工序操作传输对象列表
     */
    @ApiOperation(value = "工序批量继续")
    @PutMapping(value = "/resumeOperationBatch")
    public void resumeOperationBatch(@RequestBody List<OperationOperatorDTO> operationOperatorDTOS) {
        orderOperationService.resumeOperationBatch(operationOperatorDTOS);
    }

    /**
     * 生产任务结束
     *
     * @param operationOperatorDTO 工序传入对象
     * @return 结束信息
     */
    @PutMapping("/endOperation")
    @ApiOperation(value = "生产任务结束")
    public Map<Object, String> endOperation(@RequestBody OperationOperatorDTO operationOperatorDTO) {
        return orderOperationService.endOperation(operationOperatorDTO);
    }

    /**
     * 根据当前工序查询下一道工序
     *
     * @param id 工序ID
     * @return Map  工序列表
     */
    @GetMapping(value = "/getNextOperation/{id}")
    @ApiOperation(value = "根据当前工序查询下一道工序")
    public Map<String, Object> getNextOperation(@PathVariable int id) {
        return orderOperationService.getOrderOperationByOpeartionId(id);
    }

    /**
     * 生产任务批量结束
     *
     * @param operationOperatorDTOS 工序操作传输对象列表
     * @return 结束信息
     */
    @PutMapping(value = "/endOperationBatch")
    @ApiOperation(value = "生产任务批量结束")
    public List<Map<Object, String>> endOperationBatch(@RequestBody List<OperationOperatorDTO> operationOperatorDTOS) {
        return orderOperationService.endOperationBatch(operationOperatorDTOS);
    }

    /**
     * 生产任务终止
     *
     * @param operationOperatorDTO 工序传入对象
     */
    @ApiOperation(value = "生产任务终止")
    @PutMapping(value = "/cancelOperation")

    public void cancelOperation(@RequestBody OperationOperatorDTO operationOperatorDTO) {
        orderOperationService.cancelOperation(operationOperatorDTO);
    }

    /**
     * 生产任务批量终止
     *
     * @param operationOperatorDTOS 工序操作传输对象列表
     */
    @PutMapping(value = "/cancelOperationBatch")
    @ApiOperation(value = "生产任务批量终止")

    public void cancelOperationBatch(@RequestBody List<OperationOperatorDTO> operationOperatorDTOS) {
        orderOperationService.cancelOperationBatch(operationOperatorDTOS);
    }

    /***
     * 切换加工工位
     * @param operationId 工序ID
     * @param workCenterId  工位ID
     */
    @ApiOperation(value = "切换加工工位")
    @PutMapping(value = "/changeWorkCenter/{operationId}/{workCenterId}")
    public void changeWorkCenter(@PathVariable int operationId, @PathVariable int workCenterId) {
        orderOperationService.changeWorkCenter(operationId, workCenterId);
    }

    /***
     * 批量切换加工工位
     * @param  changeWorkCenterDto 工序ID列表和工位ID
     */
    @ApiOperation(value = "批量切换加工工位")
    @PutMapping(value = "/changeManyWorkCenter")
    public void changeManyWorkCenter(@RequestBody ChangeWorkCenterDTO changeWorkCenterDto) {
        orderOperationService.changeManyWorkCenter(changeWorkCenterDto);
    }

    /**
     * 根据工单id获取工序信息
     *
     * @param orderId 工单id
     * @return 工序信息列表
     */
    @ApiOperation(value = "根据工单id，获取工序信息")
    @GetMapping("/getOperationByOrderId/{orderId}")
    public List<OrderOperationDTO> getOperationByOrderId(@PathVariable int orderId) {
        return orderOperationService.getOperationByOrderId(orderId);
    }

    /**
     * 根据工序ID查询工序
     *
     * @param id     工序ID
     * @param taskId 任务Id
     * @return 工序信息
     */
    @GetMapping(value = "/getOrderOperationById/{id}/{taskId}")
    @ApiOperation(value = "根据工序ID查询工序")
    public OrderOperationDTO getOrderOperationById(@PathVariable int id, @PathVariable int taskId) {
        return orderOperationService.getOperationById(id, taskId);
    }

    /**
     * 根据工序ID列表查询工序列表
     *
     * @param idIn ID列表
     * @return 工序信息列表
     */
    @GetMapping(value = "/getOperationListByIdIn/{idIn}")
    @ApiOperation(value = "根据ID列表查询工序")
    public List<OrderOperationDTO> getOperationListByIdIn(@PathVariable List<Integer> idIn) {
        return orderOperationService.getOperationByIdIn(idIn);
    }


    /**
     * 根据工位，工序状态，查询所有的工序分页数据"
     *
     * @param operationQueryDTO 分页查询对象
     * @return 工序分页数据
     */
    @EnableFilter
    @ApiOperation(value = "根据工位，工序状态，查询所有的工序分页数据")
    @PostMapping("/getOperationPageByWorkCenterIdAndOperationState")
    public Page<OrderTaskDTO> getOperationPageByWorkCenterIdAndOperationState(@RequestBody OperationQueryDTO operationQueryDTO) {
        return orderTaskService.getTaskByQuery(operationQueryDTO);
    }

    /**
     * 根据工位ID查询工序
     *
     * @param workCenterId 工位ID
     * @return 工序列表信息
     */
    @GetMapping(value = "/getOrderOperationByWorkCenterId/{workCenterId}")
    @ApiOperation(value = "根据工位ID查询工序")
    public List<OrderOperationDTO> getOrderOperationByWorkCenterId(@PathVariable int workCenterId) {
        return orderOperationService.getOrderOperationByWorkCenterId(workCenterId);
    }

    /**
     * 根据任务idID查询生产任务执行记录
     *
     * @param taskId 生产任务ID
     * @return 工序操作记录
     */
    @GetMapping(value = "/getProcedureRecordByOperationId/{taskId}")
    @ApiOperation(value = "根据任务idID查询生产任务执行记录")
    public List<ProcedureRecordDTO> getProcedureRecordByOperationId(@PathVariable int taskId) {
        return orderOperationService.getProcedureRecordByOperationId(taskId);
    }

    /**
     * 根据工位ID统计工序数量
     *
     * @param workCenterId 工位ID
     * @return 工序进度列表
     * @throws ParseException 解析异常
     */
    @ApiOperation(value = "根据工位ID统计工序数量")
    @GetMapping(value = "/getWorkCountByState/{workCenterId}")
    public OrderOperationNumDTO getWorkCountByState(@PathVariable int workCenterId) throws ParseException {
        return orderOperationService.getOperationIdByWorkCenterId(workCenterId);
    }

    /**
     * 拆分工序
     *
     * @param operationId  工序Id
     * @param count        拆分数量
     * @param workCenterId 工位编码
     */
    @ApiOperation(value = "工序拆分")
    @PostMapping(value = "/splitOrderOperation")
    public void splitOrderOperation(@RequestParam int operationId, @RequestParam BigDecimal count,
                                    @RequestParam(required = false) Integer workCenterId) {
        orderOperationService.splitOrderOperation(operationId, count, workCenterId);
    }

    @ApiOperation("工序id和位置id查询设备采集参数")
    @PostMapping(value = "/getEquipCollectData")
    public OperationParamDto getEquipCollectData(@RequestBody RouteParamQuery query) {
        return orderOperationService.getEquipCollectData(query);
    }

    @ApiOperation("工序id和位置id查询设备下发参数")
    @PostMapping(value = "/getEquipSendData")
    public OperationParamDto getEquipSendData(@RequestBody RouteParamQuery query) {
        return orderOperationService.getEquipSendData(query);
    }

    @ApiOperation("记录设备采集参数")
    @PostMapping("/recordCollectParam")
    public void recordCollectParam(@RequestBody RecordParamDto recordParamDto) {
        orderOperationService.recordCollectParam(recordParamDto);
    }

    @ApiOperation("记录并下发参数")
    @PostMapping("/recordSendParam")
    public void recordSendParam(@RequestBody RecordParamDto recordParamDto) {
        orderOperationService.recordSendParam(recordParamDto);
    }

    @ApiOperation("根据记录历史头表id获取历史记录")
    @GetMapping("/findByRecordId/{recordId}")
    public ParamHistoryDataDto findByRecordId(@PathVariable Integer recordId) {
        return orderOperationService.findByRecordId(recordId);
    }


    @ApiOperation("测试自动采集和下发功能")
    @GetMapping("/test")
    public void test(@RequestParam Integer taskId,@RequestParam Integer event){
        orderOperationService.test(taskId,event);
    }
}








