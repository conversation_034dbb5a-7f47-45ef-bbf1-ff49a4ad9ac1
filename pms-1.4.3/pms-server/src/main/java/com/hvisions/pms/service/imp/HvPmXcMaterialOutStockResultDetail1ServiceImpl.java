package com.hvisions.pms.service.imp;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.pms.dao.HvPmXcMaterialOutStockResultMapper;
import com.hvisions.pms.dto.HvPmXcMaterialOutStockResultDetail1DTO;
import com.hvisions.pms.entity.HvPmXcMaterialOutStockResultDetail1;
import com.hvisions.pms.repository.HvPmXcMaterialOutStockResultDetail1Repository;
import com.hvisions.pms.repository.HvPmXcMaterialOutStockResultRepository;
import com.hvisions.pms.service.HvPmXcMaterialOutStockResultDetail1Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/5/8
 */
@Service
@Slf4j
public class HvPmXcMaterialOutStockResultDetail1ServiceImpl implements HvPmXcMaterialOutStockResultDetail1Service {

    @Autowired
    private HvPmXcMaterialOutStockResultDetail1Repository detail1Repository;

    @Autowired
    private HvPmXcMaterialOutStockResultRepository resultRepository;

    @Autowired
    private HvPmXcMaterialOutStockResultMapper resultMapper;

    @Override
    public List<HvPmXcMaterialOutStockResultDetail1DTO> getByTaskNo(String taskNo) {
        List<HvPmXcMaterialOutStockResultDetail1> detail1List = detail1Repository.getByTaskNo(taskNo);
        List<HvPmXcMaterialOutStockResultDetail1DTO> detail1DTOList = DtoMapper.convertList(detail1List,HvPmXcMaterialOutStockResultDetail1DTO.class);
        return detail1DTOList;
    }

    @Override
    public long createDetail1(HvPmXcMaterialOutStockResultDetail1DTO detail1DTO) {
        HvPmXcMaterialOutStockResultDetail1 detail1 = detail1Repository.saveAndFlush(DtoMapper.convert(detail1DTO, HvPmXcMaterialOutStockResultDetail1.class));
        return detail1.getId();
    }

    @Override
    public long updateDetail1(HvPmXcMaterialOutStockResultDetail1DTO detail1DTO) {
        HvPmXcMaterialOutStockResultDetail1 detail1 = detail1Repository.save(DtoMapper.convert(detail1DTO, HvPmXcMaterialOutStockResultDetail1.class));
        return detail1.getId();
    }

    @Override
    @Transactional
    public void deleteDetail1(long id, String taskNo) {
        detail1Repository.deleteById(id);
    }

    @Override
    public List<HvPmXcMaterialOutStockResultDetail1> getHvPmXcMaterialOutStockResultDetail1ByWorkOrderCode(String workOrderCode) {
        return resultMapper.getHvPmXcMaterialOutStockResultDetail1ByWorkOrderCode(workOrderCode);
    }

    @Override
    public List<HvPmXcMaterialOutStockResultDetail1DTO> getByTaskNoList(List<String> taskNoList) {
        return resultMapper.getByTaskNoList(taskNoList);
    }
}
