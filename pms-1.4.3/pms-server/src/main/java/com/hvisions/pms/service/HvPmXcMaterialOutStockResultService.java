package com.hvisions.pms.service;

import com.hvisions.pms.dto.HvPmXcMaterialOutStockResultDTO;
import com.hvisions.thirdparty.common.dto.ProfileOutboundInfoDTO;

import java.text.ParseException;

/**
 * <AUTHOR>
 * @Date 2024/5/8
 */
public interface HvPmXcMaterialOutStockResultService {
    /**
     * 根据taskNo获取列表
     * @param taskNo
     * @return
     */
    HvPmXcMaterialOutStockResultDTO getByTaskNo(String taskNo);

    /**
     * 添加
     * @param resultDTO
     * @return
     */
    long createOutStockResult(HvPmXcMaterialOutStockResultDTO resultDTO);

    /**
     * 修改
     * @param resultDTO
     * @return
     */
    long updateOutStockResult(HvPmXcMaterialOutStockResultDTO resultDTO);

    /**
     * 删除
     * @param id
     * @return
     */
    void deleteOutStockResult(int id);

    /**
     * 添加出库结果
     * @param profileOutboundInfoDTO
     * @return
     */
    void addOutStockResultAndDetail(ProfileOutboundInfoDTO profileOutboundInfoDTO) throws ParseException;

    /**
     * 根据任务号修改切割计划状态为“开始切割”
     * @param taskCode
     */
    void upXcCutPlanStatusByTaskCode(String taskCode);
}
