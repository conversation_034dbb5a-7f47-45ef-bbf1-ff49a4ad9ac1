package com.hvisions.pms.controller;


import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.pms.entity.plan.HvPmPolishPlanDetail0;
import com.hvisions.pms.plan.HvPmPolishPlanDTO;
import com.hvisions.pms.plan.HvPmPolishPlanDetail0DTO;
import com.hvisions.pms.plan.HvPmPolishPlanTabQueryDTO;
import com.hvisions.pms.repository.plan.HvPmPolishPlanDetail0Repository;
import com.hvisions.pms.service.HvPmPolishPlanDetail0Service;
import com.hvisions.pms.service.HvPmPolishPlanService;
import com.hvisions.pms.service.HvPmXcMaterialCutPlanService;
import com.hvisions.thirdparty.common.dto.AssemblyWorkOrderDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import java.io.IOException;
import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <P> 打磨计划<P>
 *
 * <AUTHOR>
 * @date 2024/9/21
 */
@Slf4j
@Api(tags = "打磨计划")
@RestController
@RequestMapping("/polishPlan")
public class HvPmPolishPlanController {

    @Autowired
    private HvPmXcMaterialCutPlanService hvPmXcMaterialCutPlanService;

    @Autowired
    private HvPmPolishPlanService hvPmPolishPlanService;
    @Autowired
    private HvPmPolishPlanDetail0Repository hvPmPolishPlanDetail0Repository;
    @Autowired
    private HvPmPolishPlanDetail0Service hvPmPolishPlanDetail0Service;


    /**
     * 分页查询
     *
     * @param hvPmPolishPlanTabQueryDTO
     * @return
     */
    @PostMapping("/getPage")
    @ApiOperation("分页模糊查询")
    public Page<HvPmPolishPlanDTO> getPage(@RequestBody HvPmPolishPlanTabQueryDTO hvPmPolishPlanTabQueryDTO) {
        return hvPmPolishPlanService.getPage(hvPmPolishPlanTabQueryDTO);
    }

    @GetMapping("/isExistsCode/{code}")
    @ApiOperation("查询工作号是否存在")
    public boolean isExistsCode(@PathVariable String code) {
        return hvPmPolishPlanService.isExistsCode(code);
    }

    @ApiOperation("新增打磨计划")
    @PostMapping("/createPolishPlan")
    public long createPolishPlan(@RequestBody HvPmPolishPlanDTO hvPmPolishPlanDTO){
        return hvPmPolishPlanService.createPolishPlan(hvPmPolishPlanDTO);
    }

    @ApiOperation("修改打磨计划")
    @PostMapping("/updatePolishPlan")
    public long updatePolishPlan(@RequestBody HvPmPolishPlanDTO hvPmPolishPlanDTO){
        return hvPmPolishPlanService.updatePolishPlan(hvPmPolishPlanDTO);
    }


    @ApiOperation("删除打磨计划")
    @DeleteMapping("/deletePolishPlanById/{id}")
    public void deletePolishPlanById(@PathVariable long id){
        hvPmPolishPlanService.deletePolishPlanById(id);
    }

    /**
     * 获取导入模板
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    @ApiResultIgnore
    @GetMapping("/getImportTemplate")
    @ApiOperation("获取导入模板")
    public ResultVO<ExcelExportDto> getImportTemplate() throws IOException, IllegalAccessException {
        return hvPmPolishPlanService.getImportTemplate();
    }

    /**
     * 导入切割计划
     * @param file
     * @return
     * @throws IllegalAccessException
     * @throws ParseException
     * @throws IOException
     */
    @PostMapping("/importPolishPlan")
    @ApiOperation("导入打磨计划")
    public ImportResult importPolishPlan(@RequestParam("file") MultipartFile file) throws IllegalAccessException, ParseException, IOException {
        return hvPmPolishPlanService.importPolishPlan(file);
    }

    @ApiResultIgnore
    @PostMapping(value = "/exportPolishPlan")
    @ApiOperation(value = "导出打磨计划")
    public ResultVO<ExcelExportDto> exportPolishPlan(@RequestBody HvPmPolishPlanTabQueryDTO hvPmPolishPlanTabQueryDTO) throws IOException, IllegalAccessException {
        return hvPmPolishPlanService.exportPolishPlan(hvPmPolishPlanTabQueryDTO);
    }


    /**
     * 根据打磨计划id获取零件列表
     *
     * @param id
     * @return
     */
    @GetMapping("/getDetail0List/{id}")
    @ApiOperation("根据打磨计划id获取零件列表")
    public List<HvPmPolishPlanDetail0> getDetail0List(@PathVariable long id) {
        return hvPmPolishPlanDetail0Repository.getAllByCodeId(id);
    }

    /**
     * 添加零件
     *
     * @param hvPmPolishPlanDetail0DTO
     * @return
     */
    @PostMapping("/createDetail0")
    @ApiOperation("添加零件")
    public long createDetail0(@RequestBody HvPmPolishPlanDetail0DTO hvPmPolishPlanDetail0DTO) {
        return hvPmPolishPlanDetail0Service.createDetail0(hvPmPolishPlanDetail0DTO);
    }


    /**
     * 修改零件
     *
     * @param hvPmPolishPlanDetail0DTO
     * @return
     */
    @PutMapping("/updateDetail0")
    @ApiOperation("修改零件")
    public long updateDetail0(@RequestBody HvPmPolishPlanDetail0DTO hvPmPolishPlanDetail0DTO) {
        return hvPmPolishPlanDetail0Service.updateDetail0(hvPmPolishPlanDetail0DTO);
    }

    /**
     * 删除零件
     *
     * @param id
     */
    @DeleteMapping("/deleteDetail0ById/{id}")
    @ApiOperation("删除零件")
    public void deleteDetail0ById(@PathVariable long id) {
        hvPmPolishPlanDetail0Service.deleteDetail0ById(id);
    }


    /**
     * 根据codeId获取零件的集合
     *
     * @param id
     * @return
     */
    @GetMapping("/getDetailDTOMapByCodeId/{id}")
    @ApiOperation("根据codeId获取零件的集合")
    public Map<String, Object> getDetailDTOMapByCodeId(@PathVariable long id) {
        Map<String, Object> detailMap = new HashMap<>();
        detailMap.put("detail0", hvPmPolishPlanDetail0Service.getDetailDTOListByCodeId(id));
        return detailMap;
    }

    /**
     * 根据组立系统下发添加打磨计划
     *
     * @param assemblyWorkOrderDTO 接收的打磨计划
     */
    @ApiOperation("根据组立系统下发添加打磨计划")
    @PostMapping("/addPolishPlan")
    public void addPolishPlan(@RequestBody AssemblyWorkOrderDTO assemblyWorkOrderDTO) {
        hvPmPolishPlanService.addPolishPlan(assemblyWorkOrderDTO);
    }

    /**
     * 手动下发打磨计划
     * @param hvPmPolishPlanDTO
     * @param userInfo
     */
    @PostMapping("/sendPolishPlan")
    @ApiOperation("下发打磨计划")
    public ResultVO<?> sendPolishPlan(@RequestBody HvPmPolishPlanDTO hvPmPolishPlanDTO,@UserInfo @ApiIgnore UserInfoDTO userInfo) {
      return  hvPmPolishPlanService.sendPolishPlan(hvPmPolishPlanDTO,userInfo);
    }


    @GetMapping("/getHvPmPolishPlanDTOByCode/{code}")
    @ApiOperation("根据任务编号获取打磨计划")
    public HvPmPolishPlanDTO getHvPmPolishPlanDTOByCode(@PathVariable String code) {
        return hvPmPolishPlanService.getHvPmPolishPlanDTOByCode(code);
    }

    @GetMapping("/getByWorkOrderCode/{workOrderCode}")
    @ApiOperation("根据工单号获取打磨计划")
    public HvPmPolishPlanDTO  getByWorkOrderCode(@PathVariable String workOrderCode){
        return hvPmPolishPlanService.getByWorkOrderCode(workOrderCode);
    }
}
