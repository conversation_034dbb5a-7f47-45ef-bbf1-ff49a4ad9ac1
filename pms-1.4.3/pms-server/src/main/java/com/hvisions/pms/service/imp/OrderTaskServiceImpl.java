package com.hvisions.pms.service.imp;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.pms.dao.OrderTaskMapper;
import com.hvisions.pms.dao.TaskMapper;
import com.hvisions.pms.dto.OperationQueryDTO;
import com.hvisions.pms.entity.HvPmOrderTask;
import com.hvisions.pms.entity.HvPmTaskUser;
import com.hvisions.pms.entity.HvPmWorkOrder;
import com.hvisions.pms.entity.plan.HvPmWorkPlan;
import com.hvisions.pms.enums.OperationStateEnum;
import com.hvisions.pms.repository.OrderTaskRepository;
import com.hvisions.pms.repository.TaskUserRepository;
import com.hvisions.pms.repository.WorkOrderRepository;
import com.hvisions.pms.repository.plan.HvPmWorkPlanRepository;
import com.hvisions.pms.service.OrderTaskService;
import com.hvisions.pms.task.dto.AssignDTO;
import com.hvisions.pms.task.dto.OrderTaskDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>Title: OrderTaskServiceImpl</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/3/25</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Service
public class OrderTaskServiceImpl implements OrderTaskService {

    private final OrderTaskRepository orderTaskRepository;
    private final WorkOrderRepository workOrderRepository;
    private final HvPmWorkPlanRepository workPlanRepository;
    private final TaskMapper taskMapper;
    private final TaskUserRepository taskUserRepository;
    private final OrderTaskMapper orderTaskMapper;

    @Autowired
    public OrderTaskServiceImpl(OrderTaskRepository orderTaskRepository, WorkOrderRepository workOrderRepository, HvPmWorkPlanRepository workPlanRepository, TaskMapper taskMapper, TaskUserRepository taskUserRepository,OrderTaskMapper orderTaskMapper) {
        this.orderTaskRepository = orderTaskRepository;
        this.workOrderRepository = workOrderRepository;
        this.workPlanRepository = workPlanRepository;
        this.taskMapper = taskMapper;
        this.taskUserRepository = taskUserRepository;
        this.orderTaskMapper = orderTaskMapper;
    }

    /**
     * 根据工单ID查询生产任务
     *
     * @param orderId 工单Id
     * @return 生产任务
     */
    @Override
    public List<OrderTaskDTO> getAllByOrderId(Integer orderId) {
        List<HvPmOrderTask> hvPmOrderTaskList = orderTaskRepository.getAllByOrderId(orderId);
        return DtoMapper.convertList(hvPmOrderTaskList, OrderTaskDTO.class);
    }

    /**
     * 根据工位Id查询 生产任务
     *
     * @param workCenterId 工位ID
     * @return 生产任务信息
     */
    @Override
    public List<OrderTaskDTO> getAllByWorkCenterId(Integer workCenterId) {
        List<HvPmOrderTask> hvPmOrderTaskList = orderTaskRepository.getAllByWorkCenterId(workCenterId);
        return DtoMapper.convertList(hvPmOrderTaskList, OrderTaskDTO.class);
    }


    /**
     * 根据ID列表查询生产任务
     *
     * @param idIn 生产任务ID列表
     * @return 生产任务
     */
    @Override
    public List<OrderTaskDTO> getOperationByIdIn(List<Integer> idIn) {
        return DtoMapper.convertList(orderTaskRepository.getAllByIdIn(idIn), OrderTaskDTO.class);
    }

    /**
     * 根据工位id列表查询生产任务数量
     *
     * @param workCenterIds 工位id列表
     * @return
     */
    @Override
    public Map<Integer, Integer> getTaskCountByWorkCenterId(List<Integer> workCenterIds) {
        Map<Integer, Integer> map = new HashMap<>();
        for (Integer workCenterId : workCenterIds) {
            List<OrderTaskDTO> allByEquipmentId = getAllByWorkCenterId(workCenterId);
            List<OrderTaskDTO> collect =
                    allByEquipmentId.stream().filter(t -> t.getState()
                            .equals(OperationStateEnum.RUNNING.getCode())).collect(Collectors.toList());
            map.put(workCenterId, collect.size());
        }
        return map;
    }

    /**
     * 根据工位id，工序状态查询工单信息
     *
     * @param operationQueryDTO 分页查询对象
     * @return 工序的分页列表
     */
    @Override
    public Page<OrderTaskDTO> getTaskByQuery(OperationQueryDTO operationQueryDTO) {
        Page<OrderTaskDTO> result = PageHelperUtil.getPage(taskMapper::getTaskDtoByQuery, operationQueryDTO);
        List<HvPmWorkOrder> orders = workOrderRepository.findAllById(result.stream()
                .map(OrderTaskDTO::getOrderId).collect(Collectors.toList()));
        for (OrderTaskDTO orderOperationDTO : result) {
            orders.stream()
                    .filter(t -> t.getId().equals(orderOperationDTO.getOrderId()))
                    .findFirst()
                    .ifPresent(byId -> {
                        String materialCode = byId.getMaterialCode();
                        String materialName = byId.getMaterialName();
                        String eigenvalue = byId.getEigenvalue();
                        BigDecimal quantity = byId.getQuantity();
                        orderOperationDTO.setMaterialName(materialName);
                        orderOperationDTO.setWorkOrderQuantity(quantity);
                        orderOperationDTO.setMaterialCode(materialCode);
                        orderOperationDTO.setEigenvalue(eigenvalue);
                        HvPmWorkPlan workPlan = workPlanRepository.getByPlanCode(byId.getPlanCode());
                        if (workPlan != null) {
                            orderOperationDTO.setWorkPlanStartTime(workPlan.getPlanStartTime());
                            orderOperationDTO.setWorkPlanEndTime(workPlan.getPlanEndTime());
                        }
                    });
        }
        return result;
    }

    /**
     * 分配任务
     *
     * @param assignDTO 任务与人员
     */
    @Override
    public void assignTask(AssignDTO assignDTO) {
        List<HvPmOrderTask> allByIdIn = orderTaskRepository.getAllByIdIn(assignDTO.getTaskIdList());
        //获取工单任务状态编码列表
        //已经结束的任务不能分配
        if (allByIdIn != null && allByIdIn.size() > 0) {
            List<Integer> collect = allByIdIn.stream().map(HvPmOrderTask::getState).collect(Collectors.toList());
            if (collect.contains(OperationStateEnum.FINISH.getCode())) {
                throw new BaseKnownException(10000, "已完成任务不能分配");
            }
            if (collect.contains(OperationStateEnum.SCARP.getCode())) {
                throw new BaseKnownException(10000, "已报废任务不能分配");
            }
        }

        List<HvPmTaskUser> allByTaskIdIn = taskUserRepository.getAllByTaskIdIn(assignDTO.getTaskIdList());
        taskUserRepository.deleteByIdIn(allByTaskIdIn.stream().map(HvPmTaskUser::getId).collect(Collectors.toList()));
        List<HvPmTaskUser> hvPmTaskUsers = new ArrayList<>();
        for (Integer integer : assignDTO.getTaskIdList()) {
            HvPmTaskUser hvPmTaskUser = new HvPmTaskUser();
            hvPmTaskUser.setUserId(assignDTO.getUserId());
            hvPmTaskUser.setTaskId(integer);
            hvPmTaskUsers.add(hvPmTaskUser);
        }
        taskUserRepository.saveAll(hvPmTaskUsers);
    }

    @Override
    public void updateByLineReportRecord(OrderTaskDTO orderTaskDTO) {
        orderTaskMapper.updateByLineReportRecord(orderTaskDTO);
    }

    @Override
    public int deleteByWorkOrderCode(String workOrderCode) {
        return orderTaskRepository.deleteByWorkOrderCode(workOrderCode);
    }


}