package com.hvisions.pms.service.imp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.bom.dto.HvBmFrameDTO;
import com.hvisions.hiperbase.client.HvBmFrameClient;
import com.hvisions.hiperbase.client.MaterialPointClient;
import com.hvisions.hiperbase.materials.dto.MaterialPointDTO;
import com.hvisions.pms.consts.CallMaterialRecordConst;
import com.hvisions.pms.dao.HvPmCallMaterialRecordMapper;
import com.hvisions.pms.entity.HvPmCallMaterialRecord;
import com.hvisions.pms.entity.HvPmCallMaterialRecordDetail;
import com.hvisions.pms.entity.plan.HvPmCallFrameMaterial;
import com.hvisions.pms.exportdto.CallMaterialRecordDetailExportDTO;
import com.hvisions.pms.exportdto.CallMaterialRecordExportDTO;
import com.hvisions.pms.service.HvPmCallFrameMaterialService;
import com.hvisions.pms.service.HvPmCallMaterialRecordDetailService;
import com.hvisions.pms.service.HvPmCallMaterialRecordService;
import com.hvisions.pms.service.HvPmPlanProductBomService;
import com.hvisions.pms.util.RedisUtils;
import com.hvisions.thirdparty.common.dto.WeldLineCallMaterialsDTO;
import com.hvisions.wms.client.StockClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import springfox.documentation.annotations.ApiIgnore;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-05-27 15:01
 */
@Service
@Slf4j
public class HvPmCallMaterialRecordServiceImpl extends ServiceImpl<HvPmCallMaterialRecordMapper, HvPmCallMaterialRecord> implements HvPmCallMaterialRecordService {
    @Autowired
    private HvBmFrameClient hvBmFrameClient;
    @Autowired
    private MaterialPointClient materialPointClient;
    @Autowired
    private HvPmPlanProductBomService hvPmPlanProductBomService;
    @Autowired
    private HvPmCallMaterialRecordDetailService hvPmCallMaterialRecordDetailService;
    @Autowired
    private HvPmCallFrameMaterialService hvPmCallFrameMaterialService;
    @Autowired
    private StockClient stockClient;
    @Autowired
    private HvPmCallFrameMaterialService frameMaterialService;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private HvPmCallMaterialRecordMapper hvPmCallMaterialRecordMapper;


    @Override
    public Page<HvPmCallMaterialRecord> pageList(QueryWrapper<HvPmCallMaterialRecord> queryWrapper, Integer pageNo, Integer pageSize) {
        Page<HvPmCallMaterialRecord> page = new Page<>(pageNo, pageSize);
        Page<HvPmCallMaterialRecord> list = this.page(page, queryWrapper);
        List<HvBmFrameDTO> hvBmFrameDTOS = hvBmFrameClient.getAll().getData();
        List<MaterialPointDTO> materialPointDTOS = materialPointClient.getAllMaterialPoint().getData();
        Map<String, String> frameMap = new HashMap<>();
        Map<String, String> materialPointAreaMap = new HashMap<>();
        for (HvBmFrameDTO hvBmFrameDTO : hvBmFrameDTOS) {
            frameMap.put(hvBmFrameDTO.getFrameCode(), hvBmFrameDTO.getFrameName());
        }
        for (MaterialPointDTO materialPointDTO : materialPointDTOS) {
            materialPointAreaMap.put(materialPointDTO.getPointCode(), materialPointDTO.getPointName());
        }

        List<HvPmCallMaterialRecord> collect = list.getRecords().stream().map(hvPmCallMaterialRecord -> {
            if (StringUtils.isNotBlank(hvPmCallMaterialRecord.getFrameCode())) {
                hvPmCallMaterialRecord.setFrameCode(hvPmCallMaterialRecord.getFrameCode() + "-" + frameMap.get(hvPmCallMaterialRecord.getFrameCode()));
            }

            if (StringUtils.isNotBlank(hvPmCallMaterialRecord.getPointCode())) {
                hvPmCallMaterialRecord.setPointCode(hvPmCallMaterialRecord.getPointCode() + '-' + materialPointAreaMap.get(hvPmCallMaterialRecord.getPointCode()));
            }
            return hvPmCallMaterialRecord;
        }).collect(Collectors.toList());
        page.setRecords(collect);
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createCallMaterialRecord(List<WeldLineCallMaterialsDTO> weldLineCallMaterialsDTOS) {
        try {
            HvPmCallMaterialRecord hvPmCallMaterialRecord;
            HvPmCallMaterialRecordDetail hvPmCallMaterialRecordDetail;
            List<HvPmCallMaterialRecordDetail> hvPmCallMaterialRecordDetails = new ArrayList<>();
            //循环产线叫料请求
            for (WeldLineCallMaterialsDTO weldLineCallMaterialsDTO : weldLineCallMaterialsDTOS) {
                boolean exists = hvPmCallMaterialRecordMapper.exists(new LambdaQueryWrapper<HvPmCallMaterialRecord>()
                        .eq(HvPmCallMaterialRecord::getRequestCode, weldLineCallMaterialsDTO.getRequestCode()));
                if (exists) {
                    throw new BaseKnownException("叫料记录已存在！"+weldLineCallMaterialsDTO.getRequestCode());
                }
                hvPmCallMaterialRecordDetails.clear();
                //添加主表数据
                hvPmCallMaterialRecord = DtoMapper.convert(weldLineCallMaterialsDTO, HvPmCallMaterialRecord.class);
                hvPmCallMaterialRecord.setWorkOrderCode(weldLineCallMaterialsDTO.getWorkOrder());
                hvPmCallMaterialRecord.setCreateTime(new Date());
                this.save(hvPmCallMaterialRecord);
                LambdaQueryWrapper<HvPmCallFrameMaterial> lqw = new LambdaQueryWrapper<>();
                lqw.eq(HvPmCallFrameMaterial::getFrameCode, weldLineCallMaterialsDTO.getFrameCode());
                List<HvPmCallFrameMaterial> frameMaterials = frameMaterialService.list(lqw);
                if (frameMaterials == null || frameMaterials.isEmpty()) {
                    throw new BaseKnownException("料框不存在库存！");
                }
                for (HvPmCallFrameMaterial frameMaterial : frameMaterials) {
                    hvPmCallMaterialRecordDetail = new HvPmCallMaterialRecordDetail();
                    hvPmCallMaterialRecordDetail.setRecordId(hvPmCallMaterialRecord.getId());
                    hvPmCallMaterialRecordDetail.setMaterialCode(frameMaterial.getMaterialCode());
                    hvPmCallMaterialRecordDetail.setQuantity(frameMaterial.getQuantity());
                    hvPmCallMaterialRecordDetail.setBlockCode(frameMaterial.getBlockCode());
                    hvPmCallMaterialRecordDetails.add(hvPmCallMaterialRecordDetail);
                }
                hvPmCallMaterialRecordDetailService.saveBatch(hvPmCallMaterialRecordDetails);
            }
        } catch (Exception e) {
            log.error("叫料记录生成失败:{}", e.getMessage());
            throw new BaseKnownException("叫料记录生成失败，请联系管理员" + e.getMessage());
        }
    }

    @Override
    public HvPmCallMaterialRecord getRecordByRequestCode(String taskCode) {
        LambdaQueryWrapper<HvPmCallMaterialRecord> lb = new LambdaQueryWrapper<>();
        lb.eq(HvPmCallMaterialRecord::getRequestCode, taskCode);
        lb.orderByDesc(HvPmCallMaterialRecord::getCreateTime);
        lb.last("limit 1");
        return getOne(lb);
    }

    //导出叫料记录
    @Override
    public ResultVO<ExcelExportDto> exportCallMaterialRecord(HvPmCallMaterialRecord hvPmCallMaterialRecord) throws IOException, IllegalAccessException {
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(getExportInfo(hvPmCallMaterialRecord).getBody());
        excelExportDto.setFileName(CallMaterialRecordConst.PLAN_EXPORT_FILE_NAME);
        return ResultVO.success(excelExportDto);
    }


    //获取需要导出的数据内容
    @ApiIgnore
    public ResponseEntity<byte[]> getExportInfo(HvPmCallMaterialRecord condition) throws IOException, IllegalAccessException {
        // 获取叫料记录
        List<HvPmCallMaterialRecord> hvPmCallMaterialRecordList = getListByQuery(condition);
        //获取叫料记录id列表
        List<Long> recordIds = hvPmCallMaterialRecordList.stream()
                .map(HvPmCallMaterialRecord::getId)
                .collect(Collectors.toList());
        //获取物料详情
        List<HvPmCallMaterialRecordDetail> detailList = hvPmCallMaterialRecordDetailService.getDetailListByRecordIds(recordIds);

        //叫料记录
        List<CallMaterialRecordExportDTO> callMaterialRecordExportDTOS = DtoMapper.convertList(hvPmCallMaterialRecordList, CallMaterialRecordExportDTO.class);
        //物料详情
        List<CallMaterialRecordDetailExportDTO> callMaterialRecordDetailExportDTOS = DtoMapper.convertList(detailList, CallMaterialRecordDetailExportDTO.class);

        HSSFWorkbook hssfWorkbook = new HSSFWorkbook();
        ExcelUtil.addSheetToWorkBook(callMaterialRecordExportDTOS, CallMaterialRecordConst.PLAN_EXPORT_SHEET_NAME, CallMaterialRecordExportDTO.class,null, hssfWorkbook);
        ExcelUtil.addSheetToWorkBook(callMaterialRecordDetailExportDTOS, CallMaterialRecordConst.PLAN_DETAIL0_EXPORT_SHEET_NAME, CallMaterialRecordDetailExportDTO.class,null, hssfWorkbook);
        return ExcelUtil.generateHttpExcelFile(hssfWorkbook, CallMaterialRecordConst.PLAN_EXPORT_FILE_NAME);
    }

    //获取叫料记录
    public List<HvPmCallMaterialRecord> getListByQuery( HvPmCallMaterialRecord condition){
        QueryWrapper<HvPmCallMaterialRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.gt(!StringUtils.isEmpty(condition.getStartTimeUTC()), "request_time", condition.getStartTimeUTC())
                .lt(!StringUtils.isEmpty(condition.getEndTimeUTC()), "request_time", condition.getEndTimeUTC())
                .eq(!StringUtils.isEmpty(condition.getRequestCode()), "request_code", condition.getRequestCode())
                .eq(!StringUtils.isEmpty(condition.getRequestUserCode()), "request_user_code", condition.getRequestUserCode())
                .eq(!StringUtils.isEmpty(condition.getWorkOrderCode()), "work_order_code", condition.getWorkOrderCode())
                .eq(!StringUtils.isEmpty(condition.getFrameCode()), "frame_code", condition.getFrameCode())
                .eq(!StringUtils.isEmpty(condition.getPointCode()), "point_code", condition.getPointCode())
                .orderByDesc("id");
        List<HvPmCallMaterialRecord> hvPmCallMaterialRecordList = this.list(queryWrapper);

        List<HvBmFrameDTO> hvBmFrameDTOS = hvBmFrameClient.getAll().getData();
        List<MaterialPointDTO> materialPointDTOS = materialPointClient.getAllMaterialPoint().getData();
        Map<String, String> frameMap = new HashMap<>();
        Map<String, String> materialPointAreaMap = new HashMap<>();
        for (HvBmFrameDTO hvBmFrameDTO : hvBmFrameDTOS) {
            frameMap.put(hvBmFrameDTO.getFrameCode(), hvBmFrameDTO.getFrameName());
        }
        for (MaterialPointDTO materialPointDTO : materialPointDTOS) {
            materialPointAreaMap.put(materialPointDTO.getPointCode(), materialPointDTO.getPointName());
        }

        hvPmCallMaterialRecordList.stream().forEach(hvPmCallMaterialRecord -> {
            if (StringUtils.isNotBlank(hvPmCallMaterialRecord.getFrameCode())) {
                hvPmCallMaterialRecord.setFrameCode(hvPmCallMaterialRecord.getFrameCode() + "-" + frameMap.get(hvPmCallMaterialRecord.getFrameCode()));
            }

            if (StringUtils.isNotBlank(hvPmCallMaterialRecord.getPointCode())) {
                hvPmCallMaterialRecord.setPointCode(hvPmCallMaterialRecord.getPointCode() + '-' + materialPointAreaMap.get(hvPmCallMaterialRecord.getPointCode()));
            }
        });
        return hvPmCallMaterialRecordList;
    }
}
