package com.hvisions.pms.service.imp;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.client.LocationExtendClient;
import com.hvisions.hiperbase.equipment.location.LocationDTO;
import com.hvisions.pms.dao.XcCutPlanReportMapper;
import com.hvisions.pms.dto.HomeSevenDataDTO;
import com.hvisions.pms.entity.CutPartsQueryDTO;
import com.hvisions.pms.entity.CuttingCountResultDTO;
import com.hvisions.pms.service.XcCutPlanReportService;
import org.apache.ibatis.annotations.MapKey;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * <P>    <P>
 *
 * <AUTHOR>
 * @date 2024/11/19
 */
@Service
public class XcCutPlanReportServiceImpl implements XcCutPlanReportService {


    @Autowired
    private XcCutPlanReportMapper xcCutPlanReportMapper;

    @Autowired
    private LocationExtendClient locationExtendClient;

    public LocationDTO getLocationDTOResultVO(String lineCode) {
        ResultVO<LocationDTO> locationDTOResultVO = locationExtendClient.getLineInfoByLineCode(lineCode);
        if (!locationDTOResultVO.isSuccess() || locationDTOResultVO.getData() == null) {
            throw new BaseKnownException("未产线编号为"+ lineCode +"的产线信息");
        }
        return locationDTOResultVO.getData();
    }

    @Override
    public Integer getOneDataXcCutByLineCode(String lineCode, String date) {
        //根据产线编号查询id
        LocationDTO locationDTO = getLocationDTOResultVO(lineCode);
        return xcCutPlanReportMapper.getOneDataXcCutByLineId(locationDTO.getId(),date);
    }

    @Override
    public Integer getOneDataXcPlanCutByLineCode(String lineCode, String date) {
        //根据产线编号查询id
        LocationDTO locationDTO = getLocationDTOResultVO(lineCode);
        return xcCutPlanReportMapper.getOneDataXcPlanCutByLineId(locationDTO.getId(),date);
    }

    @Override
    public HomeSevenDataDTO getXcCutSevenDataByLineCode(String lineCode, String date) {
        //根据产线编号查询id
        LocationDTO locationDTO = getLocationDTOResultVO(lineCode);
        return xcCutPlanReportMapper.getXcCutSevenDataByLineId(locationDTO.getId(),date);
    }

    @Override
    public Integer getOneWeekXcCutByLineCode(String lineCode, String date) {
        //根据产线编号查询id
        LocationDTO locationDTO = getLocationDTOResultVO(lineCode);
        return xcCutPlanReportMapper.getOneWeekXcCutByLineId(locationDTO.getId(),date);
    }

    @Override
    public Integer getOneMonthXcCutByLineCode(String lineCode, String date) {
        //根据产线编号查询id
        LocationDTO locationDTO = getLocationDTOResultVO(lineCode);
        return xcCutPlanReportMapper.getOneMonthXcCutByLineId(locationDTO.getId(),date);
    }

    @Override
    public Integer getOneYearXcCutByLineCode(String lineCode, String date) {
        //根据产线编号查询id
        LocationDTO locationDTO = getLocationDTOResultVO(lineCode);
        return xcCutPlanReportMapper.getOneYearXcCutByLineId(locationDTO.getId(),date);
    }

    @Override
    public Integer getOneWeekXcPlanCutByLineCode(String lineCode, String date) {
        //根据产线编号查询id
        LocationDTO locationDTO = getLocationDTOResultVO(lineCode);
        return xcCutPlanReportMapper.getOneWeekXcPlanCutByLineId(locationDTO.getId(),date);
    }

    @Override
    public Integer getOneMonthXcPlanCutByLineCode(String lineCode, String date) {
        //根据产线编号查询id
        LocationDTO locationDTO = getLocationDTOResultVO(lineCode);
        return xcCutPlanReportMapper.getOneMonthXcPlanCutByLineId(locationDTO.getId(),date);
    }

    @Override
    public Integer getOneYearXcPlanCutByLineCode(String lineCode, String date) {
        //根据产线编号查询id
        LocationDTO locationDTO = getLocationDTOResultVO(lineCode);
        return xcCutPlanReportMapper.getOneYearXcPlanCutByLineId(locationDTO.getId(),date);
    }

    @Override
    public Integer getOneDataXcCutPartByLineCode(String lineCode, String date) {
        //根据产线编号查询id
        LocationDTO locationDTO = getLocationDTOResultVO(lineCode);
        return xcCutPlanReportMapper.getOneDataXcCutPartByLineId(locationDTO.getId(),date);
    }

    @Override
    public Integer getOneDataXcPlanCutPartByLineCode(String lineCode, String date) {
        //根据产线编号查询id
        LocationDTO locationDTO = getLocationDTOResultVO(lineCode);
        return xcCutPlanReportMapper.getOneDataXcPlanCutPartByLineId(locationDTO.getId(),date);
    }

    @Override
    public HomeSevenDataDTO getXcCutPartSevenDataByLineCode(String lineCode, String date) {
        //根据产线编号查询id
        LocationDTO locationDTO = getLocationDTOResultVO(lineCode);
        return xcCutPlanReportMapper.getXcCutPartSevenDataByLineId(locationDTO.getId(),date);
    }

    @Override
    public Integer getOneWeekXcCutPartByLineCode(String lineCode, String date) {
        //根据产线编号查询id
        LocationDTO locationDTO = getLocationDTOResultVO(lineCode);
        return xcCutPlanReportMapper.getOneWeekXcCutPartByLineId(locationDTO.getId(),date);
    }

    @Override
    public Integer getOneMonthXcCutPartByLineCode(String lineCode, String date) {
        //根据产线编号查询id
        LocationDTO locationDTO = getLocationDTOResultVO(lineCode);
        return xcCutPlanReportMapper.getOneMonthXcCutPartByLineId(locationDTO.getId(),date);
    }

    @Override
    public Integer getOneYearXcCutPartByLineCode(String lineCode, String date) {
        //根据产线编号查询id
        LocationDTO locationDTO = getLocationDTOResultVO(lineCode);
        return xcCutPlanReportMapper.getOneYearXcCutPartByLineId(locationDTO.getId(),date);
    }

    @Override
    public Integer getOneWeekXcPlanCutPartByLineCode(String lineCode, String date) {
        //根据产线编号查询id
        LocationDTO locationDTO = getLocationDTOResultVO(lineCode);
        return xcCutPlanReportMapper.getOneWeekXcPlanCutPartByLineId(locationDTO.getId(),date);
    }

    @Override
    public Integer getOneMonthXcPlanCutPartByLineCode(String lineCode, String date) {
        //根据产线编号查询id
        LocationDTO locationDTO = getLocationDTOResultVO(lineCode);
        return xcCutPlanReportMapper.getOneMonthXcPlanCutPartByLineId(locationDTO.getId(),date);
    }

    @Override
    public Integer getOneYearXcPlanCutPartByLineCode(String lineCode, String date) {
        //根据产线编号查询id
        LocationDTO locationDTO = getLocationDTOResultVO(lineCode);
        return xcCutPlanReportMapper.getOneYearXcPlanCutPartByLineId(locationDTO.getId(),date);
    }

    @Override
    public Integer getCuttingCountDateRange(CutPartsQueryDTO cutPartsQueryDTO) {
        if ("custom".equals(cutPartsQueryDTO.getDateRange())) {
            if (cutPartsQueryDTO.getStartDate() == null || cutPartsQueryDTO.getEndDate() == null) {
                throw new BaseKnownException("自定义日期必须提供 startDate 和 endDate");
            }
        }
        // 1. 参数校验
        if (cutPartsQueryDTO.getLineCode() == null || cutPartsQueryDTO.getDateRange() == null) {
            throw new BaseKnownException("参数 lineCode 和 dateRange 不能为空");
        }
        List<String> validRanges = Arrays.asList("last7Days", "last30Days", "currentWeek", "currentMonth", "custom");
        if (!validRanges.contains(cutPartsQueryDTO.getDateRange())) {
            throw new BaseKnownException("无效的 dateRange 参数");
        }
        // 2. 根据产线编号查询id
        LocationDTO locationDTO = getLocationDTOResultVO(cutPartsQueryDTO.getLineCode());
        if (locationDTO == null || locationDTO.getId() == null) {
            throw new BaseKnownException("无效的产线编号: " + cutPartsQueryDTO.getLineCode());
        }

        // 3. 处理自定义日期的 endDate
        String processedEndDate = "custom".equals(cutPartsQueryDTO.getDateRange())
                ? cutPartsQueryDTO.getProcessedEndDateAsString()
                : cutPartsQueryDTO.getEndDate();

        // 4. 调用Mapper
        return xcCutPlanReportMapper.getCuttingCountDateRange(
                locationDTO.getId(),
                cutPartsQueryDTO.getDateRange(),
                cutPartsQueryDTO.getStartDate(),
                processedEndDate // 传入处理后的 endDate
        );
    }

    @Override
    @MapKey("date")
    public List<CuttingCountResultDTO> getCuttingCountByDate(CutPartsQueryDTO cutPartsQueryDTO) {
        if ("custom".equals(cutPartsQueryDTO.getDateRange())) {
            if (cutPartsQueryDTO.getStartDate() == null || cutPartsQueryDTO.getEndDate() == null) {
                throw new BaseKnownException("自定义日期必须提供 startDate 和 endDate");
            }
        }
        // 1. 参数校验
        if (cutPartsQueryDTO.getLineCode() == null || cutPartsQueryDTO.getDateRange() == null) {
            throw new BaseKnownException("参数 lineCode 和 dateRange 不能为空");
        }
        List<String> validRanges = Arrays.asList("last7Days", "last30Days", "currentWeek", "currentMonth", "custom");
        if (!validRanges.contains(cutPartsQueryDTO.getDateRange())) {
            throw new BaseKnownException("无效的 dateRange 参数");
        }
        // 2. 根据产线编号查询id
        LocationDTO locationDTO = getLocationDTOResultVO(cutPartsQueryDTO.getLineCode());
        if (locationDTO == null || locationDTO.getId() == null) {
            throw new BaseKnownException("无效的产线编号: " + cutPartsQueryDTO.getLineCode());
        }

        // 3. 处理自定义日期的 endDate
        String processedEndDate = "custom".equals(cutPartsQueryDTO.getDateRange())
                ? cutPartsQueryDTO.getProcessedEndDateAsString()
                : cutPartsQueryDTO.getEndDate();

        return xcCutPlanReportMapper.getCuttingCountByDate(
                locationDTO.getId(),
                cutPartsQueryDTO.getDateRange(),
                cutPartsQueryDTO.getStartDate(),
                processedEndDate // 传入处理后的 endDate
        );
    }

    @Override
    public Integer getCuttingPlanCountDateRange(CutPartsQueryDTO cutPartsQueryDTO) {
        if ("custom".equals(cutPartsQueryDTO.getDateRange())) {
            if (cutPartsQueryDTO.getStartDate() == null || cutPartsQueryDTO.getEndDate() == null) {
                throw new BaseKnownException("自定义日期必须提供 startDate 和 endDate");
            }
        }
        // 1. 参数校验
        if (cutPartsQueryDTO.getLineCode() == null || cutPartsQueryDTO.getDateRange() == null) {
            throw new BaseKnownException("参数 lineCode 和 dateRange 不能为空");
        }
        List<String> validRanges = Arrays.asList("last7Days", "last30Days", "currentWeek", "currentMonth", "custom");
        if (!validRanges.contains(cutPartsQueryDTO.getDateRange())) {
            throw new BaseKnownException("无效的 dateRange 参数");
        }
        // 2. 根据产线编号查询id
        LocationDTO locationDTO = getLocationDTOResultVO(cutPartsQueryDTO.getLineCode());
        if (locationDTO == null || locationDTO.getId() == null) {
            throw new BaseKnownException("无效的产线编号: " + cutPartsQueryDTO.getLineCode());
        }

        // 3. 处理自定义日期的 endDate
        String processedEndDate = "custom".equals(cutPartsQueryDTO.getDateRange())
                ? cutPartsQueryDTO.getProcessedEndDateAsString()
                : cutPartsQueryDTO.getEndDate();

        // 4. 调用Mapper
        return xcCutPlanReportMapper.getCuttingPlanCountDateRange(
                locationDTO.getId(),
                cutPartsQueryDTO.getDateRange(),
                cutPartsQueryDTO.getStartDate(),
                processedEndDate // 传入处理后的 endDate
        );
    }

}
