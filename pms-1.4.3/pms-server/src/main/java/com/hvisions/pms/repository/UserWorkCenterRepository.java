package com.hvisions.pms.repository;

import com.hvisions.pms.entity.HvPmUserWorkCenter;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>Title: UserEquipmentRepository</p >
 * <p>Description: 仓储层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/11</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface UserWorkCenterRepository extends JpaRepository<HvPmUserWorkCenter, Integer> {


    /**
     * 根据用户ID和工位ID删除关联关系
     *
     * @param equipmentId 工位ID
     * @param userId      用户ID
     */
    @Transactional(rollbackFor = Exception.class)
    @Modifying
    void deleteByWorkCenterIdAndUserId(int equipmentId, int userId);

    /**
     * 根据人员ID查询关联的工位列表
     *
     * @param userId 人员ID
     * @return 工位列表
     */
    List<HvPmUserWorkCenter> getByUserId(int userId);


    /**
     * 根据工位ID查找关联关系
     *
     * @param equipmentId 工位ID
     * @return 关联关系列表
     */
    List<HvPmUserWorkCenter> getAllByWorkCenterId(int equipmentId);

    @Modifying
    @Transactional
    void deleteByIdIn(List<Integer> idIn);


}
