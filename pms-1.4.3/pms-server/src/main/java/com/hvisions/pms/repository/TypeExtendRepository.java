package com.hvisions.pms.repository;

import com.hvisions.pms.entity.HvPmTypeExtendRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>Title: TypeExtendRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/9/28</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Repository
public interface TypeExtendRepository extends JpaRepository<HvPmTypeExtendRelation, Integer> {

    List<HvPmTypeExtendRelation> getAllByWorkOrderTypeId(Integer typeId);

    @Transactional(rollbackFor = Exception.class)
    void deleteByIdIn(List<Integer> integers);

    List<HvPmTypeExtendRelation> getAllByExtendCode(String name);

    @Transactional(rollbackFor = Exception.class)
    void deleteByWorkOrderTypeIdAndExtendCode(Integer typeId, String extendCode);
}