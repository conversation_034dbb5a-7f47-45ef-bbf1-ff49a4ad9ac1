package com.hvisions.pms.service;

import com.hvisions.pms.entity.plan.HvPmXcMaterialCutPlanDetail1;
import com.hvisions.pms.plan.HvPmXcMaterialCutPlanDetail1DTO;

import java.util.List;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/8/26
 */
public interface HvPmXcMaterialCutPlanDetail1Service {

    long createDetail1(HvPmXcMaterialCutPlanDetail1DTO hvPmXcMaterialCutPlanDetail1DTO);

    List<HvPmXcMaterialCutPlanDetail1> getDetail1List(long id);

    long updateDetail1(HvPmXcMaterialCutPlanDetail1DTO hvPmXcMaterialCutPlanDetail1DTO);

    void deleteDetail1ById(long id);

    List<HvPmXcMaterialCutPlanDetail1> getDetail1ListByOrderIds(List<Long> orderIds);
}
