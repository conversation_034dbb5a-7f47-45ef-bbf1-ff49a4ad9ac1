package com.hvisions.pms.service.imp;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.pms.dto.ShipRawMaterialDetailDTO;
import com.hvisions.pms.entity.HvPmShipRawMaterialDetail;
import com.hvisions.pms.repository.ShipRawMaterialDetailRepository;
import com.hvisions.pms.service.ShipRawMaterialDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/5/22
 */
@Service
@Slf4j
public class ShipRawMaterialDetailServiceImpl implements ShipRawMaterialDetailService {

    @Autowired
    private ShipRawMaterialDetailRepository shipRawMaterialDetailRepository;

    @Override
    public List<ShipRawMaterialDetailDTO> getByRawMaterialId(Long rawMaterialId) {
        List<HvPmShipRawMaterialDetail> list = shipRawMaterialDetailRepository.getAllByRawMaterialId(rawMaterialId);
        return DtoMapper.convertList(list, ShipRawMaterialDetailDTO.class);
    }

    @Override
    public Long createShipRawMaterialDetail(ShipRawMaterialDetailDTO detailDTO) {
        HvPmShipRawMaterialDetail detail = shipRawMaterialDetailRepository.saveAndFlush(DtoMapper.convert(detailDTO, HvPmShipRawMaterialDetail.class));
        return detail.getId();
    }

    @Override
    public Long updateShipRawMaterialDetail(ShipRawMaterialDetailDTO detailDTO) {
        HvPmShipRawMaterialDetail detail = shipRawMaterialDetailRepository.save(DtoMapper.convert(detailDTO, HvPmShipRawMaterialDetail.class));
        return detail.getId();
    }

    @Override
    public void deleteShipRawMaterialDetail(Long id) {
        shipRawMaterialDetailRepository.deleteById(id);
    }

    @Override
    public HvPmShipRawMaterialDetail getByRawMaterialIdAndSpecifications(Long rawMaterialId, String specifications) {
        return shipRawMaterialDetailRepository.getByRawMaterialIdAndSpecifications(rawMaterialId, specifications);
    }
}
