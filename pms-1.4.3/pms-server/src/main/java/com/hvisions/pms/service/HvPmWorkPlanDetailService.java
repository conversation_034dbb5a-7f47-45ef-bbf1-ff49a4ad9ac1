package com.hvisions.pms.service;


import com.hvisions.pms.plan.HvPmWorkPlanDetailDTO;

import java.math.BigDecimal;
import java.util.List;


/**
 * <p>Title: HvPmWorkPlanDetailService</p >
 * <p>Description: 生产计划明细 service</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/14</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface HvPmWorkPlanDetailService {

    /**
     * 新增或者更新
     *
     * @param hvPmWorkPlanDetailDTO 计划明细DTO
     * @return 返回新增的id
     */
    int addOrUpdate(HvPmWorkPlanDetailDTO hvPmWorkPlanDetailDTO);


    /**
     * 根据计划编号查找计划明细
     *
     * @param planId 计划编号
     * @return 计划明细 DTO
     */
    List<HvPmWorkPlanDetailDTO> findByPlanId(int planId);


    /**
     * 根据工单号查找计划详情
     *
     * @param orderCode 工单号
     * @return 计划详情实体
     */
    HvPmWorkPlanDetailDTO getHvPmWorkPlanDetailByOrderCode(String orderCode);


    /**
     * 根据工单号删除计划详情
     *
     * @param orderCode 工单号
     */
    void deleteByWorkOrderCode(String orderCode);


    /**
     * 报工
     *  @param code     工单id
     * @param num      工单流水号
     * @param quantity 工单完成数量
     * @return ture false
     */
    boolean orderFinish(String code, int num, BigDecimal quantity);
}
