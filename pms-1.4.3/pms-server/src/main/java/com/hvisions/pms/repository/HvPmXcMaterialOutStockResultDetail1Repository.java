package com.hvisions.pms.repository;

import com.hvisions.pms.entity.HvPmXcMaterialOutStockResultDetail1;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/5/8
 */
@Repository
public interface HvPmXcMaterialOutStockResultDetail1Repository extends JpaRepository<HvPmXcMaterialOutStockResultDetail1,Long> {
    void deleteByTaskNo(String taskNo);

    List<HvPmXcMaterialOutStockResultDetail1> getByTaskNo(String taskNo);

    List<HvPmXcMaterialOutStockResultDetail1> getHvPmXcMaterialOutStockResultDetail1ByResultId(Long resultId);
}
