package com.hvisions.pms.service;

import com.hvisions.pms.dto.HomeSevenDataDTO;
import com.hvisions.pms.entity.CutPartsQueryDTO;
import com.hvisions.pms.entity.CuttingResultDTO;

import java.util.List;

/**
 * <P>    <P>
 *
 * <AUTHOR>
 * @date 2024/11/16
 */
public interface CutPlanReportService {

    Integer getOneDataSteelCutByLineCode(String lineCode, String date);

    Integer getOneDataSteelPlanCutByLineCode(String lineCode, String date);
    
    HomeSevenDataDTO getCutSevenDataByLineCode(String lineCode, String date);

    Integer getOneWeekSteelCutByLineCode(String lineCode, String date);

    Integer getOneMonthSteelCutByLineCode(String lineCode, String date);

    Integer getOneYearSteelCutByLineCode(String lineCode, String date);

    Integer getOneWeekSteelPlanCutByLineCode(String lineCode, String date);

    Integer getOneMonthSteelPlanCutByLineCode(String lineCode, String date);

    Integer getOneYearSteelPlanCutByLineCode(String lineCode, String date);

    Integer getOneDataSteelPartCutByLineCode(String lineCode, String date);

    Integer getOneDataSteelPartPlanCutByLineCode(String lineCode, String date);

    HomeSevenDataDTO getPartCutSevenDataByLineCode(String lineCode, String date);

    Integer getOneWeekSteelPartCutByLineCode(String lineCode, String date);

    Integer getOneMonthSteelPartCutByLineCode(String lineCode, String date);

    Integer getOneYearSteelPartCutByLineCode(String lineCode, String date);

    Integer getOneWeekSteelPartPlanCutByLineCode(String lineCode, String date);

    Integer getOneMonthSteelPartPlanCutByLineCode(String lineCode, String date);

    Integer getOneYearSteelPartPlanCutByLineCode(String lineCode, String date);

    CuttingResultDTO getCuttingLengthByDateRange(CutPartsQueryDTO cutPartsQueryDTO);

    List<CuttingResultDTO> getCuttingLengthByDate(CutPartsQueryDTO cutPartsQueryDTO);
}
