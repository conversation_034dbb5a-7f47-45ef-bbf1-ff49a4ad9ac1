package com.hvisions.pms.repository;

import com.hvisions.pms.entity.HvPmMaterialConsume;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: MaterialConsumeRepository</p >
 * <p>Description: 物料消耗仓储层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/18</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface MaterialConsumeRepository extends JpaRepository<HvPmMaterialConsume, Integer> {
    /**
     * 根据工序ID查询物料使用情况
     *
     * @param operationId 工序ID
     * @return 物料使用情况
     */
    List<HvPmMaterialConsume> getAllByOperationId(int operationId);




}
