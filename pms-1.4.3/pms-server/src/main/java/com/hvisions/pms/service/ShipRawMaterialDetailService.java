package com.hvisions.pms.service;

import com.hvisions.pms.dto.ShipRawMaterialDetailDTO;
import com.hvisions.pms.entity.HvPmShipRawMaterialDetail;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/5/22
 */
public interface ShipRawMaterialDetailService {
    /**
     * 根据RawMaterialId获取列表
     * @param rawMaterialId
     * @return
     */
    List<ShipRawMaterialDetailDTO> getByRawMaterialId(Long rawMaterialId);

    /**
     * 添加
     * @param detailDTO
     * @return
     */
    Long createShipRawMaterialDetail(ShipRawMaterialDetailDTO detailDTO);

    /**
     * 修改
     * @param detailDTO
     * @return
     */
    Long updateShipRawMaterialDetail(ShipRawMaterialDetailDTO detailDTO);

    /**
     * 删除
     * @param id
     */
    void deleteShipRawMaterialDetail(Long id);

    HvPmShipRawMaterialDetail getByRawMaterialIdAndSpecifications(Long rawMaterialId,String specifications);
}
