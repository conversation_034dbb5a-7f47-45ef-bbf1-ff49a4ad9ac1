package com.hvisions.pms.controller;

import com.hvisions.pms.dao.StatisticsMapper;
import com.hvisions.pms.dto.*;
import com.hvisions.pms.service.StatisticsService;
import com.hvisions.pms.statistics.ActualAndPlan;
import com.hvisions.pms.statistics.CellMaterial;
import com.hvisions.pms.statistics.CrewMaterial;
import com.hvisions.pms.statistics.MaterialStatisticsDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title: StatisticsController</p>
 * <p>Description: 统计信息控制器</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/4/7</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping("/statistics")
@Api(description = "统计信息控制器")
@Slf4j
public class StatisticsController {
    @Autowired
    StatisticsMapper statisticsMapper;

    @Autowired
    StatisticsService statisticsService;


    /**
     * 查询物料信息根据产线分组
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 时间段内的物料产出信息
     */
    @ApiOperation("查询物料信息根据产线分组")
    @GetMapping("/findMaterialByCell")
    public List<CellMaterial> findMaterialByCell(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date beginTime,
                                                 @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        return statisticsService.findMaterialByCell(beginTime, endTime);
    }

    /**
     * 查询物料信息根据班组分组
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 时间段内的物料产出信息
     */
    @ApiOperation("查询物料信息根据班组分组")
    @GetMapping("/findMaterialByCrew")
    public List<CrewMaterial> findMaterialByCrew(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date beginTime,
                                                 @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        return statisticsService.findMaterialByCrew(beginTime, endTime);
    }

    /**
     * 查询一段时间物料计划投入和实际投入信息
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 时间段内的物料计划和实际投入信息
     */
    @ApiOperation("查询一段时间内工单物料的实际用量和计划用量")
    @GetMapping("/findActualAndPlan")
    public List<ActualAndPlan> FindActualAndPlan(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date beginTime,
                                                 @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        Assert.notNull(beginTime, "开始时间不能为空");
        Assert.notNull(endTime, "结束时间不能为空");
        try {
            return statisticsMapper.findActualAndPlan(beginTime, endTime);
        } catch (Exception e) {
            log.error("查询错误,请检查设备数据库是否名字为'equipment',可以调整statistics.xml中的查询语句来进行调整", e);
            return new ArrayList<>();
        }
    }

    @ApiOperation("获取生产概要")
    @GetMapping("/getWork")
    public WorkOutlineDTO getWork() {
        Calendar calendar1 = Calendar.getInstance();
        calendar1.set(calendar1.get(Calendar.YEAR), calendar1.get(Calendar.MONTH), calendar1.get(Calendar.DAY_OF_MONTH),
                0, 0, 0);
        Date beginOfDate = calendar1.getTime();
        Calendar calendar2 = Calendar.getInstance();
        calendar1.set(calendar2.get(Calendar.YEAR), calendar2.get(Calendar.MONTH), calendar2.get(Calendar.DAY_OF_MONTH),
                23, 59, 59);
        Date endOfDate = calendar1.getTime();
        return statisticsMapper.getWorkOutLineByNowDate(beginOfDate, endOfDate);
    }

    /**
     * 统计一段时间内的物料完工情况
     *
     * @param beginTime 计划结束时间在此之后
     * @param endTime   计划结束时间在此之前
     * @return 物料完工情况
     */
    @ApiOperation("统计一段时间内的物料完工情况")
    @GetMapping("/findMaterialQuantity")
    public List<MaterialStatistics> findMaterialQuantity(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date beginTime,
                                                         @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        Assert.notNull(beginTime, "开始时间不能为空");
        Assert.notNull(endTime, "结束时间不能为空");
        return statisticsMapper.findMaterial(beginTime, endTime);
    }


    /**
     * 统计工单计划信息
     *
     * @return
     */
    @ApiOperation("统计工单计划信息")
    @GetMapping("/statisticsWorkPlanInfo")
    public DailyWorkPlanDTO statisticsWorkPlanInfo() {
        return statisticsService.statisticsWorkPlanInfo();
    }

    @ApiOperation("获取当天计划数量")
    @GetMapping("/getTodayCount")
    public Map<String, TodayWorkOrderCountDTO> getTodayCount() {
        return statisticsService.getTodayCount();
    }


    @ApiOperation("获取某个工艺路线的近七天的产量")
    @GetMapping("/getSevenData/{routeName}/{date}")
    public HomeSevenDataDTO getSevenDataByRouteName(@PathVariable String routeName, @PathVariable String date) {
        return statisticsService.getSevenDataByRouteName(routeName, date);
    }

    @ApiOperation("获取当天某个工艺路线的实际产量")
    @GetMapping("/getTodayDataByRouteName/{routeName}/{date}")
    public Integer getTodayDataByRouteName(@PathVariable String routeName, @PathVariable String date) {
        return statisticsService.getTodayDataByRouteName(routeName, date);
    }

    @ApiOperation("获取当天某个工艺路线的计划产量")
    @GetMapping("/getTodayDataPlanByRouteName/{routeName}/{date}")
    public Integer getTodayDataPlanByRouteName(@PathVariable String routeName, @PathVariable String date) {
        return statisticsService.getTodayDataPlanByRouteName(routeName, date);
    }
}









