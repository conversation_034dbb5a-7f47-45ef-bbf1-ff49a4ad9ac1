package com.hvisions.pms.controller;

import com.hvisions.pms.dto.OperationFileDTO;
import com.hvisions.pms.service.OperationFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: OperationFileController</p >
 * <p>Description: 工序文件关系</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/2/22</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "OperationFileController")
@Api(description = "工序文件关系")
public class OperationFileController {


    @Autowired
    OperationFileService operationFileService;

    /**
     * 根据工序文件关系id查询工序文件关系
     *
     * @param id 工序文件关系id
     * @return 工序文件关系
     */
    @GetMapping(value = "/getOperationFileById/{id}")
    @ApiOperation(value = "根据工序文件关系id查询工序文件关系")
    public OperationFileDTO getOperationFileById(@PathVariable int id) {
        return operationFileService.getOperationFileById(id);
    }

    /**
     * 根据工序ID查询
     *
     * @param operationId 工序ID
     * @return 工序文件关系
     */
    @GetMapping(value = "/getOperationFileByOperationId/{operationId}")
    @ApiOperation(value = "根据工序ID查询工序文件关系")
    public List<OperationFileDTO> getOperationFileByOperationId(@PathVariable int operationId) {
        return operationFileService.getOperationFileByOperationId(operationId);
    }




    /**
     * 更新工序文件关系
     *
     * @param operationFileDTO 工序文件关系DTO
     * @return 工序文件关系Id
     */
    @PostMapping("/updateOperationFile")
    @ApiOperation(value = "更新工序文件关系")
    public int updateOperationFile(@RequestBody OperationFileDTO operationFileDTO) {
        return operationFileService.createOrUpdateOperationFile(operationFileDTO);
    }




}
