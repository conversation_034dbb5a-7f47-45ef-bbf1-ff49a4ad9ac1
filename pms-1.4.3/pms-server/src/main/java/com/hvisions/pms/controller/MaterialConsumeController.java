package com.hvisions.pms.controller;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.pms.enums.WorkOrderExceptionEnum;
import com.hvisions.pms.service.MaterialConsumeService;
import com.hvisions.pms.viewdto.MaterialByCrewQueryDTO;
import com.hvisions.pms.viewdto.MaterialConsumeFormDTO;
import com.hvisions.pms.viewdto.MaterialConsumeQueryDTO;
import com.hvisions.pms.viewdto.MaterialUsedQueryDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcOperations;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;

import static java.lang.String.format;

/**
 * <p>Title: MaterialConsumeController</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/18</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Api(description = "物料消耗统计控制器")
@RestController
@RequestMapping(value = "/materialConsume")
public class MaterialConsumeController {

    @Autowired
    JdbcOperations jdbcOperations;

    @Autowired
    MaterialConsumeService materialConsumeService;

    @Autowired
    HttpServletRequest request;


    /**
     * 物料使用报表
     *
     * @param materialUsedQueryDTO 物料使用查询条件
     * @return 物料使用情况
     */
    @ApiOperation(value = "物料使用报表")
    @PostMapping(value = "/getMaterialGroupByOperationId")
    public List<Map<String, Object>> getByOperationId(@RequestBody MaterialUsedQueryDTO materialUsedQueryDTO) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String sql;
        if (materialUsedQueryDTO.getActualStartTime() != null || materialUsedQueryDTO.getActualEndTime() != null) {
            if (materialUsedQueryDTO.getMaterialName() != null) {
                String materialName = "%" + materialUsedQueryDTO.getMaterialName() + "%";

                sql = format("SELECT sum(DISTINCT plan_count) as sum_plan_count ,sum(actual_count) as " +
                                "sum_actual_count,max" +
                                "(material_code) as material_code" +
                                ",max(material_name) as material_name,operation_id,max(operation_code) as operation_code\n" +
                                "FROM `material_consume` WHERE actual_end_time >= '%s' AND  actual_end_time <= '%s' " +
                                "AND material_name like '%s'   GROUP BY operation_id,material_name;",
                        format.format(materialUsedQueryDTO.getActualStartTime()),
                        format.format(materialUsedQueryDTO.getActualEndTime()),
                        materialName);
            } else {
                sql = format("SELECT sum(DISTINCT plan_count) as sum_plan_count ,sum(actual_count) as " +
                                "sum_actual_count,max(material_code) as material_code" +
                                ",max(material_name) as material_name,operation_id,max(operation_code) as operation_code\n" +
                                "FROM `material_consume` WHERE actual_end_time >= '%s' AND  actual_end_time <= '%s' " +
                                " GROUP BY operation_id,material_name;",
                        format.format(materialUsedQueryDTO.getActualStartTime()),
                        format.format(materialUsedQueryDTO.getActualEndTime()));
            }
        } else {
            throw new BaseKnownException(WorkOrderExceptionEnum.PLEASE_PASS_IN_THE_QUERY_TIME);
        }
        return jdbcOperations.queryForList(sql);


    }

    /**
     * 根据开始结束时间查询
     *
     * @param materialConsumeQueryDTO 查询条件DTO
     * @return 分页信息
     */
    @ApiOperation(value = "根据时间查询工序物料消耗(物料使用情况报表)")
    @PostMapping(value = "/getAllByDate")
    public List<Map<String, Object>> getAllByDate(@RequestBody MaterialConsumeQueryDTO materialConsumeQueryDTO) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String sql;
        if (materialConsumeQueryDTO.getPlanStartTime() != null || materialConsumeQueryDTO.getPlanEndTime() != null) {
            sql = format("SELECT h.material_id,max(h.material_code) as material_code,max(h.material_name) as material_name,sum(h.plan_count) as sum_plan_count,sum(h.actual_count) as sum_actual_count  FROM " +
                            "material_consume h " +
                            "WHERE plan_start_time BETWEEN '%s' AND '%s'  " +
                            "GROUP BY material_id;",
                    format.format(materialConsumeQueryDTO.getPlanStartTime()),
                    format.format(materialConsumeQueryDTO.getPlanEndTime()));
        } else {
            throw new BaseKnownException(WorkOrderExceptionEnum.PLEASE_PASS_IN_THE_QUERY_TIME);
        }
        return jdbcOperations.queryForList(sql);
    }


    /**
     * 根据工单计划时间查询物料投入
     *
     * @param materialByCrewQueryDTO 条件DTO
     * @return 投入物料信息
     */
    @ApiOperation(value = "根据工单计划时间查询物料投入(物料汇总统计报表)")
    @PostMapping(value = "/getInvestmentByDate")
    public List<MaterialConsumeFormDTO> getInvestmentByDate(@RequestBody MaterialByCrewQueryDTO materialByCrewQueryDTO) {
        return materialConsumeService.getInvestmentByDate(materialByCrewQueryDTO);
    }

    /**
     * 根据时间且产线查询物料产出信息
     *
     * @param materialByCrewQueryDTO 查询条件
     * @return 物料产出信息
     */
    @ApiOperation(value = "根据时间且产线查询物料产出信息(产出物料分析)")
    @PostMapping(value = "/getAllByDateAndCell")
    public List<Map<String, Object>> getAllByDateAndCell(@RequestBody MaterialByCrewQueryDTO materialByCrewQueryDTO) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String sql;
        String siteNum = request.getHeader("siteNum");
        if (materialByCrewQueryDTO.getActualStartTime() != null || materialByCrewQueryDTO.getActualEndTime() != null) {
            if (materialByCrewQueryDTO.getCellId() == null) {
                sql = format("SELECT operation_oid,material_id,max(material_code) as material_code," +
                                "max(material_name) as material_name,max(operation_code) as operation_code,crew_name,\n" +
                                "sum(out_put_count) as out_put_count FROM material_by_crew WHERE actual_end_time >= " +
                                "'%s' AND  actual_end_time <= '%s' " +
                                "GROUP BY crew_name,material_id,operation_oid;",
                        format.format(materialByCrewQueryDTO.getActualStartTime()),
                        format.format(materialByCrewQueryDTO.getActualEndTime()));
            } else {
                sql = format("SELECT operation_oid,material_id,max(material_code) as material_code," +
                                "max(material_name) as material_name,max(operation_code) as operation_code,crew_name,\n" +
                                "sum(out_put_count) as out_put_count FROM material_by_crew WHERE actual_end_time >=" +
                                " '%s' AND  actual_end_time <= '%s'  AND cell_id = '%s' " +
                                "GROUP BY crew_name,material_id,operation_oid;",
                        format.format(materialByCrewQueryDTO.getActualStartTime()),
                        format.format(materialByCrewQueryDTO.getActualEndTime()),
                        materialByCrewQueryDTO.getCellId());
            }
        } else {
            throw new BaseKnownException(WorkOrderExceptionEnum.PLEASE_PASS_IN_THE_QUERY_TIME);
        }
        return jdbcOperations.queryForList(sql);
    }


    /**
     * 根据班组工序分组统计工时
     *
     * @param materialByCrewQueryDTO 查询条件
     * @return 根据班组工序分组统计工时
     */
    @ApiOperation(value = "根据班组工序分组统计工时(工时分析)")
    @PostMapping(value = "/getWorkTimeGroupByOperationAndCrewName")
    public List<Map<String, Object>> getWorkTimeGroupByOperationAndCrewName(@RequestBody MaterialByCrewQueryDTO materialByCrewQueryDTO) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String sql = null;
        String siteNum = request.getHeader("siteNum");
        if (materialByCrewQueryDTO.getCellId() != null) {
            if (materialByCrewQueryDTO.getActualStartTime() != null && materialByCrewQueryDTO.getActualEndTime() != null) {
                sql = format("SELECT sum(work_time) as work_time ,max(m.operation_code) as operation_code ,m.operation_oid,m.crew_name \n" +
                                "           from work_time_crew m where m.cell_id ='%s' " +
                                "AND m.end_time >= '%s' AND  m.end_time <= '%s' group by m.crew_name,m.operation_oid;",
                        materialByCrewQueryDTO.getCellId(),
                        format.format(materialByCrewQueryDTO.getActualStartTime()),
                        format.format(materialByCrewQueryDTO.getActualEndTime())
                );
            } else {
                if (materialByCrewQueryDTO.getActualStartTime() == null && materialByCrewQueryDTO.getActualEndTime() == null) {
                    sql = format("SELECT sum(work_time) as work_time ,max(m.operation_code) as operation_code ,m.operation_oid,m.crew_name " +
                                    " from work_time_crew m where m.cell_id ='%s' group by m.crew_name,m" +
                                    ".operation_oid;",
                            materialByCrewQueryDTO.getCellId()
                    );
                }
            }
        } else {
            if (materialByCrewQueryDTO.getActualStartTime() != null && materialByCrewQueryDTO.getActualEndTime() != null) {
                sql = format("SELECT sum(work_time) as work_time ,max(m.operation_code) as operation_code ,m.operation_oid,m.crew_name " +
                                " from work_time_crew m where  " +
                                "m.end_time >= '%s' AND  m.end_time <= '%s' group by m.crew_name,m.operation_oid;",
                        format.format(materialByCrewQueryDTO.getActualStartTime()),
                        format.format(materialByCrewQueryDTO.getActualEndTime())
                );

            } else {
                if (materialByCrewQueryDTO.getActualStartTime() == null && materialByCrewQueryDTO.getActualEndTime() == null) {
                    sql = format("SELECT sum(work_time) as work_time ,max(m.operation_code) as operation_code ,m.operation_oid,m.crew_name " +
                            " from work_time_crew  group by m.crew_name,m.operation_oid;"
                    );
                }
            }
        }
        return jdbcOperations.queryForList(sql);

    }

    /**
     * 物料分析
     *
     * @param materialByCrewQueryDTO 查询条件
     * @return 物料分析数据
     */
    @ApiOperation(value = "根据实际开始结束时间和产线查询物料消耗(物料消耗分析)")
    @PostMapping(value = "/getMaterialConsume")
    public List<Map<String, Object>> getMaterialConsume(@RequestBody MaterialByCrewQueryDTO materialByCrewQueryDTO) {
        List<Map<String, Object>> maps = null;
        String siteNum = request.getHeader("siteNum");
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (materialByCrewQueryDTO.getCellId() != null) {
            if (materialByCrewQueryDTO.getActualStartTime() != null && materialByCrewQueryDTO.getActualEndTime() != null) {
                String sql = format("SELECT * FROM material_form  WHERE cell_id = '%s' AND " +
                                "actual_end_time >= '%s' AND  actual_end_time <= '%s' ; ",
                        materialByCrewQueryDTO.getCellId(),
                        format.format(materialByCrewQueryDTO.getActualStartTime()),
                        format.format(materialByCrewQueryDTO.getActualEndTime())
                );
                maps = jdbcOperations.queryForList(sql);
            } else {
                if (materialByCrewQueryDTO.getActualStartTime() == null && materialByCrewQueryDTO.getActualEndTime() == null) {
                    String sql = format("SELECT * FROM material_form m   WHERE cell_id = '%s';",
                            materialByCrewQueryDTO.getCellId()
                    );
                    maps = jdbcOperations.queryForList(sql);
                }
            }
        } else {
            if (materialByCrewQueryDTO.getActualStartTime() != null && materialByCrewQueryDTO.getActualEndTime() != null) {
                String sql = format("SELECT * FROM material_form   WHERE actual_end_time >= '%s' AND  actual_end_time" +
                                " <= '%s' ; ",
                        format.format(materialByCrewQueryDTO.getActualStartTime()),
                        format.format(materialByCrewQueryDTO.getActualEndTime())
                );
                maps = jdbcOperations.queryForList(sql);
            } else {
                if (materialByCrewQueryDTO.getActualStartTime() == null && materialByCrewQueryDTO.getActualEndTime() == null) {
                    String sql = "SELECT * FROM material_form ";
                    maps = jdbcOperations.queryForList(sql);
                }
            }


        }
        if (maps != null && maps.size() > 0) {
            maps.removeIf(map -> map.get("actual_count") == null);
        }
        return maps;
    }

}
