package com.hvisions.pms.repository;


import com.hvisions.pms.entity.HvPmWorkOrderComplete;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * <P>    <P>
 *
 * <AUTHOR>
 * @date 2024/12/10
 */
@Repository
public interface WorkOrderCompleteRepository extends JpaRepository<HvPmWorkOrderComplete, Long> {


    @Modifying
    @Query("DELETE FROM HvPmWorkOrderComplete w WHERE w.orderCode = :orderCode")
    void deleteByOrderCode(@Param("orderCode") String orderCode);

}
