package com.hvisions.pms.repository;

import com.hvisions.pms.entity.HvPmMaterialByCrew;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <p>Title: MaterialByCrewRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/19</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface MaterialByCrewRepository extends JpaRepository<HvPmMaterialByCrew, Integer> {


    /**
     * 根据产线开始结束时间查询物料产出
     *
     * @param actualStartTime 实际开始时间
     * @param actualEndTime   实际结束时间
     * @return 物料产出信息
     */
    @Query(value = "select m from  HvPmMaterialByCrew  m  where m.actualEndTime >= ?1 AND m.actualEndTime <= ?2 ")
    List<HvPmMaterialByCrew> getAllByDate(Date actualStartTime, Date actualEndTime);

    /**
     * 根据产线ID 查询物料产出数量
     *
     * @param cellId 产线ID
     * @return 物料产出数量
     */
    List<HvPmMaterialByCrew> getAllByCellId(int cellId);


}
