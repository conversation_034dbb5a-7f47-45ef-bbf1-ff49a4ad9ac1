package com.hvisions.pms.repository.plan;

import com.hvisions.pms.entity.plan.HvPmWorkPlan;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * <p>Title: HvPmWorkPlanRepository</p >
 * <p>Description: 生产计划 repository</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/14</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */

@Repository
public interface HvPmWorkPlanRepository extends JpaRepository<HvPmWorkPlan, Integer>, JpaSpecificationExecutor<HvPmWorkPlan> {


    @Override
    Page<HvPmWorkPlan> findAll(Specification<HvPmWorkPlan> spec, Pageable pageable);


    /**
     * 根据计划编码查询生产计划
     *
     * @param code 计划编码
     * @return 生产计划
     */
    HvPmWorkPlan getByPlanCode(String code);

    /**
     * 根据id列表查询生产计划
     *
     * @param idIn 根据ID列表查询生产计划
     * @return 生产计划列表
     */
    List<HvPmWorkPlan> getAllByIdIn(List<Integer> idIn);
}
