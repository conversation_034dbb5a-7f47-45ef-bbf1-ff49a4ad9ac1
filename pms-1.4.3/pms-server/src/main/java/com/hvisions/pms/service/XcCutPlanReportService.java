package com.hvisions.pms.service;

import com.hvisions.pms.dto.HomeSevenDataDTO;
import com.hvisions.pms.entity.CutPartsQueryDTO;
import com.hvisions.pms.entity.CuttingCountResultDTO;

import java.util.List;

/**
 * <P>    <P>
 *
 * <AUTHOR>
 * @date 2024/11/19
 */
public interface XcCutPlanReportService {

    Integer getOneDataXcCutByLineCode(String lineCode, String date);

    Integer getOneDataXcPlanCutByLineCode(String lineCode, String date);

    HomeSevenDataDTO getXcCutSevenDataByLineCode(String lineCode, String date);

    Integer getOneWeekXcCutByLineCode(String lineCode, String date);

    Integer getOneMonthXcCutByLineCode(String lineCode, String date);

    Integer getOneYearXcCutByLineCode(String lineCode, String date);

    Integer getOneWeekXcPlanCutByLineCode(String lineCode, String date);

    Integer getOneMonthXcPlanCutByLineCode(String lineCode, String date);

    Integer getOneYearXcPlanCutByLineCode(String lineCode, String date);

    Integer getOneDataXcCutPartByLineCode(String lineCode, String date);

    Integer getOneDataXcPlanCutPartByLineCode(String lineCode, String date);

    HomeSevenDataDTO getXcCutPartSevenDataByLineCode(String lineCode, String date);

    Integer getOneWeekXcCutPartByLineCode(String lineCode, String date);

    Integer getOneMonthXcCutPartByLineCode(String lineCode, String date);

    Integer getOneYearXcCutPartByLineCode(String lineCode, String date);

    Integer getOneWeekXcPlanCutPartByLineCode(String lineCode, String date);

    Integer getOneMonthXcPlanCutPartByLineCode(String lineCode, String date);

    Integer getOneYearXcPlanCutPartByLineCode(String lineCode, String date);

    Integer getCuttingCountDateRange(CutPartsQueryDTO cutPartsQueryDTO);

    List<CuttingCountResultDTO> getCuttingCountByDate(CutPartsQueryDTO cutPartsQueryDTO);

    Integer getCuttingPlanCountDateRange(CutPartsQueryDTO cutPartsQueryDTO);
}
