package com.hvisions.pms.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hvisions.pms.dto.HvPmCallMaterialDTO;
import com.hvisions.pms.entity.plan.HvPmCallFrameMaterial;
import com.hvisions.pms.service.HvPmCallFrameMaterialService;
import com.hvisions.thirdparty.common.dto.HvPmCallFrameMaterialDTO;
import com.hvisions.thirdparty.common.dto.LineSchedulingDTO;
import com.hvisions.thirdparty.common.dto.MaterialCuttingLineReportDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-05-27 14:57
 */


@RestController
@RequestMapping(value = "/HvPmCallFrameMaterial")
public class HvPmCallFrameMaterialController {

    @Autowired
    private HvPmCallFrameMaterialService frameMaterialService;

    /**
     * 分页查询
     *
     * @return Page<HvPmCallFrameMaterial>
     */
    @ApiOperation(value = "分页查询")
    @PostMapping(value = "/getPage")
    public Page<HvPmCallFrameMaterial> getPage(@RequestBody HvPmCallMaterialDTO callFrameMaterialDTO) {
        return frameMaterialService.getPage(callFrameMaterialDTO);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "/save")
    public void save(@RequestBody HvPmCallFrameMaterial callFrameMaterial) {
        frameMaterialService.save(callFrameMaterial);
    }

    @ApiOperation(value = "根据id删除")
    @PostMapping(value = "/removeById")
    public void removeById(@RequestBody HvPmCallFrameMaterial callFrameMaterial) {
        frameMaterialService.removeById(callFrameMaterial);
    }

    @ApiOperation(value = "根据id修改")
    @PostMapping(value = "/updateById")
    public void updateById(@RequestBody HvPmCallFrameMaterial callFrameMaterial) {
        frameMaterialService.updateById(callFrameMaterial);
    }

    @ApiOperation(value = "查询全部")
    @PostMapping(value = "/list")
    public List<HvPmCallFrameMaterial> list(@RequestBody HvPmCallFrameMaterial callFrameMaterial) {
        return frameMaterialService.list();
    }


    @ApiOperation(value = "根据报工更新线边料框")
    @PostMapping(value = "/updateFrameMaterial")
    public void updateFrameMaterial(@RequestBody MaterialCuttingLineReportDTO steelPlateDTO) {
        frameMaterialService.updateFrameMaterial(steelPlateDTO);
    }

    @ApiOperation(value = "保存出库的料框")
    @PostMapping(value = "/saveFrameMaterial")
    public void saveFrameMaterial(@RequestParam String frameCode) {
        frameMaterialService.saveFrameMaterial(frameCode);
    }

    @ApiOperation(value = "更新")
    @PostMapping(value = "/update")
    public void update(@RequestBody HvPmCallFrameMaterialDTO callFrameMaterialDTO) {
        frameMaterialService.updateByQuery(callFrameMaterialDTO);
    }

    @ApiOperation(value = "更新物料")
    @PostMapping(value = "/updateMaterialDTO")
    public void  updateMaterialDTO(@RequestBody LineSchedulingDTO lineSchedulingDTO){
        frameMaterialService.updateMaterialDTO(lineSchedulingDTO);
    }

    @ApiOperation(value = "更新料框状态")
    @PostMapping(value = "/updateStatus")
    public void updateStatus(@RequestParam String frameCode){
        frameMaterialService.updateStatus(frameCode);
    }

    @ApiOperation(value = "根据料框编号删除")
    @PostMapping(value = "/removeByFrameCode")
    public void removeByFrameCode(@RequestParam String frameCode) {
        LambdaQueryWrapper<HvPmCallFrameMaterial> lqw = new LambdaQueryWrapper<>();
        lqw.eq(HvPmCallFrameMaterial::getFrameCode, frameCode);
        frameMaterialService.remove(lqw);
    }

    ;

}
