package com.hvisions.pms.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.pms.entity.HvPmPlanProductBom;
import com.hvisions.pms.service.HvPmPlanProductBomService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-06
 */

@RestController
@RequestMapping(value = "/HvPmPlanProductBom")
@Api(description = "订单Bom与工艺")
public class HvPmPlanProductBomController {

    @Autowired
    private HvPmPlanProductBomService hvPmPlanProductBomService;


    /**
     * 分页查询
     */
    @ApiOperation("分页查询")
    @PostMapping("/list")
    public Page<HvPmPlanProductBom> list(@RequestBody HvPmPlanProductBom hvPmPlanProductBom,
                     @RequestParam(name="pageNum", defaultValue="1") Integer pageNo,
                     @RequestParam(name="pageSize", defaultValue="10") Integer pageSize)

    {
        LambdaQueryWrapper<HvPmPlanProductBom> lqw = new LambdaQueryWrapper<>();
        // 计划ID
        lqw.eq(hvPmPlanProductBom.getPlanId() != null,HvPmPlanProductBom::getPlanId, hvPmPlanProductBom.getPlanId());
        // 计划编号
        lqw.eq(!StringUtils.isEmpty(hvPmPlanProductBom.getPlanCode()),HvPmPlanProductBom::getPlanCode, hvPmPlanProductBom.getPlanCode());
        // 零件物料编号
        lqw.eq(!StringUtils.isEmpty(hvPmPlanProductBom.getMaterialCode()),HvPmPlanProductBom::getMaterialCode, hvPmPlanProductBom.getMaterialCode());
        // 船型
        lqw.eq(!StringUtils.isEmpty(hvPmPlanProductBom.getModel()),HvPmPlanProductBom::getModel, hvPmPlanProductBom.getModel());
        // 分段号
        lqw.eq(!StringUtils.isEmpty(hvPmPlanProductBom.getSegmentationCode()),HvPmPlanProductBom::getSegmentationCode, hvPmPlanProductBom.getSegmentationCode());
        // 工艺路线编号
        lqw.eq(!StringUtils.isEmpty(hvPmPlanProductBom.getRouteCode()),HvPmPlanProductBom::getRouteCode, hvPmPlanProductBom.getRouteCode());
        // 工艺路线版本
        lqw.eq(!StringUtils.isEmpty(hvPmPlanProductBom.getRouteVersion()),HvPmPlanProductBom::getRouteVersion, hvPmPlanProductBom.getRouteVersion());

        return hvPmPlanProductBomService.pageList(lqw,pageNo,pageSize);
    }

    /**
     * 添加
     *
     * @param hvPmPlanProductBom HvPmPlanProductBom
     */
    @ApiOperation(value = "添加HvPmPlanProductBom信息")
    @PostMapping(value = "/add")
    public Boolean addHvPmPlanProductBom(@RequestBody HvPmPlanProductBom hvPmPlanProductBom) {
        return hvPmPlanProductBomService.save(hvPmPlanProductBom);
    }

    /**
     * 批量添加
     *
     * @param hvPmPlanProductBomList HvPmPlanProductBomList
     */
    @ApiOperation(value = "添加HvPmPlanProductBom信息")
    @PostMapping(value = "/addList")
    public Integer addHvPmPlanProductBomList(@RequestBody List<HvPmPlanProductBom> hvPmPlanProductBomList) {
        return hvPmPlanProductBomService.InsertHvPmPlanProductBomList(hvPmPlanProductBomList);
    }

    /**
     * 删除
     *
     * @param id 主键
     */
    @ApiOperation(value = "删除HvPmPlanProductBom信息")
    @DeleteMapping(value = "/delete/{id}")
    public Boolean deleteHvPmPlanProductBom(@PathVariable Integer id) {
        return hvPmPlanProductBomService.removeById(id);
    }

    /**
     * 删除
     *
     * @param idList 主键
     */
    @ApiOperation(value = "批量删除HvPmPlanProductBom信息")
    @DeleteMapping(value = "/delete")
    public Boolean deleteHvPmPlanProductBom(@RequestBody List<Integer> idList) {
        return hvPmPlanProductBomService.removeByIds(idList);
    }

    /**
     * 修改
     *
     * @param hvPmPlanProductBom HvPmPlanProductBom
     */
    @ApiOperation(value = "修改HvPmAgvTaskRecord")
    @PutMapping(value = "/update")
    public Boolean updateHvPmAgvTaskRecord(@RequestBody HvPmPlanProductBom hvPmPlanProductBom) {
        return hvPmPlanProductBomService.updateById(hvPmPlanProductBom);
    }

    /**
     * 根据id获取
     *
     * @param id 主键
     * @return HvPmPlanProductBom HvPmPlanProductBom
     */
    @ApiOperation(value = "根据id获取HvPmPlanProductBom")
    @GetMapping(value = "/get/{id}")
    public HvPmPlanProductBom getList(@PathVariable Integer id) {
        return hvPmPlanProductBomService.getById(id);
    }

    /**
     * 根据工单编号删除HvPmPlanProductBom信息
     * @param planCode
     * @return
     */
    @ApiOperation("/根据工单编号删除HvPmPlanProductBom信息")
    @DeleteMapping("/deleteByPlanCode/{planCode}")
    public Boolean deleteByPlanCode(@PathVariable String planCode) {
       return hvPmPlanProductBomService.deleteByPlanCode(planCode);
    }

    /**
     * 根据工单号获取齐套时间
     */
    @ApiOperation("/根据工单号获取齐套时间")
    @GetMapping("/getCompleteTimeByPlanCode/{planCode}")
    public Date getCompleteTimeByPlanCode(@PathVariable String planCode) {
        return hvPmPlanProductBomService.getCompleteTimeByPlanCode(planCode);
    }


}
