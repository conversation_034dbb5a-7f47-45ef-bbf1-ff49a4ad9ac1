package com.hvisions.pms.service.imp;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.framework.client.DictionaryItemClient;
import com.hvisions.framework.dto.dictionary.DictionaryItemDTO;
import com.hvisions.hiperbase.client.LocationExtendClient;
import com.hvisions.hiperbase.client.MaterialClient;
import com.hvisions.hiperbase.client.ShiftClient;
import com.hvisions.hiperbase.equipment.location.LocationDTO;
import com.hvisions.hiperbase.materials.dto.MaterialDTO;
import com.hvisions.hiperbase.schedule.dto.ShiftDTO;
import com.hvisions.pms.consts.HvPmXcMaterialCutPlanConst;
import com.hvisions.pms.dao.HvPmXcMaterialCutPlanMapper;
import com.hvisions.pms.dto.HvPmMaterialPreparationDTO;
import com.hvisions.pms.dto.WorkOrderDTO;
import com.hvisions.pms.entity.HvPmWorkOrder;
import com.hvisions.pms.entity.plan.HvPmXcMaterialCutPlan;
import com.hvisions.pms.entity.plan.HvPmXcMaterialCutPlanDetail0;
import com.hvisions.pms.entity.plan.HvPmXcMaterialCutPlanDetail1;
import com.hvisions.pms.enums.CutPlanStatusEnum;
import com.hvisions.pms.enums.WorkOrderStateEnum;
import com.hvisions.pms.enums.XcCutPlanStatusEnum;
import com.hvisions.pms.exportdto.XcMaterialCutPlanDetail0ExportDTO;
import com.hvisions.pms.exportdto.XcMaterialCutPlanDetail1ExportDTO;
import com.hvisions.pms.exportdto.XcMaterialCutPlanExportDTO;
import com.hvisions.pms.importTemplate.xcMaterialCutPlanDetail0Template;
import com.hvisions.pms.importTemplate.xcMaterialCutPlanDetail1Template;
import com.hvisions.pms.importTemplate.xcMaterialCutPlanTemplate;
import com.hvisions.pms.plan.HvPmXcMaterialCutPlanDTO;
import com.hvisions.pms.plan.HvPmXcMaterialCutPlanDetail0DTO;
import com.hvisions.pms.plan.HvPmXcMaterialCutPlanDetail1DTO;
import com.hvisions.pms.plan.HvPmXcMaterialCutPlanTabQueryDTO;
import com.hvisions.pms.pools.CommonPoolExecutor;
import com.hvisions.pms.repository.WorkOrderRepository;
import com.hvisions.pms.repository.plan.HvPmXcMaterialCutPlanDetail0Repository;
import com.hvisions.pms.repository.plan.HvPmXcMaterialCutPlanDetail1Repository;
import com.hvisions.pms.repository.plan.HvPmXcMaterialCutPlanRepository;
import com.hvisions.pms.service.HvPmMaterialPreparationService;
import com.hvisions.pms.service.HvPmXcMaterialCutPlanDetail0Service;
import com.hvisions.pms.service.HvPmXcMaterialCutPlanDetail1Service;
import com.hvisions.pms.service.HvPmXcMaterialCutPlanService;
import com.hvisions.thirdparty.common.dto.ProfilePartsDTO;
import com.hvisions.thirdparty.common.dto.ProfilePartsGensDTO;
import com.hvisions.thirdparty.common.dto.ProfilePartsGensPartInfoDTO;
import com.hvisions.thridparty.client.MaterialCuttingLineClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/8/26
 */
@Service
@Slf4j
public class HvPmXcMaterialCutPlanServiceImpl implements HvPmXcMaterialCutPlanService {
    @Resource
    private HvPmXcMaterialCutPlanRepository hvPmXcMaterialCutPlanRepository;

    @Resource
    private HvPmXcMaterialCutPlanDetail0Repository hvPmXcMaterialCutPlanDetail0Repository;

    @Resource
    private HvPmXcMaterialCutPlanDetail1Repository hvPmXcMaterialCutPlanDetail1Repository;

    @Resource
    private HvPmXcMaterialCutPlanDetail0Service hvPmXcMaterialCutPlanDetail0Service;

    @Resource
    private HvPmXcMaterialCutPlanDetail1Service hvPmXcMaterialCutPlanDetail1Service;

    @Resource
    private LocationExtendClient locationExtendClient;

    @Resource
    private WorkOrderRepository workOrderRepository;

    @Autowired
    private HvPmXcMaterialCutPlanService hvPmXcMaterialCutPlanService;

    @Resource
    private MaterialCuttingLineClient materialCuttingLineClient;

    @Resource
    private ShiftClient shiftClient;

    @Resource
    private MaterialClient materialClient;

    @Resource
    private DictionaryItemClient dictionaryItemClient;

    @Resource
    private HvPmXcMaterialCutPlanMapper hvPmXcMaterialCutPlanMapper;

    @Autowired
    private HvPmMaterialPreparationService hvPmMaterialPreparationService;

    //分页查询
    @Override
    public Page<HvPmXcMaterialCutPlanDTO> getPage(HvPmXcMaterialCutPlanTabQueryDTO hvPmXcMaterialCutPlanTabQueryDTO) {
        Page<HvPmXcMaterialCutPlanDTO> page = PageHelperUtil.getPage(hvPmXcMaterialCutPlanMapper::getPage, hvPmXcMaterialCutPlanTabQueryDTO);
        //获取所有产线
        List<LocationDTO> lineList = locationExtendClient.getLocationListByType(40).getData();
        page.map(hvPmXcMaterialCutPlanDTO -> {
            for (LocationDTO locationDTO : lineList) {
                if (locationDTO.getId().equals(hvPmXcMaterialCutPlanDTO.getLineId())) {
                    hvPmXcMaterialCutPlanDTO.setLineName(locationDTO.getName());
                    break;
                }
            }
            return hvPmXcMaterialCutPlanDTO;
        });
        return page;
    }

    //查询是否存在工单号
    @Override
    public boolean isExistsOrderNo(String orderNo) {
        HvPmXcMaterialCutPlan hvPmXcMaterialCutPlan = hvPmXcMaterialCutPlanRepository.getByOrderNoEquals(orderNo);
        return hvPmXcMaterialCutPlan != null;
    }


    @Override
    public HvPmXcMaterialCutPlan getByOrderNoEquals(String orderNo) {
        return hvPmXcMaterialCutPlanRepository.getByOrderNoEquals(orderNo);
    }

    //新增型材切割计划
    @Override
    public long createXcCutPlan(HvPmXcMaterialCutPlanDTO hvPmXcMaterialCutPlanDTO) {
        //处理前端可能传过来 ISO 8601格式的时间字符串
        if(hvPmXcMaterialCutPlanDTO.getProdDate().contains("Z")){
            String time = checkIso8601Time(hvPmXcMaterialCutPlanDTO.getProdDate());
            hvPmXcMaterialCutPlanDTO.setProdDate(time);
        }
        HvPmXcMaterialCutPlan hvPmXcMaterialCutPlan = DtoMapper.convert(hvPmXcMaterialCutPlanDTO, HvPmXcMaterialCutPlan.class);
        hvPmXcMaterialCutPlan.setStatus(0);
        HvPmXcMaterialCutPlan materialCutPlan = hvPmXcMaterialCutPlanRepository.saveAndFlush(hvPmXcMaterialCutPlan);
        return materialCutPlan.getId();
    }

    //Iso8601时间格式转换
    public String checkIso8601Time(String isoTime) {
        System.out.println("ISO8601格式"+isoTime);
        // 解析ISO 8601格式的时间字符串
        Instant instant = Instant.parse(isoTime);
        // 将Instant对象转换为LocalDateTime，这里使用UTC时区
        // 如果需要考虑时区转换，可以使用ZoneId.of("时区名称")，例如ZoneId.of("Asia/Shanghai")
        LocalDateTime localDateTime = instant.atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime();
        // 定义日期时间格式器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 格式化日期时间
        String formattedDateTime = localDateTime.format(formatter);
        System.out.println("Formatted Date Time: " + formattedDateTime);
        return formattedDateTime;
    }

    private static ProfilePartsGensPartInfoDTO getProfilePartsGensPartInfoDTO(HvPmXcMaterialCutPlanDetail1 detail1DTO) {
        ProfilePartsGensPartInfoDTO partInfoDTO = new ProfilePartsGensPartInfoDTO();
        partInfoDTO.setWork_order_code(detail1DTO.getWorkOrderCode());
        partInfoDTO.setMaterialCode(detail1DTO.getMaterialCode());
        partInfoDTO.setMaterialName(detail1DTO.getMaterialName());
        partInfoDTO.setOutFlag(detail1DTO.getOutFlag());
        partInfoDTO.setSpecialPurchaseTypeCode(detail1DTO.getSpecialPurchaseTypeCode());
        partInfoDTO.setBlockCode(detail1DTO.getBlockCode());
        partInfoDTO.setSegmentationCode(detail1DTO.getSegmentationCode());
        return partInfoDTO;
    }

    private ProfilePartsDTO getProfilePartsDTO(String orderNo, String lineCode) {
        ProfilePartsDTO profileTask = new ProfilePartsDTO();
        profileTask.setId(UUID.randomUUID().toString().replace("-", ""));
        profileTask.setName("型材切割计划取消");
        profileTask.setTimestamp(System.currentTimeMillis());
        profileTask.setOrder_no(orderNo);
        // 根据线体编号 查询 工序编号
        ResultVO<List<DictionaryItemDTO>> outsourcingArea = dictionaryItemClient.findAll(lineCode);
        List<DictionaryItemDTO> itemDTOList = outsourcingArea.getData();
        if (itemDTOList.isEmpty()) {
            throw new BaseKnownException("字典未配置外发区库区类型：outsourcingArea");
        }
        for (DictionaryItemDTO dictionaryItemDTO : itemDTOList) {
            if (dictionaryItemDTO.getItemKey().equals(lineCode)) {
                profileTask.setStation_id(dictionaryItemDTO.getItemValue());
            }
        }
        return profileTask;
    }

    //根据套料系统下发添加型材切割任务
    @Override
    public void addXcMaterialCutPlan(List<HvPmXcMaterialCutPlanDTO> cutPlan) {

        Set<WorkOrderDTO> orderDTOSet = new HashSet<>();
        WorkOrderDTO workOrderDTO;
        for (HvPmXcMaterialCutPlanDTO hvPmXcMaterialCutPlanDTO : cutPlan) {
            //生成产线ID
            LocationDTO locationDTO = locationExtendClient.getLocationByCode(hvPmXcMaterialCutPlanDTO.getLineCode()).getData();
            if (locationDTO == null) {
                throw new BaseKnownException("线体编号：" + hvPmXcMaterialCutPlanDTO.getLineCode() + "未维护！");
            }
            hvPmXcMaterialCutPlanDTO.setLineId(locationDTO.getId());
            hvPmXcMaterialCutPlanDTO.setLineName(locationDTO.getName());
            //保存型材切割计划
            long cutPlanId = this.createXcCutPlan(hvPmXcMaterialCutPlanDTO);
            hvPmXcMaterialCutPlanDTO.setId(cutPlanId);


            if (hvPmXcMaterialCutPlanDTO.getDetailList() != null) {
                List<HvPmXcMaterialCutPlanDetail0DTO> detailList = hvPmXcMaterialCutPlanDTO.getDetailList();
                for (HvPmXcMaterialCutPlanDetail0DTO hvPmXcMaterialCutPlanDetail0DTO : detailList) {

                    hvPmXcMaterialCutPlanDetail0DTO.setOrderId(cutPlanId);
                    hvPmXcMaterialCutPlanDetail0DTO.setOrderNo(hvPmXcMaterialCutPlanDTO.getOrderNo());
                    long detail0Id =  hvPmXcMaterialCutPlanDetail0Service.createDetail0(hvPmXcMaterialCutPlanDetail0DTO);

                    if (hvPmXcMaterialCutPlanDetail0DTO.getDetail1List() != null) {
                        List<HvPmXcMaterialCutPlanDetail1DTO> detail1List = hvPmXcMaterialCutPlanDetail0DTO.getDetail1List();
                        for (HvPmXcMaterialCutPlanDetail1DTO hvPmXcMaterialCutPlanDetail1DTO : detail1List) {
                            workOrderDTO = new WorkOrderDTO();
                            workOrderDTO.setWorkOrderCode(hvPmXcMaterialCutPlanDetail1DTO.getWorkOrderCode());
                            workOrderDTO.setCellId(hvPmXcMaterialCutPlanDTO.getLineId());
                            orderDTOSet.add(workOrderDTO);

                            hvPmXcMaterialCutPlanDetail1DTO.setOrderId(cutPlanId);
                            hvPmXcMaterialCutPlanDetail1DTO.setOrderNo(hvPmXcMaterialCutPlanDTO.getOrderNo());
                            hvPmXcMaterialCutPlanDetail1DTO.setSubPlanId(detail0Id);
                            hvPmXcMaterialCutPlanDetail1DTO.setSubPlanNo(hvPmXcMaterialCutPlanDetail0DTO.getSubPlanNo());
                            if (hvPmXcMaterialCutPlanDetail1DTO.getMaterialName() == null) {
                                hvPmXcMaterialCutPlanDetail1DTO.setMaterialName(hvPmXcMaterialCutPlanDetail1DTO.getMaterialCode());
                            }
                            hvPmXcMaterialCutPlanDetail1Service.createDetail1(hvPmXcMaterialCutPlanDetail1DTO);
                        }
                    }

                }
            }

            //更新工单的产线
            for (WorkOrderDTO orderDTO : orderDTOSet) {
                HvPmWorkOrder workOrder = workOrderRepository.getHvPmWorkOrderByWorkOrderCode(orderDTO.getWorkOrderCode());
                if (workOrder == null) {
                    throw new BaseKnownException("工单号：" + orderDTO.getWorkOrderCode() + "未维护！");
                }
                workOrder.setCellId(orderDTO.getCellId());
                workOrderRepository.save(workOrder);
                //下发计划
//                if (WorkOrderStateEnum.NOT_ISSUED.getCode().equals(workOrder.getWorkOrderState())) {
//                    WorkIssuedDTO workIssuedDTO = new WorkIssuedDTO();
//                    workIssuedDTO.setOrderIds(Collections.singletonList(workOrder.getId()));
//                    workIssuedDTO.setWorkOrderDTO(DtoMapper.convert(workOrder, WorkOrderDTO.class));
//                    workOrderService.batchIssued(workIssuedDTO);
//                }
            }

            setMatPrepFlag(hvPmXcMaterialCutPlanDTO);

            //型材切割计划下发
            CommonPoolExecutor.execute(() -> {
                hvPmXcMaterialCutPlanService.sendXcOrder(hvPmXcMaterialCutPlanDTO, null);
            });
        }
    }

    private String setMatPrepFlag(HvPmXcMaterialCutPlanDTO hvPmXcMaterialCutPlanDTO) {
        HvPmMaterialPreparationDTO preparationByWorkOrderCode = hvPmMaterialPreparationService.getPreparationByWorkOrderCode(hvPmXcMaterialCutPlanDTO.getOrderNo());
        if (preparationByWorkOrderCode != null) {
            if (preparationByWorkOrderCode.getStatus() == 3) {
                hvPmXcMaterialCutPlanDTO.setMatPrepFlag("2");
            } else {
                hvPmXcMaterialCutPlanDTO.setMatPrepFlag("1");
            }
        } else
            hvPmXcMaterialCutPlanDTO.setMatPrepFlag("0");
        return hvPmXcMaterialCutPlanDTO.getMatPrepFlag();
    }

    //修改型材切割计划
    @Override
    public long updateXcCutPlan(HvPmXcMaterialCutPlanDTO hvPmXcMaterialCutPlanDTO) {
        String orderNo = hvPmXcMaterialCutPlanDTO.getOrderNo();
        String lineCode = hvPmXcMaterialCutPlanDTO.getLineCode();
        HvPmXcMaterialCutPlan materialCutPlan = null;
        ProfilePartsDTO profileTask = getProfilePartsDTO(orderNo, lineCode);
        ResultVO<LocationDTO> locationByCode = locationExtendClient.getLocationByCode(hvPmXcMaterialCutPlanDTO.getLineCode());
        Integer sendLineId = locationByCode.getData().getId();

        ResultVO resultVO = materialCuttingLineClient.cancelProfileTask(profileTask, sendLineId);
        if (resultVO.isSuccess()) {
            //处理前端可能传过来 ISO 8601格式的时间字符串
            if (hvPmXcMaterialCutPlanDTO.getProdDate().contains("Z")) {
                String time = checkIso8601Time(hvPmXcMaterialCutPlanDTO.getProdDate());
                hvPmXcMaterialCutPlanDTO.setProdDate(time);
            }
            HvPmXcMaterialCutPlan hvPmXcMaterialCutPlan = DtoMapper.convert(hvPmXcMaterialCutPlanDTO, HvPmXcMaterialCutPlan.class);
            LocationDTO locationDTO = locationExtendClient.getLocationById(hvPmXcMaterialCutPlan.getLineId()).getData();
            hvPmXcMaterialCutPlan.setLineCode(locationDTO.getCode());
            materialCutPlan = hvPmXcMaterialCutPlanRepository.saveAndFlush(hvPmXcMaterialCutPlan);
            String s = setMatPrepFlag(hvPmXcMaterialCutPlanDTO);
            hvPmXcMaterialCutPlanDTO.setLineCode(locationDTO.getCode());
            if (s.equals("0")) {
                hvPmXcMaterialCutPlan.setStatus(1);
            } else if (s.equals("1")) {
                hvPmXcMaterialCutPlan.setStatus(3);
            } else if (s.equals("2")) {
                hvPmXcMaterialCutPlan.setStatus(4);
            }

            hvPmXcMaterialCutPlanService.sendXcOrder(hvPmXcMaterialCutPlanDTO, null);

        } else {
            throw new BaseKnownException("修改失败！错误信息为：" + resultVO.getMessage());
        }


        if (materialCutPlan != null) {
            return materialCutPlan.getId();
        }
        return 0;
    }

    @Override
    public ResultVO<?> sendXcOrder(HvPmXcMaterialCutPlanDTO hvPmXcMaterialCutPlan, UserInfoDTO userInfo) {
        ProfilePartsDTO profilePart = new ProfilePartsDTO();

        profilePart.setId(UUID.randomUUID().toString().replace("-", ""));
        profilePart.setName("型材切割计划下发");
        profilePart.setTimestamp(System.currentTimeMillis());

        // 根据线体编号 查询 工序编号
        ResultVO<List<DictionaryItemDTO>> outsourcingArea = dictionaryItemClient.findAll(hvPmXcMaterialCutPlan.getLineCode());
        List<DictionaryItemDTO> itemDTOList = outsourcingArea.getData();
        if (itemDTOList.isEmpty()) {
            throw new BaseKnownException("字典未配置外发区库区类型：outsourcingArea");
        }
        for (DictionaryItemDTO dictionaryItemDTO : itemDTOList) {
            if(dictionaryItemDTO.getItemKey().equals(hvPmXcMaterialCutPlan.getLineCode())){
                profilePart.setStation_id(dictionaryItemDTO.getItemValue());
            }
        }

        //工单号
        profilePart.setOrder_no(hvPmXcMaterialCutPlan.getOrderNo());
        //工单类型
        profilePart.setOrderType("1");
        //船型
        profilePart.setShipmode(hvPmXcMaterialCutPlan.getShipMode());
        //船号
        profilePart.setShipcode(hvPmXcMaterialCutPlan.getShipCode());
        //分段号
        profilePart.setSegmentationCode(hvPmXcMaterialCutPlan.getSegmentationCode());
        //计划完成时间
        profilePart.setProd_date(hvPmXcMaterialCutPlan.getProdDate());
        if (hvPmXcMaterialCutPlan.getMatPrepFlag() == null) {
            profilePart.setMatPrepFlag(setMatPrepFlag(hvPmXcMaterialCutPlan));
        } else if (!hvPmXcMaterialCutPlan.getMatPrepFlag().isEmpty()) {
            profilePart.setMatPrepFlag(hvPmXcMaterialCutPlan.getMatPrepFlag());
        }
        String workOrder = null;
        List<ProfilePartsGensDTO> gensList = new ArrayList<>();
        //型材零件工单集合
        List<HvPmWorkOrder> workOrderList = new ArrayList<>();

        //查询gen文件信息
        List<HvPmXcMaterialCutPlanDetail0> detail0S = hvPmXcMaterialCutPlanDetail0Repository.getAllByOrderId(hvPmXcMaterialCutPlan.getId());
        if (detail0S.isEmpty()) {
            throw new BaseKnownException("切割计划编号为："+hvPmXcMaterialCutPlan.getOrderNo() +"的gen文件信息列表为空！无法下发！");
        }

        //组装 gen文件信息列表数据
        for(HvPmXcMaterialCutPlanDetail0 detail0DTO :detail0S){
            ProfilePartsGensDTO profilePartsGensDTO = new ProfilePartsGensDTO();
            profilePartsGensDTO.setSubplanno(detail0DTO.getSubPlanNo());
            profilePartsGensDTO.setQty(detail0DTO.getQty());
            profilePartsGensDTO.setGenfilepath(detail0DTO.getGenFilePath());
            profilePartsGensDTO.setMaterialCode(detail0DTO.getMaterialCode());
            List<ProfilePartsGensPartInfoDTO> partInfoList = new ArrayList<>();

            //查询零件信息
            List<HvPmXcMaterialCutPlanDetail1> detail1S = hvPmXcMaterialCutPlanDetail1Repository.getAllBySubPlanId(detail0DTO.getId());
            if (detail1S.isEmpty()) {
                throw new BaseKnownException("切割计划编号为："+hvPmXcMaterialCutPlan.getOrderNo() +"的零件为空！无法下发！");
            }

            //组装 零件信息数据
            for(HvPmXcMaterialCutPlanDetail1 detail1DTO :detail1S){
                //根据物料编码查询物料
                ResultVO<MaterialDTO> material = materialClient.getMaterialByMaterialCodeAndEigenvalue(detail1DTO.getMaterialCode(), "1");
                if (material.getData() == null) {
                    throw new BaseKnownException("切割计划：" + detail0DTO.getOrderNo() + "的物料：" + detail1DTO.getMaterialCode() + "不存在");
                }
                HvPmWorkOrder order = workOrderRepository.getHvPmWorkOrderByWorkOrderCode(detail1DTO.getWorkOrderCode());
                if (order == null) {
                    throw new BaseKnownException("工单号：" + workOrder + "的工单信息不存在！");
                }
                workOrderList.add(order);
                ProfilePartsGensPartInfoDTO partInfoDTO = getProfilePartsGensPartInfoDTO(detail1DTO);
                partInfoList.add(partInfoDTO);
                workOrder = detail1DTO.getWorkOrderCode();
            }
            profilePartsGensDTO.setPartInfo(partInfoList);
            gensList.add(profilePartsGensDTO);
        }

        //班次
        if (!StringUtils.isBlank(workOrder)) {
            HvPmWorkOrder order = workOrderRepository.getHvPmWorkOrderByWorkOrderCode(workOrder);
            if (order == null) {
                throw new BaseKnownException("工单号：" + workOrder + "的工单信息不存在！");
            }
            if (order.getShiftId() == null) {
                throw new BaseKnownException("工单号：" + workOrder + "的工单的班次未配置（未下发！）");
            }
            ResultVO<ShiftDTO> shiftVo = shiftClient.getShiftById(order.getShiftId());
            profilePart.setBc(shiftVo.getData().getShiftCode());
        }
        //gen文件列表
        profilePart.setGens(gensList);

        //调用第三方接口-型材切割计划下发
        ResultVO<?> v = materialCuttingLineClient.sendXcOrder(profilePart);
        if (userInfo != null) {
            hvPmXcMaterialCutPlan.setSendUserId(userInfo.getId());
        }
        if (v.getCode() != null && v.getCode() == 201) {
            return v;
        }
        if (v.isSuccess()) {
            // 处理成功的情况
            hvPmXcMaterialCutPlan.setStatus(CutPlanStatusEnum.SEND_OK.getCode());
            hvPmXcMaterialCutPlan.setSendTime(new Date());
            //修改工单状态
            updateWorkOrderState(workOrderList);
        } else {
            // 处理失败的情况
            hvPmXcMaterialCutPlan.setStatus(CutPlanStatusEnum.SEND_ERROR.getCode());
            hvPmXcMaterialCutPlan.setSendTime(new Date());
            log.error("切割线任务下发失败！" + "切割编码为" + hvPmXcMaterialCutPlan.getOrderNo() + ";" + "原因：" + v.getMessage());
        }
        hvPmXcMaterialCutPlanRepository.save(DtoMapper.convert(hvPmXcMaterialCutPlan, HvPmXcMaterialCutPlan.class));
        return v;
    }

    //修改工单状态
    public void updateWorkOrderState(List<HvPmWorkOrder> workOrderList){
        if (!workOrderList.isEmpty()){
            for (HvPmWorkOrder workOrder : workOrderList) {
                workOrderRepository.updateWorkOrderState(WorkOrderStateEnum.ALREADY_ISSUED.getCode(), workOrder.getWorkOrderCode(),new Date());
            }
        }
    }


    //删除型材切割计划和从表数据
    @Override
    @Transactional
    public void deleteXcCutPlanById(long id) {
        HvPmXcMaterialCutPlan one = hvPmXcMaterialCutPlanRepository.getOne(id);

        ProfilePartsDTO profileTask = getProfilePartsDTO(one.getOrderNo(), one.getLineCode());

        ResultVO resultVO = materialCuttingLineClient.cancelProfileTask(profileTask, one.getLineId());
        if (resultVO.isSuccess()) {
            List<HvPmXcMaterialCutPlanDetail0> detail0s = hvPmXcMaterialCutPlanDetail0Repository.getAllByOrderId(id);
            if (detail0s.size() > 0) {
                for (HvPmXcMaterialCutPlanDetail0 detail0 : detail0s) {
                    hvPmXcMaterialCutPlanDetail1Repository.deleteBySubPlanId(detail0.getId());
                }
            }
            hvPmXcMaterialCutPlanDetail0Repository.deleteByOrderId(id);
            hvPmXcMaterialCutPlanRepository.deleteById(id);
        } else {
            throw new BaseKnownException("删除失败！错误信息为：" + resultVO.getMessage());
        }
    }

    //获取导入模板
    @Override
    public ResultVO<ExcelExportDto> getImportTemplate() throws IOException, IllegalAccessException {
        HSSFWorkbook hssfWorkbook = new HSSFWorkbook();
        List<HvPmXcMaterialCutPlanDTO> hvPmXcMaterialCutPlanDTOS = new ArrayList<>();
        List<HvPmXcMaterialCutPlanDetail0DTO> hvPmXcMaterialCutPlanDetail0DTOS = new ArrayList<>();
        List<HvPmXcMaterialCutPlanDetail1DTO> hvPmXcMaterialCutPlanDetail1DTOS = new ArrayList<>();
        ExcelUtil.addSheetToWorkBook(hvPmXcMaterialCutPlanDTOS, "MaterialCutPlan", xcMaterialCutPlanTemplate.class, null, hssfWorkbook);
        ExcelUtil.addSheetToWorkBook(hvPmXcMaterialCutPlanDetail0DTOS, "Gens", xcMaterialCutPlanDetail0Template.class, null, hssfWorkbook);
        ExcelUtil.addSheetToWorkBook(hvPmXcMaterialCutPlanDetail1DTOS, "PartInfo", xcMaterialCutPlanDetail1Template.class, null, hssfWorkbook);
        ResponseEntity<byte[]> responseEntity = ExcelUtil.generateHttpExcelFile(hssfWorkbook, "xcMaterialCutPlanTemplate.xls");
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(responseEntity.getBody());
        excelExportDto.setFileName("xcMaterialCutPlanTemplate.xls");
        return ResultVO.success(excelExportDto);
    }

    //导入切割计划
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResult importXcMaterialCutPlan(MultipartFile file) throws IllegalAccessException, ParseException, IOException {
        //获取所有产线
        List<LocationDTO> lineList = locationExtendClient.getLocationListByType(40).getData();
        HashMap<String, Integer> map = new HashMap<>();
        for (LocationDTO locationDTO : lineList) {
            map.put(locationDTO.getCode(), locationDTO.getId());
        }
        // 导入切割计划
        List<HvPmXcMaterialCutPlanDTO> hvPmXcMaterialCutPlanDTOS = ExcelUtil.getEntityList(file, 0, HvPmXcMaterialCutPlanDTO.class);
        if (hvPmXcMaterialCutPlanDTOS.size() > 0) {
            for (HvPmXcMaterialCutPlanDTO dto : hvPmXcMaterialCutPlanDTOS) {
                if (dto.getOrderNo() != null && !dto.getOrderNo().equals("")) {
                    dto.setLineId(map.get(dto.getLineCode()));
                    createXcCutPlan(dto);
                }
            }
        }
        // 导入gen信息
        List<HvPmXcMaterialCutPlanDetail0DTO> detail0DTOS = ExcelUtil.getEntityList(file, 1, HvPmXcMaterialCutPlanDetail0DTO.class);
        if (detail0DTOS.size() > 0) {
            for (HvPmXcMaterialCutPlanDetail0DTO detail0DTO : detail0DTOS) {
                if (detail0DTO.getOrderNo() != null && !detail0DTO.getOrderNo().equals("")) {
                    HvPmXcMaterialCutPlan cutPlan = hvPmXcMaterialCutPlanRepository.getByOrderNoEquals(detail0DTO.getOrderNo());
                    if (cutPlan == null) {
                        throw new BaseKnownException("导入失败。gen中切割计划子编号" + detail0DTO.getSubPlanNo() + "对应的作业号:" + detail0DTO.getOrderNo() + "在型材切割计划中不存在");
                    }
                    detail0DTO.setOrderId(cutPlan.getId());
                    hvPmXcMaterialCutPlanDetail0Service.createDetail0(detail0DTO);
                }
            }
        }
        // 导入零件信息
        List<HvPmXcMaterialCutPlanDetail1DTO> detail1DTOS = ExcelUtil.getEntityList(file, 2, HvPmXcMaterialCutPlanDetail1DTO.class);
        if (detail1DTOS.size() > 0) {
            for (HvPmXcMaterialCutPlanDetail1DTO detail1DTO : detail1DTOS) {
                if (detail1DTO.getSubPlanNo() != null && !detail1DTO.getSubPlanNo().equals("")) {
                    HvPmXcMaterialCutPlan cutPlan = hvPmXcMaterialCutPlanRepository.getByOrderNoEquals(detail1DTO.getOrderNo());
                    HvPmXcMaterialCutPlanDetail0DTO detail0DTO = hvPmXcMaterialCutPlanDetail0Repository.getBySubPlanNoEquals(detail1DTO.getSubPlanNo());
                    if (cutPlan == null) {
                        throw new BaseKnownException("导入失败。零件信息数据中零件号" + detail1DTO.getMaterialCode() + "对应的切割计划编号:" + detail1DTO.getOrderNo() + "在切割计划中不存在");
                    }
                    if (detail0DTO == null) {
                        throw new BaseKnownException("导入失败。零件信息数据中零件号" + detail1DTO.getMaterialCode() + "对应的切割计划子编号:" + detail1DTO.getSubPlanNo() + "在切割计划中不存在");
                    }
                    detail1DTO.setOrderId(cutPlan.getId());
                    detail1DTO.setSubPlanId(detail0DTO.getId());
                    hvPmXcMaterialCutPlanDetail1Service.createDetail1(detail1DTO);
                }
            }
        }
        return new ImportResult();
    }

    /**
     * 导出型材切割计划
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    @Override
    public ResultVO<ExcelExportDto> exportXcMaterialCutPlan(HvPmXcMaterialCutPlanTabQueryDTO hvPmXcMaterialCutPlanTabQueryDTO) throws IOException, IllegalAccessException {
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(geteExportInfo(hvPmXcMaterialCutPlanTabQueryDTO).getBody());
        excelExportDto.setFileName( HvPmXcMaterialCutPlanConst.PLAN_EXPORT_FILE_NAME);
        return ResultVO.success(excelExportDto);
    }

    //获取需要导出的数据内容
//    @Override
    @ApiIgnore
    public ResponseEntity<byte[]> geteExportInfo(HvPmXcMaterialCutPlanTabQueryDTO queryDTO) throws IOException, IllegalAccessException {
        // 获取型材切割计划主表列表
        List<HvPmXcMaterialCutPlanDTO> xcMaterialCutPlanDTOList = hvPmXcMaterialCutPlanMapper.getPage(queryDTO);
        //获取型材切割计划id列表
        List<Long> orderIds = xcMaterialCutPlanDTOList.stream()
                .map(HvPmXcMaterialCutPlanDTO::getId)
                .collect(Collectors.toList());
        
        //型材切割计划
        List<XcMaterialCutPlanExportDTO> hvPmXcMaterialCutPlanDTOS = DtoMapper.convertList(xcMaterialCutPlanDTOList, XcMaterialCutPlanExportDTO.class);

        //设置状态描述
        for (XcMaterialCutPlanExportDTO xcMaterialCutPlanExportDTO : hvPmXcMaterialCutPlanDTOS) {
            if(xcMaterialCutPlanExportDTO.getStatus()!=null  ){
                String name = XcCutPlanStatusEnum.getEnumByCode(xcMaterialCutPlanExportDTO.getStatus()).getName();
                xcMaterialCutPlanExportDTO.setStatusDes(name);
            }else{
                xcMaterialCutPlanExportDTO.setStatusDes("未知状态");
            }
        }

        // gen文件列表
        List<XcMaterialCutPlanDetail0ExportDTO> hvPmXcMaterialCutPlanDetail0DTOS = DtoMapper.convertList(hvPmXcMaterialCutPlanDetail0Service.getDetail0ListByOrderIds(orderIds), XcMaterialCutPlanDetail0ExportDTO.class);
        // 零件信息列表
        List<XcMaterialCutPlanDetail1ExportDTO> hvPmXcMaterialCutPlanDetail1DTOS = DtoMapper.convertList(hvPmXcMaterialCutPlanDetail1Service.getDetail1ListByOrderIds(orderIds), XcMaterialCutPlanDetail1ExportDTO.class);

        HSSFWorkbook hssfWorkbook = new HSSFWorkbook();
        ExcelUtil.addSheetToWorkBook(hvPmXcMaterialCutPlanDTOS, HvPmXcMaterialCutPlanConst.PLAN_EXPORT_SHEET_NAME, XcMaterialCutPlanExportDTO.class,null, hssfWorkbook);
        ExcelUtil.addSheetToWorkBook(hvPmXcMaterialCutPlanDetail0DTOS, HvPmXcMaterialCutPlanConst.PLAN_DETAIL0_EXPORT_SHEET_NAME,XcMaterialCutPlanDetail0ExportDTO.class,null, hssfWorkbook);
        ExcelUtil.addSheetToWorkBook(hvPmXcMaterialCutPlanDetail1DTOS,  HvPmXcMaterialCutPlanConst.PLAN_DETAIL1_EXPORT_SHEET_NAME,XcMaterialCutPlanDetail1ExportDTO.class,null, hssfWorkbook);
        return ExcelUtil.generateHttpExcelFile(hssfWorkbook, HvPmXcMaterialCutPlanConst.PLAN_EXPORT_FILE_NAME);

    }


}
