package com.hvisions.pms.service.imp;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.client.*;
import com.hvisions.hiperbase.equipment.location.LocationDTO;
import com.hvisions.hiperbase.materials.dto.MaterialDTO;
import com.hvisions.hiperbase.materials.dto.MaterialGroupDTO;
import com.hvisions.hiperbase.materials.dto.MaterialTypeDTO;
import com.hvisions.hiperbase.schedule.dto.ShiftDTO;
import com.hvisions.pms.consts.HvPmMaterialCutPlanConst;
import com.hvisions.pms.dao.HvPmMaterialCutPlanMapper;
import com.hvisions.pms.dao.OrderMapper;
import com.hvisions.pms.dto.WorkIssuedDTO;
import com.hvisions.pms.dto.WorkOrderDTO;
import com.hvisions.pms.entity.HvPmWorkOrder;
import com.hvisions.pms.entity.plan.HvPmMaterialCutPlan;
import com.hvisions.pms.entity.plan.HvPmMaterialCutPlanDetail0;
import com.hvisions.pms.entity.plan.HvPmMaterialCutPlanDetail1;
import com.hvisions.pms.enums.CutPlanStatusEnum;
import com.hvisions.pms.enums.WorkOrderStateEnum;
import com.hvisions.pms.exportdto.MaterialCutPlanDetail0ExportDTO;
import com.hvisions.pms.exportdto.MaterialCutPlanDetail1ExportDTO;
import com.hvisions.pms.exportdto.MaterialCutPlanDetail2ExportDTO;
import com.hvisions.pms.exportdto.MaterialCutPlanExportDTO;
import com.hvisions.pms.importTemplate.MaterialCutPlanDetail0Template;
import com.hvisions.pms.importTemplate.MaterialCutPlanDetail1Template;
import com.hvisions.pms.importTemplate.MaterialCutPlanTemplate;
import com.hvisions.pms.plan.HvPmMaterialCutPlanDTO;
import com.hvisions.pms.plan.HvPmMaterialCutPlanDetail0DTO;
import com.hvisions.pms.plan.HvPmMaterialCutPlanDetail1DTO;
import com.hvisions.pms.plan.HvPmMaterialCutPlanQueryDTO;
import com.hvisions.pms.repository.WorkOrderRepository;
import com.hvisions.pms.repository.plan.HvPmMaterialCutPlanDetail0Repository;
import com.hvisions.pms.repository.plan.HvPmMaterialCutPlanDetail1Repository;
import com.hvisions.pms.repository.plan.HvPmMaterialCutPlanDetail2Repository;
import com.hvisions.pms.repository.plan.HvPmMaterialCutPlanRepository;
import com.hvisions.pms.service.*;
import com.hvisions.thirdparty.common.dto.*;
import com.hvisions.thridparty.client.SteelPlateLineClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class HvPmMaterialCutPlanServiceImpl implements HvPmMaterialCutPlanService {

    @Autowired
    private HvPmMaterialCutPlanRepository hvPmMaterialCutPlanRepository;
    @Autowired
    private HvPmMaterialCutPlanMapper hvPmMaterialCutPlanMapper;
    @Autowired
    private SteelPlateLineClient steelPlateLineClient;
    @Resource
    private MaterialClient materialClient;
    @Resource
    private MaterialGroupClient materialGroupClient;
    @Resource
    private HvPmMaterialCutPlanDetail1Repository hvPmMaterialCutPlanDetail1Repository;
    @Resource
    private HvPmMaterialCutPlanDetail0Repository hvPmMaterialCutPlanDetail0Repository;
    @Autowired
    private HvPmMaterialCutPlanDetail0Service hvPmMaterialCutPlanDetail0Service;
    @Autowired
    private HvPmMaterialCutPlanDetail1Service hvPmMaterialCutPlanDetail1Service;
    @Autowired
    private HvPmMaterialCutPlanDetail2Service hvPmMaterialCutPlanDetail2Service;
    @Autowired
    private LocationExtendClient locationExtendClient;
    @Resource
    private ShiftClient shiftClient;
    @Resource
    private WorkOrderRepository workOrderRepository;
    @Resource
    private OrderMapper orderMapper;
    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private MaterialTypeClient materialTypeClient;
    @Resource
    private HvPmMaterialCutPlanDetail2Repository hvPmMaterialCutPlanDetail2Repository;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public Page<HvPmMaterialCutPlanDTO> getPage(HvPmMaterialCutPlanQueryDTO hvPmMaterialCutPlanQueryDTO) {
        Page<HvPmMaterialCutPlanDTO> page = PageHelperUtil.getPage(hvPmMaterialCutPlanMapper::getPage, hvPmMaterialCutPlanQueryDTO);
        //获取所有产线
        List<LocationDTO> lineList = locationExtendClient.getLocationListByType(40).getData();
        page.map(hvPmMaterialCutPlanDTO -> {
            for (LocationDTO locationDTO : lineList) {
                if (locationDTO.getId().equals(hvPmMaterialCutPlanDTO.getLineId())) {
                    hvPmMaterialCutPlanDTO.setLineName(locationDTO.getName());
                    break;
                }
            }
            return hvPmMaterialCutPlanDTO;
        });
        return page;
    }

    @Override
    public long createCutPlan(HvPmMaterialCutPlanDTO hvPmMaterialCutPlanDTO) {
        HvPmMaterialCutPlan hvPmMaterialCutPlan = DtoMapper.convert(hvPmMaterialCutPlanDTO, HvPmMaterialCutPlan.class);
        hvPmMaterialCutPlan.setStatus(0);

        HvPmMaterialCutPlan materialCutPlan = hvPmMaterialCutPlanRepository.saveAndFlush(hvPmMaterialCutPlan);
        return materialCutPlan.getId();
    }

    @Override
    public long updateCutPlan(HvPmMaterialCutPlanDTO hvPmMaterialCutPlanDTO) {
        HvPmMaterialCutPlan hvPmMaterialCutPlan = DtoMapper.convert(hvPmMaterialCutPlanDTO, HvPmMaterialCutPlan.class);
        HvPmMaterialCutPlan materialCutPlan = hvPmMaterialCutPlanRepository.save(hvPmMaterialCutPlan);
        return materialCutPlan.getId();
    }

    //修改并取消零件切割任务
    @Override
    public ResultVO<?> updateCutPlanAndCancel(HvPmMaterialCutPlanDTO hvPmMaterialCutPlanDTO) {

        //调用零件切割任务取消
        ResultVO<?> resultVO = cutTaskCancel(hvPmMaterialCutPlanDTO.getLineId(),"2",hvPmMaterialCutPlanDTO.getCutPlanCode());
        if (resultVO.isSuccess()){
            //根据产线id查询产线编码
            ResultVO<LocationDTO> locationDTOResultVO = locationExtendClient.getLineById(hvPmMaterialCutPlanDTO.getLineId());
            //设置产线编码
            if (locationDTOResultVO.isSuccess() && locationDTOResultVO.getData().getCode() !=null ){
                hvPmMaterialCutPlanDTO.setLineCode(locationDTOResultVO.getData().getCode());
            }
            //允许修改
            HvPmMaterialCutPlan hvPmMaterialCutPlan = DtoMapper.convert(hvPmMaterialCutPlanDTO, HvPmMaterialCutPlan.class);
            hvPmMaterialCutPlanRepository.save(hvPmMaterialCutPlan);
        }
        return resultVO;
    }

    @Override
    @Transactional
    public ResultVO<?>  deleteCutPlanById(long id) {
        //查询切割计划
        HvPmMaterialCutPlan cutPlan = hvPmMaterialCutPlanRepository.getOne(id);

        if (cutPlan ==null){
            throw new BaseKnownException("没有找到切割id为"+id+"的切割任务");
        }

        //调用零件切割任务取消
        ResultVO<?> resultVO = cutTaskCancel(cutPlan.getLineId(),"1",cutPlan.getCutPlanCode());
        if (resultVO.isSuccess()){
            //允许删除
            hvPmMaterialCutPlanDetail0Repository.deleteByCutPlanId(id);
            hvPmMaterialCutPlanDetail1Repository.deleteByCutPlanId(id);
            hvPmMaterialCutPlanRepository.deleteById(id);
        }
        return resultVO;
    }

    @Override
    public boolean isExistsCutPlanCode(String cutPlanCode) {
        HvPmMaterialCutPlan materialCutPlan = hvPmMaterialCutPlanRepository.getByCutPlanCodeEquals(cutPlanCode);
        return materialCutPlan != null;
    }

    @Override
    public ResultVO<?> sendOrder(HvPmMaterialCutPlanDTO hvPmMaterialCutPlan, UserInfoDTO userInfo) {
        // 1. 初始化钢板部件DTO
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        SteelPlatePartsDTO steelPlatePart = new SteelPlatePartsDTO();
        steelPlatePart.setCode(hvPmMaterialCutPlan.getCutPlanCode());
        steelPlatePart.setSteel_code(hvPmMaterialCutPlan.getMaterialType());
        steelPlatePart.setSteel_length(hvPmMaterialCutPlan.getLength());
        steelPlatePart.setSteel_width(hvPmMaterialCutPlan.getWidth());
        steelPlatePart.setSteel_thickness(hvPmMaterialCutPlan.getThickness());
        steelPlatePart.setIs_surplus(hvPmMaterialCutPlan.getSurplusMaterialFlag());
        steelPlatePart.setQty("1");
        steelPlatePart.setCutting_draw(hvPmMaterialCutPlan.getTaoFilePath());
        steelPlatePart.setMarking_file_path(hvPmMaterialCutPlan.getMarkingFilePath());
        steelPlatePart.setCutting_program(hvPmMaterialCutPlan.getCutFilePath());
        steelPlatePart.setPdfFilePath(hvPmMaterialCutPlan.getPdfFilePath());
        steelPlatePart.setScrap_weight(hvPmMaterialCutPlan.getScrapWeight());
        steelPlatePart.setProd_date(simpleDateFormat.format(hvPmMaterialCutPlan.getPlanEndTime()));
        steelPlatePart.setPriority(String.valueOf(hvPmMaterialCutPlan.getSequence()));
        steelPlatePart.setStore_code("0000");
        steelPlatePart.setSegmentationCode(hvPmMaterialCutPlan.getSegmentationCode());
        steelPlatePart.setShipNo(hvPmMaterialCutPlan.getShipNumber());
        //线体编号
        ResultVO<LocationDTO> line = locationExtendClient.getLocationById(hvPmMaterialCutPlan.getLineId());
        if (!line.isSuccess()) {
            throw new BaseKnownException("该产线不存在");
        }
        steelPlatePart.setLineCode(line.getData().getCode());
        // 2. 工单信息处理（流式优化）
        String workOrder = hvPmMaterialCutPlan.getDetailList().stream()
                .map(HvPmMaterialCutPlanDetail0DTO::getWorkOrder)
                .filter(StringUtils::isNotBlank)
                .findFirst()
                .orElse(null);
        if (!StringUtils.isBlank(workOrder)) {
            HvPmWorkOrder order = workOrderRepository.getHvPmWorkOrderByWorkOrderCode(workOrder);
            if (order == null) {
                throw new BaseKnownException("工单号：" + workOrder + "的工单信息不存在！");
            }
            if (order.getShiftId() == null) {
                throw new BaseKnownException("工单号：" + workOrder + "的工单的班次未配置（未下发！）");
            }
            ResultVO<ShiftDTO> shiftVo = shiftClient.getShiftById(order.getShiftId());
            //班次
            steelPlatePart.setBc(shiftVo.getData().getShiftCode());
        }
        // 3. 零件数据准备
        List<HvPmMaterialCutPlanDetail0> detail0 = hvPmMaterialCutPlanDetail0Repository.getAllByCutPlanId(hvPmMaterialCutPlan.getId());
        if (detail0.isEmpty()) {
            throw new BaseKnownException("零件为空！无法下发！");
        }
        // 4. 批量数据收集
        List<String> materialCodes = detail0.stream()
                .map(HvPmMaterialCutPlanDetail0::getMaterialCode)
                .distinct()//去重操作
                .collect(Collectors.toList());

        List<String> workOrders = detail0.stream()
                .map(HvPmMaterialCutPlanDetail0::getWorkOrder)
                .distinct()
                .collect(Collectors.toList());
        // 5. 并行批量查询
        Map<String, MaterialDTO> materialMap = batchMaterialQuery(materialCodes);
        Map<String, HvPmWorkOrder> workOrderMap = batchWorkOrderQuery(workOrders);
        Map<Integer, MaterialGroupDTO> groupMap = batchMaterialGroupQuery();
        // 6. 物料类型映射
        ResultVO<List<MaterialTypeDTO>> materialTypeResult = materialTypeClient.getAllMaterialType();
        Map<Integer, String> materialTypeMap = materialTypeResult.getData().stream()
                .collect(Collectors.toMap(
                        MaterialTypeDTO::getId,
                        MaterialTypeDTO::getMaterialTypeCode,
                        (oldVal, newVal) -> oldVal,
                        LinkedHashMap::new
                ));
        // 7. 构建零件列表（带严格校验）
        List<MatDTO> matList = detail0.stream().map(detail -> {
            // 7.1 物料校验
            MaterialDTO material = Optional.ofNullable(materialMap.get(detail.getMaterialCode()))
                    .orElseThrow(() -> new BaseKnownException("物料不存在: " + detail.getMaterialCode()));
            // 7.2 工单校验
            HvPmWorkOrder order = Optional.ofNullable(workOrderMap.get(detail.getWorkOrder()))
                    .orElseThrow(() -> new BaseKnownException("工单不存在: " + detail.getWorkOrder()));
            // 7.3 物料组校验
            MaterialGroupDTO group = Optional.ofNullable(groupMap.get(material.getMaterialGroup()))
                    .orElseThrow(() -> new BaseKnownException("物料组不存在: " + material.getMaterialGroup()));
            // 7.4 构建对象
            return buildMatDTO(detail, material, order, group, materialTypeMap);
        }).collect(Collectors.toList());

        steelPlatePart.setMat(matList);
        // 8. 余料处理
        List<SurplusDTO> surplusDTOS = hvPmMaterialCutPlanDetail1Repository
                .getAllByCutPlanId(hvPmMaterialCutPlan.getId()).stream()
                .map(this::buildSurplusDTO)
                .collect(Collectors.toList());
        steelPlatePart.setSurplus(surplusDTOS);
        // 9. 执行下发
        ResultVO<?> result = steelPlateLineClient.sendOrder(steelPlatePart, hvPmMaterialCutPlan.getLineId());
        // 10. 结果处理
        processResult(hvPmMaterialCutPlan, result, userInfo);
        // 11. 零件工单状态修改
        List<HvPmWorkOrder> orderList = workOrderRepository.findByWorkOrderCodeIn(workOrders);
        orderList.forEach(order -> {
            order.setWorkOrderState(1);
        });
        workOrderRepository.saveAll(orderList);
        return result;
    }

    //----------- 核心工具方法 -----------
    // 批量物料查询
    private Map<String, MaterialDTO> batchMaterialQuery(List<String> materialCodes) {
        ResultVO<List<MaterialDTO>> result = materialClient.findByMaterialCodeIn(materialCodes);
        if (!result.isSuccess() || result.getData() == null) {
            throw new BaseKnownException("物料查询失败: " + result.getMessage());
        }

        // 验证完整性
        Set<String> missingCodes = new HashSet<>(materialCodes);
        missingCodes.removeAll(result.getData().stream()
                .map(MaterialDTO::getMaterialCode)
                .collect(Collectors.toSet()));

        if (!missingCodes.isEmpty()) {
            throw new BaseKnownException("缺失物料数据: " + String.join(",", missingCodes));
        }

        return result.getData().stream()
                .collect(Collectors.toMap(MaterialDTO::getMaterialCode, Function.identity()));
    }

    // 批量工单查询
    private Map<String, HvPmWorkOrder> batchWorkOrderQuery(List<String> workOrders) {
        List<HvPmWorkOrder> orders = workOrderRepository.findByWorkOrderCodeIn(workOrders);

        Set<String> foundCodes = orders.stream()
                .map(HvPmWorkOrder::getWorkOrderCode)
                .collect(Collectors.toSet());

        Set<String> missing = workOrders.stream()
                .filter(code -> !foundCodes.contains(code))
                .collect(Collectors.toSet());

        if (!missing.isEmpty()) {
            throw new BaseKnownException("工单数据不全，缺失: " + String.join(",", missing));
        }

        return orders.stream()
                .collect(Collectors.toMap(HvPmWorkOrder::getWorkOrderCode, Function.identity()));
    }

    // 批量物料组查询（适配String参数）
    private Map<Integer, MaterialGroupDTO> batchMaterialGroupQuery() {
        ResultVO<List<MaterialGroupDTO>> result = materialGroupClient.getAllMaterialGroup();
        if (!result.isSuccess() || result.getData() == null) {
            throw new BaseKnownException("物料组查询失败: " + result.getMessage());
        }

        return result.getData().stream()
                .collect(Collectors.toMap(
                        MaterialGroupDTO::getId,  // 使用Integer类型作为Key
                        Function.identity()
                ));
    }

    // 构建MatDTO对象
    private MatDTO buildMatDTO(HvPmMaterialCutPlanDetail0 detail,
                               MaterialDTO material,
                               HvPmWorkOrder order,
                               MaterialGroupDTO group,
                               Map<Integer, String> materialTypeMap) {
        MatDTO mat = new MatDTO();
        mat.setMatCode(detail.getMaterialCode());
        mat.setQty(detail.getQuality());
        mat.setWorkOrderCode(detail.getWorkOrder());
        mat.setMatName(detail.getMaterialName());
        mat.setLength(detail.getLength());
        mat.setWidth(detail.getWidth());
        mat.setThick(detail.getThick());
        mat.setDraw(detail.getMaterialPicture());
        mat.setNo(detail.getNo());
        mat.setShipType((String) material.getExtend().get("ship_type"));
        mat.setBlock(order.getSegmentationCode());
        mat.setStage((String) material.getExtend().get("stage"));
        mat.setMaterialsType(materialTypeMap.get(material.getMaterialType()));
        mat.setPt(group.getGroupCode());
        mat.setOutFlag(order.getOutFlag());
        mat.setSpecialPurchaseTypeCode(order.getSpecialPurchaseTypeCode());
        mat.setGps2((String) material.getExtend().get("gps2"));
        mat.setGps3((String) material.getExtend().get("gps3"));
        mat.setGps4((String) material.getExtend().get("gps4"));
        mat.setTextureOfMaterial(material.getQuality());
        mat.setSpecifications(material.getSpecs());
        mat.setBlockCode(order.getBlockCode());
        mat.setWeight(material.getWeight());
        mat.setGrooveDirection("0000");
        return mat;
    }

    // 余料对象构建
    private SurplusDTO buildSurplusDTO(HvPmMaterialCutPlanDetail1 detail) {
        SurplusDTO dto = new SurplusDTO();
        dto.setCode(detail.getMaterialCode());
        dto.setLength(detail.getLength());
        dto.setWidth(detail.getWidth());
        dto.setThick(detail.getThickness());
        dto.setWeight(Double.parseDouble(detail.getWeight()));
        return dto;
    }

    // 结果处理
    private void processResult(HvPmMaterialCutPlanDTO plan, ResultVO<?> result, UserInfoDTO user) {
        if (user != null) {
            plan.setSendUserId(user.getId());
        }

        plan.setSendTime(new Date());
        plan.setStatus(result.isSuccess() ?
                CutPlanStatusEnum.SEND_OK.getCode() :
                CutPlanStatusEnum.SEND_ERROR.getCode());

        if (!result.isSuccess()) {
            log.error("下发失败[{}] 原因: {}", plan.getCutPlanCode(), result.getMessage());
        }
        hvPmMaterialCutPlanRepository.save(DtoMapper.convert(plan, HvPmMaterialCutPlan.class));
    }

    /**
     *
     * 下载导入模版
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    @Override
    public ResultVO<ExcelExportDto> getImportTemplate() throws IOException, IllegalAccessException {
        HSSFWorkbook hssfWorkbook = new HSSFWorkbook();
        List<HvPmMaterialCutPlanDTO> hvPmMaterialCutPlanDTOS = new ArrayList<>();
        List<HvPmMaterialCutPlanDetail0DTO> hvPmMaterialCutPlanDetail0DTOS = new ArrayList<>();
        List<HvPmMaterialCutPlanDetail1DTO> hvPmMaterialCutPlanDetail1DTOS = new ArrayList<>();
        ExcelUtil.addSheetToWorkBook(hvPmMaterialCutPlanDTOS, "钢板切割计划列表", MaterialCutPlanTemplate.class, null, hssfWorkbook);
        ExcelUtil.addSheetToWorkBook(hvPmMaterialCutPlanDetail0DTOS, "零件", MaterialCutPlanDetail0Template.class, null, hssfWorkbook);
        ExcelUtil.addSheetToWorkBook(hvPmMaterialCutPlanDetail1DTOS, "余料", MaterialCutPlanDetail1Template.class, null, hssfWorkbook);
        ResponseEntity<byte[]> responseEntity = ExcelUtil.generateHttpExcelFile(hssfWorkbook, "钢板切割计划导入模版.xls");
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(responseEntity.getBody());
        excelExportDto.setFileName("钢板切割计划导入模版.xls");
        return ResultVO.success(excelExportDto);
    }

    /**
     * 导入钢板切割计划
     * @param file bom信息文档
     * @return
     * @throws IllegalAccessException
     * @throws ParseException
     * @throws IOException
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResult importMaterialCutPlan(MultipartFile file) throws IllegalAccessException, ParseException, IOException {
        //获取所有产线
        List<LocationDTO> lineList = locationExtendClient.getLocationListByType(40).getData();
        HashMap<String, Integer> map = new HashMap<>();
        for (LocationDTO locationDTO : lineList) {
            map.put(locationDTO.getCode(), locationDTO.getId());
        }
        // 导入切割计划
        List<HvPmMaterialCutPlanDTO> hvPmMaterialCutPlanDTOS = ExcelUtil.getEntityList(file, 0, HvPmMaterialCutPlanDTO.class);
        if (hvPmMaterialCutPlanDTOS.size() > 0) {
            for (HvPmMaterialCutPlanDTO dto : hvPmMaterialCutPlanDTOS) {
                if (dto.getCutPlanCode() != null && !dto.getCutPlanCode().equals("")) {
                    dto.setLineId(map.get(dto.getLineCode()));
                    createCutPlan(dto);
                }
            }
        }
        // 导入零件信息
        List<HvPmMaterialCutPlanDetail0DTO> detail0DTOS = ExcelUtil.getEntityList(file, 1, HvPmMaterialCutPlanDetail0DTO.class);
        if (detail0DTOS.size() > 0) {
            for (HvPmMaterialCutPlanDetail0DTO detail0DTO : detail0DTOS) {
                if (detail0DTO.getCutPlanCode() != null && !detail0DTO.getCutPlanCode().equals("")) {
                    HvPmMaterialCutPlan cutPlan = hvPmMaterialCutPlanRepository.getByCutPlanCodeEquals(detail0DTO.getCutPlanCode());
                    if (cutPlan == null) {
                        throw new BaseKnownException("导入失败。零件信息数据中零件号" + detail0DTO.getMaterialCode() + "对应的切割计划编号:" + detail0DTO.getCutPlanCode() + "在切割计划中不存在");
                    }
                    detail0DTO.setCutPlanId(cutPlan.getId());
                    HvPmWorkOrder order = workOrderRepository.getHvPmWorkOrderByWorkOrderCode(detail0DTO.getWorkOrder());
                    if (order == null) {
                        throw new BaseKnownException("工单号：" + detail0DTO.getWorkOrder() + "的工单信息不存在！");
                    }
                    detail0DTO.setBlockCode(order.getBlockCode());
                    hvPmMaterialCutPlanDetail0Service.createDetail0(detail0DTO);
                }
            }
        }
        // 导入余料信息
        List<HvPmMaterialCutPlanDetail1DTO> detail1DTOS = ExcelUtil.getEntityList(file, 2, HvPmMaterialCutPlanDetail1DTO.class);
        if (detail1DTOS.size() > 0) {
            for (HvPmMaterialCutPlanDetail1DTO detail1DTO : detail1DTOS) {
                if (detail1DTO.getCutPlanCode() != null && !detail1DTO.getCutPlanCode().equals("")) {
                    HvPmMaterialCutPlan cutPlan = hvPmMaterialCutPlanRepository.getByCutPlanCodeEquals(detail1DTO.getCutPlanCode());
                    if (cutPlan == null) {
                        throw new BaseKnownException("导入失败。余料信息数据中零件号" + detail1DTO.getMaterialCode() + "对应的切割计划编号:" + detail1DTO.getCutPlanCode() + "在切割计划中不存在");
                    }
                    detail1DTO.setCutPlanId(cutPlan.getId());
                    hvPmMaterialCutPlanDetail1Service.createDetail1(detail1DTO);
                }
            }
        }
        return new ImportResult();
    }


    /**
     * 导出钢板切割计划
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    @Override
    @ApiIgnore
    public ResultVO<ExcelExportDto> exportMaterialCutPlan(HvPmMaterialCutPlanQueryDTO hvPmMaterialCutPlanQueryDTO) throws IOException, IllegalAccessException {
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(geteExportInfo(hvPmMaterialCutPlanQueryDTO).getBody());
        excelExportDto.setFileName(HvPmMaterialCutPlanConst.PLAN_EXPORT_FILE_NAME );
        return ResultVO.success(excelExportDto);
    }

    //获取需要导出的数据内容
    @Override
    @ApiIgnore
    public ResponseEntity<byte[]> geteExportInfo(HvPmMaterialCutPlanQueryDTO queryDTO) throws IOException, IllegalAccessException {

        // 获取钢板切割计划主表列表
        List<HvPmMaterialCutPlanDTO> materialCutPlanDTOList = hvPmMaterialCutPlanMapper.getPage(queryDTO);
        //获取钢板切割计划id列表
        List<Long> cutPlanIds = materialCutPlanDTOList.stream()
                .map(HvPmMaterialCutPlanDTO::getId)
                .collect(Collectors.toList());

        // 钢板切割计划主表列表
        List<MaterialCutPlanExportDTO> hvPmMaterialCutPlanDTOS = DtoMapper.convertList(materialCutPlanDTOList, MaterialCutPlanExportDTO.class);

        //设置状态描述
        for (MaterialCutPlanExportDTO materialCutPlanExportDTO : hvPmMaterialCutPlanDTOS) {
            if(materialCutPlanExportDTO.getStatus()!=null  ){
                String name = CutPlanStatusEnum.getEnumByCode(materialCutPlanExportDTO.getStatus()).getName();
                materialCutPlanExportDTO.setStatusDes(name);
            }else{
                materialCutPlanExportDTO.setStatusDes("未知状态");
            }
        }

        // 零件信息
        List<MaterialCutPlanDetail0ExportDTO> hvPmMaterialCutPlanDetail0DTOS = DtoMapper.convertList(hvPmMaterialCutPlanDetail0Service.getDetail0ListByCutPlanIds(cutPlanIds), MaterialCutPlanDetail0ExportDTO.class);
        // 余料信息
        List<MaterialCutPlanDetail1ExportDTO> hvPmMaterialCutPlanDetail1DTOS = DtoMapper.convertList(hvPmMaterialCutPlanDetail1Service.getDetail1ListByCutPlanIds(cutPlanIds), MaterialCutPlanDetail1ExportDTO.class);
        // 时间信息
        List<MaterialCutPlanDetail2ExportDTO> hvPmMaterialCutPlanDetail2DTOS = DtoMapper.convertList(hvPmMaterialCutPlanDetail2Service.getDetail2ListByCutPlanIds(cutPlanIds), MaterialCutPlanDetail2ExportDTO.class);
        HSSFWorkbook hssfWorkbook = new HSSFWorkbook();
        ExcelUtil.addSheetToWorkBook(hvPmMaterialCutPlanDTOS, HvPmMaterialCutPlanConst.PLAN_EXPORT_SHEET_NAME, MaterialCutPlanExportDTO.class,null, hssfWorkbook);
        ExcelUtil.addSheetToWorkBook(hvPmMaterialCutPlanDetail0DTOS, HvPmMaterialCutPlanConst.PLAN_DETAIL0_EXPORT_SHEET_NAME,MaterialCutPlanDetail0ExportDTO.class,null, hssfWorkbook);
        ExcelUtil.addSheetToWorkBook(hvPmMaterialCutPlanDetail1DTOS,  HvPmMaterialCutPlanConst.PLAN_DETAIL1_EXPORT_SHEET_NAME,MaterialCutPlanDetail1ExportDTO.class,null, hssfWorkbook);
        ExcelUtil.addSheetToWorkBook(hvPmMaterialCutPlanDetail2DTOS,  HvPmMaterialCutPlanConst.PLAN_DETAIL2_EXPORT_SHEET_NAME,MaterialCutPlanDetail2ExportDTO.class,null, hssfWorkbook);
        return ExcelUtil.generateHttpExcelFile(hssfWorkbook, HvPmMaterialCutPlanConst.PLAN_EXPORT_FILE_NAME);
    }


    @Override
    @Transactional
    public void addMaterialCutPlan(List<HvPmMaterialCutPlanDTO> cutPlan) {
        // 1. 数据预处理：收集工单号（使用cutPlanCode作为唯一键）
        Map<String, Set<String>> planWorkOrderMap = collectPlanWorkOrders(cutPlan);

        // 2. 批量获取工单信息
        Set<String> allWorkOrderCodes = planWorkOrderMap.values().stream()
                .flatMap(Set::stream)
                .collect(Collectors.toSet());
        Map<String, HvPmWorkOrder> workOrderMap = getWorkOrderMap(allWorkOrderCodes);

        // 3. 获取线体信息并填充lineId
        Set<String> lineCodes = extractLineCodes(cutPlan);
        Map<String, LocationDTO> locationMap = getLocationMap(lineCodes);
        validateAndFillLineIds(cutPlan, locationMap);

        // 4. 处理每个切割计划（事务内操作）
        cutPlan.forEach(dto -> processSingleCutPlan(dto, planWorkOrderMap, workOrderMap));
    }

    /**
     * 收集切割计划与工单号的映射关系（Key: cutPlanCode）
     */
    private Map<String, Set<String>> collectPlanWorkOrders(List<HvPmMaterialCutPlanDTO> cutPlan) {
        return cutPlan.stream()
                .collect(Collectors.toMap(
                        HvPmMaterialCutPlanDTO::getCutPlanCode,
                        dto -> Optional.ofNullable(dto.getDetailList())
                                .orElse(Collections.emptyList())
                                .stream()
                                .map(HvPmMaterialCutPlanDetail0DTO::getWorkOrder)
                                .collect(Collectors.toSet()),
                        (existing, replacement) -> existing // 处理重复键（根据业务需求调整）
                ));
    }

    /**
     * 批量获取工单信息
     */
    private Map<String, HvPmWorkOrder> getWorkOrderMap(Set<String> workOrderCodes) {
        if (workOrderCodes.isEmpty()) {
            return Collections.emptyMap();
        }

        return workOrderRepository.findByWorkOrderCodeIn(workOrderCodes)
                .stream()
                .collect(Collectors.toMap(
                        HvPmWorkOrder::getWorkOrderCode,
                        Function.identity(),
                        (existing, replacement) -> existing // 处理重复工单号
                ));
    }

    /**
     * 提取线体编码并去重
     */
    private Set<String> extractLineCodes(List<HvPmMaterialCutPlanDTO> cutPlan) {
        return cutPlan.stream()
                .map(HvPmMaterialCutPlanDTO::getLineCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    /**
     * 获取线体信息并进行空校验
     */
    private Map<String, LocationDTO> getLocationMap(Set<String> lineCodes) {
        if (lineCodes.isEmpty()) {
            return Collections.emptyMap();
        }

        ResultVO<List<LocationDTO>> result = locationExtendClient.getLocationsByCodes(new ArrayList<>(lineCodes));
        if (!result.isSuccess() || result.getData() == null) {
            throw new BaseKnownException("获取线体信息失败！");
        }
        return result.getData().stream()
                .collect(Collectors.toMap(LocationDTO::getCode, Function.identity()));
    }

    /**
     * 填充线体ID并进行有效性校验
     */
    private void validateAndFillLineIds(List<HvPmMaterialCutPlanDTO> cutPlan, Map<String, LocationDTO> locationMap) {
        cutPlan.forEach(dto -> {
            LocationDTO location = locationMap.get(dto.getLineCode());
            if (location == null) {
                throw new BaseKnownException("线体编号：" + dto.getLineCode() + " 不存在！");
            }
            dto.setLineId(location.getId());
        });
    }

    /**
     * 处理单个切割计划（含事务提交后的异步操作）
     */
    public void processSingleCutPlan(HvPmMaterialCutPlanDTO dto,
                                     Map<String, Set<String>> planWorkOrderMap,
                                     Map<String, HvPmWorkOrder> workOrderMap) {
        // 获取关联工单信息
        Set<String> workOrderCodes = planWorkOrderMap.get(dto.getCutPlanCode());
        List<HvPmWorkOrder> relatedOrders = workOrderCodes.stream()
                .map(workOrderMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 计算最早开始时间
        Date earliestStart = relatedOrders.stream()
                .map(HvPmWorkOrder::getPlanStartTime)
                .filter(Objects::nonNull)
                .min(Date::compareTo)
                .orElse(null);
        dto.setPlanStartTime(earliestStart);

        // 创建切割计划
        long cutPlanId = createCutPlan(dto);
        dto.setId(cutPlanId);

        // 处理明细数据
        Set<WorkOrderDTO> workOrderDTOSet = processDetails(dto, cutPlanId);

        // 更新工单信息
        updateWorkOrders(workOrderDTOSet, workOrderMap);

        long delaySeconds = 10;
        long score = System.currentTimeMillis() + delaySeconds * 1000;
        redisTemplate.opsForZSet().add("delay:cut_plan", String.valueOf(dto.getId()), score);
    }

    private Set<WorkOrderDTO> processDetails(HvPmMaterialCutPlanDTO dto, long cutPlanId) {
        Set<WorkOrderDTO> orderSet = new HashSet<>();

        // 批量处理Detail0
        if (dto.getDetailList() != null && !dto.getDetailList().isEmpty()) {
            List<HvPmMaterialCutPlanDetail0> detail0Entities = dto.getDetailList().stream()
                    .map(detail -> convertDetail0(detail, dto, cutPlanId))
                    .collect(Collectors.toList());

            // 批量保存Detail0
            List<HvPmMaterialCutPlanDetail0> savedDetails = hvPmMaterialCutPlanDetail0Service.createDetail0Batch(detail0Entities);

            // 创建工单DTO
            savedDetails.forEach(detail ->
                    orderSet.add(createWorkOrderDTO(DtoMapper.convert(detail, HvPmMaterialCutPlanDetail0DTO.class),
                            Long.valueOf(dto.getLineId())))
            );
        }

        // 批量处理Detail1
        if (dto.getDetail1List() != null && !dto.getDetail1List().isEmpty()) {
            List<HvPmMaterialCutPlanDetail1> detail1Entities = dto.getDetail1List().stream()
                    .map(detail -> convertDetail1(detail, dto, cutPlanId))
                    .collect(Collectors.toList());

            // 批量保存Detail1
            hvPmMaterialCutPlanDetail1Service.createDetail1Batch(detail1Entities);
        }

        return orderSet;
    }

    // 转换Detail0 DTO到Entity并设置公共字段
    private HvPmMaterialCutPlanDetail0 convertDetail0(HvPmMaterialCutPlanDetail0DTO detail,
                                                      HvPmMaterialCutPlanDTO dto,
                                                      long cutPlanId) {
        HvPmMaterialCutPlanDetail0 entity = DtoMapper.convert(detail, HvPmMaterialCutPlanDetail0.class);
        entity.setCutPlanId(cutPlanId);
        entity.setCutPlanCode(dto.getCutPlanCode());
        entity.setSurplusMaterialFlag(dto.getSurplusMaterialFlag());
        return entity;
    }

    // 转换Detail1 DTO到Entity并设置公共字段
    private HvPmMaterialCutPlanDetail1 convertDetail1(HvPmMaterialCutPlanDetail1DTO detail,
                                                      HvPmMaterialCutPlanDTO dto,
                                                      long cutPlanId) {
        HvPmMaterialCutPlanDetail1 entity = DtoMapper.convert(detail, HvPmMaterialCutPlanDetail1.class);
        entity.setCutPlanId(cutPlanId);
        entity.setCutPlanCode(dto.getCutPlanCode());
        return entity;
    }

    private WorkOrderDTO createWorkOrderDTO(HvPmMaterialCutPlanDetail0DTO detail, Long lineId) {
        WorkOrderDTO workOrder = new WorkOrderDTO();
        workOrder.setWorkOrderCode(detail.getWorkOrder());
        workOrder.setCellId(Math.toIntExact(lineId));
        return workOrder;
    }

    private void updateWorkOrders(Set<WorkOrderDTO> orderDTOs,
                                  Map<String, HvPmWorkOrder> workOrderMap) {

        List<HvPmWorkOrder> orderList = new ArrayList<>();
        orderDTOs.forEach(order -> {
            HvPmWorkOrder workOrder = Optional.ofNullable(workOrderMap.get(order.getWorkOrderCode()))
                    .orElseThrow(() ->
                            new BaseKnownException("工单号：" + order.getWorkOrderCode() + "未维护！"));

            if (!Objects.equals(workOrder.getCellId(), order.getCellId())) {
                workOrder.setCellId(order.getCellId());
                orderList.add(workOrder);
            }

            if (WorkOrderStateEnum.NOT_ISSUED.getCode().equals(workOrder.getWorkOrderState())) {
                issueWorkOrder(workOrder);
            }
        });
        workOrderRepository.saveAll(orderList);
    }

    private void issueWorkOrder(HvPmWorkOrder workOrder) {
        WorkIssuedDTO issuedDTO = new WorkIssuedDTO();
        issuedDTO.setOrderIds(Collections.singletonList(workOrder.getId()));
        issuedDTO.setWorkOrderDTO(DtoMapper.convert(workOrder, WorkOrderDTO.class));
        workOrderService.batchIssued(issuedDTO);
    }

    @Override
    public HvPmMaterialCutPlan getByPlanCode(String code) {
        return hvPmMaterialCutPlanRepository.getByCutPlanCodeEquals(code);
    }

    //零件切割任务取消
    @Override
    public ResultVO<?> cutTaskCancel(int lineId,String type, String code) {

        //组装 零件切割任务取消DTO
        PartCutTaskCancelDTO dto = new PartCutTaskCancelDTO();
        dto.setLineId(lineId);
        dto.setName("零件切割任务取消");
        //组装 零件切割任务取消详情DTO
        PartCutTaskCancelDataDTO dataDTO = new PartCutTaskCancelDataDTO();
        dataDTO.setType(type);
        dataDTO.setCode(code);
        dto.setData(dataDTO);

        //调用 发送-零件切割任务取消接口
        return steelPlateLineClient.sendPartCutTaskCancel(dto);
    }

    @Override
    public List<String> getShipNumberList() {
        return hvPmMaterialCutPlanMapper.getShipNumberList();
    }

    @Override
    public HvPmMaterialCutPlan getById(Long planId) {
        return hvPmMaterialCutPlanRepository.getById(planId);
    }

    @Override
    public HvPmMaterialCutPlanDTO buildCutPlanDTO(HvPmMaterialCutPlan plan, Long planId) {
        HvPmMaterialCutPlanDTO dto = new HvPmMaterialCutPlanDTO();
        List<HvPmMaterialCutPlanDetail0> detail0List = hvPmMaterialCutPlanDetail0Repository.getAllByCutPlanId(planId);
        List<HvPmMaterialCutPlanDetail1> detail1List = hvPmMaterialCutPlanDetail1Repository.getAllByCutPlanId(planId);
        BeanUtils.copyProperties(plan, dto);
        List<HvPmMaterialCutPlanDetail0DTO> detailDTOList = new ArrayList<>();
        List<HvPmMaterialCutPlanDetail1DTO> detail1DTOList = new ArrayList<>();
        for (HvPmMaterialCutPlanDetail0 detail0 : detail0List) {
            HvPmMaterialCutPlanDetail0DTO detail0DTO = new HvPmMaterialCutPlanDetail0DTO();
            BeanUtils.copyProperties(detail0, detail0DTO);
            detailDTOList.add(detail0DTO);
        }
        for (HvPmMaterialCutPlanDetail1 detail1 : detail1List) {
            HvPmMaterialCutPlanDetail1DTO detail1DTO = new HvPmMaterialCutPlanDetail1DTO();
            BeanUtils.copyProperties(detail1, detail1DTO);
            detail1DTOList.add(detail1DTO);
        }
        dto.setDetailList(detailDTOList);
        dto.setDetail1List(detail1DTOList);
        return dto;
    }

}
