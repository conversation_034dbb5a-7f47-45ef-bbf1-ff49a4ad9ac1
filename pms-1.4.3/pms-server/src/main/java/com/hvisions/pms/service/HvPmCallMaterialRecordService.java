package com.hvisions.pms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.pms.entity.HvPmCallMaterialRecord;
import com.hvisions.thirdparty.common.dto.WeldLineCallMaterialsDTO;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-05-27 15:01
 */
public interface HvPmCallMaterialRecordService extends IService<HvPmCallMaterialRecord> {
    Page<HvPmCallMaterialRecord> pageList(QueryWrapper<HvPmCallMaterialRecord> queryWrapper, Integer pageNo, Integer pageSize);

    void createCallMaterialRecord(List<WeldLineCallMaterialsDTO> weldLineCallMaterialsDTOS);

    HvPmCallMaterialRecord getRecordByRequestCode(String taskCode);

    ResultVO<ExcelExportDto> exportCallMaterialRecord(HvPmCallMaterialRecord hvPmCallMaterialRecord) throws IOException, IllegalAccessException;
}
