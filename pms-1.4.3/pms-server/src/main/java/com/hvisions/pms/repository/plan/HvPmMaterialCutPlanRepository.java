package com.hvisions.pms.repository.plan;

import com.hvisions.pms.entity.plan.HvPmMaterialCutPlan;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface HvPmMaterialCutPlanRepository extends JpaRepository<HvPmMaterialCutPlan,Long> {

    /**
     * 是否存在cutPlanCode
     *
     * @param cutPlanCode
     * @return
     */
    HvPmMaterialCutPlan getByCutPlanCodeEquals(String cutPlanCode);

    HvPmMaterialCutPlan getById(Long id);
}
