package com.hvisions.pms.service;

import com.hvisions.pms.dto.MaterialActualCodeDTO;
import com.hvisions.pms.dto.OperationMaterialDTO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>Title: OperationMaterialService</p>
 * <p>Description: 工序物料关系服务</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/1/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface OperationMaterialService {
    /**
     * 新增或者更新工序和物料关系
     *
     * @param operationMaterialDTOS 物料和工序关系列表
     */
    void createOrUpdateOperationMaterial(List<OperationMaterialDTO> operationMaterialDTOS);

    /**
     * 根据工序id获取相关的物料列表
     *
     * @param operationId 工序id
     * @return 相关的物料列表
     */
    List<OperationMaterialDTO> getOperationMaterialListByOperationId(int operationId);

    /**
     * 根据id删除相关数据
     *
     * @param id 关联关系的id
     */
    void deleteOperationMaterialById(int id);

    /**
     * 根据id列表删除投入物料信息
     *
     * @param idList id列表
     */
    void deleteOperationMaterialByIdList(List<Integer> idList);

    /**
     * 根据工序ID删除物料信息
     *
     * @param operationId 工序ID
     */
    void deleteOperationMaterialByOperationId(int operationId);


    /**
     * 录入实际物料
     *
     * @param materialActualCodeDTO 实际物料
     */
    void inPutActualCode(MaterialActualCodeDTO materialActualCodeDTO);

    /**
     * 回料
     *
     * @param id                  投入料信息Id
     * @param returnMaterialCount 回料数量
     */
    void returnMaterial(int id, BigDecimal returnMaterialCount);

}









