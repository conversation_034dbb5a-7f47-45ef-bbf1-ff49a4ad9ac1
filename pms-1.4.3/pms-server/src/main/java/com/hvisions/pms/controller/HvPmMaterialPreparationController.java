package com.hvisions.pms.controller;

import com.hvisions.pms.dto.*;
import com.hvisions.pms.service.HvPmMaterialPreparationDetailService;
import com.hvisions.pms.service.HvPmMaterialPreparationService;
import com.hvisions.thirdparty.common.dto.MaterialPreparationContentDTO;
import com.hvisions.thirdparty.common.dto.MaterialPreparationResultDTO;
import com.hvisions.thirdparty.common.dto.StockInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description HvPmMaterialPreparationConttroller
 * <AUTHOR>
 * @Date 2024-05-21
 */
@Slf4j
@Api(tags = "备料请求")
@RestController
@RequestMapping("/materialPreparation")
public class HvPmMaterialPreparationController {
    @Autowired
    private HvPmMaterialPreparationService hvPmMaterialPreparationService;
    
    @Autowired
    private HvPmMaterialPreparationDetailService hvPmMaterialPreparationDetailService;
    
    @ApiOperation(value = "获取HvPmMaterialPreparation列表")
    @PostMapping(value = "/list")
    public Page<HvPmMaterialPreparationDTO> getList(@RequestBody HvPmMaterialPreparationQueryDTO queryDTO) {
        return hvPmMaterialPreparationService.getPage(queryDTO);
    }
    
    /**
     * 添加
     *
     * @param hvPmMaterialPreparationDTO HvPmMaterialPreparation
     */
    @ApiOperation(value = "添加HvPmMaterialPreparation信息")
    @PostMapping(value = "/add")
    public void addHvPmMaterialPreparation(@Valid @RequestBody HvPmMaterialPreparationDTO hvPmMaterialPreparationDTO) {
        hvPmMaterialPreparationService.addHvPmMaterialPreparation(hvPmMaterialPreparationDTO);
    }

    /**
     * 删除
     *
     * @param id 主键
     */
    @ApiOperation(value = "删除HvPmMaterialPreparation信息")
    @DeleteMapping(value = "/delete/{id}")
    public void deleteHvPmMaterialPreparation(@PathVariable Long id) {
        hvPmMaterialPreparationService.deleteHvPmMaterialPreparation(id);
    }

    /**
     * 修改
     *
     * @param hvPmMaterialPreparationDTO HvPmMaterialPreparation
     */
    @ApiOperation(value = "修改HvPmMaterialPreparation")
    @PutMapping(value = "/update")
    public void updateHvPmMaterialPreparation(@Valid @RequestBody HvPmMaterialPreparationDTO hvPmMaterialPreparationDTO) {
        hvPmMaterialPreparationService.updateHvPmMaterialPreparation(hvPmMaterialPreparationDTO);
    }

    /**
     * 根据id获取
     *
     * @param id 主键
     * @return HvPmMaterialPreparation hvPmMaterialPreparationDTO
     */
    @ApiOperation(value = "根据id获取HvPmMaterialPreparation")
    @GetMapping(value = "/get/{id}")
    public HvPmMaterialPreparationDTO getList(@PathVariable Long id) {
        return hvPmMaterialPreparationService.getHvPmMaterialPreparationById(id);
    }

    /**
     * 查询全部
     * @return 列表
     */
    @ApiOperation(value = "获取HvPmMaterialPreparation列表")
    @GetMapping(value = "/getAll")
    public List<HvPmMaterialPreparationDTO> getAll(){
        return hvPmMaterialPreparationService.getAll();
    }


    @ApiOperation(value = "根据id获取备料请求明细列表")
    @GetMapping(value = "/getByPreparationId/{id}")
    public List<HvPmMaterialPreparationDetailDTO> getByPreparationId(@PathVariable Long id) {
        return hvPmMaterialPreparationDetailService.getByPreparationId(id);
    }

    @ApiOperation("添加备料计划")
    @PostMapping("/addMaterialPreparationPlan")
    public void addMaterialPreparationPlan(@RequestBody MaterialPreparationContentDTO materialPreparationContentDTO){
        hvPmMaterialPreparationService.addMaterialPreparationPlan(materialPreparationContentDTO);
    }

    @ApiOperation("更新备料内容")
    @PostMapping("/updatePreparation")
    public void updatePreparation(@RequestBody MaterialPreparationResultDTO materialPreparationResultDTO){
        hvPmMaterialPreparationService.updatePreparation(materialPreparationResultDTO);
        //修改主表的状态
        hvPmMaterialPreparationService.updatePreparationStatus(materialPreparationResultDTO.getWorkOrderCode());
    }


    @ApiOperation(value = "根据id获取备料请求明细和结果列表")
    @GetMapping(value = "/getGroupByPreparationId/{id}")
    public List<HvPmMaterialPreparationDetailGroupDTO> getGroupByPreparationId(@PathVariable Long id) {
        return hvPmMaterialPreparationDetailService.getGroupByPreparationId(id);
    }


    @ApiOperation(value = "获取HvPmMaterialPreparation列表")
    @PostMapping(value = "/getGroupDetailsByQuery")
    public Page<HvPmMaterialPreparationDetailDTO> getGroupDetailsByQuery(@RequestBody HvPmMaterialPreparationDetailQueryDTO queryDTO) {
        return hvPmMaterialPreparationDetailService.getGroupDetailsByQuery(queryDTO);
    }

    @ApiOperation(value = "根据工单号获取备料主表数据")
    @PostMapping(value = "/getPreparationByWorkOrderCode/{workOrderCode}")
    public HvPmMaterialPreparationDTO getPreparationByWorkOrderCode(@PathVariable String workOrderCode ) {
        return hvPmMaterialPreparationService.getPreparationByWorkOrderCode(workOrderCode);
    }

    @ApiOperation(value = "型材库存同步时占用库存")
    @PostMapping(  "/stockSyncXcOccupyStock")
    public void stockSyncXcOccupyStock(@RequestBody StockInfoDTO stockInfoDTO) {
         hvPmMaterialPreparationService.stockSyncXcOccupyStock(stockInfoDTO);
    }
}
