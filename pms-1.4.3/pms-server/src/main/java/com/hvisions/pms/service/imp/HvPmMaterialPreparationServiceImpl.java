package com.hvisions.pms.service.imp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.client.MaterialClient;
import com.hvisions.hiperbase.materials.dto.MaterialDTO;
import com.hvisions.pms.dao.HvPmMaterialPreparationDetailMapper;
import com.hvisions.pms.dao.HvPmMaterialPreparationMapper;
import com.hvisions.pms.dto.*;
import com.hvisions.pms.entity.HvPmMaterialPreparation;
import com.hvisions.pms.entity.HvPmMaterialPreparationDetail;
import com.hvisions.pms.entity.HvPmShipRawMaterial;
import com.hvisions.pms.entity.HvPmShipRawMaterialDetail;
import com.hvisions.pms.entity.plan.HvPmXcMaterialCutPlan;
import com.hvisions.pms.enums.XcCutPlanStatusEnum;
import com.hvisions.pms.repository.HvPmMaterialPreparationDetailRepository;
import com.hvisions.pms.repository.HvPmMaterialPreparationRepository;
import com.hvisions.pms.repository.plan.HvPmXcMaterialCutPlanRepository;
import com.hvisions.pms.service.*;
import com.hvisions.pms.utils.SerialCodeUtilsV2;
import com.hvisions.thirdparty.common.dto.*;
import com.hvisions.thridparty.client.MaterialCuttingLineClient;
import com.hvisions.thridparty.client.ProfileCompactStorageClient;
import com.hvisions.wms.client.StockClient;
import com.hvisions.wms.client.WaresLocationClient;
import com.hvisions.wms.dto.location.WaresLocationDTO;
import com.hvisions.wms.dto.stock.StockMaterialPreparationDTO;
import com.hvisions.wms.dto.stock.StockOccupyDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;


/**
 * @Description HvPmMaterialPreparationServiceImpl
 * <AUTHOR>
 * @Date 2024-05-21
 */
@Service
public class HvPmMaterialPreparationServiceImpl implements HvPmMaterialPreparationService {

    private static final Logger log = LoggerFactory.getLogger(HvPmMaterialPreparationServiceImpl.class);
    @Autowired
    private HvPmMaterialPreparationMapper hvPmMaterialPreparationMapper;

    @Autowired
    private HvPmMaterialPreparationDetailMapper hvPmMaterialPreparationDetailMapper;

    @Autowired
    private HvPmMaterialPreparationRepository hvPmMaterialPreparationRepository;

    @Autowired
    private HvPmMaterialPreparationDetailRepository hvPmMaterialPreparationDetailRepository;

    @Autowired
    private HvPmMaterialPreparationDetailService hvPmMaterialPreparationDetailService;

    @Autowired
    private WorkOrderService workOrderService;

    /*@Autowired
    private SerialCodeUtils serialCodeUtils;
    */

    @Autowired
    private SerialCodeUtilsV2 serialCodeUtilsV2;

    @Autowired
    private ProfileCompactStorageClient profileCompactStorageClient;

    @Autowired
    private ShipRawMaterialService shipRawMaterialService;

    @Autowired
    private ShipRawMaterialDetailService shipRawMaterialDetailService;

    @Autowired
    private MaterialCuttingLineClient materialCuttingLineClient;

    @Autowired
    private HvPmXcMaterialCutPlanService hvPmXcMaterialCutPlanService;

    @Autowired
    private HvPmXcMaterialCutPlanRepository hvPmXcMaterialCutPlanRepository;

    @Autowired
    private StockClient stockClient;

    @Autowired
    private WaresLocationClient waresLocationClient;

    @Resource
    private MaterialClient materialClient;



    @Override
    public Page<HvPmMaterialPreparationDTO> getPage(HvPmMaterialPreparationQueryDTO queryDTO) {
        return PageHelperUtil.getPage(hvPmMaterialPreparationMapper::getPage, queryDTO);
    }

    @Override
    public Long addHvPmMaterialPreparation(HvPmMaterialPreparationDTO hvPmMaterialPreparationDTO) {
        return hvPmMaterialPreparationRepository.save(DtoMapper.convert(hvPmMaterialPreparationDTO, HvPmMaterialPreparation.class)).getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteHvPmMaterialPreparation(Long id) {
        HvPmMaterialPreparation hvPmMaterialPreparation = hvPmMaterialPreparationRepository.getOne(id);
        hvPmMaterialPreparationRepository.deleteById(id);
        hvPmMaterialPreparationDetailRepository.deleteByPreparationId(id);
        //删除备料库存占用
        stockClient.deleteStockOccupyByOrderCode(hvPmMaterialPreparation.getWorkOrderCode());
    }

    @Override
    public void updateHvPmMaterialPreparation(HvPmMaterialPreparationDTO hvPmMaterialPreparationDTO) {
        hvPmMaterialPreparationRepository.save(DtoMapper.convert(hvPmMaterialPreparationDTO, HvPmMaterialPreparation.class));
    }

    @Override
    public HvPmMaterialPreparationDTO getHvPmMaterialPreparationById(Long id) {
        Optional<HvPmMaterialPreparation> optional = hvPmMaterialPreparationRepository.findById(id);
        return optional.map(hvPmMaterialPreparation -> DtoMapper.convert(hvPmMaterialPreparation, HvPmMaterialPreparationDTO.class)).orElse(null);
    }

    @Override
    public List<HvPmMaterialPreparationDTO> getAll() {
        return DtoMapper.convertList(hvPmMaterialPreparationRepository.findAll(), HvPmMaterialPreparationDTO.class);
    }

    @Override
    public HvPmMaterialPreparation getByWorkOrderCode(String workOrderCode) {
        return hvPmMaterialPreparationMapper.getByWorkOrderCode(workOrderCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addMaterialPreparationPlan(MaterialPreparationContentDTO materialPreparationContentDTO) {
        //检查数据是否合法
        checkMaterialPreparationContentDTO(materialPreparationContentDTO);

        HvPmXcMaterialCutPlan hvPmXcMaterialCutPlan = hvPmXcMaterialCutPlanService.getByOrderNoEquals(materialPreparationContentDTO.getWorkOrderCode());
        if (hvPmXcMaterialCutPlan == null) {
            throw new BaseKnownException( "不存在编号为："+ materialPreparationContentDTO.getWorkOrderCode() +"的切割计划！");
        }

        //修改型材切割计划的状态为 “备料中”
        hvPmXcMaterialCutPlan.setStatus(XcCutPlanStatusEnum.PREPARATION_ING.getCode());
        hvPmXcMaterialCutPlanRepository.save(hvPmXcMaterialCutPlan);

        LambdaQueryWrapper<HvPmMaterialPreparation> lqw = new LambdaQueryWrapper<>();
        lqw.eq(HvPmMaterialPreparation::getWorkOrderCode, materialPreparationContentDTO.getWorkOrderCode());
        HvPmMaterialPreparation hvPmMaterialPreparation = hvPmMaterialPreparationMapper.selectOne(lqw);
        if (hvPmMaterialPreparation != null) {
            throw new BaseKnownException("工单号：" + materialPreparationContentDTO.getWorkOrderCode() + "备料任务已存在！");
        }
        //需要保存的备料计划主表信息
        HvPmMaterialPreparationDTO hvPmMaterialPreparationDTO = new HvPmMaterialPreparationDTO();
        hvPmMaterialPreparationDTO.setWorkOrderCode(materialPreparationContentDTO.getWorkOrderCode());
        hvPmMaterialPreparationDTO.setApplyTime(new Date());
        hvPmMaterialPreparationDTO.setShipCode(hvPmXcMaterialCutPlan.getShipCode());
        hvPmMaterialPreparationDTO.setSegmentationCode(hvPmXcMaterialCutPlan.getSegmentationCode());
        hvPmMaterialPreparationDTO.setStatus(0);
        //添加进入备料计划主表
        Long preparationId = addHvPmMaterialPreparation(hvPmMaterialPreparationDTO);

        //处理备料逻辑
        prepareMaterials(materialPreparationContentDTO, preparationId, hvPmXcMaterialCutPlan);

        //添加或修改船型分段原料
        addOrUpShipRawMaterial(materialPreparationContentDTO, hvPmXcMaterialCutPlan);
    }

     //处理备料逻辑
    @Transactional(rollbackFor = Exception.class)
    public void prepareMaterials(MaterialPreparationContentDTO materialPreparationContentDTO, Long preparationId, HvPmXcMaterialCutPlan hvPmXcMaterialCutPlan) {
        //统计是否需要备料
        int blCount= 0;
        //新增库存占用集合
        ArrayList<StockOccupyDTO> addStockOccupyDTOS = new ArrayList<>();

        // 循环遍历备料物料编码，查询库存中是否存在
        for (MaterialPreparationDataDTO dataDTO : materialPreparationContentDTO.getData()) {

            if (dataDTO.getMaterialCode() == null || dataDTO.getMaterialCode().isEmpty()) {
                throw new BaseKnownException("物料编号不能为空！");
            }
            if (dataDTO.getSepces() == null || dataDTO.getSepces().isEmpty()) {
                throw new BaseKnownException("型材原材规格不能为空！");
            }
            if (dataDTO.getQuantity() == null || dataDTO.getQuantity() <= 0) {
                throw new BaseKnownException("型材原材数量不能小于！");
            }

            //物料备料需求数量
            int reqQuantity = dataDTO.getQuantity();
            //可用库存数量
            int handQuantity = 0;
            // 查询库存数量
            StockMaterialPreparationDTO stocksDTO = stockClient.getQuantityByMaterialCodeAndLocationCode(dataDTO.getMaterialCode(), "XCLK").getData();
            if (stocksDTO != null) {
                //存在库存
                //查询库存占用数量
                StockMaterialPreparationDTO usedCountDTO = stockClient.getUsedCountByMaterialCodeAndLocationCode(dataDTO.getMaterialCode(), "XCLK").getData();
                //统计可用库存数量
                if (usedCountDTO != null) {
                    handQuantity = stocksDTO.getQuantity().intValue() - usedCountDTO.getQuantity().intValue();
                } else {
                    handQuantity = stocksDTO.getQuantity().intValue();
                }
                int tempQuantity = 0; //临时计算值
                int occupyQuantity = 0; //新增的占用库存数
                //1.库存能够满足备料需求，这个物料不用备料，只需要占用库存
                if (handQuantity >= reqQuantity) {
                    occupyQuantity = reqQuantity;
                    //组装占用库存的数据
                    getStockOccupy(materialPreparationContentDTO.getWorkOrderCode(),addStockOccupyDTOS,occupyQuantity, stocksDTO.getLocationId(),stocksDTO.getMaterialId());

                } else if (handQuantity > 0 && handQuantity < reqQuantity) {
                    //2.库存只满足部分的备料数量，占用全部可用库存，进行部分备料
                    //部分备料数量 =  需求数 - 可用库存数
                    tempQuantity = reqQuantity - handQuantity;
                    occupyQuantity =  handQuantity;
                    //组装占用库存的数据
                    getStockOccupy(materialPreparationContentDTO.getWorkOrderCode(),addStockOccupyDTOS,occupyQuantity, stocksDTO.getLocationId(),stocksDTO.getMaterialId());

                    //部分备料
                    blCount = blCount + 1;
                    dataDTO.setQuantity(tempQuantity);
                    //下发备料任务
                    sendPreparationTask(dataDTO, preparationId, materialPreparationContentDTO.getWorkOrderCode(),hvPmXcMaterialCutPlan);
                } else {
                    //3.没有可用库存,全部备料
                    blCount = blCount + 1;
                    //下发备料任务
                    sendPreparationTask(dataDTO, preparationId, materialPreparationContentDTO.getWorkOrderCode(),hvPmXcMaterialCutPlan);
                }
            } else {
                //不存在库存，全部备料
                blCount = blCount + 1;
                //数据逐条处理并下发备料
                sendPreparationTask(dataDTO, preparationId, materialPreparationContentDTO.getWorkOrderCode(),hvPmXcMaterialCutPlan);
            }
        }
        //修改 备料计划主表的状态为1已下发
        HvPmMaterialPreparation materialPreparation= hvPmMaterialPreparationMapper.selectById(preparationId);
        materialPreparation.setStatus(1);
        hvPmMaterialPreparationMapper.updateById(materialPreparation);

        if (blCount ==0 ){
                //库存能够满足所有备料的需求，直接发送“备料完成通知”给型材切割线
                MaterialPreparationCompleteDTO materialPreparationCompleteDTO = new MaterialPreparationCompleteDTO();
                materialPreparationCompleteDTO.setWorkOrderCode(materialPreparationContentDTO.getWorkOrderCode());
                materialPreparationCompleteDTO.setLineId(hvPmXcMaterialCutPlan.getLineId());
                materialPreparationCompleteDTO.setResult("OK");

                //调用 下发产线备料完成通知 接口
                ResultVO finishVo = materialCuttingLineClient.sendMaterialPreparationComplete(materialPreparationCompleteDTO);

                //修改 备料计划主表的状态为完成
                try {
                    if (finishVo.isSuccess()) {
                        //状态 3-完成
                        materialPreparation.setStatus(3);
                        //完成时间
                        materialPreparation.setFinishTime(new Date());

                        //修改型材切割计划的状态为 “备料完成”
                        hvPmXcMaterialCutPlan.setStatus(XcCutPlanStatusEnum.PREPARATION_FINISH.getCode());
                        hvPmXcMaterialCutPlanRepository.save(hvPmXcMaterialCutPlan);
                    } else {
                        //状态 2-下发失败
                        materialPreparation.setStatus(2);
                        throw new BaseKnownException("下发产线备料完成通知失败！"+finishVo.getMessage());
                    }
                } catch (Exception e) {
                    throw e; // 重新抛出异常，以便它可以被外部的事务管理器捕获
                }
                //更新计划
                hvPmMaterialPreparationMapper.updateById(materialPreparation);
        }

        //批量新增库存占用记录
        if(addStockOccupyDTOS!=null &&  !addStockOccupyDTOS.isEmpty()){
            stockClient.batchAddOccupyStorage(addStockOccupyDTOS);
        }
    }

    //添加或修改船型分段原料
    public void addOrUpShipRawMaterial(MaterialPreparationContentDTO materialPreparationContentDTO, HvPmXcMaterialCutPlan hvPmXcMaterialCutPlan) {
        List<MaterialPreparationDataDTO> data = materialPreparationContentDTO.getData();
        //添加到船型分段原料
        HvPmShipRawMaterial shipRawMaterial = shipRawMaterialService.getByShipModelAndSegmentationCode(hvPmXcMaterialCutPlan.getShipMode(), hvPmXcMaterialCutPlan.getSegmentationCode());
        ShipRawMaterialDetailDTO shipRawMaterialDetailDTO;
        if (shipRawMaterial == null) {
            //船型分段原料不存在，添加船型分段原料和明细
            //添加船型分段原料
            ShipRawMaterialDTO shipRawMaterialDTO = new ShipRawMaterialDTO();
            shipRawMaterialDTO.setShipModel(hvPmXcMaterialCutPlan.getShipMode());
            shipRawMaterialDTO.setSegmentationCode(hvPmXcMaterialCutPlan.getSegmentationCode());
            Long shipRawMaterialId = shipRawMaterialService.createShipRawMaterial(shipRawMaterialDTO);
            //添加明细
            for (MaterialPreparationDataDTO datum : data) {
                shipRawMaterialDetailDTO = new ShipRawMaterialDetailDTO();
                shipRawMaterialDetailDTO.setRawMaterialId(shipRawMaterialId);
                shipRawMaterialDetailDTO.setQuantity(datum.getQuantity());
                shipRawMaterialDetailDTO.setSpecifications(datum.getSepces());
                shipRawMaterialDetailService.createShipRawMaterialDetail(shipRawMaterialDetailDTO);
            }
        } else {
            //船型分段原料存在，添加或修改明细
            for (MaterialPreparationDataDTO datum : data) {
                HvPmShipRawMaterialDetail shipRawMaterialDetail = shipRawMaterialDetailService.getByRawMaterialIdAndSpecifications(shipRawMaterial.getId(), datum.getSepces());
                if (shipRawMaterialDetail == null) {
                    //明细不存在，添加
                    shipRawMaterialDetailDTO = new ShipRawMaterialDetailDTO();
                    shipRawMaterialDetailDTO.setRawMaterialId(shipRawMaterial.getId());
                    shipRawMaterialDetailDTO.setQuantity(datum.getQuantity());
                    shipRawMaterialDetailDTO.setSpecifications(datum.getSepces());
                    shipRawMaterialDetailService.createShipRawMaterialDetail(shipRawMaterialDetailDTO);
                } else {
                    //明细存在，修改
                    shipRawMaterialDetail.setQuantity(datum.getQuantity());
                    shipRawMaterialDetailService.updateShipRawMaterialDetail(DtoMapper.convert(shipRawMaterialDetail, ShipRawMaterialDetailDTO.class));
                }
            }
        }
    }

    //检查数据是否合法
    public void checkMaterialPreparationContentDTO(MaterialPreparationContentDTO materialPreparationContentDTO){
        if (materialPreparationContentDTO.getWorkOrderCode() == null || materialPreparationContentDTO.getWorkOrderCode().isEmpty()){
            throw new BaseKnownException("工单编号不能为空！");
        }
        if (materialPreparationContentDTO.getData().isEmpty()){
            throw new BaseKnownException("备料物料列表不能为空！");
        }
    }


    //新增库存占用记录
    public void getStockOccupy(String workOrderCode,List<StockOccupyDTO> addStockOccupyDTOS,Integer occupyNum,Integer locationId,Integer materialId){
            StockOccupyDTO stockOccupyDTO = new StockOccupyDTO();
            stockOccupyDTO.setLocationId(locationId);
            stockOccupyDTO.setMaterialId(materialId);
            stockOccupyDTO.setQuantity(BigDecimal.valueOf(occupyNum));
            //占用的型材切割计划编码
            stockOccupyDTO.setOrderCode(workOrderCode);
            //占用的描述
            stockOccupyDTO.setDescription("型材备料占用");
            //占用的操作类型
            stockOccupyDTO.setOperation("占用");
            //新增占用记录
            addStockOccupyDTOS.add(stockOccupyDTO);
    }

    public void sendPreparationTask  ( MaterialPreparationDataDTO datum, Long preparationId,String workOrderCode,HvPmXcMaterialCutPlan hvPmXcMaterialCutPlan){

            HvPmMaterialPreparationDetailDTO detailDTO = new HvPmMaterialPreparationDetailDTO();
            //主表id
            detailDTO.setPreparationId(preparationId);
            //任务号
            String taskNo1 = serialCodeUtilsV2.generateCode("prepareTaskNo");
            if (StringUtils.isEmpty(taskNo1)) {
                throw new BaseKnownException("编码规则：taskNo 不存在~");
            }
            detailDTO.setTaskCode(taskNo1);
            //型材原材规格
            detailDTO.setSepces(datum.getSepces());
            detailDTO.setMaterialCode(datum.getMaterialCode());
            //需求数量
            detailDTO.setReqQuantity(datum.getQuantity());
            //创建时间
            detailDTO.setCreateTime(new Date());
            detailDTO.setOccupy(0);
            detailDTO.setShipCode(hvPmXcMaterialCutPlan.getShipCode());
            detailDTO.setSegmentationCode(hvPmXcMaterialCutPlan.getSegmentationCode());
            //添加进入从表
            Long detailId = hvPmMaterialPreparationDetailService.addHvPmMaterialPreparationDetailReturnId(detailDTO);

            /**
             * 2.备料任务下发给（华工）型材立库
             */
            MaterialPreparationTaskDTO materialPreparationTaskDTO = new MaterialPreparationTaskDTO();
            //第一层的数据：消息时间、数据、消息编号
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            materialPreparationTaskDTO.setMsgTime(dateFormat.format(new Date()));
            //创建第二层 datas 的集合
            List<MaterialPreparationTaskDetail> detailList = new ArrayList<>();
            MaterialPreparationTaskDetail detail = new MaterialPreparationTaskDetail();
            detail.setQty(datum.getQuantity());
            detail.setSepces(datum.getSepces());
            detail.setMatCode(datum.getMaterialCode());
            detail.setWorkOrderCode(workOrderCode);
            detail.setTaskCode(taskNo1);
            detail.setShipCode(hvPmXcMaterialCutPlan.getShipCode());
            detail.setSegmentationCode(hvPmXcMaterialCutPlan.getSegmentationCode());
            detailList.add(detail);
            materialPreparationTaskDTO.setDatas(detailList);
            //任务下发
            ResultVO resultVO = profileCompactStorageClient.sendPreparationTask(materialPreparationTaskDTO);

            //修改从表的状态
            HvPmMaterialPreparationDetail hvPmMaterialPreparationDetail = hvPmMaterialPreparationDetailMapper.selectById(detailId);
            if (resultVO.isSuccess()) {
                //下发成功
                hvPmMaterialPreparationDetail.setStatus(1);
            } else {
                //下发失败
                hvPmMaterialPreparationDetail.setStatus(2);
            }
            hvPmMaterialPreparationDetailMapper.updateById(hvPmMaterialPreparationDetail);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePreparation(MaterialPreparationResultDTO materialPreparationResultDTO) {

        HvPmMaterialPreparation hvPmMaterialPreparation = getByWorkOrderCode(materialPreparationResultDTO.getWorkOrderCode());
        if (hvPmMaterialPreparation == null) {
            throw new BaseKnownException("工单编号为" + materialPreparationResultDTO.getWorkOrderCode() + "的备料计划不存在");
        }
        if (materialPreparationResultDTO.getData().size() > 1) {
            throw new BaseKnownException("任务号为" + materialPreparationResultDTO.getTaskCode() + "的备料请求明细只能有一个");
        }

        //获取子表数据
        List<HvPmMaterialPreparationDetailDTO> detailDTOS = hvPmMaterialPreparationDetailService.getByPreparationId(hvPmMaterialPreparation.getId());
        if (detailDTOS.size() < 1) {
            throw new BaseKnownException("任务号为" + materialPreparationResultDTO.getTaskCode() + "的备料计划没有备料请求明细和结果");
        }


        for (HvPmMaterialPreparationDetailDTO detail : detailDTOS) {
            if (detail.getTaskCode().equals(materialPreparationResultDTO.getTaskCode())) {
                //更新需求数量
//                detail.setReqQuantity(materialPreparationResultDTO.getData().get(0).getReqQuantity());
                //更新实际数量
                detail.setActQuantity(materialPreparationResultDTO.getData().get(0).getActQuantity());
                //更新状态
                detail.setStatus(3);
                //更新从表
                hvPmMaterialPreparationDetailService.updateHvPmMaterialPreparationDetail(detail);
            }
        }
    }

    //修改主表状态
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePreparationStatus(String workOrderCode) {
        HvPmMaterialPreparation hvPmMaterialPreparation = getByWorkOrderCode(workOrderCode);
        List<HvPmMaterialPreparationDetailDTO> dtoList = hvPmMaterialPreparationDetailService.getByPreparationId(hvPmMaterialPreparation.getId());

        int i = 0;
        int j = 0;
        int k = 0;
        // 统计子表各种状态的数量，根据子表中的状态，修改主表的状态
        for (HvPmMaterialPreparationDetailDTO dto : dtoList) {
            if (dto.getStatus() == 1) {
                i++;
            } else if (dto.getStatus() == 2) {
                j++;
            } else if (dto.getStatus() == 3) {
                k++;
            }
        }
        //状态 ：0 新建 1：已下发 2：下发失败 3：完成
        if (i == dtoList.size()) {
            //更新状态
            hvPmMaterialPreparation.setStatus(1);
        } else if (j > 0) {
            hvPmMaterialPreparation.setStatus(2);
        } else if (k == dtoList.size()) {
            hvPmMaterialPreparation.setStatus(3);
            //完成时间
            hvPmMaterialPreparation.setFinishTime(new Date());
            //下发产线备料完成通知
            sendPreparationComplete(workOrderCode);
        }
        //更新计划
        hvPmMaterialPreparationMapper.updateById(hvPmMaterialPreparation);
    }

    //下发产线备料完成通知
    public void sendPreparationComplete(String workOrderCode) {
        MaterialPreparationCompleteDTO materialPreparationCompleteDTO = new MaterialPreparationCompleteDTO();
        HvPmXcMaterialCutPlan cutPlan = hvPmXcMaterialCutPlanService.getByOrderNoEquals(workOrderCode);
        if (cutPlan == null) {
            throw new BaseKnownException("作业号为" + workOrderCode + "的型材计划不存在！");
        }
        materialPreparationCompleteDTO.setWorkOrderCode(workOrderCode);
        materialPreparationCompleteDTO.setLineId(cutPlan.getLineId());
        materialPreparationCompleteDTO.setResult("OK");
        //调用 下发产线备料完成通知 接口
        ResultVO vo = materialCuttingLineClient.sendMaterialPreparationComplete(materialPreparationCompleteDTO);
        if (vo.isSuccess()) {
            //修改型材切割计划的状态为 “备料完成”
            cutPlan.setStatus(XcCutPlanStatusEnum.PREPARATION_FINISH.getCode());
            hvPmXcMaterialCutPlanRepository.save(cutPlan);
        }
    }

    @Override
    public HvPmMaterialPreparationDTO getPreparationByWorkOrderCode(String workOrderCode){
        HvPmMaterialPreparation hvPmMaterialPreparation = hvPmMaterialPreparationMapper.getByWorkOrderCode(workOrderCode);
        HvPmMaterialPreparationDTO hvPmMaterialPreparationDTO = DtoMapper.convert(hvPmMaterialPreparation, HvPmMaterialPreparationDTO.class);
        return hvPmMaterialPreparationDTO;
    }

    @Override
    public void stockSyncXcOccupyStock(StockInfoDTO stockInfoDTO) {
        //方案一：库存同步接口可以同步“型材切割计划编码"的字段
        //获取库位id
        ResultVO<WaresLocationDTO> waresLocationDTOResultVO = waresLocationClient.getByCode("XCLK");
        if (!waresLocationDTOResultVO.isSuccess()) {
            throw new BaseKnownException("库区编码：XCLK,不存在");
        }
        Integer waresLocationId = waresLocationDTOResultVO.getData().getId();
        //占用库存的列表
        List<StockOccupyDTO> addStockOccupyDTOS = new ArrayList<>();

        for (StockDetailDTO stockDetailDTO : stockInfoDTO.getList()) {
            for (MaterialStockDTO materialStockDTO : stockDetailDTO.getMaterialList()) {
                //根据物料编码查询物料
                ResultVO<MaterialDTO> materialVo = materialClient.getMaterialByMaterialCodeAndEigenvalue(materialStockDTO.getMaterialCode(), "1");
                if (materialVo.getData() == null) {
                    throw new BaseKnownException("物料编码：" + materialStockDTO.getMaterialCode() + "不存在");
                }

                //库存同步的数量
                Integer syncQuality = materialStockDTO.getQuality();
                //根据 切割计划编码和物料编码，查询备料计划详情列表
                List<HvPmMaterialPreparationDetailDTO> detailDTOList = hvPmMaterialPreparationMapper.getDetailByWorkOrderCodeAndMaterialCode(stockInfoDTO.getOrderCode(), materialStockDTO.getMaterialCode());

                //判断是否存在
                if (detailDTOList != null && !detailDTOList.isEmpty()) {
                    for (HvPmMaterialPreparationDetailDTO detailDTO : detailDTOList) {
                        //备料总数
                        Integer reqQuantity = detailDTO.getReqQuantity();
                        //已备料数
                        Integer actQuantity = detailDTO.getActQuantity() == null ? 0 : detailDTO.getActQuantity();
                        //需要占用库存数量
                        Integer needQuantity = reqQuantity - actQuantity;
                        //新增占用库存数量
                        Integer addStockOccupyQuantity = 0;

                        if (needQuantity > 0) {
                            //1.如果同步的物料数量 >= 需要占用库存数量
                            if (syncQuality >= needQuantity) {
                                //新增占用库存数量 = 需要占用库存数量
                                addStockOccupyQuantity = needQuantity;
                                //组装占用库存的列表
                                getStockOccupy(stockInfoDTO.getOrderCode(), addStockOccupyDTOS, addStockOccupyQuantity, waresLocationId, materialVo.getData().getId());
                                //修改备料详情列表为完成
                                detailDTO.setActQuantity(detailDTO.getReqQuantity());
                                detailDTO.setStatus(3);
                                hvPmMaterialPreparationDetailService.updateHvPmMaterialPreparationDetail(detailDTO);
                                syncQuality = syncQuality - reqQuantity;
                            } else if (syncQuality > 0 && syncQuality < needQuantity) {
                                //2.如果同步的物料数量 < 需要占用库存数量
                                //新增占用库存数量 =同步的物料数量
                                addStockOccupyQuantity = syncQuality;
                                //组装占用库存的列表
                                getStockOccupy(stockInfoDTO.getOrderCode(), addStockOccupyDTOS, addStockOccupyQuantity, waresLocationId, materialVo.getData().getId());
                                //修改备料详情列表为（1-已下发）
                                detailDTO.setActQuantity(actQuantity + addStockOccupyQuantity);
                                detailDTO.setStatus(1);
                                hvPmMaterialPreparationDetailService.updateHvPmMaterialPreparationDetail(detailDTO);
                                break;
                            } else {
                                break;
                            }

                        }


                    }
                }


            }
        }
        //判断是否都备料完成
        //是,修改备料计划状态并下发备料完成通知
        this.updatePreparationStatus(stockInfoDTO.getOrderCode());

        //批量新增库存占用记录
        if (addStockOccupyDTOS != null && !addStockOccupyDTOS.isEmpty()) {
            stockClient.batchAddOccupyStorage(addStockOccupyDTOS);
        }


    }

    //方案二：库存同步接口可以不能同步“型材切割计划编码”的字段
    public void  noSyncOrderCode(StockInfoDTO stockInfoDTO){
//        List<StockOccupyDTO> addStockOccupyDTOS = new ArrayList<>();
//        for (StockDetailDTO stockDetailDTO : stockInfoDTO.getList()) {
//            ResultVO<WaresLocationDTO> waresLocationDTOResultVO = waresLocationClient.getByCode("XCLK");
//            for (MaterialStockDTO materialStockDTO : stockDetailDTO.getMaterialList()) {
//                //同步的物料数量
//                Integer syncQuantity = materialStockDTO.getQuality();
//                //根据物料编码查询已下发状态的备料计划明细(明细信息和型材切割计划编码)，并且按照创建时间从早到晚排序
//                List<HvPmMaterialPreparationDetailDTO> detailDTOList =   hvPmMaterialPreparationDetailService.getDetailByMaterialCodeAndStatus(materialStockDTO.getMaterialCode(),1);
//                if (detailDTOList != null && detailDTOList.size() > 0) {
//                    //备料详情逐条的进行处理
//                    for (HvPmMaterialPreparationDetailDTO dto : detailDTOList) {
//                        //修改备料详情
//                        dto.setActQuantity(dto.getReqQuantity());
//                        dto.setStatus(3);
//                        hvPmMaterialPreparationDetailService.updateHvPmMaterialPreparationDetail(dto);
//                        //占用库存
//                        //根据物料编码查询物料
//                        ResultVO<MaterialDTO> material = materialClient.getMaterialByMaterialCodeAndEigenvalue(materialStockDTO.getMaterialCode(), "1");
//                        if (material.getData() == null) {
//                            throw new BaseKnownException("物料编码：" +materialStockDTO.getMaterialCode() + "不存在");
//                        }
//                        //组装占用库存的列表
//                        getStockOccupy(dto.getWorkOrderCode(),addStockOccupyDTOS,dto.getReqQuantity(),waresLocationDTOResultVO.getData().getId(),material.getData().getId());
//                        syncQuantity = syncQuantity -1;
//                        //同步物料已用完，跳出循环
//                        if(syncQuantity == 0){
//                            break;
//                        }
//                    }
//
//                }
//            }
//        }
//
//        //获取备料计划的明细已经全部完成，但备料计划状态还是"已下发"状态的备料计划
//        List<HvPmMaterialPreparation> hvPmMaterialPreparationDTOList =  hvPmMaterialPreparationMapper.getListByStatus(1);
//        for (HvPmMaterialPreparation hvPmMaterialPreparation : hvPmMaterialPreparationDTOList) {
//            //下发备料完成通知
//            sendPreparationComplete(hvPmMaterialPreparation.getWorkOrderCode());
//            //更新备料计划为完成
//            hvPmMaterialPreparation.setStatus(3);
//            hvPmMaterialPreparation.setFinishTime(new Date());
//            hvPmMaterialPreparationMapper.updateById(hvPmMaterialPreparation);
//        }
//
//        //批量新增库存占用记录
//        if(addStockOccupyDTOS!=null){
//            stockClient.batchAddOccupyStorage(addStockOccupyDTOS);
//        }
    }




}
