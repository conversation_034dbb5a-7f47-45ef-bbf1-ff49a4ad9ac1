package com.hvisions.pms.service;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.pms.dto.HvPmLineReportDTO;
import com.hvisions.pms.entity.HvPmLineReport;
import com.hvisions.thirdparty.common.dto.DetailReportDTO;
import com.hvisions.thirdparty.common.dto.MaterialCuttingLineReportDTO;
import org.springframework.data.domain.Page;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-11 9:46
 */
public interface HvPmLineReportService {
    /**
     * 保存
     *
     * @param hvPmLineReportDTO HvPmLineReport
     *
     */
    HvPmLineReport addHvPmLineReport(HvPmLineReportDTO hvPmLineReportDTO);

    /**
     * 通过id删除
     *
     * @param id 主键
     *
     */
    void deleteHvPmLineReport(long id);

    /**
     * 修改
     *
     * @param hvPmLineReportDTO HvPmLineReport
     *
     */
    void updateHvPmLineReport(HvPmLineReportDTO hvPmLineReportDTO);

    /**
     * 获取
     *
     * @param id 主键
     * @return HvPmLineReport hvPmLineReportDTO HvPmLineReport
     */
    HvPmLineReportDTO getHvPmLineReportById(long id);

    /**
     * 获取列表
     *
     * @return List<HvPmLineReportDTO> HvPmLineReport列表
     */
    List<HvPmLineReportDTO> getAll();

    Page<HvPmLineReportDTO> findPageByCondition(HvPmLineReportDTO condition);

    void lineReportExecute(MaterialCuttingLineReportDTO steelPlateDTO);

    ResultVO<ExcelExportDto> exportLineReport(HvPmLineReportDTO condition)  throws IOException, IllegalAccessException;

    void steelPlateReport(DetailReportDTO detailReportDTO);
}
