package com.hvisions.pms.service;

import com.hvisions.pms.entity.plan.HvPmMaterialCutPlanDetail1;
import com.hvisions.pms.plan.HvPmMaterialCutPlanDetail1DTO;

import java.util.List;

public interface HvPmMaterialCutPlanDetail1Service {

    /**
     * 根据切割计划id获取细节列表
     * @param id
     * @return
     */
    List<HvPmMaterialCutPlanDetail1> getAllDetail1ByCutPlanId(long id);

    /**
     * 添加
     * @param hvPmMaterialCutPlanDetail1DTO
     * @return
     */
    long createDetail1(HvPmMaterialCutPlanDetail1DTO hvPmMaterialCutPlanDetail1DTO);

    /**
     * 修改
     * @param hvPmMaterialCutPlanDetail1DTO
     * @return
     */
    long updateDetail1(HvPmMaterialCutPlanDetail1DTO hvPmMaterialCutPlanDetail1DTO);

    /**
     * 删除
     * @param id
     */
    void deleteDetail1ById(long id);

    List<HvPmMaterialCutPlanDetail1> getDetail1ListByCutPlanIds(List<Long> cutPlanIds);

    void createDetail1Batch(List<HvPmMaterialCutPlanDetail1> detail1Entities);
}
