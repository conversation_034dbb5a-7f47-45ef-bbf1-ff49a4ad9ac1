package com.hvisions.pms.repository.changeshift;

import com.hvisions.pms.entity.changeshift.HvPmChangeShiftsRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: HvPmChangeShiftsRecord</p >
 * <p>Description: 交接班记录仓储层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-10-12</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Repository
public interface ChangeShiftsRecordRepository extends JpaRepository<HvPmChangeShiftsRecord, Integer> {


    @Query(value = "select  max(h.id) from HvPmChangeShiftsRecord h where h.workCenterId = ?1 ")
    Integer getMaxId(int workCenterId);


    /**
     * 根据交接班状态查询
     *
     * @param state       交接班状态
     * @param workCenterId 设备Id
     * @return 交接班记录
     */
    List<HvPmChangeShiftsRecord> getAllByStateAndWorkCenterId(int state, int workCenterId);


    /**
     * 根据交接班记录状态和接班人查询
     *
     * @param state       交接班记录状态
     * @param workCenterId 设备Id
     * @return 交接班记录状态
     */
    HvPmChangeShiftsRecord getHvPmChangeShiftsRecordByStateAndWorkCenterId(int state, int workCenterId);

    /**
     * 根据交接班记录状态和接班人查询
     *
     * @param state       交接班记录状态
     * @param overMan     接班人ID
     * @param workCenterId 设备Id
     * @return 交接班记录状态
     */
    HvPmChangeShiftsRecord getHvPmChangeShiftsRecordByStateAndHandManAndWorkCenterId(int state, int overMan,
                                                                                    int workCenterId);
}