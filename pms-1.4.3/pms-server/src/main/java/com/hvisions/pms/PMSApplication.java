package com.hvisions.pms;

import com.hvisions.pms.config.RedisProperties;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <p>Title: DemoApplication</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/7</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@EnableScheduling
@SpringBootApplication
@EnableCaching
@EnableAsync
@EnableDiscoveryClient
@MapperScan("com.hvisions.pms.dao")
@EnableConfigurationProperties(RedisProperties.class)
@EnableFeignClients(basePackages = {
        "com.hvisions.hiperbase.client",
        "com.hvisions.framework.client",
        "com.hvisions.thridparty.client",
        "com.hvisions.wms.client"})
public class PMSApplication extends SpringBootServletInitializer {

    public static void main(String[] args) {
        SpringApplication.run(PMSApplication.class, args);
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        return builder.sources(PMSApplication.class);
    }


}
