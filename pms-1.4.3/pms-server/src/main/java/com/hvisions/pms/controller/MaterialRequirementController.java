package com.hvisions.pms.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.EasyExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.pms.dto.materialRequirement.HvPmMaterialBomItemDTO;
import com.hvisions.pms.dto.materialRequirement.HvPmMaterialBomItemQueryDTO;
import com.hvisions.pms.dto.materialRequirement.MaterialRequirementDTO;
import com.hvisions.pms.dto.materialRequirement.MaterialRequirementQueryDTO;
import com.hvisions.pms.dto.productWorkOrder.ProductWorkOrderQueryDTO;
import com.hvisions.pms.entity.HvPmMaterialRequirement;
import com.hvisions.pms.service.HvPmMaterialBomItemService;
import com.hvisions.pms.service.MaterialRequirementService;
import com.hvisions.pms.utils.SerialCodeUtilsV2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping(value = "/materialRequirement")
@Api(description = "物料需求")
public class MaterialRequirementController {

    @Autowired
    private MaterialRequirementService materialRequirementService;
    @Autowired
    private SerialCodeUtilsV2 serialCodeUtilsV2;

    @Autowired
    private HvPmMaterialBomItemService materialBomItemService;

    @PostMapping(value = "/getPage")
    @ApiOperation("分页查询")
    public Page<MaterialRequirementDTO> getPage(@RequestBody MaterialRequirementQueryDTO materialRequirementQueryDTO) {
        return materialRequirementService.getPage(materialRequirementQueryDTO);
    }

    @GetMapping(value = "/getByCode/{id}")
    @ApiOperation("根据code查询")
    public MaterialRequirementDTO getByCode(@PathVariable String id) {
        return DtoMapper.convert(materialRequirementService.getOne(new LambdaQueryWrapper<HvPmMaterialRequirement>()
                .eq(HvPmMaterialRequirement::getRequirementCode, id)), MaterialRequirementDTO.class);
    }

    @PostMapping(value = "/addMaterialRequirement")
    @ApiOperation("添加物料需求")
    public ResultVO addMaterialRequirement(@RequestBody MaterialRequirementDTO materialRequirementDTO, @UserInfo @ApiIgnore UserInfoDTO userInfo) {
//        需求编号
        materialRequirementDTO.setRequirementCode(serialCodeUtilsV2.generateCode("RequirementCode"));
        materialRequirementDTO.setUpdaterName(userInfo.getUserName());
        materialRequirementDTO.setCreatorName(userInfo.getUserName());
        materialRequirementDTO.setCreateTime(LocalDateTime.now());
        materialRequirementDTO.setUpdateTime(LocalDateTime.now());
        return ResultVO.success(materialRequirementService.save(DtoMapper.convert(materialRequirementDTO, HvPmMaterialRequirement.class)));
    }

    @PutMapping(value = "/updateByCode")
    @ApiOperation("更新物料需求(根据requirementCode)")
    public ResultVO updateByCode(@RequestBody MaterialRequirementDTO materialRequirementDTO, @UserInfo @ApiIgnore UserInfoDTO userInfo) {
        materialRequirementDTO.setUpdateTime(LocalDateTime.now());
        materialRequirementDTO.setUpdaterName(userInfo.getUserName());
        return ResultVO.success(materialRequirementService.update(DtoMapper.convert(materialRequirementDTO, HvPmMaterialRequirement.class),
                new LambdaQueryWrapper<HvPmMaterialRequirement>()
                        .eq(HvPmMaterialRequirement::getRequirementCode,materialRequirementDTO.getRequirementCode())));
    }

    @DeleteMapping(value = "/deleteByCode/{id}")
    @ApiOperation("删除物料需求(根据requirementCode)")
    public ResultVO deleteByCode(@PathVariable String id) {
        return ResultVO.success(materialRequirementService.remove(new LambdaQueryWrapper<HvPmMaterialRequirement>()
                .eq(HvPmMaterialRequirement::getRequirementCode,id)));
    }

    /**
     * 导出
     *
     * @param materialRequirementQueryDTO
     */
    @ApiOperation(value = "导出")
    @PostMapping(value = "/exportData")
    @ApiResultIgnore
    public ResultVO<ExcelExportDto> exportData(@RequestBody MaterialRequirementQueryDTO materialRequirementQueryDTO) {
        List<HvPmMaterialRequirement> list =  materialRequirementService.findListByCondition(materialRequirementQueryDTO);
        return ResultVO.success(EasyExcelUtil.getExcel(list, HvPmMaterialRequirement.class, System.currentTimeMillis() + "物料需求计划数据.xlsx"));
    }

    /**
     * 根据生产工单编号获取对应的子项物料
     */
    @PostMapping("/getMaterialByWorkOrderCode")
    @ApiOperation(value = "根据生产工单编号获取对应的子项物料")
    public List<HvPmMaterialBomItemDTO> getMaterialByWorkOrderCode(@RequestBody MaterialRequirementQueryDTO materialRequirementQueryDTO) {
        HvPmMaterialRequirement materialRequirement = materialRequirementService.getOne(new LambdaQueryWrapper<HvPmMaterialRequirement>().eq(HvPmMaterialRequirement::getWorkOrderCode, materialRequirementQueryDTO.getWorkOrderCode()));
        HvPmMaterialBomItemQueryDTO queryDTO = new HvPmMaterialBomItemQueryDTO();
        queryDTO.setPage(materialRequirementQueryDTO.getPage());
        queryDTO.setPageSize(materialRequirementQueryDTO.getPageSize());
        queryDTO.setMaterialRequirementCode(materialRequirement.getRequirementCode());
        return  materialBomItemService.getPageMaterialByRequirementCode(queryDTO).getContent();
    }

}
