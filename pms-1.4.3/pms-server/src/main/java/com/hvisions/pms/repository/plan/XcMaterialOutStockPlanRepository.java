package com.hvisions.pms.repository.plan;

import com.hvisions.pms.entity.plan.HvPmXcMaterialOutStockPlan;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/5/7
 */
@Repository
public interface XcMaterialOutStockPlanRepository extends JpaRepository<HvPmXcMaterialOutStockPlan,Integer> {

    /**
     * 是否存在型材出库计划
     * @param taskNo
     * @return
     */
    HvPmXcMaterialOutStockPlan getByTaskNo(String taskNo);

    List<HvPmXcMaterialOutStockPlan> getByWorkOrderCode(String workOrderCode);
}
