package com.hvisions.pms.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.annotation.EnableFilter;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.pms.dto.HvPmLineReportDTO;
import com.hvisions.pms.service.HvPmLineReportService;
import com.hvisions.thirdparty.common.dto.DetailReportDTO;
import com.hvisions.thirdparty.common.dto.MaterialCuttingLineReportDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-11 9:57
 */
@RestController
@RequestMapping(value = "/hvPmLineReport")
@Api(description = "产线报工记录")
public class LineReportController {
    @Autowired
    private HvPmLineReportService hvPmLineReportService;
    /**
     * 添加
     *
     * @param hvPmLineReportDTO HvPmLineReport
     */
    @ApiOperation(value = "添加HvPmLineReport信息")
    @PostMapping(value = "/add")
    public void addHvPmLineReport(@Valid @RequestBody HvPmLineReportDTO hvPmLineReportDTO) {
        hvPmLineReportService.addHvPmLineReport(hvPmLineReportDTO);
    }

    /**
     * 删除
     *
     * @param id 主键
     */
    @ApiOperation(value = "删除HvPmLineReport信息")
    @DeleteMapping(value = "/delete/{id}")
    public void deleteHvPmLineReport(@PathVariable Integer id) {
        hvPmLineReportService.deleteHvPmLineReport(id);
    }

    /**
     * 修改
     *
     * @param hvPmLineReportDTO HvPmLineReport
     */
    @ApiOperation(value = "修改HvPmLineReport")
    @PutMapping(value = "/update")
    public void updateHvPmLineReport(@Valid @RequestBody HvPmLineReportDTO hvPmLineReportDTO) {
        hvPmLineReportService.updateHvPmLineReport(hvPmLineReportDTO);
    }

    /**
     * 根据id获取
     *
     * @param id 主键
     * @return HvPmLineReport hvPmLineReportDTO
     */
    @ApiOperation(value = "根据id获取HvPmLineReport")
    @GetMapping(value = "/get/{id}")
    public HvPmLineReportDTO getList(@PathVariable Integer id) {
        return hvPmLineReportService.getHvPmLineReportById(id);
    }

    /**
     * 查询全部
     * @return 列表
     */
    @ApiOperation(value = "获取HvPmLineReport列表")
    @GetMapping(value = "/getAll")
    public List<HvPmLineReportDTO> getAll(){
        return hvPmLineReportService.getAll();
    }

    /**
     * 根据 条件分页查询产线报工记录
     *
     * @param condition 查询条件
     * @return 检查项目分页信息
     */
    @EnableFilter
    @PostMapping(value = "/findPageByCondition")
    @ApiOperation(value = "根据 条件分页查询产线报工记录")
    public Page<HvPmLineReportDTO> findPageByCondition(@RequestBody HvPmLineReportDTO condition) {
        return hvPmLineReportService.findPageByCondition(condition);
    }

    /**
     * 产线报工
     *
     */
    @ApiOperation(value = "添加HvPmLineReport信息")
    @PostMapping(value = "/lineReportExecute")
    public void lineReportExecute(@Valid @RequestBody MaterialCuttingLineReportDTO steelPlateDTO){
        hvPmLineReportService.lineReportExecute(steelPlateDTO);
    }

    /**
     * 导出
     *
     */
    @ApiOperation(value = "导出HvPmLineReport信息")
    @PostMapping(value = "/exportLineReport")
    @ApiResultIgnore
    public ResultVO<ExcelExportDto> exportLineReport(@RequestBody HvPmLineReportDTO condition) throws IOException, IllegalAccessException {
        return  hvPmLineReportService.exportLineReport(condition);
    }


    /**
     *  报工
     * @param detailReportDTO detailReportDTO
     */
    @ApiOperation("接收-钢板零件报工(切割计划报工)")
    @PostMapping("/steelPlateReport")
    public void steelPlateReport(@RequestBody DetailReportDTO detailReportDTO){
        hvPmLineReportService.steelPlateReport(detailReportDTO);
    }
}
