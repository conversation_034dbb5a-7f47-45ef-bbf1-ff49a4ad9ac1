package com.hvisions.pms.service.imp;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.hiperbase.client.MaterialPointAreaClient;
import com.hvisions.pms.dao.MaterialCutPlanProcessRecordMapper;
import com.hvisions.pms.entity.HvPmMaterialCutPlanProcessRecord;
import com.hvisions.pms.service.MaterialCutPlanProcessRecordService;
import com.hvisions.thirdparty.common.dto.AGVStatusDTO;
import com.hvisions.thirdparty.common.dto.DetailReportDTO;
import com.hvisions.thirdparty.common.dto.IWMSCallMaterialDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-06-27 15:07
 */
@Service
public class MaterialCutPlanProcessRecordServiceImpl extends ServiceImpl<MaterialCutPlanProcessRecordMapper, HvPmMaterialCutPlanProcessRecord> implements MaterialCutPlanProcessRecordService {
        @Autowired
        private MaterialCutPlanProcessRecordMapper materialCutPlanProcessRecordMapper;
        @Autowired
        private MaterialPointAreaClient materialPointAreaClient;

        private final SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        @Override
        public List<HvPmMaterialCutPlanProcessRecord> getAll() {
            return materialCutPlanProcessRecordMapper.getAll();
        }

        @Override
        public List<HvPmMaterialCutPlanProcessRecord> findByLineCode(String lineCode) {
            return materialCutPlanProcessRecordMapper.findByLineCode(lineCode);
        }

        @Override
        public boolean addData() {
            List<String> lineCodeList = Arrays.asList("ZNDBQGX","ZNBJQGAX","BJJGFHHJXSL","YXXZLZHXSL","XZLZHAXSL");
            List<Integer> hjStepList = Arrays.asList(9,10,11,12);
            List<Integer> qgStepList = Arrays.asList(1,2,3,4,5,6,7,8);
            HvPmMaterialCutPlanProcessRecord hvPmMaterialCutPlanProcessRecord;
            ArrayList<HvPmMaterialCutPlanProcessRecord> list = new ArrayList<>();
            for (String lineCode : lineCodeList) {
                if(lineCode.equals("ZNDBQGX") || lineCode.equals("ZNBJQGAX")){
                    for (Integer integer : qgStepList) {
                        hvPmMaterialCutPlanProcessRecord = new HvPmMaterialCutPlanProcessRecord();
                        hvPmMaterialCutPlanProcessRecord.setCreateDateTime(new Date());
                        hvPmMaterialCutPlanProcessRecord.setLineCode(lineCode);
                        hvPmMaterialCutPlanProcessRecord.setStepId(integer);
                        hvPmMaterialCutPlanProcessRecord.setStepStatus(0);
                        list.add(hvPmMaterialCutPlanProcessRecord);
                    }
                }else{
                    for (Integer integer : hjStepList) {
                        hvPmMaterialCutPlanProcessRecord = new HvPmMaterialCutPlanProcessRecord();
                        hvPmMaterialCutPlanProcessRecord.setCreateDateTime(new Date());
                        hvPmMaterialCutPlanProcessRecord.setLineCode(lineCode);
                        hvPmMaterialCutPlanProcessRecord.setStepId(integer);
                        hvPmMaterialCutPlanProcessRecord.setStepStatus(0);
                        list.add(hvPmMaterialCutPlanProcessRecord);
                    }
                }
            }
            this.saveBatch(list);
            return false;
        }

        public void updateDataForHjThreeStep(AGVStatusDTO agvStatusDTO){
            //大板切割线
            if(agvStatusDTO.getCurrentPositionCode().equals("Q101")){
                HvPmMaterialCutPlanProcessRecord hvPmMaterialCutPlanProcessRecord = new HvPmMaterialCutPlanProcessRecord();
                hvPmMaterialCutPlanProcessRecord.setLineCode("YXXZLZHXSL");
                hvPmMaterialCutPlanProcessRecord.setSequence(3);
                hvPmMaterialCutPlanProcessRecord.setStepStatus(1);
                hvPmMaterialCutPlanProcessRecord.setStepEndTime(new Date());
                hvPmMaterialCutPlanProcessRecord.setProcessId(2);
                materialCutPlanProcessRecordMapper.updateData(hvPmMaterialCutPlanProcessRecord);
            }

            //部件切割A线
            if(agvStatusDTO.getCurrentPositionCode().equals("Q303")){
                HvPmMaterialCutPlanProcessRecord hvPmMaterialCutPlanProcessRecord = new HvPmMaterialCutPlanProcessRecord();
                hvPmMaterialCutPlanProcessRecord.setLineCode("XZLZHAXSL");
                hvPmMaterialCutPlanProcessRecord.setSequence(3);
                hvPmMaterialCutPlanProcessRecord.setStepStatus(1);
                hvPmMaterialCutPlanProcessRecord.setStepEndTime(new Date());
                hvPmMaterialCutPlanProcessRecord.setProcessId(2);
                materialCutPlanProcessRecordMapper.updateData(hvPmMaterialCutPlanProcessRecord);
            }

            //型材切割B线
            if(agvStatusDTO.getCurrentPositionCode().equals("R201")){
                HvPmMaterialCutPlanProcessRecord hvPmMaterialCutPlanProcessRecord = new HvPmMaterialCutPlanProcessRecord();
                hvPmMaterialCutPlanProcessRecord.setLineCode("BJJGFHHJXSL");
                hvPmMaterialCutPlanProcessRecord.setSequence(3);
                hvPmMaterialCutPlanProcessRecord.setStepStatus(1);
                hvPmMaterialCutPlanProcessRecord.setStepEndTime(new Date());
                hvPmMaterialCutPlanProcessRecord.setProcessId(2);
                materialCutPlanProcessRecordMapper.updateData(hvPmMaterialCutPlanProcessRecord);
            }
        }


        public boolean updateDataForQgTwoToSixStep(DetailReportDTO detailReportDTO) {
            String pickEndTime = detailReportDTO.getPickEndTime(); //6
            String pickStartTime = detailReportDTO.getPickStartTime(); //5
            String cuttingEndTime = detailReportDTO.getCuttingEndTime(); //4
            String cuttingStartTime = detailReportDTO.getCuttingStartTime(); //3
            String outEndTime = detailReportDTO.getOutEndTime(); //2
            HvPmMaterialCutPlanProcessRecord hvPmMaterialCutPlanProcessRecord;
            try {
                if(StringUtils.isNotBlank(outEndTime)){
                    hvPmMaterialCutPlanProcessRecord = new HvPmMaterialCutPlanProcessRecord();
                    hvPmMaterialCutPlanProcessRecord.setLineCode(detailReportDTO.getLineCode());
                    hvPmMaterialCutPlanProcessRecord.setSequence(2);
                    hvPmMaterialCutPlanProcessRecord.setStepStatus(1);
                    hvPmMaterialCutPlanProcessRecord.setStepEndTime(format.parse(outEndTime));
                    hvPmMaterialCutPlanProcessRecord.setProcessId(2);
                    materialCutPlanProcessRecordMapper.updateData(hvPmMaterialCutPlanProcessRecord);
                }

                if(StringUtils.isNotBlank(cuttingStartTime)){
                    hvPmMaterialCutPlanProcessRecord = new HvPmMaterialCutPlanProcessRecord();
                    hvPmMaterialCutPlanProcessRecord.setLineCode(detailReportDTO.getLineCode());
                    hvPmMaterialCutPlanProcessRecord.setSequence(3);
                    hvPmMaterialCutPlanProcessRecord.setStepStatus(1);
                    hvPmMaterialCutPlanProcessRecord.setStepEndTime(format.parse(cuttingStartTime));
                    hvPmMaterialCutPlanProcessRecord.setProcessId(2);
                    materialCutPlanProcessRecordMapper.updateData(hvPmMaterialCutPlanProcessRecord);
                }

                if(StringUtils.isNotBlank(cuttingEndTime)){
                    hvPmMaterialCutPlanProcessRecord = new HvPmMaterialCutPlanProcessRecord();
                    hvPmMaterialCutPlanProcessRecord.setLineCode(detailReportDTO.getLineCode());
                    hvPmMaterialCutPlanProcessRecord.setSequence(4);
                    hvPmMaterialCutPlanProcessRecord.setStepStatus(1);
                    hvPmMaterialCutPlanProcessRecord.setStepEndTime(format.parse(cuttingEndTime));
                    hvPmMaterialCutPlanProcessRecord.setProcessId(2);
                    materialCutPlanProcessRecordMapper.updateData(hvPmMaterialCutPlanProcessRecord);
                }

                if(StringUtils.isNotBlank(pickStartTime)){
                    hvPmMaterialCutPlanProcessRecord = new HvPmMaterialCutPlanProcessRecord();
                    hvPmMaterialCutPlanProcessRecord.setLineCode(detailReportDTO.getLineCode());
                    hvPmMaterialCutPlanProcessRecord.setSequence(5);
                    hvPmMaterialCutPlanProcessRecord.setStepStatus(1);
                    hvPmMaterialCutPlanProcessRecord.setStepEndTime(format.parse(pickStartTime));
                    hvPmMaterialCutPlanProcessRecord.setProcessId(2);
                    materialCutPlanProcessRecordMapper.updateData(hvPmMaterialCutPlanProcessRecord);

                }

                if(StringUtils.isNotBlank(pickEndTime)){
                    hvPmMaterialCutPlanProcessRecord = new HvPmMaterialCutPlanProcessRecord();
                    hvPmMaterialCutPlanProcessRecord.setLineCode(detailReportDTO.getLineCode());
                    hvPmMaterialCutPlanProcessRecord.setSequence(6);
                    hvPmMaterialCutPlanProcessRecord.setStepStatus(1);
                    hvPmMaterialCutPlanProcessRecord.setStepEndTime(format.parse(pickStartTime));
                    hvPmMaterialCutPlanProcessRecord.setProcessId(2);
                    materialCutPlanProcessRecordMapper.updateData(hvPmMaterialCutPlanProcessRecord);

                }
                return true;
            }catch (Exception e){
                return false;
            }

        }


    public boolean updateDataForQgSevenStep(AGVStatusDTO agvStatusDTO) {
        if(agvStatusDTO.getAutoSupplementing() != 1){
            return false;
        }
        try {
            String lineCode = materialPointAreaClient.findLineCodeByMaterialPointCode(agvStatusDTO.getCurrentPositionCode()).getData();
            HvPmMaterialCutPlanProcessRecord hvPmMaterialCutPlanProcessRecord = new HvPmMaterialCutPlanProcessRecord();
            hvPmMaterialCutPlanProcessRecord.setLineCode(lineCode);
            hvPmMaterialCutPlanProcessRecord.setSequence(lineCode.equals("ZNXCQGBX") ? 5 : 7);
            hvPmMaterialCutPlanProcessRecord.setStepStatus(1);
            hvPmMaterialCutPlanProcessRecord.setStepEndTime(new Date());
            hvPmMaterialCutPlanProcessRecord.setProcessId(lineCode.equals("ZNXCQGBX") ? 3 : 2);
            materialCutPlanProcessRecordMapper.updateData(hvPmMaterialCutPlanProcessRecord);
            return true;
        }catch (Exception e){
            return false;
        }
    }

    public void updateDataForQgEightStep(AGVStatusDTO agvStatusDTO) {
        String lineCode = materialPointAreaClient.findLineCodeByMaterialPointCode(agvStatusDTO.getCurrentPositionCode()).getData();
        //大板切割线
        if(agvStatusDTO.getPalletCode().equals("100001") && lineCode.equals("ZNDBQGX")){
            HvPmMaterialCutPlanProcessRecord hvPmMaterialCutPlanProcessRecord = new HvPmMaterialCutPlanProcessRecord();
            hvPmMaterialCutPlanProcessRecord.setLineCode(lineCode);
            hvPmMaterialCutPlanProcessRecord.setSequence(8);
            hvPmMaterialCutPlanProcessRecord.setStepStatus(1);
            hvPmMaterialCutPlanProcessRecord.setStepEndTime(new Date());
            hvPmMaterialCutPlanProcessRecord.setProcessId(2);
            materialCutPlanProcessRecordMapper.updateData(hvPmMaterialCutPlanProcessRecord);
        }

        //部件切割A线
        if(agvStatusDTO.getPalletCode().equals("100002") && lineCode.equals("ZNBJQGAX")){
            HvPmMaterialCutPlanProcessRecord hvPmMaterialCutPlanProcessRecord = new HvPmMaterialCutPlanProcessRecord();
            hvPmMaterialCutPlanProcessRecord.setLineCode(lineCode);
            hvPmMaterialCutPlanProcessRecord.setSequence(8);
            hvPmMaterialCutPlanProcessRecord.setStepStatus(1);
            hvPmMaterialCutPlanProcessRecord.setStepEndTime(new Date());
            hvPmMaterialCutPlanProcessRecord.setProcessId(2);
            materialCutPlanProcessRecordMapper.updateData(hvPmMaterialCutPlanProcessRecord);
        }

        //型材切割B线
        if (agvStatusDTO.getPalletCode().equals("600001") && lineCode.equals("ZNXCQGBX")) {
            HvPmMaterialCutPlanProcessRecord hvPmMaterialCutPlanProcessRecord = new HvPmMaterialCutPlanProcessRecord();
            hvPmMaterialCutPlanProcessRecord.setLineCode(lineCode);
            hvPmMaterialCutPlanProcessRecord.setSequence(6);
            hvPmMaterialCutPlanProcessRecord.setStepStatus(1);
            hvPmMaterialCutPlanProcessRecord.setStepEndTime(new Date());
            hvPmMaterialCutPlanProcessRecord.setProcessId(3);
            materialCutPlanProcessRecordMapper.updateData(hvPmMaterialCutPlanProcessRecord);
        }
    }



    @Override
    public boolean updateData(HvPmMaterialCutPlanProcessRecord hvPmMaterialCutPlanProcessRecord) {
        try {
            hvPmMaterialCutPlanProcessRecord.setStepStatus(1);
            hvPmMaterialCutPlanProcessRecord.setStepEndTime(new Date());
            materialCutPlanProcessRecordMapper.updateData(hvPmMaterialCutPlanProcessRecord);
            return true;
        }catch (Exception e){
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public void updateDataForAGVStatusDTO(AGVStatusDTO agvStatusDTO) {

        updateDataForQgSevenStep(agvStatusDTO);
        updateDataForQgEightStep(agvStatusDTO);
        updateDataForHjThreeStep(agvStatusDTO);
    }

    @Override
    public void updateDataForIWMSCallMaterialDTO(IWMSCallMaterialDTO callMaterialDTO) {
        String lineCode = callMaterialDTO.getLineCode();
        HvPmMaterialCutPlanProcessRecord hvPmMaterialCutPlanProcessRecord;
        switch (lineCode){
            case "BJJGFHHJXSL":
            case "YXXZLZHXSL":
            case "XZLZHAXSL":
                hvPmMaterialCutPlanProcessRecord = new HvPmMaterialCutPlanProcessRecord();
                hvPmMaterialCutPlanProcessRecord.setLineCode(lineCode);
                hvPmMaterialCutPlanProcessRecord.setSequence(3);
                hvPmMaterialCutPlanProcessRecord.setStepStatus(1);
                hvPmMaterialCutPlanProcessRecord.setStepEndTime(new Date());
                hvPmMaterialCutPlanProcessRecord.setProcessId(2);
                materialCutPlanProcessRecordMapper.updateData(hvPmMaterialCutPlanProcessRecord);
                break;
            default:
                break;
        }
    }
}
