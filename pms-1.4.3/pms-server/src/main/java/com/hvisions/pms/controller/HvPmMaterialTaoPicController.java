package com.hvisions.pms.controller;

import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.client.LocationExtendClient;
import com.hvisions.hiperbase.equipment.location.LocationDTO;
import com.hvisions.pms.dto.HvPmMaterialTaoPicDTO;
import com.hvisions.pms.dto.HvPmMaterialTaoPicDetailDTO;
import com.hvisions.pms.dto.HvPmMaterialTaoPicDetailQueryDTO;
import com.hvisions.pms.dto.HvPmXcMaterialCutPlanQueryDTO;
import com.hvisions.pms.entity.HvPmMaterialTaoPicDetail;
import com.hvisions.pms.service.HvPmMaterialTaoPicDetailService;
import com.hvisions.pms.service.HvPmMaterialTaoPicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title: HvPmMaterialTaoPicController</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2024年4月19日</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Deprecated
@RestController
@RequestMapping(value = "/hvPmMaterialTaoPic")
@Api(description = "型材切割")
public class HvPmMaterialTaoPicController {
    @Resource
    private HvPmMaterialTaoPicService hvPmMaterialTaoPicService;
    @Resource
    private HvPmMaterialTaoPicDetailService hvPmMaterialTaoPicDetailService;
    @Resource
    private LocationExtendClient locationExtendClient;


    @ApiOperation(value = "获取HvPmMaterialTaoPic列表")
    @PostMapping(value = "/list")
    public Page<HvPmMaterialTaoPicDTO> getList(@RequestBody HvPmXcMaterialCutPlanQueryDTO hvPmMaterialTaoPicDTO) {
        return hvPmMaterialTaoPicService.getPage(hvPmMaterialTaoPicDTO);
    }

    @GetMapping("/getLineList")
    @ApiOperation("获取产线列表")
    public List<LocationDTO> getLineList() {
        ResultVO<List<LocationDTO>> locationListByType = locationExtendClient.getLocationListByType(40);
        return locationListByType.getData();
    }

    /**
     * 添加
     *
     * @param hvPmMaterialTaoPicDTO HvPmMaterialTaoPic
     */
    @ApiOperation(value = "添加HvPmMaterialTaoPic信息")
    @PostMapping(value = "/add")
    public void addHvPmMaterialTaoPic(@Valid @RequestBody HvPmMaterialTaoPicDTO hvPmMaterialTaoPicDTO) {
        hvPmMaterialTaoPicService.addHvPmMaterialTaoPic(hvPmMaterialTaoPicDTO);
    }

    /**
     * 删除
     *
     * @param id 主键
     */
    @ApiOperation(value = "删除HvPmMaterialTaoPic信息")
    @DeleteMapping(value = "/delete/{id}")
    public void deleteHvPmMaterialTaoPic(@PathVariable Long id) {
        hvPmMaterialTaoPicService.deleteHvPmMaterialTaoPic(id);
    }

    /**
     * 修改
     *
     * @param hvPmMaterialTaoPicDTO HvPmMaterialTaoPic
     */
    @ApiOperation(value = "修改HvPmMaterialTaoPic")
    @PutMapping(value = "/update")
    public void updateHvPmMaterialTaoPic(@Valid @RequestBody HvPmMaterialTaoPicDTO hvPmMaterialTaoPicDTO) {
        hvPmMaterialTaoPicService.updateHvPmMaterialTaoPic(hvPmMaterialTaoPicDTO);
    }

    /**
     * 根据id获取
     *
     * @param id 主键
     * @return HvPmMaterialTaoPic hvPmMaterialTaoPicDTO
     */
    @ApiOperation(value = "根据id获取HvPmMaterialTaoPic")
    @GetMapping(value = "/get/{id}")
    public HvPmMaterialTaoPicDTO getList(@PathVariable Long id) {
        return hvPmMaterialTaoPicService.getHvPmMaterialTaoPicById(id);
    }

    @ApiOperation(value = "获取HvPmMaterialTaoPic列表")
    @GetMapping(value = "/getAll")
    public List<HvPmMaterialTaoPicDTO> getAll() {
        return hvPmMaterialTaoPicService.getAll();
    }

    @ApiOperation(value = "根据id获取套料信息列表")
    @GetMapping(value = "/getByTaoPicId/{id}")
    public List<HvPmMaterialTaoPicDetail> getByCutPlanId(@PathVariable Long id) {
        return hvPmMaterialTaoPicDetailService.getByCutPlanId(id);
    }

    @ApiOperation(value = "获取套料信息")
    @PostMapping(value = "/getByTaoPicDetail")
    public Page<HvPmMaterialTaoPicDetailDTO> getDetail(@RequestBody HvPmMaterialTaoPicDetailQueryDTO queryDTO) {
        return hvPmMaterialTaoPicDetailService.getByTaoPicDetail(queryDTO);
    }

    @ApiOperation(value = "型材切割任务下发")
    @PostMapping(value = "/sendOrder")
    public void sendOrder(@RequestBody HvPmMaterialTaoPicDTO hvPmMaterialTaoPicDTO, @UserInfo @ApiIgnore UserInfoDTO userInfo) {
        hvPmMaterialTaoPicService.sendOrder(hvPmMaterialTaoPicDTO, userInfo);
    }
}