package com.hvisions.pms.service;

import com.hvisions.pms.dto.OperationParameterByVersionDTO;
import com.hvisions.pms.dto.OperationParameterDTO;
import com.hvisions.pms.dto.OperationParameterUpdateDTO;
import com.hvisions.pms.parameterdto.OperationParameterPhoneDTO;

import java.util.List;

/**
 * <p>Title: OperationParameterService</p >
 * <p>Description: 工序参数服务层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/2/20</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface OperationParameterService {


    /**
     * 新增工序参数
     *
     * @param operationParameterDTO 工序参数DTO
     * @return 新增工序参数的ID
     */
    int createParameter(OperationParameterDTO operationParameterDTO);

    /**
     * 新增工序参数录入表单
     *
     * @param taskId 工序ID
     */
    void createParameterVersion(int taskId);

    /**
     * 手机端工序参数录入
     *
     * @param operationParameterPhoneDTO 工序参数录入条件
     */
    void insertParameterActualValue(OperationParameterPhoneDTO operationParameterPhoneDTO);


    /**
     * 根据工序ID 参数版本查询
     *
     * @param version     工序参数版本
     * @param operationId 工序ID
     * @return 工序参数信息列表
     */
    List<OperationParameterByVersionDTO> getParameterByVersionAndOperationId(int operationId, int version);

    /**
     * 获取工序下所有参数版本
     *
     * @param operationId 工序ID
     * @return 工序参数版本
     */
    List<Integer> getVersionsByOperationId(int operationId);

    /**
     * 录入工序参数
     *
     * @param operationParameterUpdateDTO 工序参数DTO
     */
    void updateParameter(List<OperationParameterUpdateDTO> operationParameterUpdateDTO);

    /**
     * 根据ID查询工序参数
     *
     * @param id 工序参数ID
     * @return 工序参数信息
     */
    OperationParameterByVersionDTO getParameterById(int id);

    /**
     * 根据工序ID查询工序参数列表
     *
     * @param operationId 工序ID
     * @return 工序参数列表
     */
    List<OperationParameterDTO> getParameterByOperationId(int operationId);


    /**
     * 根据参数用途查询
     *
     * @param parameterUsage 参数用途
     * @return 参数列表
     */
    List<OperationParameterDTO> getParameterByUsage(int parameterUsage);

    /**
     * 根据工序id删除工序参数
     *
     * @param operationId 工序Id
     */
    void deleteByOperationId(int operationId);

    /**
     * 根据工序ID与参数用途查询
     *
     * @param operationId    工序ID
     * @param parameterUsage 参数用途
     * @return 工序参数
     */
    OperationParameterDTO getByOperationIdAndParameterUsage(int operationId, int parameterUsage);
}
