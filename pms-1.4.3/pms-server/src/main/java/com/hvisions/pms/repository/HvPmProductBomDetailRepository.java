package com.hvisions.pms.repository;

import com.hvisions.pms.entity.HvPmProductBomDetail;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

@Repository
public interface HvPmProductBomDetailRepository extends JpaRepository<HvPmProductBomDetail, Long> {

    HvPmProductBomDetail findByPalletCodeAndPlanProductBomId(String palletCode,Long planProductBomId);

    List<HvPmProductBomDetail> findByWorkOrderCode(String workOrderCode);

    //找不等于当前工单的料框（料框对应其他工单有历史数据，仅需要新建的组立工单）
    @Query(value = "select h.workOrderCode from HvPmProductBomDetail h inner join HvPmWorkOrder a on h.workOrderCode = a.workOrderCode where h.workOrderCode <>  :workOrderCode and h.palletCode = :palletCode and a.workOrderState = 0")
    Set<String> getOrderOrderBomDetailByPalletCode(@Param("palletCode") String palletCode, @Param("workOrderCode") String workOrderCode);

    List<HvPmProductBomDetail> findByStockId(int stockId);

    List<HvPmProductBomDetail> findByStockIdAndWorkOrderCodeAndPlanProductBomId(int stockId, String workOrderCode, Long planProductBomId);

    List<HvPmProductBomDetail> findByPalletCode(String palletCode);


    @Modifying
    @Transactional
    void  deleteByStockId(Integer stockId);

    //根据工单号删除
    @Modifying
    @Query("DELETE FROM HvPmProductBomDetail o WHERE o.workOrderCode = :workOrderCode")
    int deleteByWorkOrderCode(@Param("workOrderCode") String workOrderCode);

    void deleteByPalletCode(String palletCode);
}
