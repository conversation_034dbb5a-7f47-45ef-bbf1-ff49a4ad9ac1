package com.hvisions.pms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.pms.entity.HvPmAgvTaskRecord;
import com.hvisions.pms.plan.HvPmAgvTaskRecordTabQueryDTO;
import com.hvisions.thirdparty.common.dto.AGVStatusDTO;
import com.hvisions.thirdparty.common.dto.LineSchedulingDTO;
import com.hvisions.thirdparty.common.dto.OutCoordinationFlatStockDTO;
import com.hvisions.thirdparty.common.dto.WeldLineCallMaterialsDTO;
import org.springframework.data.domain.Page;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-03-29 11:54
 */
public interface HvPmAgvTaskRecordService extends IService<HvPmAgvTaskRecord> {
    Page<HvPmAgvTaskRecord> pageList(HvPmAgvTaskRecordTabQueryDTO hvPmAgvTaskRecordTabQueryDTO);

    ResultVO<ExcelExportDto> exportHvPmAvgTaskRecord(HvPmAgvTaskRecordTabQueryDTO hvPmAgvTaskRecordTabQueryDTO) throws IOException, IllegalAccessException;


    void saveLineTask(LineSchedulingDTO lineSchedulingDTO);

    void updateTaskByTaskCode(String taskCode, String status,String currentPositionCode);

    HvPmAgvTaskRecord getByTaskCode(String taskCode);
    
    void repeatedExecution(long raskRecordId);

    void manualScheduling(long raskRecordId);

    void emptyPalletCirculation(AGVStatusDTO agvStatusDTO);

    void saveTaskRecordByCllMaterialsTask(List<WeldLineCallMaterialsDTO> callMaterialsDTOS);

    void outFlatStockToIwmsInStock(OutCoordinationFlatStockDTO outCoordinationFlatStockDTO);

}
