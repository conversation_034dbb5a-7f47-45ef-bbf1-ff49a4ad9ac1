package com.hvisions.pms.service.imp;

import com.hvisions.pms.entity.HvPmProductBomDetail;
import com.hvisions.pms.repository.HvPmProductBomDetailRepository;
import com.hvisions.pms.service.HvPmPlanProductBomService;
import com.hvisions.pms.service.HvPmProductBomDetailService;
import com.hvisions.pms.service.WorkOrderService;
import com.hvisions.wms.client.StockClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class HvPmProductBomDetailServiceImpl implements HvPmProductBomDetailService {
    @Autowired
    private HvPmProductBomDetailRepository bomDetailRepository;
    @Autowired
    private WorkOrderService workOrderService;
    @Autowired
    private HvPmPlanProductBomService productBomService;
    @Autowired
    private StockClient stockClient;

    @Override
    public List<HvPmProductBomDetail> findByPalletCode(String palletCode) {
        // 获取所有BOM明细
        return bomDetailRepository.findByPalletCode(palletCode);
    }

    @Override
    public void deleteByPalletCode(String palletCode) {
        bomDetailRepository.deleteByPalletCode(palletCode);
    }
}
