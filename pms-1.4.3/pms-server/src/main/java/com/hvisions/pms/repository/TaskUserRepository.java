package com.hvisions.pms.repository;

import com.hvisions.pms.entity.HvPmTaskUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>Title: TaskUserRepository</p >
 * <p>Description: 任务分配</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2022/1/17</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Repository
public interface TaskUserRepository extends JpaRepository<HvPmTaskUser, Integer> {


    List<HvPmTaskUser> getAllByTaskIdIn(List<Integer> taskIdList);

    HvPmTaskUser getAllByTaskId(Integer taskId);

    @Transactional(rollbackFor = Exception.class)
    void deleteByIdIn(List<Integer> idList);

}