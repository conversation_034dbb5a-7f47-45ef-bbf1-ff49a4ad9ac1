package com.hvisions.pms.repository;

import com.hvisions.pms.entity.HvPmOperationFile;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: OperationFileRepository</p >
 * <p>Description: 工序文件关系仓储层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/2/21</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface OperationFileRepository extends JpaRepository<HvPmOperationFile, Integer> {

    /**
     * 根据工序ID查询工序文件关系
     *
     * @param operationId 工序ID
     * @return 工序文件关系列表
     */
    List<HvPmOperationFile> getByOperationId(int operationId);


    /**
     * 根据ID列表删除工序文件关系
     *
     * @param idList ID列表
     */
    void deleteByIdIn(List<Integer> idList);


}
