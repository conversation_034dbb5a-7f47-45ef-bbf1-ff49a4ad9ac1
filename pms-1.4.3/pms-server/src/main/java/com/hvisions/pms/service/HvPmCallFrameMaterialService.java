package com.hvisions.pms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.pms.dto.HvPmCallMaterialDTO;
import com.hvisions.pms.entity.plan.HvPmCallFrameMaterial;
import com.hvisions.thirdparty.common.dto.HvPmCallFrameMaterialDTO;
import com.hvisions.thirdparty.common.dto.LineSchedulingDTO;
import com.hvisions.thirdparty.common.dto.MaterialCuttingLineReportDTO;
import org.springframework.data.domain.Page;

import java.util.List;

public interface HvPmCallFrameMaterialService extends IService<HvPmCallFrameMaterial> {
    void updateFrameMaterial(MaterialCuttingLineReportDTO steelPlateDTO);

    List<HvPmCallFrameMaterial> findFrameMaterials(String materialCode, String shipNo, String blockCode);

    void saveFrameMaterial(String frameCode);

    HvPmCallFrameMaterial findOneByFrameCodeAndMCode(String frameCode, String materialCode);


    void updateByQuery(HvPmCallFrameMaterialDTO callFrameMaterialDTO);

    void updateMaterialDTO(LineSchedulingDTO lineSchedulingDTO);

    void updateStatus(String frameCode);

    Page<HvPmCallFrameMaterial> getPage(HvPmCallMaterialDTO callMaterialDTO);
}
