package com.hvisions.pms.service;

import com.hvisions.pms.dto.HvPmWorkOrderCompleteDTO;

/**
 * <P>    <P>
 *
 * <AUTHOR>
 * @date 2024/12/5
 */
public interface WorkOrderCompleteService {
    long addWorkOrderComplete(HvPmWorkOrderCompleteDTO workOrderCompleteDTO);

    HvPmWorkOrderCompleteDTO getWorkOrderCompleteByOrderCode(String orderCode);

    void deleteWorkOrderCompleteByOrderCode(String orderCode);

    void handReportWorkToMES(String orderCode);
}
