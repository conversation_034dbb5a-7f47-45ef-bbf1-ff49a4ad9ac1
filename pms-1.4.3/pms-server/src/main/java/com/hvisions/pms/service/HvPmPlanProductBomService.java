package com.hvisions.pms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.pms.entity.HvPmPlanProductBom;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-06
 */
public interface HvPmPlanProductBomService extends IService<HvPmPlanProductBom> {

    Integer InsertHvPmPlanProductBomList(List<HvPmPlanProductBom> hvPmPlanProductBomList);

    Page<HvPmPlanProductBom> pageList(LambdaQueryWrapper<HvPmPlanProductBom> lqw, Integer pageNo, Integer pageSize);

    List<HvPmPlanProductBom> getListByWorkOrder(String workOrderCode);

    List<HvPmPlanProductBom> getListByWorkOrderAndFrameCode(String workOrderCode,String frameCode);

    Boolean deleteByPlanCode(String planCode);

    Date getCompleteTimeByPlanCode(String planCode);

    List<HvPmPlanProductBom> getListByPlanCodes(List<String> planCodes);

    void updateframecode(HvPmPlanProductBom pmPlanProductBom);

    void updateFrameCodeById(HvPmPlanProductBom pmPlanProductBom);
}
