package com.hvisions.pms.service.imp;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.framework.client.DictionaryItemClient;
import com.hvisions.hiperbase.client.LocationExtendClient;
import com.hvisions.hiperbase.client.MaterialClient;
import com.hvisions.hiperbase.client.MaterialPointAreaClient;
import com.hvisions.hiperbase.client.MaterialPointClient;
import com.hvisions.hiperbase.equipment.location.LocationDTO;
import com.hvisions.hiperbase.materials.dto.MaterialDTO;
import com.hvisions.pms.consts.HvPmXcMaterialOutStockPlanConst;
import com.hvisions.pms.dao.XcMaterialOutStockPlanMapper;
import com.hvisions.pms.dto.WorkOrderDTO;
import com.hvisions.pms.entity.HvPmPlanRouteInfo;
import com.hvisions.pms.entity.plan.HvPmXcMaterialOutStockPlan;
import com.hvisions.pms.exportdto.XcMaterialOutStockPlanExportDTO;
import com.hvisions.pms.exportdto.XcMaterialOutStockPlanResultDetail1ExportDTO;
import com.hvisions.pms.importTemplate.XcMaterialOutStockPlanTemplate;
import com.hvisions.pms.plan.XcMaterialOutStockPlanDTO;
import com.hvisions.pms.plan.XcMaterialOutStockPlanQueryDTO;
import com.hvisions.pms.repository.HvPmMESReportInfoRepository;
import com.hvisions.pms.repository.HvPmXcMaterialOutStockResultDetail1Repository;
import com.hvisions.pms.repository.HvPmXcMaterialOutStockResultRepository;
import com.hvisions.pms.repository.WorkOrderRepository;
import com.hvisions.pms.repository.plan.XcMaterialOutStockPlanRepository;
import com.hvisions.pms.service.*;
import com.hvisions.pms.utils.SerialCodeUtilsV2;
import com.hvisions.thirdparty.common.dto.*;
import com.hvisions.thridparty.client.MaterialCuttingLineClient;
import com.hvisions.thridparty.client.MesClient;
import com.hvisions.thridparty.client.ProfileCompactStorageClient;
import com.hvisions.wms.client.StockClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/5/7
 */
@Service
@Slf4j
public class XcMaterialOutStockPlanServiceImpl implements XcMaterialOutStockPlanService {

    @Autowired
    private XcMaterialOutStockPlanMapper xcMaterialOutStockPlanMapper;

    @Autowired
    private XcMaterialOutStockPlanRepository xcMaterialOutStockPlanRepository;

    @Autowired
    private MaterialPointAreaClient materialPointAreaClient;

    @Autowired
    private MaterialPointClient materialPointClient;

    @Autowired
    private SerialCodeUtilsV2 serialCodeUtils;

    @Autowired
    private HvPmXcMaterialOutStockResultRepository resultRepository;

    @Autowired
    private HvPmXcMaterialOutStockResultDetail1Repository detail1Repository;

    @Autowired
    private ProfileCompactStorageClient profileCompactStorageClient;

    @Resource
    private DictionaryItemClient dictionaryItemClient;

    @Resource
    private MaterialClient materialClient;

    @Autowired
    private LocationExtendClient locationExtendClient;

    @Resource
    private MaterialCuttingLineClient materialCuttingLineClient;

    @Autowired
    private WorkOrderService workOrderService;

    @Autowired
    private WorkOrderRepository workOrderRepository;

    @Resource
    private HvPmMESReportInfoRepository hvPmMESReportInfoRepository;

    @Resource
    private ProductRouteService productRouteService;

    @Resource
    private HvPmPlanRouteInfoService hvPmPlanRouteInfoService;

    @Resource
    private HvPmXcMaterialCutPlanService hvPmXcMaterialCutPlanService;

    @Autowired
    private MesClient mesClient;

    @Autowired
    private StockClient stockClient;

    @Autowired
    private HvPmXcMaterialOutStockResultDetail1Service detail1Service;

    @Override
    public Page<XcMaterialOutStockPlanDTO> getPage(XcMaterialOutStockPlanQueryDTO xcMaterialOutStockPlanQueryDTO) {
        return PageHelperUtil.getPage(xcMaterialOutStockPlanMapper::getPage, xcMaterialOutStockPlanQueryDTO);
    }

    @Override
    public int createOutStockPlan(XcMaterialOutStockPlanDTO xcMaterialOutStockPlanDTO) {
        HvPmXcMaterialOutStockPlan plan = xcMaterialOutStockPlanRepository.saveAndFlush(DtoMapper.convert(xcMaterialOutStockPlanDTO, HvPmXcMaterialOutStockPlan.class));
        return plan.getId();
    }

    @Override
    public int updateOutStockPlan(XcMaterialOutStockPlanDTO xcMaterialOutStockPlanDTO) {
        HvPmXcMaterialOutStockPlan plan = xcMaterialOutStockPlanRepository.save(DtoMapper.convert(xcMaterialOutStockPlanDTO, HvPmXcMaterialOutStockPlan.class));
        return plan.getId();
    }

    @Override
    @Transactional
    public void deleteOutStockPlanById(int id, String taskNo) {
        xcMaterialOutStockPlanRepository.deleteById(id);
        resultRepository.deleteByTaskNo(taskNo);
        detail1Repository.deleteByTaskNo(taskNo);
    }

    @Override
    public boolean isExistsOutStockPlan(String taskNo) {
        HvPmXcMaterialOutStockPlan plan = xcMaterialOutStockPlanRepository.getByTaskNo(taskNo);
        return plan != null;
    }

    @Override
    public ResultVO<ExcelExportDto> getImportTemplate() throws IOException, IllegalAccessException {
        ResponseEntity<byte[]> result = ExcelUtil.generateImportFile(XcMaterialOutStockPlanTemplate.class, "XcMaterialOutStockPlan");
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(result.getBody());
        excelExportDto.setFileName(HvPmXcMaterialOutStockPlanConst.PLAN_IMPORT_TEMPLTE);
        return ResultVO.success(excelExportDto);
    }

    @Override
    public ImportResult importPlan(MultipartFile file) throws IOException, IllegalAccessException {
        List<XcMaterialOutStockPlanDTO> xcMaterialOutStockPlanDTOS = ExcelUtil.getEntityList(file, 0, XcMaterialOutStockPlanDTO.class);
        if (!xcMaterialOutStockPlanDTOS.isEmpty()) {
            for (XcMaterialOutStockPlanDTO dto : xcMaterialOutStockPlanDTOS) {

                String taskNo = serialCodeUtils.generateCode("taskNo");
                if (StringUtils.isEmpty(taskNo)) {
                    throw new BaseKnownException("编码规则不存在~");
                }
                // 任务号-工单号
                dto.setTaskNo(taskNo + "-" + dto.getWorkOrderCode());
                dto.setStatus(0);
                createOutStockPlan(dto);

            }
        }
        return new ImportResult();
    }


    @Override
    public ResultVO<ExcelExportDto> exportOutStockPlan(XcMaterialOutStockPlanQueryDTO xcMaterialOutStockPlanQueryDTO) throws IOException, IllegalAccessException {
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(geteExportInfo(xcMaterialOutStockPlanQueryDTO).getBody());
        excelExportDto.setFileName(HvPmXcMaterialOutStockPlanConst.PLAN_EXPORT_FILE_NAME);
        return ResultVO.success(excelExportDto);
    }

    //获取需要导出的数据内容
    @ApiIgnore
    public ResponseEntity<byte[]> geteExportInfo(XcMaterialOutStockPlanQueryDTO queryDTO) throws IOException, IllegalAccessException {
        // 获取型材出库计划主表列表
        List<XcMaterialOutStockPlanDTO> xcMaterialOutStockPlanDTOList = xcMaterialOutStockPlanMapper.getPage(queryDTO);
        List<String> taskNoList = xcMaterialOutStockPlanDTOList.stream()
                .map(XcMaterialOutStockPlanDTO::getTaskNo)
                .collect(Collectors.toList());

        //型材出库计划
        List<XcMaterialOutStockPlanExportDTO> xcMaterialOutStockPlanExportDTOS = DtoMapper.convertList(xcMaterialOutStockPlanDTOList, XcMaterialOutStockPlanExportDTO.class);
        //型材出库计划结果
        List<XcMaterialOutStockPlanResultDetail1ExportDTO> xcMaterialOutStockPlanResultDetail1ExportDTOS = DtoMapper.convertList(detail1Service.getByTaskNoList(taskNoList), XcMaterialOutStockPlanResultDetail1ExportDTO.class);

        HSSFWorkbook hssfWorkbook = new HSSFWorkbook();
        ExcelUtil.addSheetToWorkBook(xcMaterialOutStockPlanExportDTOS, HvPmXcMaterialOutStockPlanConst.PLAN_EXPORT_SHEET_NAME, XcMaterialOutStockPlanExportDTO.class,null, hssfWorkbook);
        ExcelUtil.addSheetToWorkBook(xcMaterialOutStockPlanResultDetail1ExportDTOS, HvPmXcMaterialOutStockPlanConst.PLAN_DETAIL1_EXPORT_SHEET_NAME,XcMaterialOutStockPlanResultDetail1ExportDTO.class,null, hssfWorkbook);
        return ExcelUtil.generateHttpExcelFile(hssfWorkbook, HvPmXcMaterialOutStockPlanConst.PLAN_EXPORT_FILE_NAME);

    }

    @Override
    public List<HvPmXcMaterialOutStockPlan> getByWorkOrderCode(String workOrderCode) {
        return xcMaterialOutStockPlanRepository.getByWorkOrderCode(workOrderCode);
    }

    @Override
    public int createOutStockPlanByRequests(MaterialRequestsDTO materialRequestsDTO) {
        ResultVO<LocationDTO> locationVO = locationExtendClient.getHvBmLocationByCode(materialRequestsDTO.getStationId());
        if(locationVO.getData() == null){
            throw new BaseKnownException("工位：" + materialRequestsDTO.getStationId() + "不存在！");
        }
        LocationDTO locationDTO = locationVO.getData();
        //HvPmWorkOrder workOrder = workOrderRepository.getHvPmWorkOrderByWorkOrderCode(materialRequestsDTO.getWorkOrderCode());
        //if (workOrder == null) {
        //    throw new BaseKnownException("工单：" + materialRequestsDTO.getWorkOrderCode() + "不存在！");
        //}
        boolean existsOrderNo = hvPmXcMaterialCutPlanService.isExistsOrderNo(materialRequestsDTO.getWorkOrderCode());
        if (!existsOrderNo) {
            throw new BaseKnownException("工作号：" + materialRequestsDTO.getWorkOrderCode() + "不存在！");
        }
        //校验物料编码
        if(materialRequestsDTO.getMaterialCode() ==null){
            throw new BaseKnownException("物料编码不能为空");
        }
        //校验数量
        if(materialRequestsDTO.getQuantity() ==null || materialRequestsDTO.getQuantity() <= 0){
            throw new BaseKnownException("数量要大于0");
        }


        XcMaterialOutStockPlanDTO OutStockPlan = new XcMaterialOutStockPlanDTO();
        //根据工单号和型材规格 查询任务号是否存在
        XcMaterialOutStockPlanDTO xcMaterialOutStockPlanDTO =  xcMaterialOutStockPlanMapper.getByWorkOrderCodeAndSepces(materialRequestsDTO.getWorkOrderCode(),materialRequestsDTO.getSepces());
        if(xcMaterialOutStockPlanDTO == null){
            String taskNo = serialCodeUtils.generateCode("taskNo");
            if (StringUtils.isEmpty(taskNo)) {
                throw new BaseKnownException("编码规则不存在~");
            }
            // 任务号加上 - 加上 工单号用于后续生产订单开工
            OutStockPlan.setTaskNo(taskNo + "-" + materialRequestsDTO.getWorkOrderCode());
        }else{
            BeanUtils.copyProperties(xcMaterialOutStockPlanDTO, OutStockPlan);
        }

        OutStockPlan.setWorkOrderCode(materialRequestsDTO.getWorkOrderCode());
        OutStockPlan.setSepces(materialRequestsDTO.getSepces());
        OutStockPlan.setQuantity(materialRequestsDTO.getQuantity());
       /* //根据工位查询对应的料点
        ResultVO<MaterialPointAreaDTO> materialPointAreaByStationCode = materialPointAreaClient.getMaterialPointAreaByStationCode(materialRequestsDTO.getStationId());
        if (materialPointAreaByStationCode.getData() == null) {
            throw new BaseKnownException("工位：" + materialRequestsDTO.getStationId() + "对应的料点区域不存在~");
        }
        //料点区域ID
        String pointAreaCode = materialPointAreaByStationCode.getData().getPointAreaCode();
        //根据料点区域id查询料点编码
        ResultVO<MaterialPointDTO> materialPointByPointAreaCode = materialPointClient.getMaterialPointByPointAreaCode(pointAreaCode);
        if (materialPointByPointAreaCode.getData() == null) {
            throw new BaseKnownException("料点区域：" + pointAreaCode + "没有料点信息~");

        }*/
        if ("ZNXCQGAX".equals(materialRequestsDTO.getLocation())) {
            OutStockPlan.setLocation("XCCK01");
        } else if ("ZNXCQGBX".equals(materialRequestsDTO.getLocation())) {
            OutStockPlan.setLocation("XCCK02");
        }
        int outStockPlanId = this.createOutStockPlan(OutStockPlan);

        ProfileOutboundTaskDTO profileOutboundInfoDTO = new ProfileOutboundTaskDTO();
        profileOutboundInfoDTO.setMsgId(String.valueOf(UUID.randomUUID()));
        // 获取当前时间
        LocalDateTime currentTime = LocalDateTime.now();
        // 定义时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 格式化当前时间
        String formattedTime = currentTime.format(formatter);
        profileOutboundInfoDTO.setMsgTime(formattedTime);
        ProfileTaskDetail profileTaskDetail = new ProfileTaskDetail();
        BeanUtils.copyProperties(OutStockPlan, profileTaskDetail);
        profileTaskDetail.setQty(OutStockPlan.getQuantity());
        //物料编码
        profileTaskDetail.setMaterialCode(materialRequestsDTO.getMaterialCode());
        ArrayList<ProfileTaskDetail> list = new ArrayList<>();
        list.add(profileTaskDetail);
        profileOutboundInfoDTO.setDatas(list);
        int status = 1;
        //发送-型材立库出库任务
        ResultVO<?> resultVO = profileCompactStorageClient.sendOutboundTask(profileOutboundInfoDTO);
        if (!resultVO.isSuccess()) {
            status = 2;
            log.error("发送-型材立库出库任务失败~");
        }
        OutStockPlan.setId(outStockPlanId);
        OutStockPlan.setStatus(status);
        OutStockPlan.setMaterialCode(materialRequestsDTO.getMaterialCode());
        OutStockPlan.setSendTime(new Date());
        xcMaterialOutStockPlanRepository.save(DtoMapper.convert(OutStockPlan, HvPmXcMaterialOutStockPlan.class));

        //型材切割允许投料 ,mes 报开工
        //根据切割编号查询对应的型材零件工单
        //String workOrderCode = materialRequestsDTO.getWorkOrderCode();

        //HvPmPlanRouteInfo routeInfo = productRouteService.getRouteByWorkOrderAndStation(materialRequestsDTO.getWorkOrderCode(), workOrder.getRouteId(), materialRequestsDTO.getStationId());
        HvPmPlanRouteInfo stepIdByWorkOrderCode = hvPmPlanRouteInfoService.getStepIdByWorkOrderCode(materialRequestsDTO.getWorkOrderCode());
        //ProductionOrderStartDTO productionOrderStartDTO = new ProductionOrderStartDTO();
        //productionOrderStartDTO.setPickingPeople("领料人");
        //productionOrderStartDTO.setActualStartTime(formattedTime);
        //productionOrderStartDTO.setProcessOrderIdList();
        //productionOrderStartDTO.setPickingOrderList();
        //mesClient.sendProductionOrderStart(productionOrderStartDTO);

        //ProductionOrderStartDTO mesSendReportingDTO = new ProductionOrderStartDTO();
        // 工序工单ID
        //mesSendReportingDTO.setProcessOrderId(routeInfo.getStepId());
        // 实际开始时间
        //mesSendReportingDTO.setActualStartTime(formattedTime);
        //MES 报工内容
        //HvPmMESReportInfo reportInfo = new HvPmMESReportInfo();
        //reportInfo.setLineId(locationDTO.getParentId());
        //reportInfo.setReportData(JSON.toJSONString(mesSendReportingDTO));
        //reportInfo.setStatus(0);
        //ResultVO<?> mesVo = mesClient.xcProductionOrderStart(mesSendReportingDTO);
        //if(!mesVo.isSuccess()){
        //    reportInfo.setStatus(1);
        //}
        //reportInfo.setReportType(0);
        //hvPmMESReportInfoRepository.save(reportInfo);
        return outStockPlanId;
    }


    @Override
    public MaterialCuttingLineOnLineDTO getCuttingLineOneLineInfo(ProfileOutboundInfoDTO profileOutboundInfoDTO) {
        //任务号找出库计划的上料为位置，料点找到库区，在找到产线
        HvPmXcMaterialOutStockPlan plan = xcMaterialOutStockPlanRepository.getByTaskNo(profileOutboundInfoDTO.getTaskCode());
        if (plan == null) {
            throw new BaseKnownException("任务号：+" + profileOutboundInfoDTO.getTaskCode() + "的出库计划不存在！");
        }
        String pointCode = plan.getLocation();
        //料点找区域》找工位》找产线编号
        //ResultVO<MaterialPointDTO> pointVo = materialPointClient.getMaterialPointByPointCode(pointCode);
        //if (!pointVo.isSuccess() && pointVo.getData() == null) {
        //    throw new BaseKnownException("查询料点:" + pointCode + "信息失败！");
        //}
        //MaterialPointDTO pointDTO = pointVo.getData();
        //ResultVO<MaterialPointAreaDTO> areaVo = materialPointAreaClient.getById(pointDTO.getPointAreaId());
        //if (!pointVo.isSuccess() && pointVo.getData() == null) {
        //    throw new BaseKnownException("查询料点区域Id:" + pointDTO.getPointAreaId() + "信息失败！");
        //}
        //MaterialPointAreaDTO areaDTO = areaVo.getData();
//
        //ResultVO<LocationDTO> locationVo = locationExtendClient.getLocationById(areaDTO.getLocationId());
        //if (!pointVo.isSuccess() && pointVo.getData() == null) {
        //    throw new BaseKnownException("查询工位Id:" + areaDTO.getLocationId() + "信息失败！");
        //}
        //LocationDTO stationDTO = locationVo.getData();
        //locationVo = locationExtendClient.getLocationById(stationDTO.getParentId());
        //if (!pointVo.isSuccess() && pointVo.getData() == null) {
        //    throw new BaseKnownException("查询产线Id:" + areaDTO.getLocationId() + "信息失败！");
        //}
        //LocationDTO lineDTO = locationVo.getData();
        //上料点信息下发
        MaterialCuttingLineOnLineDTO materialCuttingLineOnLineDTO = new MaterialCuttingLineOnLineDTO();
        materialCuttingLineOnLineDTO.setName("上料点信息下发");
        materialCuttingLineOnLineDTO.setId(UUID.randomUUID().toString().replace("-", ""));
        //车间编号作为字段KEY
        //ResultVO<List<DictionaryItemDTO>> d = dictionaryItemClient.findAll(lineDTO.getCode());
        //String station = null;
        //if (d.isSuccess()) {
        //    for (DictionaryItemDTO datum : d.getData()) {
        //        station = datum.getItemKey();
        //    }
        //}
        //if (station == null) {
        //throw new BaseKnownException("字典未配置 型材切割线：" + lineDTO.getCode() + "的上线点位");
        //}
        materialCuttingLineOnLineDTO.setOrderNo(plan.getWorkOrderCode());
        if (pointCode.equals("XCCK02"))
            materialCuttingLineOnLineDTO.setStation_id("ZNXCQGBX");
        else if ("XCCK01".equals(pointCode))
            materialCuttingLineOnLineDTO.setStation_id("ZNXCQGAX");
        else
            throw new BaseKnownException("未知出库位置:" + pointCode);

        List<MaterialCuttingLineOnLineDataDTO> data = new ArrayList<>();
        MaterialCuttingLineOnLineDataDTO dataDTO;
        ResultVO vo;
        for (OutboundMaterialDTO outboundMaterialDTO : profileOutboundInfoDTO.getMaterialList()) {
            vo = materialClient.getByCode(outboundMaterialDTO.getMaterialCode(), "1");
            com.hvisions.hiperbase.materials.dto.MaterialDTO materialDTO = (MaterialDTO) vo.getData();
            if (materialDTO == null) {
                throw new BaseKnownException("物料编号:" + outboundMaterialDTO.getMaterialCode() + "未在系统维护！");
            }
            dataDTO = new MaterialCuttingLineOnLineDataDTO();
            dataDTO.setRaw_dimension(materialDTO.getSpecs());
            dataDTO.setRaw_num(Math.toIntExact(outboundMaterialDTO.getQuality()));
            dataDTO.setMaterial_code(materialDTO.getMaterialCode());
            data.add(dataDTO);
        }
        materialCuttingLineOnLineDTO.setRaw_data(data);
        //获取当前时间戳
        long nowTimestamp = Instant.now().getEpochSecond();
        String nowTimestampStr = String.valueOf(nowTimestamp);
        materialCuttingLineOnLineDTO.setTimestamp(nowTimestampStr);
        return materialCuttingLineOnLineDTO;
    }


    @Override
    public void sendCuttingLineOneLineInfo(ProfileOutboundInfoDTO profileOutboundInfoDTO) {
        MaterialCuttingLineOnLineDTO cuttingLineOnLineDTO = getCuttingLineOneLineInfo(profileOutboundInfoDTO);
        ResultVO<?> v = materialCuttingLineClient.sendOrderAndMaterial(cuttingLineOnLineDTO);
        if (!v.isSuccess()) {
            throw new BaseKnownException("上料信息下发失败！" + v.getMessage());
        }
        //任务号找任务，找到工单号
        HvPmXcMaterialOutStockPlan OutStockPlan = xcMaterialOutStockPlanRepository.getByTaskNo(profileOutboundInfoDTO.getTaskCode());
        WorkOrderDTO orderDTO = workOrderService.findByWorkOrderCode(OutStockPlan.getWorkOrderCode());
        //TODO后续优化
        //       workOrderRepository.updateWorkOrderState(3, orderDTO.getId());
    }

    @Override
    public List<XcMaterialOutStockPlanDTO> getListByWorkOrderCode(String workOrderCode) {
        List<HvPmXcMaterialOutStockPlan> planList = xcMaterialOutStockPlanMapper.getByWorkOrderCode(workOrderCode);
        if (planList == null) {
            return null;
        }
        List<XcMaterialOutStockPlanDTO> planDTOList = new ArrayList<>();
        for (HvPmXcMaterialOutStockPlan plan : planList) {
            XcMaterialOutStockPlanDTO planDTO = DtoMapper.convert(plan, XcMaterialOutStockPlanDTO.class);
            planDTOList.add(planDTO);
        }
        return planDTOList;
    }

    @Override
    public ResultVO<?> handSendOrder(XcMaterialOutStockPlanDTO xcMaterialOutStockPlanDTO, UserInfoDTO userInfo) {

        HvPmXcMaterialOutStockPlan outStockPlan = xcMaterialOutStockPlanRepository.getByTaskNo(xcMaterialOutStockPlanDTO.getTaskNo());

        if (outStockPlan == null) {
            throw new BaseKnownException("不存在出库计划"+xcMaterialOutStockPlanDTO.getTaskNo());
        }
        //校验数据
        checkOutStockPlan(outStockPlan);

        //组装数据
        ProfileOutboundTaskDTO profileOutboundInfoDTO = new ProfileOutboundTaskDTO();
        profileOutboundInfoDTO.setMsgId(String.valueOf(UUID.randomUUID()));
        // 获取当前时间
        LocalDateTime currentTime = LocalDateTime.now();
        // 定义时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 格式化当前时间
        String formattedTime = currentTime.format(formatter);
        profileOutboundInfoDTO.setMsgTime(formattedTime);
        ProfileTaskDetail profileTaskDetail = new ProfileTaskDetail();
        BeanUtils.copyProperties(outStockPlan, profileTaskDetail);
        profileTaskDetail.setQty(outStockPlan.getQuantity());
        ArrayList<ProfileTaskDetail> list = new ArrayList<>();
        list.add(profileTaskDetail);
        profileOutboundInfoDTO.setDatas(list);
        int status = 1;
        //发送-型材立库出库任务
        ResultVO<?> resultVO = profileCompactStorageClient.sendOutboundTask(profileOutboundInfoDTO);
        if (!resultVO.isSuccess()) {
            status = 2;
            log.error("发送-型材立库出库任务失败~");
        }
        outStockPlan.setStatus(status);
        outStockPlan.setSendTime(new Date());
        outStockPlan.setSendUserId(userInfo.getId());
        xcMaterialOutStockPlanRepository.save(outStockPlan);
        return resultVO;
    }



    private void checkOutStockPlan(HvPmXcMaterialOutStockPlan outStockPlan) {
        if (outStockPlan.getWorkOrderCode().isEmpty()) {
            throw new BaseKnownException("工单号不能为空!");
        }

        if (outStockPlan.getMaterialCode().isEmpty()) {
            throw new BaseKnownException("物料编码不能为空！");
        }

        if (outStockPlan.getSepces().isEmpty()) {
            throw new BaseKnownException("型材原材规格不能为空!");
        }
        if (outStockPlan.getQuantity()<=0) {
            throw new BaseKnownException("数量不能小于等于0");
        }
        if (outStockPlan.getLocation().isEmpty()) {
            throw new BaseKnownException("上料位置不能为空！");
        }

    }


}
