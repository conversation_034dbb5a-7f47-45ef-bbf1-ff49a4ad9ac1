package com.hvisions.pms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.EasyExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.bom.dto.HvBmFrameDTO;
import com.hvisions.hiperbase.client.HvBmFrameClient;
import com.hvisions.hiperbase.client.MaterialClient;
import com.hvisions.hiperbase.materials.dto.MaterialDTO;
import com.hvisions.pms.entity.HvPmFrameMaterial;
import com.hvisions.pms.service.FrameMaterialService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-04-01 13:52
 */
@RestController
@RequestMapping(value = "/hvBmFrameMaterial")
@Api(tags = "料框物料缓存")
public class FrameMaterialController {
    @Autowired
    private FrameMaterialService hvBmFrameMaterialService;


    /**
     * 分页查询
     */
    @ApiOperation("分页查询")
    @PostMapping("/list")
    public ResultVO list(@RequestBody HvPmFrameMaterial hvPmFrameMaterial,
                         @RequestParam(name="pageNum", defaultValue="1") Integer pageNo,
                         @RequestParam(name="pageSize", defaultValue="10") Integer pageSize)
    {
        QueryWrapper<HvPmFrameMaterial> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(!StringUtils.isEmpty(hvPmFrameMaterial.getFrameCode()),"frame_code", hvPmFrameMaterial.getFrameCode())
                .eq(!StringUtils.isEmpty(hvPmFrameMaterial.getMaterialCode()),"material_code", hvPmFrameMaterial.getMaterialCode());
        Page<HvPmFrameMaterial> page = new Page<>(pageNo,pageSize);
        return ResultVO.success(hvBmFrameMaterialService.page(page,queryWrapper));
    }


    /**
     * 添加
     *
     * @param hvPmFrameMaterial HvPmAgvTaskRecord
     */
    @ApiOperation(value = "添加HvPmAgvTaskRecord信息")
    @PostMapping(value = "/add")
    public ResultVO addHvPmAgvTaskRecord(@RequestBody HvPmFrameMaterial hvPmFrameMaterial, @UserInfo @ApiIgnore UserInfoDTO userInfo) {
        boolean isExist = hvBmFrameMaterialService.findExist(hvPmFrameMaterial);
        if(isExist){
            throw new BaseKnownException("该料框与物料缓存数据已存在");
        }
        hvPmFrameMaterial.setCreatorId(userInfo.getId());
        hvPmFrameMaterial.setCreateTime(new Date());
        return ResultVO.success(hvBmFrameMaterialService.save(hvPmFrameMaterial));
    }

    /**
     * 删除
     *
     * @param id 主键
     */
    @ApiOperation(value = "删除HvPmAgvTaskRecord信息")
    @DeleteMapping(value = "/delete/{id}")
    public ResultVO deleteHvPmAgvTaskRecord(@PathVariable Integer id) {
        return ResultVO.success(hvBmFrameMaterialService.removeById(id));
    }

    /**
     * 修改
     *
     * @param hvPmFrameMaterial HvPmAgvTaskRecord
     */
    @ApiOperation(value = "修改HvPmAgvTaskRecord")
    @PutMapping(value = "/update")
    public ResultVO updateHvPmAgvTaskRecord(@RequestBody HvPmFrameMaterial hvPmFrameMaterial, @UserInfo @ApiIgnore UserInfoDTO userInfo) {

        hvPmFrameMaterial.setUpdaterId(userInfo.getId());
        hvPmFrameMaterial.setUpdateTime(new Date());
        return ResultVO.success(hvBmFrameMaterialService.updateById(hvPmFrameMaterial));
    }

    /**
     * 根据id获取
     *
     * @param id 主键
     * @return HvPmAgvTaskRecord hvPmAgvTaskRecordDTO
     */
    @ApiOperation(value = "根据id获取HvPmAgvTaskRecord")
    @GetMapping(value = "/get/{id}")
    public ResultVO getList(@PathVariable Integer id) {
        return ResultVO.success(hvBmFrameMaterialService.getById(id));
    }

    /**
     * 查询全部
     * @return 列表
     */
    @ApiOperation(value = "获取HvPmAgvTaskRecord列表")
    @GetMapping(value = "/getAll")
    public ResultVO getAll(){
        return ResultVO.success(hvBmFrameMaterialService.list());
    }
}
