package com.hvisions.pms.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.annotation.EnableFilter;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.pms.plan.HvPmWorkPlanAddAndUpdateDTO;
import com.hvisions.pms.plan.HvPmWorkPlanDTO;
import com.hvisions.pms.plan.HvPmWorkPlanQueryDTO;
import com.hvisions.pms.plan.IssuerDTO;
import com.hvisions.pms.service.HvPmWorkPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**
 * <p>Title: HvPmWorkPlanController</p >
 * <p>Description: 生产计划 controller</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/14</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/workPlan")
@Slf4j
@Api(description = "生产计划controller")
public class WorkPlanController {

    private final HvPmWorkPlanService hvPmWorkPlanService;

    @Autowired
    public WorkPlanController(HvPmWorkPlanService hvPmWorkPlanService) {
        this.hvPmWorkPlanService = hvPmWorkPlanService;
    }


    /**
     * 新增生产计划
     *
     * @param hvPmPlanAddAndUpdateDTO 生产计划新增和更新DTO
     * @return 返回新增的id
     */
    @EnableFilter
    @ApiOperation(value = "新增生产计划")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public int addWorkPlan(@RequestBody HvPmWorkPlanAddAndUpdateDTO hvPmPlanAddAndUpdateDTO) {

        return hvPmWorkPlanService.addOrUpdate(hvPmPlanAddAndUpdateDTO);
    }

    /**
     * 删除生产计划
     *
     * @param id 生产计划id
     */
    @ApiOperation(value = "删除生产计划")
    @RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
    public void deleteWorkPlan(@PathVariable int id) {
        hvPmWorkPlanService.delete(id);
    }


    /**
     * 更新生产计划
     *
     * @param hvPmPlanAddAndUpdateDTO 新增和更新DTO
     */
    @EnableFilter
    @ApiOperation(value = "更新生产计划")
    @RequestMapping(value = "/update", method = RequestMethod.PUT)
    public void updateWorkPlan(@RequestBody HvPmWorkPlanAddAndUpdateDTO hvPmPlanAddAndUpdateDTO) {

        hvPmWorkPlanService.addOrUpdate(hvPmPlanAddAndUpdateDTO);
    }

    /**
     * 根据计划编码查找计划
     *
     * @param planCode 计划编号
     * @return 计划集合
     */
    @EnableFilter
    @ApiOperation(value = "根据计划编码查找计划")
    @RequestMapping(value = "findWorkPlanByPlanCode/{planCode}", method = RequestMethod.GET)
    public HvPmWorkPlanDTO findWorkPlanByPlanCode(@PathVariable String planCode) {

        return hvPmWorkPlanService.getPlanByCode(planCode);
    }


    /**
     * 根据生产计划ID查询生产计划
     *
     * @param id 生产计划ID
     * @return 生产计划信息
     */
    @GetMapping(value = "/getWorkPlanById/{id}")
    @ApiOperation(value = "根据生产计划ID查询生产计划")
    public HvPmWorkPlanDTO getWorkPlanById(@PathVariable Integer id) {
        return hvPmWorkPlanService.getPlanById(id);
    }

    /**
     * 根据id列表查询生产计划
     *
     * @param idIn 根据ID列表查询生产计划
     * @return 生产计划列表
     */
    @PostMapping(value = "/getWorkPlanListByIdIn")
    @ApiOperation(value = "根据id列表查询生产计划列表")
    public List<HvPmWorkPlanDTO> getWorkPlanListByIdIn(@RequestBody List<Integer> idIn) {
        return hvPmWorkPlanService.getWorkPlanListByIdIn(idIn);
    }

    /**
     * 模糊查询计划
     *
     * @param hvPmWorkPlanQueryDTO 生产计划查询DTO
     * @return 实体对象集合
     */
    @EnableFilter
    @ApiOperation(value = "模糊查询计划")
    @RequestMapping(value = "findWorkPlan", method = RequestMethod.POST)
    public Page<HvPmWorkPlanDTO> findWorkPlan(@RequestBody HvPmWorkPlanQueryDTO hvPmWorkPlanQueryDTO) {
        return hvPmWorkPlanService.findByCondition(hvPmWorkPlanQueryDTO);
    }


    /**
     * 下发计划
     */
    @ApiOperation(value = "下发计划")
    @RequestMapping(value = "issuerPlan", method = RequestMethod.POST)
    public void issuerPlan(@RequestBody IssuerDTO issuerDTO) {
        hvPmWorkPlanService.issuerPlan(issuerDTO);
    }


    /**
     * 重发
     *
     * @param workPlanDetailId 计划明细ID
     */
    @PutMapping(value = "/reIssuePlan")
    @ApiModelProperty(value = "重发计划")
    public void reIssuePlan(@RequestParam int workPlanDetailId) {
        hvPmWorkPlanService.reIssuePlan(workPlanDetailId);
    }

    /**
     * 导入所有生产计划信息
     *
     * @param file 生产计划信息文档
     * @return ImportResult列表
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    @EnableFilter
    @ApiOperation(value = "导入计划")
    @RequestMapping(value = "importPlan", method = RequestMethod.POST)
    public ImportResult importPlan(@RequestParam MultipartFile file) throws IllegalAccessException, ParseException, IOException {
        return hvPmWorkPlanService.importWorkPlan(file);
    }

    /**
     * 获取导入模板(支持超链接)
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @EnableFilter
    @ApiResultIgnore
    @ApiOperation(value = "获取导入模板(支持超链接)")
    @RequestMapping(value = "downloadPlanTemplate", method = RequestMethod.POST)
    public ResponseEntity<byte[]> downloadPlanTemplate() throws IOException, IllegalAccessException {
        return hvPmWorkPlanService.getWorkPlanImportTemplate();
    }


    /**
     * 获取导入模板
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @EnableFilter
    @ApiResultIgnore
    @ApiOperation(value = "获取计划导入模板")
    @RequestMapping(value = "getImportTemplate", method = RequestMethod.POST)
    public ResultVO<ExcelExportDto> getImportTemplate() throws IOException, IllegalAccessException {
        return hvPmWorkPlanService.getImportTemplate();
    }

    /**
     * 导出生产计划信息（支持超链接）
     *
     * @return excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @EnableFilter
    @ApiResultIgnore
    @ApiOperation(value = "导出生产计划信息（支持超链接）")
    @PutMapping(value = "/exportWorkPlanAll")
    public ResponseEntity<byte[]> exportWorkPlanAll() throws IOException, IllegalAccessException {
        return hvPmWorkPlanService.exportWorkPlanAll();
    }

    /**
     * 导出所有生产计划信息
     *
     * @return bom信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @EnableFilter
    @ApiResultIgnore
    @PutMapping(value = "/exportWork")
    @ApiOperation(value = "导出所有生产计划信息")
    public ResultVO<ExcelExportDto> exportWork() throws IOException, IllegalAccessException {
        return hvPmWorkPlanService.exportWork();
    }

}