package com.hvisions.pms.service;

import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.pms.dto.HvPmMaterialTaoPicDTO;
import com.hvisions.pms.dto.HvPmMaterialTaoPicDetailDTO;
import com.hvisions.pms.dto.HvPmXcMaterialCutPlanQueryDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
* <p>Title: HvPmMaterialTaoPicService</p>
* <p>Description: </p>
* <p>Company: www.h-visions.com</p>
* <p>create date: 2024年4月19日</p>
*
* <AUTHOR>
* @version :1.0.0
*/

public interface HvPmMaterialTaoPicService{
    /**
    * 保存
    *
    * @param hvPmMaterialTaoPicDTO HvPmMaterialTaoPic
    *
    */
    long addHvPmMaterialTaoPic(HvPmMaterialTaoPicDTO hvPmMaterialTaoPicDTO);

    /**
    * 通过id删除
    *
    * @param id 主键
    *
    */
    void deleteHvPmMaterialTaoPic(Long id);

    /**
    * 修改
    *
    * @param hvPmMaterialTaoPicDTO HvPmMaterialTaoPic
*
    */
    void updateHvPmMaterialTaoPic(HvPmMaterialTaoPicDTO hvPmMaterialTaoPicDTO);

    /**
    * 获取
    *
    * @param id 主键
    * @return HvPmMaterialTaoPic hvPmMaterialTaoPicDTO HvPmMaterialTaoPic
    */
    HvPmMaterialTaoPicDTO getHvPmMaterialTaoPicById(Long id);

    /**
    * 获取列表
    *
    * @return List<HvPmMaterialTaoPicDTO> HvPmMaterialTaoPic列表
    */
    List<HvPmMaterialTaoPicDTO> getAll();

    Page<HvPmMaterialTaoPicDTO> getPage(HvPmXcMaterialCutPlanQueryDTO hvPmMaterialTaoPicDTO);

    void sendOrder(HvPmMaterialTaoPicDTO hvPmMaterialTaoPicDTO, UserInfoDTO userInfo);

    List<HvPmMaterialTaoPicDTO> getByModelAndSegmentation(HvPmMaterialTaoPicDTO hvPmMaterialTaoPicDTO);
}