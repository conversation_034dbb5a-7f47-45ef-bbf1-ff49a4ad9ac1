package com.hvisions.pms.controller;

import com.hvisions.pms.dao.SearchMapper;
import com.hvisions.pms.dto.OperationQueryDTO;
import com.hvisions.pms.service.OrderTaskService;
import com.hvisions.pms.task.dto.AssignDTO;
import com.hvisions.pms.task.dto.OrderTaskDTO;
import com.hvisions.pms.task.dto.TaskStateCountDTO;
import com.hvisions.pms.task.dto.TaskStateCountQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>Title: OrderTaskCOntroller</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/3/26</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/orderTask")
@Api(value = "生产任务控制器")
public class OrderTaskController {

    @Autowired
    OrderTaskService orderTaskService;

    @Autowired
    SearchMapper searchMapper;

    /**
     * 根据工单ID查询生产任务
     *
     * @param orderId 工单Id
     * @return 生产任务
     */
    @GetMapping(value = "/getAllByOrderId/{orderId}")
    @ApiOperation(value = "根据工单ID查询生产任务")
    public List<OrderTaskDTO> getAllByOrderId(@PathVariable Integer orderId) {
        return orderTaskService.getAllByOrderId(orderId);
    }


    /**
     * 根据工位Id查询 生产任务
     *
     * @param workCenterId 工位ID
     * @return list
     */
    @GetMapping(value = "/getAllByWorkCenterId/{workCenterId}")
    @ApiOperation(value = "根据工位Id查询")
    public List<OrderTaskDTO> getAllByWorkCenterId(@PathVariable Integer workCenterId) {
        return orderTaskService.getAllByWorkCenterId(workCenterId);
    }


    /**
     * 根据ID列表查询生产任务
     *
     * @param idIn 生产任务ID列表
     * @return 生产任务
     */
    @PostMapping(value = "/getOperationByIdIn")
    @ApiOperation(value = "根据ID列表查询生产任务")
    public List<OrderTaskDTO> getOperationByIdIn(@RequestBody List<Integer> idIn) {
        return orderTaskService.getOperationByIdIn(idIn);
    }


    /**
     * 根据工位id，工序状态查询工单信息
     *
     * @param operationQueryDTO 分页查询对象
     * @return 工序的分页列表
     */
    @PostMapping(value = "/getTaskByQuery")
    @ApiOperation(value = "分页查询生产任务信息")
    public Page<OrderTaskDTO> getTaskByQuery(@RequestBody OperationQueryDTO operationQueryDTO) {
        return orderTaskService.getTaskByQuery(operationQueryDTO);
    }


    /**
     * 根据工位id列表查询生产任务数量
     *
     * @param workCenterIds 工位Id列表
     * @return map
     */
    @PostMapping(value = "/getTaskCountByWorkCenterId")
    @ApiOperation(value = "根据工位ID列表查询生产任务数量")
    public Map<Integer, Integer> getTaskCountByWorkCenterId(@RequestBody List<Integer> workCenterIds) {
        return orderTaskService.getTaskCountByWorkCenterId(workCenterIds);
    }

    /**
     * 获取不同状态的任务数量
     *
     * @param taskStateCountQuery 查询条件
     * @return 数量信息
     */
    @PostMapping(value = "/getTaskStateCount")
    @ApiOperation(value = "获取不同状态的任务数量")
    public List<TaskStateCountDTO> getTaskStateCount(@RequestBody TaskStateCountQuery taskStateCountQuery) {
        return searchMapper.getOperationStateCount(taskStateCountQuery);
    }


    /**
     * 分配任务
     *
     * @param assignDTO 任务与人员
     */
    @PostMapping(value = "/assignTask")
    @ApiOperation(value = "分配任务")
    public void assignTask(@RequestBody AssignDTO assignDTO) {
        orderTaskService.assignTask(assignDTO);
    }
}