package com.hvisions.pms.service;


import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.EntitySaver;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.pms.plan.*;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**
 * <p>Title: HvPmWorkPlanService</p >
 * <p>Description: 生产计划 service</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/14</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface HvPmWorkPlanService extends EntitySaver<ExcelWorkPlanDTO> {

    /**
     * 新增计划或更新计划
     *
     * @param hvPmWorkPlanAddAndUpdateDTO 新增和更新计划DTO
     * @return 实体id
     */
    int addOrUpdate(HvPmWorkPlanAddAndUpdateDTO hvPmWorkPlanAddAndUpdateDTO);


    /**
     * 删除计划
     *
     * @param id 计划id
     */
    void delete(int id);


    /**
     * 根据ID查询生产计划
     *
     * @param id ID
     * @return 生产计划信息
     */
    HvPmWorkPlanDTO getPlanById(int id);


    /**
     * 根据id列表查询生产计划
     *
     * @param idIn 根据ID列表查询生产计划
     * @return 生产计划列表
     */
    List<HvPmWorkPlanDTO> getWorkPlanListByIdIn(List<Integer> idIn);


    /**
     * 根据编码查询COde
     *
     * @param code code
     * @return 生产计划信息
     */
    HvPmWorkPlanDTO getPlanByCode(String code);

    /**
     * 模糊查询
     *
     * @param hvPmWorkPlanQueryDTO 计划查询DTO
     * @return 计划集合
     */
    Page<HvPmWorkPlanDTO> findByCondition(HvPmWorkPlanQueryDTO hvPmWorkPlanQueryDTO);

    /**
     * 下发计划
     *
     * @param issuerDTO 下发
     */
    void issuerPlan(IssuerDTO issuerDTO);

    /**
     * 重发
     *
     * @param workPlanDetailId 重发
     */
    void reIssuePlan(int workPlanDetailId);


    /**
     * 导出生产计划信息（支持超链接）
     *
     * @return excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */

    ResponseEntity<byte[]> exportWorkPlanAll() throws IOException, IllegalAccessException;

    /**
     * 导出所有生产计划信息
     *
     * @return bom信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */

    ResultVO<ExcelExportDto> exportWork() throws IOException, IllegalAccessException;

    /**
     * 获取导入模板
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    ResultVO<ExcelExportDto> getImportTemplate() throws IOException, IllegalAccessException;


    /**
     * 获取导入模板(支持超链接)
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    ResponseEntity<byte[]> getWorkPlanImportTemplate() throws IOException, IllegalAccessException;


    /**
     * 导入所有生产计划信息
     *
     * @param file 生产计划信息文档
     * @return ImportResult列表
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    ImportResult importWorkPlan(MultipartFile file) throws IllegalAccessException, ParseException, IOException;


}
