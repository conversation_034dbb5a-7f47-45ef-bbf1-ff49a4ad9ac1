package com.hvisions.pms.service;

/**
 * 单位服务
 */
public interface UnitService {
    /**
     * 是否有效单位
     *
     * @param unit 单位
     * @return 检查结果
     */
    boolean isValidUnit(String unit);

    /**
     * 查询单位ID
     *
     * @param unit 单位
     * @return 单位ID
     */
    Integer getUnitId(String unit);

    /**
     * 查询单位名称
     *
     * @param unitId 单位ID
     * @return 单位名称
     */
    String getUnitName(Integer unitId);
}
