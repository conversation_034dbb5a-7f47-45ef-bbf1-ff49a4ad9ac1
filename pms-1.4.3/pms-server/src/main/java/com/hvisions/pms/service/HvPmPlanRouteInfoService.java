package com.hvisions.pms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.pms.entity.HvPmPlanRouteInfo;

import java.util.List;

public interface HvPmPlanRouteInfoService extends IService<HvPmPlanRouteInfo> {
    Page<HvPmPlanRouteInfo> pageList(LambdaQueryWrapper<HvPmPlanRouteInfo> lqw, Integer pageNo, Integer pageSize);

    Integer InsertHvPmPlanRouteInfoList(List<HvPmPlanRouteInfo> hvPmPlanRouteInfoList);

    HvPmPlanRouteInfo getStepIdByOrderCodeAndStepCode(String orderCode, String operationCode);

    HvPmPlanRouteInfo getFinishRouteByWorkOrder(String workOrderCode);

    HvPmPlanRouteInfo getStepIdByWorkOrderCode(String workOrderCode);

    HvPmPlanRouteInfo getStepIdByCodeAndSequence(String workOrderCode);

    Boolean deleteByPlanCode(String planCode);
}
