var duScrollDefaultEasing=function(e){return.5>e?Math.pow(2*e,2)/2:1-Math.pow(2*(1-e),2)/2};angular.module("duScroll",["duScroll.scrollspy","duScroll.requestAnimation","duScroll.smoothScroll","duScroll.scrollContainer","duScroll.scrollHelpers"]).value("duScrollDuration",350).value("duScrollGreedy",!1).value("duScrollEasing",duScrollDefaultEasing),angular.module("duScroll.scrollHelpers",[]).run(["$window","$q","cancelAnimation","requestAnimation","duScrollEasing",function(e,t,n,r,o){var l=angular.element.prototype;this.$get=function(){return l};var i=function(e){return"undefined"!=typeof HTMLDocument&&e instanceof HTMLDocument||e.nodeType&&e.nodeType===e.DOCUMENT_NODE},u=function(e){return"undefined"!=typeof HTMLElement&&e instanceof HTMLElement||e.nodeType&&e.nodeType===e.ELEMENT_NODE},c=function(e){return u(e)||i(e)?e:e[0]};l.scrollTo=function(t,n,r){var o;if(angular.isElement(t)?o=this.scrollToElement:r&&(o=this.scrollToAnimated),o)return o.apply(this,arguments);var l=c(this);return i(l)?e.scrollTo(t,n):(l.scrollLeft=t,void(l.scrollTop=n))};var a,s;l.scrollToAnimated=function(e,l,i,u){i&&!u&&(u=o);var c=this.scrollLeft(),d=this.scrollTop(),f=Math.round(e-c),p=Math.round(l-d),m=null;a&&(n(a),s.reject());var g=this;if(s=t.defer(),!f&&!p)return s.resolve(),s.promise;var v=function(e){null===m&&(m=e);var t=e-m,n=t>=i?1:u(t/i);g.scrollTo(c+Math.ceil(f*n),d+Math.ceil(p*n)),1>n?a=r(v):(a=null,s.resolve())};return g.scrollTo(c,d),a=r(v),s.promise},l.scrollToElement=function(e,t,n,r){var o=c(this),l=this.scrollTop()+c(e).getBoundingClientRect().top-t;return u(o)&&(l-=o.getBoundingClientRect().top),this.scrollTo(0,l,n,r)};var d={scrollLeft:function(t,n,r){if(angular.isNumber(t))return this.scrollTo(t,this.scrollTop(),n,r);var o=c(this);return i(o)?e.scrollX||document.documentElement.scrollLeft||document.body.scrollLeft:o.scrollLeft},scrollTop:function(t,n,r){if(angular.isNumber(t))return this.scrollTo(this.scrollTop(),t,n,r);var o=c(this);return i(o)?e.scrollY||document.documentElement.scrollTop||document.body.scrollTop:o.scrollTop}},f=function(e,t){return function(n,r){return r?t.apply(this,arguments):e.apply(this,arguments)}};for(var p in d)l[p]=l[p]?f(l[p],d[p]):d[p]}]),angular.module("duScroll.polyfill",[]).factory("polyfill",["$window",function(e){var t=["webkit","moz","o","ms"];return function(n,r){if(e[n])return e[n];for(var o,l=n.substr(0,1).toUpperCase()+n.substr(1),i=0;i<t.length;i++)if(o=t[i]+l,e[o])return e[o];return r}}]),angular.module("duScroll.requestAnimation",["duScroll.polyfill"]).factory("requestAnimation",["polyfill","$timeout",function(e,t){var n=0,r=function(e){var r=(new Date).getTime(),o=Math.max(0,16-(r-n)),l=t(function(){e(r+o)},o);return n=r+o,l};return e("requestAnimationFrame",r)}]).factory("cancelAnimation",["polyfill","$timeout",function(e,t){var n=function(e){t.cancel(e)};return e("cancelAnimationFrame",n)}]),angular.module("duScroll.spyAPI",["duScroll.scrollContainerAPI"]).factory("spyAPI",["$rootScope","scrollContainerAPI","duScrollGreedy",function(e,t,n){var r=function(t){return function(){var r=t.container,o=r[0],l=0;o instanceof HTMLElement&&(l=o.getBoundingClientRect().top);var i,u,c,a,s,d;for(a=t.spies,u=t.currentlyActive,c=void 0,i=0;i<a.length;i++)s=a[i],d=s.getTargetPosition(),d&&d.top+s.offset-l<20&&-1*d.top+l<d.height&&(!c||c.top<d.top)&&(c={top:d.top,spy:s});c&&(c=c.spy),u===c||n&&!c||(u&&(u.$element.removeClass("active"),e.$broadcast("duScrollspy:becameInactive",u.$element)),c&&(c.$element.addClass("active"),e.$broadcast("duScrollspy:becameActive",c.$element)),t.currentlyActive=c)}},o={},l=function(e){var t=e.$id,n={spies:[]};return n.handler=r(n),o[t]=n,e.$on("$destroy",function(){i(e)}),t},i=function(e){var t=e.$id,n=o[t],r=n.container;r&&r.off("scroll",n.handler),delete o[t]},u=l(e),c=function(e){return o[e.$id]?o[e.$id]:e.$parent?c(e.$parent):o[u]},a=function(e){var t,n,r=e.$element.scope();if(r)return c(r);for(n in o)if(t=o[n],-1!==t.spies.indexOf(e))return t},s=function(e){var n=a(e);a(e).spies.push(e),n.container||(n.container=t.getContainer(e.$element.scope()),n.container.on("scroll",n.handler).triggerHandler("scroll"))},d=function(e){var t=a(e);e===t.currentlyActive&&(t.currentlyActive=null);var n=t.spies.indexOf(e);-1!==n&&t.spies.splice(n,1)};return{addSpy:s,removeSpy:d,createContext:l,destroyContext:i,getContextForScope:c}}]),angular.module("duScroll.scrollContainerAPI",[]).factory("scrollContainerAPI",["$document",function(e){var t={},n=function(e,n){var r=e.$id;return t[r]=n,r},r=function(e){return t[e.$id]?e.$id:e.$parent?r(e.$parent):void 0},o=function(n){var o=r(n);return o?t[o]:e},l=function(e){var n=r(e);n&&delete t[n]};return{getContainerId:r,getContainer:o,setContainer:n,removeContainer:l}}]),angular.module("duScroll.smoothScroll",["duScroll.scrollHelpers","duScroll.scrollContainerAPI"]).directive("duSmoothScroll",["duScrollDuration","scrollContainerAPI",function(e,t){return{link:function(n,r,o){r.on("click",function(r){if(o.href&&-1!==o.href.indexOf("#")){var l=document.getElementById(o.href.replace(/.*(?=#[^\s]+$)/,"").substring(1));if(l&&l.getBoundingClientRect){r.stopPropagation&&r.stopPropagation(),r.preventDefault&&r.preventDefault();var i=o.offset?parseInt(o.offset,10):0,u=o.duration?parseInt(o.duration,10):e,c=t.getContainer(n);c.scrollToElement(angular.element(l),isNaN(i)?0:i,isNaN(u)?0:u)}}})}}}]),angular.module("duScroll.spyContext",["duScroll.spyAPI"]).directive("duSpyContext",["spyAPI",function(e){return{restrict:"A",scope:!0,compile:function(){return{pre:function(t){e.createContext(t)}}}}}]),angular.module("duScroll.scrollContainer",["duScroll.scrollContainerAPI"]).directive("duScrollContainer",["scrollContainerAPI",function(e){return{restrict:"A",scope:!0,compile:function(){return{pre:function(t,n,r){r.$observe("duScrollContainer",function(r){angular.isString(r)&&(r=document.getElementById(r)),r=angular.isElement(r)?angular.element(r):n,e.setContainer(t,r),t.$on("$destroy",function(){e.removeContainer(t)})})}}}}}]),angular.module("duScroll.scrollspy",["duScroll.spyAPI"]).directive("duScrollspy",["spyAPI","$timeout",function(e,t){var n=function(e,t,n){angular.isElement(e)?this.target=e:angular.isString(e)&&(this.targetId=e),this.$element=t,this.offset=n};return n.prototype.getTargetElement=function(){return!this.target&&this.targetId&&(this.target=document.getElementById(this.targetId)),this.target},n.prototype.getTargetPosition=function(){var e=this.getTargetElement();return e?e.getBoundingClientRect():void 0},n.prototype.flushTargetCache=function(){this.targetId&&(this.target=void 0)},{link:function(r,o,l){var i,u=l.ngHref||l.href;u&&-1!==u.indexOf("#")?i=u.replace(/.*(?=#[^\s]+$)/,"").substring(1):l.duScrollspy&&(i=l.duScrollspy),i&&t(function(){var t=new n(i,o,-(l.offset?parseInt(l.offset,10):0));e.addSpy(t),r.$on("$destroy",function(){e.removeSpy(t)}),r.$on("$locationChangeSuccess",t.flushTargetCache.bind(t)),r.$on("$stateChangeSuccess",t.flushTargetCache.bind(t))},0)}}}]);