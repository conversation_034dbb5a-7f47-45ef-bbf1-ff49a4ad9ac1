package com.hvisions.activiti.dto.process;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>Title: ProcessDefinationQuery</p>
 * <p>Description: 部署对象分页查询对象</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/3/27</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "部署对象分页查询对象,暂不支持排序")
public class ProcessDefinationQuery extends PageInfo {

    /**
     * 部署流程名称
     */
    @ApiModelProperty(value = "部署流程名称,模糊查询")
    private String processDefinitionName;

    /**
     * 流程定义分组
     */
    @ApiModelProperty(value = "流程定义分组")
    private String processDefinitionCategory;
    /**
     * 流程定义分组模糊查询
     */
    @ApiModelProperty(value = "流程定义分组模糊查询")
    private String processDefinitionCategoryLike;
    /**
     * 流程定义分组不等于
     */
    @ApiModelProperty(value = "流程定义分组不等于")
    private String processDefinitionCategoryNotEqual;
    /**
     * 流程定义键值
     */
    @ApiModelProperty(value = "流程定义键值,模糊查询")
    private String processDefinitionKey;
    /**
     * 是否激活
     */
    @ApiModelProperty(value = "是否激活")
    private Boolean active;
    /**
     * 流程定义id
     */
    @ApiModelProperty(value = "流程定义id,精确查询")
    private String processDefinitionId;

    /**
     * 最后一个版本
     */
    @ApiModelProperty(value = "最后一个版本")
    private Boolean lastVersion;

    /**
     * 可以执行的用户
     */
    @ApiModelProperty(value = "可以执行的用户")
    private String userId;
}









